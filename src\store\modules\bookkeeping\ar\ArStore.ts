/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'
import servicev1 from '../../../../api/requestNew'
import FileSaver from 'file-saver'

const http = service
const httpv1 = servicev1
const ArStore = {
    namespaced: true,
    state: {
        invoicesList: [],
        invoicesListByPdf: [],
        ocrInvoicesListByPdf: [],
        invoicesHistoryList: [],
        invoiceDetail: {},
        totalNumber: 0,
        arTopCoaList: [],
    },
    mutations: {
        updateInvoicesList(state: {invoicesList: any[]}, list: any) {
            state.invoicesList = [...list]
        },
        updateInvoicesHistoryList(state: {invoicesHistoryList: any[]}, list: any) {
            state.invoicesHistoryList = [...list]
        },
        updateInvoiceDetail(state: {invoiceDetail: any}, detail: any) {
            state.invoiceDetail = {...detail}
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateInvoicesListByPdf(state: {invoicesListByPdf: any[]}, list: any) {
            state.invoicesListByPdf = [...list]
        },
        updateOcrInvoicesList(state: {ocrInvoicesListByPdf: any[]}, list: any) {
            state.ocrInvoicesListByPdf = [...list]
        },
        updateArTopCoa(state: {arTopCoaList: any[]}, list: any) {
            state.arTopCoaList = [...list]
        },
    },
    actions: {
        // async fetchInvoicesList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        //     const response = await http.post('/bk/file/list', payload)
        //     store.commit('updateInvoicesList', response.data.data.list)
        //     store.commit('updateTotalFoundNumber', response.data.data.totalCount)
        //     return response
        // },
        async fetchInvoicesHistoryList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await http.post('/bk/ar/invoice/list', payload)
            store.commit('updateInvoicesHistoryList', response.data.data.list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchInvoicesHistoryListV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.get('/invoice-statement/api/v1/ar', {params: payload})

            const list = response.data.data
            const totalNumber = response.data.paginated ? response.data.paginated.total : list.length

            store.commit('updateInvoicesHistoryList', list)
            store.commit('updateTotalFoundNumber', totalNumber)
            return response
        },
        async checkReferenceNoRepetition(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await http.post('/bk/ar/invoice/list', payload)
            return response
        },
        async checkReferenceNoRepetitionV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.get('/invoice-statement/api/v1/ar', {params: payload})
            return response
        },
        async reverseSalesRequestV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.patch(`/invoice-statement/api/v1/ar/${payload.id}/reverse`, {
                creator: payload?.creator,
                creator_name: payload?.creator_name,
                posting_date: payload?.posting_date,
            })
        },

        async convertSalesRequestV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.patch(`/invoice-statement/api/v1/ar/${payload.id}/cash`, {
                creator: payload?.creator,
                creator_name: payload?.creator_name,
            })
        },

        async fetchArInvoiceDetailWithIdv1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any, //{id: any},
        ) {
            if (payload.id) {
                const response = await httpv1.get(`/invoice-statement/api/v1/ar/${payload.id}`)
                store.commit('updateInvoiceDetail', response.data.data)
                return response
            } else if (payload.invoice_no) {
                const response = await httpv1.get(`/invoice-statement/api/v1/ar`, {params: payload})
                store.commit('updateInvoiceDetail', response.data.data)
                return response
            }
        },
        async deletePdfWithId(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/file/${payload}`)
        },
        // async uploadArInvoicePdf(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        //     const formData = payload
        //     const response = await http.post('/bk/file/upload', formData)
        //     return response
        // },
        // async fetchArInvoiceListWithPdf(
        //     store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        //     payload: {fileId: any},
        // ) {
        //     const response = await http.post(`/bk/ar/pdf/${payload.fileId}/invoice/list`)
        //     store.commit('updateInvoicesListByPdf', response.data.data)
        //     return response
        // },
        // async fetchArOcrResultByPdfId(
        //     store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        //     payload: {fileId: any},
        // ) {
        //     const response = await http.post(`/bk/ocr/pdf/${payload.fileId}/invoice/list`)
        //     store.commit('updateOcrInvoicesList', response.data.data)
        //     return response
        // },
        async createInvoice(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/ar/invoice', payload)
            return response
        },
        async createInvoicev1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post('/invoice-statement/api/v1/ar', payload)
            return response
        },
        async noestatementBR(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/br/reconciliation/noestatement/submit', payload)
            return response
        },
        async fetchArTopCoa(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {clientId: any},
        ) {
            const response = await http.get(`/bk/ar/coa/top?clientId=${payload.clientId}`)
            store.commit('updateArTopCoa', response.data)
            return response
        },
        async fetchArTopCoaV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get(`/invoice-statement/api/v1/ar/coa/top`, {params: payload})
            store.commit('updateArTopCoa', response.data.data)
            return response
        },
        async sendArInvoiceEmail(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/ar/invoice/email', payload)
            return response
        },
        async sendArInvoiceEmailv1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: string,
        ) {
            const response = await httpv1.post(`invoice-statement/api/v1/ar/${payload}/email`)
            return response
        },
        async exportArInvoiceList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any},
        ) {
            const subPath = `handleExport/ar/${payload.company_code}`
            const response = await httpv1.get(subPath, {
                responseType: 'blob',
            })
            const filename = (response.headers['content-disposition'] || '')
                .split('filename=')[1]
                .replace('"', '')
                .replace('"', '')

            const blob = new Blob([response.data], {
                type: response.headers['content-type'],
                // type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            })
            FileSaver.saveAs(blob, filename)
        },

        async updateBillToEmail(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.patch('/invoice-statement/api/v1/ar/bill-to-email', payload)
            return response
        },
        async syncArFromSap(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post('/ESHubService/pgo_sync_ar', payload)
            return response
        },
    },
}

export default ArStore
