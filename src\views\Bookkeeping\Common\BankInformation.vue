<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import BankInfoForm from '@/components/bookkeepingComponents/CommonComponents/BankInfoForm.vue'
import {message, Modal} from 'ant-design-vue'
import {computed, nextTick, onBeforeUnmount, onMounted, ref, watch} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useRouter} from 'vue-router'
import {EditOutlined, SearchOutlined, PlusOutlined, MailOutlined} from '@ant-design/icons-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import * as _ from 'lodash'
import {UserCompany} from '@/lib/storage'

const userCompany: any = UserCompany.get() || []
const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const props = defineProps({
    email: {
        type: String,
    },
    showTutorial: {
        type: Boolean,
    },
    company: {
        type: Object,
    },
})
const itemList = ref([] as any[])
const searchForm = ref({
    bank_account: '',
})
const current = ref({
    bankName: '',
    bankAccount: '',
    accountType: '',
    currency: '',
})

const editMode = ref(false)
const show = ref(false)
const tableLoading = ref(false)
const currentPageNumber = ref(1)
const pageSize = ref(10)
const noPlaidCursor = ref(true)
const bankNameTypeOptions = ref([
    {
        key: '0',
        value: 'BMO',
        label: 'Bank of Montreal',
        img: new URL('/src/assets/image/bank/BMO.png', import.meta.url),
    },
    {
        key: '1',
        value: 'CIBC',
        label: 'Canadian Imperial Bank of Commerce',
        img: new URL('/src/assets/image/bank/CIBC.png', import.meta.url),
    },
    {
        key: '2',
        value: 'RBC',
        label: 'Royal Bank Canada',
        img: new URL('/src/assets/image/bank/RBC.png', import.meta.url),
    },
    {
        key: '3',
        value: 'Dejardin',
        label: 'Desjardins Bank',
        img: new URL('/src/assets/image/bank/desjardins.png', import.meta.url),
    },
    {
        key: '4',
        value: 'TD',
        label: 'Toronto-Dominion Bank',
        img: new URL('/src/assets/image/bank/TD.png', import.meta.url),
    },
    {
        key: '5',
        value: 'JPCB',
        label: 'JPMorgan Chase Bank',
        img: new URL('/src/assets/image/bank/JPCB.png', import.meta.url),
    },
    {
        key: '6',
        value: 'FB',
        label: 'Fremont Bank',
        img: new URL('/src/assets/image/bank/FB.png', import.meta.url),
    },
    {
        key: '7',
        value: 'NBC',
        label: 'National Bank of Canada',
        img: new URL('/src/assets/image/bank/NBC.svg', import.meta.url),
    },
    {
        key: '8',
        value: 'ASPIRE',
        label: 'Aspire Bank',
        img: new URL('/src/assets/image/bank/ASPIRE.png', import.meta.url),
    },
    {
        key: '9',
        value: 'NOVA',
        label: 'Nova Scotia',
        img: new URL('/src/assets/image/bank/NOVA.png', import.meta.url),
    },
    {
        key: '10',
        value: 'AMEX',
        label: 'American Express',
        img: new URL('/src/assets/image/bank/AMEX.png', import.meta.url),
    },
    {
        key: '11',
        value: 'SEB',
        label: 'South East Bank',
        img: new URL('/src/assets/image/bank/SEB.png', import.meta.url),
    },
    {
        key: '12',
        value: 'EWB',
        label: 'East West Bank',
        img: new URL('/src/assets/image/bank/EWB.png', import.meta.url),
    },
    {
        key: '13',
        value: 'CITI',
        label: 'CITI Bank',
        img: new URL('/src/assets/image/bank/CITI.png', import.meta.url),
    },
    {
        key: '14',
        value: 'BOC',
        label: 'Bank Of China',
        img: new URL('/src/assets/image/bank/BOC.png', import.meta.url),
    },
    {
        key: '15',
        value: 'CMB',
        label: 'China Merchants Bank',
        img: new URL('/src/assets/image/bank/CMB.png', import.meta.url),
    },
    {
        key: '16',
        value: 'ICBC',
        label: 'Industrial And Commercial Bank Of China',
        img: new URL('/src/assets/image/bank/ICBC.jpeg', import.meta.url),
    },
    {
        key: '17',
        value: 'BONJ',
        label: 'Bank Of NanJing',
        img: new URL('/src/assets/image/bank/BONJ.jpeg', import.meta.url),
    },
    {
        key: '18',
        value: 'SANTANDER',
        label: 'Santander Bank',
        img: new URL('/src/assets/image/bank/SANTANDER.png', import.meta.url),
    },
    {
        key: '19',
        value: 'BASEINET',
        label: 'BASEinet Bank',
        img: new URL('/src/assets/image/bank/BASEINET.jpeg', import.meta.url),
    },
    {
        key: '20',
        value: 'MONEX',
        label: 'MONEX Bank',
        img: new URL('/src/assets/image/bank/MONEX.png', import.meta.url),
    },
    {
        key: '21',
        value: 'BBVA',
        label: 'BBVA Bank',
        img: new URL('/src/assets/image/bank/BBVA.png', import.meta.url),
    },
    {
        key: '22',
        value: 'HSBC',
        label: 'HSBC Bank',
        img: new URL('/src/assets/image/bank/HSBC.png', import.meta.url),
    },
])
const isFetch = ref(false)
const timer = ref()

// mapActions
const fetchAllPlaidBankList = (payload: any) => store.dispatch('BankInfoStore/fetchAllPlaidBankListV1', payload)
const deleteBank = (payload: any) => store.dispatch('BankInfoStore/deleteBank', payload)
const sendBankInfoEmail = (payload: any) => store.dispatch('BankInfoStore/sendBankInfoEmailV1', payload)
const fetchPlaidBankList = (query?: any) => store.dispatch('BrStore/fetchPlaidBank', query)
// mapMutations
const updateBankAccountTypeOptions = () => store.commit('CommonDropDownStore/updateBankAccountTypeOptions')

const updateData = async (data = {}) => {
    const searchObj: any = {
        // ...data,
        page_index: currentPageNumber.value,
        page_size: pageSize.value,
        company_code: userCompany[0].code,
        // $limit: pageSize.value,
        // $skip: (currentPageNumber.value - 1) * pageSize.value,
    }
    searchObj['$sort[create_time]'] = 'desc'
    if (searchForm.value.bank_account) {
        searchObj['account_no[$like]'] = `%${searchForm.value.bank_account}%`
        //searchObj.account_no = searchForm.value.bank_account
    }
    try {
        tableLoading.value = true
        await fetchAllPlaidBankList(searchObj)
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const search = async () => {
    currentPageNumber.value = 1
    await updateData(searchForm.value)
}
const add = () => {
    show.value = true
    editMode.value = false
    current.value = {} as any
    noPlaidCursor.value = true
}
const edit = (record: any) => {
    noPlaidCursor.value = record.plaidCursor === null
    show.value = true
    editMode.value = true
    current.value = {...record}
}
const showDialog = (bool: any) => {
    show.value = bool
}
const dismiss = () => {
    showDialog(false)
    editMode.value = false
}
const remove = (record: any) => {
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: `${i18n.t('bankInfo.bankName')} ` + record.bankName + i18n.t('bkCommonTag.msgDeleteConfirm'),
        async onOk() {
            try {
                const response = await deleteBank(record.id)
                if (response.data.code === 1000) {
                    message.success(i18n.t('ApComponents.success'))
                }
                // else {
                //     message.error({
                //         content: response.data.msg,
                //         duration: 3,
                //     })
                // }
                await updateData()
            } catch (error) {
                console.log(error)
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
        okType: 'danger',
        onCancel() {
            console.log('')
        },
    })
}
const changeCurrentPageNumber = (pageNumber: number) => {
    currentPageNumber.value = pageNumber
    updateData()
}
const changePageSize = (_pageSize: number) => {
    pageSize.value = _pageSize
    currentPageNumber.value = 1
    updateData()
}

const remappingBankType = (bankType: string): string => {
    const typeMapping = new Map([
        ['Cheque', 'chequing'],
        ['Saving', 'saving'],
        ['Credit', 'credit'],
    ])
    return typeMapping.get(bankType) || ''
}

const emit = defineEmits(['showLoading', 'closeLoading'])
const sendEmail = async (record: any) => {
    try {
        if (props.company?.email === '') {
            message.error({
                content: 'Please set your email.',
                duration: 3,
            })
            return
        }
        tableLoading.value = true
        emit('showLoading')
        const {name, email} = props.company as any
        const {id, account_no, code, company_code, currency, bank_type} = record
        const payload = {
            bank_id: id,
            bank_account: account_no,
            bank_name: code,
            currency: currency,
            bank_type: remappingBankType(bank_type),
            company_code: company_code,
            company_name: name,
            company_email: email,
        }
        const response = await sendBankInfoEmail(payload)
        if (response.data.statusCode === 200) {
            message.success(i18n.t('ApComponents.success'))
        }
        // else {
        //     message.error({
        //         content: response.data.message || 'failed',
        //         duration: 3,
        //     })
        // }
    } catch (error) {
        console.log(error)
    } finally {
        emit('closeLoading')
        tableLoading.value = false
    }
}
const bankNmFull = (row: any) => {
    const result = bankNameTypeOptions.value.find((item: any) => {
        return item.value === row.code
    })
    return result ? result.label : ''
}
const bankLogo = (bankName: any) => {
    const result = bankNameTypeOptions.value.find((item: any) => {
        return item.value === bankName
    })
    return (result ? result.img : '') as string
}
// computed mapState
const accountTypeOptions = computed(() => store.state.CommonDropDownStore.bankAccountTypeOptions)
const plaidBankList = computed(() =>
    store.state.BrStore.plaidBankList.map((x: any) => {
        return {
            id: x.id,
            bank_account: x.bank_account,
            plaid_status: x.plaid_status,
            email_status: x.email_status,
        }
    }),
)
const bankList = computed(() => {
    const copied = _.cloneDeep(store.state.BankInfoStore.bankList)
    copied.forEach((x: any) => {
        const typeIndex = accountTypeOptions.value.find((y: any) => y.value === x.bank_type)
        const plaidBank = plaidBankList.value.find((y: any) => y.bank_account === x.account_no)
        x.accountType = typeIndex ? typeIndex.key : ''
        x.plaidStatus = plaidBank?.plaid_status
        x.emailStatus = plaidBank?.email_status
    })
    return copied
})
const totalNumber = computed(() => store.state.BankInfoStore.totalNumber)

// computed mapGetters
const showTextByCode = computed({
    get: () => store.getters['CommonDropDownStore/showTextByCode'],
    set: (param?: any) => store.getters['CommonDropDownStore/showTextByCode'](param),
})
onMounted(async () => {
    itemList.value = ['common', router.currentRoute.value.meta.title]
    updateBankAccountTypeOptions()
    await updateData()
    console.log('check company info: ', props.company, props)
})
const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer.value)
    timer.value = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer.value)
        }
    }, 1000)
}
const changePage = () => {
    updateData()
}
const addTutorial = () => {
    nextTick(() => {
        const addBankInfo: any = document.getElementById('add-bank-info')

        const tutorialHeight = addBankInfo.clientHeight + 20
        const tutorialWidth = addBankInfo.clientWidth + 20
        const bodyRect = document.body.getBoundingClientRect()
        const elemRect = addBankInfo.getBoundingClientRect()
        const topOffset = elemRect.top - bodyRect.top - 10
        const leftOffset = elemRect.left - bodyRect.left - 10

        const appEle: any = document.getElementById('app')
        const tutorialDiv = document.createElement('div')
        tutorialDiv.id = 'turorialBankInfo'
        const tutorialStr = `
                <div class="tutorial-main-page" style="height: ${tutorialHeight}px; width: ${tutorialWidth}px; top: ${topOffset}px; left: ${leftOffset}px">
                    <div
                        class="inner-content"
                        style="height: ${tutorialHeight - 10}px; width: ${
            tutorialWidth - 10
        }px; top: ${topOffset}px; left: ${leftOffset}px"
                    >
                    </div>
                </div>
                <div
                    class="tutorial-tips"
                    style="top: ${topOffset + tutorialHeight + 30}px; left: ${leftOffset - 160}px; width: 240px"
                >
                    <div class="tips-content">
                        <div class="title">Please Create Banks.</div>
                        <button class="btn-select" type="link" id="turorialBankInfoBtn"> Got it </button>
                    </div>
                    <div class="arrow-up"></div>
                </div>
                `
        tutorialDiv.innerHTML = tutorialStr
        appEle.appendChild(tutorialDiv)
    })
}
watch(
    () => props.showTutorial,
    (newValue, oldValue) => {
        if (newValue !== oldValue) {
            if (newValue) {
                nextTick(() => {
                    addTutorial()
                    setTimeout(() => {
                        const _turorialBankInfoBtn = document.getElementById('turorialBankInfoBtn')
                        const _turorialBankInfo = document.getElementById('turorialBankInfo')
                        if (_turorialBankInfoBtn)
                            _turorialBankInfoBtn.addEventListener('click', function () {
                                _turorialBankInfo!.remove()
                                router.push({
                                    path: '/bookkeeping/ap/uploadInvoice',
                                    query: {showTutorial: 'true'},
                                })
                            })
                    }, 100)
                })
            }
        }
    },
    {immediate: true},
)
onBeforeUnmount(async () => {
    await fetchPlaidBankList({company_code: userCompany[0].code})
    const x = document.getElementById('turorialBankInfo')
    if (x) x.remove()
})
</script>
<template>
    <div class="page-container-customer" ref="mainRef">
        <div class="history-page-header">
            <div class="search-group-wrap">
                <a-input
                    v-model:value="searchForm.bank_account"
                    :placeholder="$t('RcInfoTable.account')"
                    :disabled="tableLoading"
                    class="search-input"
                    @input="inputChange"
                    @pressEnter="search"
                >
                    <template #suffix>
                        <search-outlined />
                    </template>
                </a-input>
                <a-popover trigger="click" placement="bottom">
                    <template #content>
                        <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                            <div class="search-input-group"></div>

                            <a-button type="primary" shape="round" :disabled="tableLoading" @click="search">
                                <template #icon>
                                    <search-outlined />
                                </template>
                                {{ i18n.t('commonTag.search') }}
                            </a-button>
                        </a-form>
                    </template>
                    <a-button v-if="false" class="search-button" :disabled="tableLoading">
                        <template #icon>
                            <svg-icon name="icon_filter"></svg-icon>
                        </template>
                        <!-- {{ i18n.t('commonTag.filter') }} -->
                    </a-button>
                </a-popover>
            </div>
            <a-button
                type="primary"
                shape="round"
                class="add-button"
                :disabled="tableLoading"
                @click="add()"
                id="add-bank-info"
            >
                <template #icon>
                    <plus-outlined />
                </template>
                {{ i18n.t('commonTag.new') }}
            </a-button>
        </div>

        <div class="history-page-content">
            <a-table
                :dataSource="bankList"
                :loading="tableLoading"
                :pagination="false"
                :scroll="{y: 'calc(100vh - 400px)'}"
            >
                <a-table-column align="right" :title="''" data-index="code" width="40px">
                    <template #default="{record}">
                        <img src="@/assets/image/bank/BMO.png" v-if="'BMO' == record.code" height="20" />
                        <img src="@/assets/image/bank/CIBC.png" v-if="'CIBC' == record.code" height="20" />
                        <img src="@/assets/image/bank/RBC.png" v-if="'RBC' == record.code" height="20" />
                        <img src="@/assets/image/bank/desjardins.png" v-if="'Dejardin' == record.code" height="20" />
                        <img src="@/assets/image/bank/TD.png" v-if="'TD' == record.code" height="20" />
                        <img src="@/assets/image/bank/JPCB.png" v-if="'JPCB' == record.code" height="20" />
                        <img src="@/assets/image/bank/FB.png" v-if="'FB' == record.code" height="20" />
                        <img src="@/assets/image/bank/ASPIRE.png" v-if="'ASPIRE' == record.code" height="20" />
                        <img src="@/assets/image/bank/NOVA.png" v-if="'NOVA' == record.code" height="20" />
                        <img src="@/assets/image/bank/AMEX.png" v-if="'AMEX' == record.code" height="20" />
                        <img src="@/assets/image/bank/SEB.png" v-if="'SEB' == record.code" height="20" />
                        <img src="@/assets/image/bank/EWB.png" v-if="'EWB' == record.code" height="20" />
                        <img src="@/assets/image/bank/CITI.png" v-if="'CITI' == record.code" height="20" />
                        <img src="@/assets/image/bank/BOC.png" v-if="'BOC' == record.code" height="20" />
                        <img src="@/assets/image/bank/CMB.png" v-if="'CMB' == record.code" height="20" />
                        <img src="@/assets/image/bank/ICBC.jpeg" v-if="'ICBC' == record.code" height="20" />
                        <img src="@/assets/image/bank/BONJ.jpeg" v-if="'BONJ' == record.code" height="20" />
                        <img src="@/assets/image/bank/SANTANDER.png" v-if="'SANTANDER' == record.code" height="20" />
                        <img src="@/assets/image/bank/BASEINET.jpeg" v-if="'BASEINET' == record.code" height="20" />
                        <img src="@/assets/image/bank/MONEX.png" v-if="'MONEX' == record.code" height="20" />
                        <img src="@/assets/image/bank/BBVA.png" v-if="'BBVA' == record.code" height="20" />
                        <img src="@/assets/image/bank/HSBC.png" v-if="'HSBC' == record.code" height="20" />
                    </template>
                </a-table-column>
                <a-table-column align="left" :title="i18n.t('bankInfo.bankName')" data-index="code" width="150px">
                    <template #default="{record}">
                        <div>{{ bankNmFull(record) }}</div>
                    </template>
                </a-table-column>
                <a-table-column
                    align="center"
                    :title="i18n.t('bankInfo.bankAccount')"
                    data-index="account_no"
                    width="150px"
                />
                <a-table-column
                    align="center"
                    :title="i18n.t('bankInfo.accountType')"
                    data-index="accountType"
                    width="60px"
                >
                    <template #default="{record}">
                        <div>
                            {{ showTextByCode('AccountType', record.accountType, 'cn') }}
                        </div>
                    </template>
                </a-table-column>
                <a-table-column align="center" :title="i18n.t('bankInfo.currency')" data-index="currency" width="40px">
                    <template #default="{record}">
                        <div>
                            {{ showTextByCode('Currency', record.currency, 'en') }}
                        </div>
                    </template>
                </a-table-column>
                <a-table-column
                    align="center"
                    :title="i18n.t('bankInfo.authorizaiton')"
                    data-index="accessToken"
                    width="40px"
                >
                    <template #default="{record}">
                        <span style="font-size: 18px">
                            <!-- <img
                                class="br-icon"
                                v-if="record.plaid_status == '0'"
                                src="@/assets/image/icon/icon_in_progress.png"
                            /> -->
                            <img
                                class="br-icon"
                                v-if="record.plaid_status == '1'"
                                src="@/assets/image/icon/icon_complete.png"
                            />
                            <img
                                class="br-icon"
                                v-if="
                                    record.plaid_status == null ||
                                    record.plaid_status == undefined ||
                                    record.plaid_status == '0' ||
                                    record.plaid_status == '2'
                                "
                                src="@/assets/image/icon/icon_incomplete.png"
                            />
                        </span>
                    </template>
                </a-table-column>
                <a-table-column
                    align="center"
                    :title="i18n.t('bankInfo.operation')"
                    key="operation"
                    fixed="right"
                    width="100px"
                >
                    <template #default="{record}">
                        <span>
                            <a-button title="Edit Bank Info" class="btn-txt" type="link" @click="edit(record)">
                                <edit-outlined />
                            </a-button>

                            <a-button
                                title="Send authorizaiton Email"
                                class="btn-txt"
                                type="link"
                                @click="sendEmail(record)"
                            >
                                <mail-outlined />
                            </a-button>
                        </span>
                    </template>
                </a-table-column>
            </a-table>
            <div class="pagination-wrap">
                <a-pagination
                    v-model:current="currentPageNumber"
                    v-model:page-size="pageSize"
                    :disabled="tableLoading"
                    :hideOnSinglePage="false"
                    :showSizeChanger="true"
                    :total="totalNumber"
                    @change="changePage"
                />
                <span
                    >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                    {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                >
            </div>
        </div>

        <a-modal
            :title="editMode ? i18n.t('bankInfo.editBankTitle') : i18n.t('bankInfo.createBankTitle')"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="620"
        >
            <bank-info-form
                :current-bank="current"
                :edit-mode="editMode"
                :noPlaidCursor="noPlaidCursor"
                :isFetch="isFetch"
                @updateData="updateData({})"
                @dismiss="dismiss"
            ></bank-info-form>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
.page-container-customer {
    border-radius: 10px;
    background-color: #fff;

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 24px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 60px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button + .search-button {
                min-width: 113px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
        }
    }

    .history-page-content {
        padding: 12px 20px;

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }

    .br-icon {
        width: 20px;
    }
}
</style>
