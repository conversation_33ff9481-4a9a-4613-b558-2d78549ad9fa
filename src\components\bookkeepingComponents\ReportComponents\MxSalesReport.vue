<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount, watch} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {numberToCurrency} from '@/lib/utils'
import {ExportOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'

import * as _ from 'lodash'
import moment from 'moment'
import type {Dayjs} from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'

const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global

const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

const today = ref(moment().format('YYYY-MM-DD'))
const dayOfMonth = ref(moment().date(1).format('YYYY-MM-DD'))
const tableLoading = ref(false)
const dataSource = computed(() =>
    store.state.ReportStore.sPReportsList.map((i: any) => {
        return {
            ...i,
            net_amount: numberToCurrency(i.net_amount),
            vat: numberToCurrency(i.vat),
            discount: numberToCurrency(i.discount),
            mx_isr: numberToCurrency(i.mx_isr),
            mx_iva: numberToCurrency(i.mx_iva),
            total_tax: numberToCurrency(i.total_tax),
            total_fee: numberToCurrency(i.total_fee),
        }
    }),
)

const total = computed(() =>
    store.state.ReportStore.sPReportsSummary.map((i: any) => {
        return {
            ...i,
            net_amount: numberToCurrency(i.net_amount),
            vat: numberToCurrency(i.vat),
            discount: numberToCurrency(i.discount),
            mx_isr: numberToCurrency(i.mx_isr),
            mx_iva: numberToCurrency(i.mx_iva),
            total_tax: numberToCurrency(i.total_tax),
            total_fee: numberToCurrency(i.total_fee),
        }
    }),
)

const searchForm = reactive<{[key: string]: string | undefined}>({
    start_date: dayOfMonth.value,
    end_date: today.value,
    report_type: 'AR',
    company_code: undefined,
    company_name: undefined,
})

const fetchReportsList = (payload: any) => store.dispatch('ReportStore/getIntegrationSPReportsList', payload)
const updateSPReportsList = (list: any) => store.commit('ReportStore/updateSPReportsList', list)
const updateSPReportsSummary = (list: any) => store.commit('ReportStore/updateSPReportsSummary', list)

const disabledDate = (current: Dayjs) => {
    return current && current > moment().endOf('day')
}

const tableScrollY = computed(() => {
    return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight - headerHeight * total.value.length
})

const columns: ColumnsType = [
    {
        title: i18n.t('reports.customer'),
        dataIndex: 'bp_name',
        key: 'bp_name',
        align: 'left',
        width: 180,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.postingDate'),
        dataIndex: 'posting_date',
        key: 'posting_date',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.documentNo'),
        dataIndex: 'engine_doc_no',
        key: 'engine_doc_no',
        align: 'left',
        width: 150,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.currency'),
        dataIndex: 'currency',
        key: 'currency',
        align: 'center',
        width: 80,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.netAmount'),
        dataIndex: 'net_amount',
        key: 'net_amount',
        align: 'right',
        width: 150,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.VAT'),
        dataIndex: 'vat',
        key: 'vat',
        align: 'right',
        width: 120,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.Discount'),
        dataIndex: 'discount',
        key: 'discount',
        align: 'right',
        width: 120,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.IsrWithholding'),
        dataIndex: 'mx_isr',
        key: 'mx_isr',
        align: 'right',
        width: 160,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.VatWithholding'),
        dataIndex: 'mx_iva',
        key: 'mx_iva',
        align: 'right',
        width: 160,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.totalTaxAmount'),
        dataIndex: 'total_tax',
        key: 'total_tax',
        align: 'right',
        width: 150,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.totalAmount'),
        dataIndex: 'total_fee',
        key: 'total_fee',
        align: 'right',
        width: 150,
        ellipsis: true,
    },
]
const exportReport = async () => {
    try {
        tableLoading.value = true
        await Promise.all([
            store.dispatch('ReportStore/exportSP', {
                company_code: userCompany[0].code,
                company_name: userCompany[0].name,
                report_type: 'AR',
                start: searchForm.start_date,
                end: searchForm.end_date,
            }),
            store.dispatch('ReportStore/exportSPPdf', {
                company_code: userCompany[0].code,
                company_name: userCompany[0].name,
                report_type: 'AR',
                start: searchForm.start_date,
                end: searchForm.end_date,
            }),
        ])
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}

const selectChange = async () => {
    await updateList()
}

const updateList = async () => {
    try {
        tableLoading.value = true

        await fetchReportsList(searchForm)
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

onBeforeMount(async () => {
    updateSPReportsList([])
    updateSPReportsSummary([])
    searchForm.company_name = userCompany[0].name
    searchForm.company_code = userCompany[0].code

    await updateList()
})
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="header-title-wrap">
                <div class="header-title">{{ i18n.t('reports.salesReport') }}</div>
                <div class="header-subtitle">{{ i18n.t('reports.updatedOn') }}: {{ today }}</div>
            </div>
            <a-divider type="vertical" style="margin-left: 60px" />
            <div class="selects-wrap">
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.start_date"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :placeholder="i18n.t('reports.startDate')"
                    :disabled="tableLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.end_date"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :placeholder="i18n.t('reports.experationDate')"
                    :disabled="tableLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
            </div>

            <a-button type="primary" shape="round" :disabled="tableLoading" @click="exportReport()">
                <export-outlined />
                {{ i18n.t('workTimeManager.export') }}
            </a-button>
        </div>
        <div class="page-content" ref="tableElWrapRef">
            <a-table
                :dataSource="dataSource"
                :columns="columns"
                :pagination="false"
                :loading="tableLoading"
                :scroll="{y: tableScrollY}"
            >
                <template #summary>
                    <a-table-summary fixed>
                        <a-table-summary-row class="table-summary-wrap" v-for="(item, index) in total" :key="index">
                            <a-table-summary-cell class="table-summary-total">
                                <a-typography-text v-if="index === 0">{{
                                    i18n.t('reports.subTotal')
                                }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text center">
                                {{ item.currency }}
                            </a-table-summary-cell>

                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.net_amount }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.vat }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.discount }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.mx_isr }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.mx_iva }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.total_tax }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.total_fee }}</a-typography-text>
                            </a-table-summary-cell>
                        </a-table-summary-row>
                    </a-table-summary>
                </template>
            </a-table>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.page-wrap {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: #fff;

    .page-header {
        min-height: 84px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        margin-bottom: 14px;

        .header-title-wrap {
            min-width: 148px;
            max-width: 148px;

            .header-title {
                white-space: nowrap;
                line-height: 26px;
                height: 26px;
                color: #004fc1;
                font-size: 20px;
                font-weight: 700;
                letter-spacing: 0;
            }

            .header-subtitle {
                white-space: nowrap;
                font-size: 12px;
                color: #262626;
                letter-spacing: 0;
                line-height: 18px;
                font-weight: 400;
            }
        }

        .ant-divider {
            border-left-color: #e2e2ea;
            height: 36px;
            top: 0;
            margin: 0 18px;
        }

        .selects-wrap {
            width: 100%;
            display: flex;
            align-items: center;

            .target-currency-select,
            .company-select,
            .coa-account-select {
                min-width: 175px;
                width: 175px;
                max-width: 500px;
                margin-right: 8px;
                flex: 1;
            }

            .data-select {
                min-width: 145px;
                width: 145px;
                max-width: 145px;
                margin-right: 8px;
                flex: 1;
            }

            .transaction-currency-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.2;
            }

            .coa-account-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.5;
                margin-right: 0;

                .select-all-item {
                    padding: 5px 12px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                    .ant-checkbox-wrapper {
                        width: 100%;
                    }
                }

                :deep(.ant-select-selector) {
                    overflow: hidden;

                    .ant-select-selection-overflow {
                        flex-wrap: nowrap;
                    }
                }
            }
        }

        .ant-btn-round {
            padding: 6px 10px;
            margin-left: 16px;
        }
    }

    .page-content {
        padding: 0 20px;
        overflow: hidden;
        height: calc(100% - 105px);
        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
        }

        // :deep(.ant-table-wrapper .ant-table .ant-table-thead .ant-table-cell) {
        //     text-align: center !important;
        // }
    }

    .table-summary-wrap {
        .table-summary-total {
            text-align: left;
            font-weight: 600;
        }

        .table-summary-text {
            text-align: right;
            &.center {
                text-align: center;
            }
        }
    }

    .page-footer {
        padding: 5px 8px;
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;
            width: 100%;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                min-width: 45px;
            }

            .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
                min-width: 35px;
            }
        }
    }
}
</style>
