/** @format */

import type {ActionContext} from 'vuex'
import service from '@/api/request'
import servicev1 from '@/api/requestNew'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
const http = service
const httpv1 = servicev1
const i18n: Composer = i18nInstance.global

const ArApBrStore = {
    namespaced: true,
    state: {
        pdfInfo: {id: '', file_url: '', file_name: '', file_type: '', xml_url: ''},
    },
    mutations: {
        updatePdfInfo(state: {pdfInfo: any}, pdf: any) {
            state.pdfInfo = {...pdf}
        },
    },
    getters: {
        brTypeList: () => {
            const list = [
                {id: 0, key: '0', value: 'RS', label: 'RS', full_name: 'Sales'}, //regular sales
                {id: 1, key: '1', value: 'RP', label: 'RP', full_name: 'Purchase'}, //regular purchase
                {id: 2, key: '2', value: 'PR', label: 'PR', full_name: ''}, //purchase refund
                {id: 3, key: '3', value: 'SR', label: 'SR', full_name: ''}, //sales refund
                {id: 4, key: '4', value: 'FT', label: 'FT', full_name: ''}, //funding trans
                {id: 5, key: '5', value: 'PY', label: 'PY', full_name: ''}, //payroll
                {id: 6, key: '6', value: 'FX', label: 'FX', full_name: ''}, //Es of different currency
                {id: 7, key: '7', value: 'ES', label: 'ES', full_name: ''}, //regular E-statement
                {id: 8, key: '8', value: 'YR', label: 'YR', full_name: 'Year End'},
                {id: 10, key: '10', value: 'EE', label: 'EE', full_name: ''},
                {id: 11, key: '11', value: 'SP', label: 'SP', full_name: ''},
                {id: 12, key: '12', value: 'PP', label: 'PP', full_name: ''},
                {id: 13, key: '13', value: 'DR', label: 'DR', full_name: ''},
            ]
            return list
        },
        fileTypeList: () => {
            const list = [
                {
                    id: 0,
                    key: '0',
                    value: '0',
                    label: i18n.t('fileTypeList.salesNotPaid'),
                    full_name: i18n.t('fileTypeList.salesNotPaid'),
                },
                {
                    id: 1,
                    key: '1',
                    value: '1',
                    label: i18n.t('fileTypeList.notPaid'),
                    full_name: i18n.t('fileTypeList.notPaid'),
                },
                {
                    id: 2,
                    key: '2',
                    value: '2',
                    label: i18n.t('fileTypeList.salesCash'),
                    full_name: i18n.t('fileTypeList.salesCash'),
                },
                {
                    id: 3,
                    key: '3',
                    value: '3',
                    label: i18n.t('fileTypeList.cashPaid'),
                    full_name: i18n.t('fileTypeList.cashPaid'),
                },
                {id: 4, key: '7', value: '7', label: i18n.t('fileTypeList.ES'), full_name: i18n.t('fileTypeList.ES')},
                {
                    id: 5,
                    key: '8',
                    value: '8',
                    label: i18n.t('fileTypeList.yearEnd'),
                    full_name: i18n.t('fileTypeList.yearEnd'),
                },
            ]
            return list
        },
        debitTypeList: () => {
            const list = [
                {id: 0, key: '0', value: 'RS', label: 'RS', full_name: 'Sales'}, //regular sales
                {id: 2, key: '2', value: 'PR', label: 'PR', full_name: ''}, //purchase refund
                {id: 4, key: '4', value: 'FT', label: 'FT', full_name: ''}, //funding trans
                {id: 6, key: '6', value: 'FX', label: 'FX', full_name: ''}, //Es of different currency
                {id: 10, key: '10', value: 'EE', label: 'EE', full_name: ''}, //Express Entry
                {id: 11, key: '11', value: 'SP', label: 'SP', full_name: ''}, //Stripe
                {id: 13, key: '13', value: 'DR', label: 'DR', full_name: ''}, //deposit and return
            ]
            return list
        },
        creditTypeList: () => {
            const list = [
                {id: 1, key: '1', value: 'RP', label: 'RP', full_name: 'Purchase'}, //regular purchase
                {id: 3, key: '3', value: 'SR', label: 'SR', full_name: ''}, //sales refund
                {id: 5, key: '5', value: 'PY', label: 'PY', full_name: ''}, //payroll
                {id: 4, key: '4', value: 'FT', label: 'FT', full_name: ''}, //funding trans
                {id: 6, key: '6', value: 'FX', label: 'FX', full_name: ''}, //Es of different currency
                {id: 10, key: '10', value: 'EE', label: 'EE', full_name: ''}, //Express Entry
                {id: 11, key: '11', value: 'SP', label: 'SP', full_name: ''}, //Stripe
                {id: 12, key: '12', value: 'PP', label: 'PP', full_name: ''}, //
                {id: 13, key: '13', value: 'DR', label: 'DR', full_name: ''}, //deposit and return
            ]
            return list
        },
    },
    actions: {
        async getFileBlobById(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: {id: any}) {
            const response = await http.get(`/bk/file/download/${payload.id}`, {
                responseType: 'blob',
            })
            return response
        },
        async getFileBlobByIdV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const subPath = '/web/static1'
            const url = `${window.location.protocol}//${window.location.host}${subPath}${payload}`
            const response = await httpv1.get(url, {
                responseType: 'blob',
            })
            return response
        },
        async updateInvoiceCreationStatus(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {invoiceCreatedStatus: any; id: any},
        ) {
            const query = {
                invoiceCreatedStatus: payload.invoiceCreatedStatus,
            }
            return http.put(`/bk/file/${payload.id}/status`, query)
        },
        async updateInvoiceCreationStatusV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {invoiceCreatedStatus: any; id: any},
        ) {
            const query = {
                invoice_created_status: payload.invoiceCreatedStatus,
            }
            return httpv1.patch(`/invoice-statement/api/v1/file/${payload.id}`, query)
        },
        async fetchRemoteMailBox(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpv1.post(`/mail-attachment/api/v2/mail-job`, payload)
        },
        async getFileObjById(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: {id: any}) {
            return await httpv1.get(`invoice-statement/api/v1/file/${payload.id}`)
        },
        async ocrUploadedFileById(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {file_id: any; company_code: any},
        ) {
            // const response = await http.post(`/bk/ocr/invoice/pdf/${payload.fileId}`)
            // temporary
            const {file_id, company_code} = payload
            const response = await httpv1.post(`invoice-statement/api/v1/file/${file_id}/ocr`, {
                companyCode: company_code,
            })
            return response
        },
        async esOcrUploadedFileById(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {
                fileId: any
                ocrType: any
                statementPeriod: any
                statementType: any
                bankAccount: any
            },
        ) {
            const response = await http.post(`/bk/ocr/estatement/pdf/${payload.fileId}`, {
                ocrType: payload.ocrType,
                statementPeriod: payload.statementPeriod,
                statementType: payload.statementType,
                bankAccount: payload.bankAccount,
            })
            return response
        },
    },
}

export default ArApBrStore
