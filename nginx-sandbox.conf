##
# You should look at the following URL's in order to grasp a solid understanding
# of Nginx configuration files in order to fully unleash the power of Nginx.
# https://www.nginx.com/resources/wiki/start/
# https://www.nginx.com/resources/wiki/start/topics/tutorials/config_pitfalls/
# https://wiki.debian.org/Nginx/DirectoryStructure
#
# In most cases, administrators will remove this file from sites-enabled/ and
# leave it as reference inside of sites-available where it will continue to be
# updated by the nginx packaging team.
#
# This file will automatically load configuration files provided by other
# applications, such as Drupal or Wordpress. These applications will be made
# available underneath a path with that package name, such as /drupal8.
#
# Please see /usr/share/doc/nginx-doc/examples/ for more detailed examples.
##

# Default server configuration
#
server {
  listen 443 ssl default_server;
  listen [::]:443 ssl default_server;

  #listen 8443;
  server_name _;
  #ssl on;
  ssl_certificate /home/<USER>/cert/inossemtimes.crt;
  ssl_certificate_key /home/<USER>/cert/inossemtimes.rsa;
  ssl_session_timeout 5m;
  ssl_protocols  TLSv1 TLSV1.1 TLSV1.2;
  #ssl_ciphers ALL: TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256;
  ssl_prefer_server_ciphers on;
  #location ~ /api/(.*) {
  #        proxy_redirect off;
  #        proxy_set_header Host $host;
  #        proxy_set_header X-Ssl on;
  #        proxy_set_header X-Real-IP $remote_addr;
  #        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  #        proxy_pass http://serverAPI;
  #    }

 #root /var/www/html;
  root /home/<USER>/www;

  # Add index.php to the list if you are using PHP
  #index index.html index.htm index.nginx-debian.html;

  server_name _;

  proxy_http_version 1.1;

  #   指定允许跨域的方法，*代表所有
  add_header Access-Control-Allow-Methods *;

  #   预检命令的缓存，如果不缓存每次会发送两次请求
  add_header Access-Control-Max-Age 3600;
  #   不带cookie请求，并设置为false
  add_header 'Access-Control-Allow-Credentials' 'true';

  #   表示允许这个域跨域调用（客户端发送请求的域名和端口）
  #   $http_origin动态获取请求客户端请求的域   不用*的原因是带cookie的请求不支持*号
  #   add_header Access-Control-Allow-Origin $http_origin;
  add_header 'Access-Control-Allow-Origin' '*';

  #   表示请求头的字段 动态获取
  add_header Access-Control-Allow-Headers 'Content-Type';
  #$http_access_control_request_headers;

  #   OPTIONS预检命令，预检命令通过时才发送请求
  #   检查请求的类型是不是预检命令
  if ($request_method = OPTIONS){
      return 200;
  }

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ /index.html;
  }
  location /newapi/ {
    proxy_pass https://eo-gateway-sandbox.inossemtimes.com/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }
  location /newgl/bk/ {
    proxy_pass https://eo-gateway-sandbox.inossemtimes.com/bkp-engine/bk/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }
  # location /web/static1/vm/ {
  #   proxy_pass http://20.151.129.218/web/static1/;
  #   add_header 'Access-Control-Allow-Origin' '*';
  #     add_header 'Access-Control-Allow-Credentials' 'true';
  # }
  location /web/static1/ {
    alias /home/<USER>/work/upload/;
    autoindex on;
  } 

  location /kyc/ {
    proxy_pass http://kyc.inossemtimes.com/api-test/;
    # proxy_pass https://eo-gateway-sandbox.inossemtimes.com/kyc/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }
  location /oms/ {
    proxy_pass https://eo-gateway-sandbox.inossemtimes.com/oms/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }

}

server {
  listen 80 default_server;
  listen [::]:80 default_server;

  # server_name _;
  # return 301 https://$host$request_uri;

  #root /var/www/html;
  root /home/<USER>/www;

  # Add index.php to the list if you are using PHP
  #index index.html index.htm index.nginx-debian.html;

  server_name _;

  proxy_http_version 1.1;

  #   指定允许跨域的方法，*代表所有
  add_header Access-Control-Allow-Methods *;

  #   预检命令的缓存，如果不缓存每次会发送两次请求
  add_header Access-Control-Max-Age 3600;
  #   不带cookie请求，并设置为false
  add_header 'Access-Control-Allow-Credentials' 'true';

  #   表示允许这个域跨域调用（客户端发送请求的域名和端口）
  #   $http_origin动态获取请求客户端请求的域   不用*的原因是带cookie的请求不支持*号
  #   add_header Access-Control-Allow-Origin $http_origin;
  add_header 'Access-Control-Allow-Origin' '*';

  #   表示请求头的字段 动态获取
  add_header Access-Control-Allow-Headers 'Content-Type';
  #$http_access_control_request_headers;

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ /index.html;
  }

  location /newapi/ {
    proxy_pass https://eo-gateway-sandbox.inossemtimes.com/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }
  location /newgl/bk/ {
    proxy_pass https://eo-gateway-sandbox.inossemtimes.com/bkp-engine/bk/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }

  location /web/static1/ {
    alias /home/<USER>/work/upload/;
    autoindex on;
  }
  location /kyc/ {
    proxy_pass http://kyc.inossemtimes.com/api-test/;
    # proxy_pass https://eo-gateway-sandbox.inossemtimes.com/kyc/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }
  location /oms/ {
    proxy_pass https://eo-gateway-sandbox.inossemtimes.com/oms/;
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
  }

}



# Virtual Host configuration for example.com
#
# You can move that to a different file under sites-available/ and symlink that
# to sites-enabled/ to enable it.
#
#server {
#	listen 80;
#	listen [::]:80;
#
#	server_name example.com;
#
#	root /var/www/example.com;
#	index index.html;
#
#	location / {
#		try_files $uri $uri/ =404;
#	}
#}
