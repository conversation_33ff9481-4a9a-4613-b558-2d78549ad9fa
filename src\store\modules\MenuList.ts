/** @format */
import type {ActionContext} from 'vuex'

const state = () => ({
    menuList: [],
})
const mutations = {
    updateMenuList(state: {menuList: any[]}, list: any) {
        state.menuList = [...list]
    },
}
const actions = {
    getFirstPage(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, menus: {[s: string]: any}) {
        if (menus.includes('/Dashboard')) {
            return {path: '/dashboard', id: '112', parentId: '91'}
            //return {path: '/bookkeeping/ap/uploadInvoice', id: '116', parentId: '115'}
        } else if (menus.includes('/Task')) {
            return {path: '/Task', id: '113', parentId: '91'}
            //return {path: '/bookkeeping/ap/uploadInvoice', id: '116', parentId: '115'}
        } else if (menus.includes('/Purchase')) {
            return {path: '/bookkeeping/ap/uploadInvoice', id: '116', parentId: '115'}
        } else if (menus.includes('/Account')) {
            return {path: '/bookkeeping/account/user', id: '116', parentId: '115'}
        } else if (menus.includes('/Sales')) {
            return {path: '/bookkeeping/ar/invoiceHistory', id: '100', parentId: '96'}
        } else if (menus.includes('/Subscribe')) {
            return {path: '/bookkeeping/subscribe/nosubscribe', id: '118', parentId: '117'}
        } else if (menus.includes('/Payment')) {
            return {path: '/bookkeeping/pmt/invoiceHistory', id: '124', parentId: '122'}
        }
        return {path: '/bookkeeping/help/faq', id: '106', parentId: '101'}
    },
}

const getters = {
    rolePermissionMapping: () => {
        return [
            // {
            //     role_id: '1',
            //     admit: ['69', '112', '80', '91', '96', '121', '81', '108', '111', '82', '101', '115'],
            //     inherit_roles: ['1', '3', '9995', '9996', '9997'],
            //     first_page: {path: '/dashboard', id: '112', parentId: '91'},
            // }, //所有菜单
            // {
            //     role_id: '3',
            //     admit: ['69', '112', '80', '91', '96', '121', '81', '108', '111', '82', '101'],
            //     inherit_roles: [],
            //     first_page: {path: '/dashboard', id: '112', parentId: '91'},
            // }, //所有菜单
            {
                role_id: '9995',
                admit: ['69', '80', '115'],
                inherit_roles: ['3', '9996', '9997'],
                first_page: {path: '/bookkeeping/account/user', id: '116', parentId: '115'},
            }, //所有菜单
            {
                role_id: '9996',
                admit: ['69', '80', '91'],
                inherit_roles: [],
                first_page: {path: '/bookkeeping/ap/uploadInvoice', id: '92', parentId: '91'},
            }, //所有菜单
            {
                role_id: '9997',
                admit: ['69', '80', '96', '111'],
                inherit_roles: [],
                first_page: {path: '/bookkeeping/ar/invoiceHistory', id: '100', parentId: '96'},
            }, //所有菜单
        ]
    },
}

export default {
    state,
    mutations,
    getters,
    actions,
}

//
// [
// '100',
//   '101',
//   '102',
//   '103',
//   '106',
//   '108',
//   '109',
//   '110',
//   '111',
//   '112',
//   '113',
//   '115',
//   '116',
//   '121',
//   '123',
//   '69',
//   '80',
//   '81',
//   '82',
//   '84',
//   '85',
//   '86',
//   '87',
//   '88',
//   '89',
//   '90',
//   '91',
//   '92',
//   '95',
//   '96',
//   '999',
// ],
