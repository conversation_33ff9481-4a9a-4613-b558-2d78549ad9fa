/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'
import serviceV1 from '../../../../api/requestNew'
import serviceV2 from '@/api/requestNewGL'

const http = service
const httpV1 = serviceV1
const httpV2 = serviceV2
const AccountDescriptionStore = {
    namespaced: true,
    state: {
        accountDescList: [],
        extendedAccountDescList: [],
        fullAccountDescList: [],
        coaGroupList: [],
        coaGroupList2: [],
        accountAddedTotalNumber: 0,
        fullAccountTotalNumber: 0,
        filteredAccountDescList: [],
        filteredAccountTotalNumber: 0,
    },
    mutations: {
        updateAccountDescription(state: {accountDescList: any[]}, list: any) {
            state.accountDescList = [...list]
        },
        updateExtendedAccountDescription(state: {extendedAccountDescList: any[]}, list: any) {
            state.extendedAccountDescList = [...list]
        },
        updateAccountAddedTotalFoundNumber(state: {accountAddedTotalNumber: any}, num: any) {
            state.accountAddedTotalNumber = num
        },
        updateAvailableAccountDescription(state: {fullAccountDescList: any[]}, list: any) {
            state.fullAccountDescList = [...list]
        },
        updateFullAccountTotalFoundNumber(state: {fullAccountTotalNumber: any}, num: any) {
            state.fullAccountTotalNumber = num
        },
        updateCoaGroupList(state: {coaGroupList: any[]}, list: void[]) {
            // list.shift()
            state.coaGroupList = [...list]
        },
        updateCoaGroupList2(state: {coaGroupList2: any[]}, list: void[]) {
            // list.shift()
            state.coaGroupList2 = [...list]
        },
        updateFilteredAccountDescList(state: {filteredAccountDescList: any[]}, list: any) {
            state.filteredAccountDescList = [...list]
        },
        updateFilteredAccountTotalNumber(state: {filteredAccountTotalNumber: any}, num: any) {
            state.filteredAccountTotalNumber = num
        },
    },
    actions: {
        async fetchFilteredAccountDesc(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await http.post('/bk/account/full/list', payload)
            store.commit('updateFilteredAccountDescList', response.data.data.list)
            store.commit('updateFilteredAccountTotalNumber', response.data.data.totalCount)
            return response
        },
        async fetchFilteredAccountDescV2(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpV2.get('/bk/coa-template', {params: payload})
            store.commit('updateFilteredAccountDescList', response.data.data.template_list)
            store.commit('updateFilteredAccountTotalNumber', response.data.data.template_list.length)
            return response
        },
        // async fetchAccountDesc(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        //     const response = await http.post('/bk/account/common/list', payload)
        //     store.commit('updateAccountDescription', response.data.data.list)
        //     store.commit('updateAccountAddedTotalFoundNumber', response.data.data.totalCount)
        //     return response
        // },
        // async fetchAccountDescV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        //     const response = await httpV1.get('/system-preferences/api/v1/coa', {params: payload})
        //     store.commit('updateAccountDescription', response.data.data)
        //     store.commit('updateAccountAddedTotalFoundNumber', response.data.total)
        //     return response
        // },

        async fetchAccountDescV2(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const queryObj: any = {}
            queryObj['company'] = payload?.company_code
            // if (payload?.bk_type === 1) {
            //     queryObj.category = 'Account Receivable'
            // }
            // if (payload?.bk_type === 2) {
            //     queryObj.category = 'Account Payable'
            // }
            // if (payload?.bk_type === 3) {
            //     queryObj.category = 'Bank'
            // }
            const response = await httpV2.get('/bk/coa', {params: payload})
            store.commit('updateAccountDescription', response.data.data.rows)
            store.commit('updateAccountAddedTotalFoundNumber', response.data.data.total)
        },
        async fetchExtendedAccountDesc(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await http.post('/bk/account/common/list', payload)
            store.commit('updateExtendedAccountDescription', response.data.data.list)
            store.commit('updateAccountAddedTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchExtendedAccountDescV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            queryObj: any,
        ) {
            queryObj.page_index = 1
            queryObj.page_size = 1000
            const response = await httpV2.get('/bk/coa', {params: queryObj})
            store.commit('updateExtendedAccountDescription', response.data.data.rows)
            // store.commit('updateAccountAddedTotalFoundNumber', response.data.total)
            return response
        },
        async createChartOfAccount(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.post('/bk/account/common', payload)
        },
        async createChartOfAccountV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            return httpV2.post('/bk/coa', payload)
        },
        async updateChartOfAccount(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any; alias: any},
        ) {
            const query = {
                id: payload.id,
                alias: payload.alias,
            }
            return http.put('/bk/account/common', query)
        },
        async updateChartOfAccountV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            return httpV2.put(`/bk/coa`, payload)
        },
        async deleteChartOfAccount(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/account/common/${payload}`)
        },
        async deleteChartOfAccountV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            return httpV2.delete(`/bk/coa`, {params: payload})
        },
        async setChartOfAccountState(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            return http.put('/bk/account/common/', payload)
        },
        async setChartOfAccountStatev1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpV2.put(`/bk/coa`, payload)
            return response
        },
        async fetchFullAccountDesc(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/account/full/list', payload)
            store.commit('updateAvailableAccountDescription', response.data.data.list)
            store.commit('updateFullAccountTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchFullAccountDescV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpV1.get(`/system-preferences/api/v1/coa-dict`, {
                params: payload,
            })
            store.commit('updateAvailableAccountDescription', response.data.data)

            if (!(payload['$skip'] && payload['$limit'])) {
                store.commit('updateFullAccountTotalFoundNumber', response.data.total)
            }
            return response
        },
        async fetchFullAccountDescV2(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpV2.get(`/bk/coa-general-table`, {
                params: payload,
            })
            store.commit('updateAvailableAccountDescription', response.data.data.rows)
            store.commit('updateFullAccountTotalFoundNumber', response.data.data.total)
            return response
        },
        async fetchFullAccountDescTotalNumberV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpV1.get(`/system-preferences/api/v1/coa-dict`)
            // store.commit('updateAvailableAccountDescription', response.data)
            store.commit('updateFullAccountTotalFoundNumber', response.data.total)
            return response
        },

        async fetchCoaGroup(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await http.get('/bk/account/coa/group')
            store.commit('updateCoaGroupList', response.data.data)
            return response
        },

        async fetchCoaGroupV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, country: string) {
            const response = await httpV2.get(`/bk/coa-template?template_name_list=1&country=${country}`)
            const res = response.data.data
            const group_list = []
            for (const props in res) {
                if (props !== 'success')
                    group_list.push({group_id: props.toString(), group_name: res[props].template_name})
            }
            store.commit('updateCoaGroupList', group_list)
            return response
        },
        async fetchCoaGroup2V1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, country: string) {
            const response = await httpV2.get(`/bk/coa-template?template_name_list=2&country=${country}`)
            const res = response.data.data
            const group_list = []
            for (const props in res) {
                if (props !== 'success')
                    group_list.push({group_id: props.toString(), group_name: res[props].template_name})
            }
            store.commit('updateCoaGroupList2', group_list)
            return response
        },
    },
}

export default AccountDescriptionStore
