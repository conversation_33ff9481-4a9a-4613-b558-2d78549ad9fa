<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount, watch, createApp} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {numberToCurrency} from '@/lib/utils'
import {SyncOutlined, ExportOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'

import * as _ from 'lodash'
import moment from 'moment'
import {v4 as uuidv4} from 'uuid'
import type {Dayjs} from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'

const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global
const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

const today = ref(moment().format('YYYY-MM-DD'))
const tableLoading = ref(false)
const cashFlowStatementList = computed(() => store.state.ReportStore.cashFlowStatementList)
const COAAccountOptions = computed(() => {
    const list: {[key: string]: string}[] = cashFlowStatementList.value.map((i: any) => {
        return {id: i.gl_account, value: i.gl_account}
    })
    return _.uniqBy(list, 'id') as {[key: string]: string}[]
})
const total = reactive({
    month: '0.00',
    year: '0.00',
})
const companies: any = ref([])
const VNodes = (_: any, {attrs}: any) => {
    return attrs.vnodes
}
const searchForm = reactive<{[key: string]: string | undefined}>({
    // end_date: today.value,
    // start: undefined,
    end_date: today.value,
    company_code: '2605',
    // transactionCurrency: undefined,
})
const reportsForm = reactive<{[key: string]: string | undefined}>({
    type: 'BS',
    end_date: '',
    start_date: '',
})
const COAAccount = ref<string[]>([])
const selectAll = ref<boolean>(false)
const indeterminate = ref<boolean>(true)

const fetchTBList = (payload: any) => store.dispatch('ReportStore/getCashFlowStatementList', payload)
const updateDisplayCashFlowStatementList = (list: any) =>
    store.commit('ReportStore/updateDisplayCashFlowStatementList', list)

const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const filterCompanyOption = (input: string, option: any) => {
    return (option.company_name || '').toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const disabledDate = (current: Dayjs) => {
    return current && current > moment().endOf('day')
}

const tableScrollY = computed(() => {
    return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight * 2
})

const columns: ColumnsType = [
    {
        title: i18n.t('reports.cashFlowName'),
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        width: '300px',
    },
    {
        title: i18n.t('reports.cashFlowMonth'),
        dataIndex: 'month',
        key: 'month',
        align: 'right',
        width: '200px',
    },
    {
        title: i18n.t('reports.cashFlowYear'),
        dataIndex: 'year',
        key: 'year',
        align: 'right',
        width: '200px',
    },
    // {
    //     title: i18n.t('reports.endCredit'),
    //     dataIndex: 'credit',
    //     key: 'credit',
    //     align: 'right',
    //     width: '150px',
    // },
]
const dataSource = computed(() =>
    store.state.ReportStore.displayCashFlowStatementList.map((i: any) => {
        // return {
        //     ...i,
        //     debit: i.endBalance > 0 ? numberToCurrency(i.endBalance) : ' - - ',
        //     debitNum: i.endBalance > 0 ? i.endBalance : 0,
        //     credit: i.endBalance < 0 ? numberToCurrency(Math.abs(i.endBalance)) : ' - - ',
        //     creditNum: i.endBalance < 0 ? Math.abs(i.endBalance) : 0,
        // }
        // return {
        //    ...i,
        //    month: i.month > 0 ? numberToCurrency(i.month) : ' - - ',
        //    monthNum: i.month > 0 ? i.month : 0,
        //    year: i.year > 0 ? numberToCurrency(i.year) : ' - - ',
        //    yearNum: i.year > 0 ? i.year : 0,
        //}
        return {
            ...i,
            month: i.month != 0 ? numberToCurrency(i.month) : ' - - ',
            monthNum: i.month != 0 ? i.month : 0,
            year: i.year != 0 ? numberToCurrency(i.year) : ' - - ',
            yearNum: i.year != 0 ? i.year : 0,
        }
    }),
)

const exportReport = async () => {
    try {
        tableLoading.value = true
        await Promise.all([
            store.dispatch('ReportStore/exportCashFlow', {
                company_code: userCompany[0].code,
                end_date: searchForm.end_date,
            }),
            store.dispatch('ReportStore/exportCashFlowPdf', {
                company_code: userCompany[0].code,
                company_name: userCompany[0].name,
                end_date: searchForm.end_date,
            }),
        ])
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}

const selectChange = async () => {
    try {
        tableLoading.value = true

        await fetchTBList(searchForm)
        COAAccount.value = COAAccountOptions.value.map((i: any) => i.value)
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

const accountChange = async () => {
    try {
        tableLoading.value = true

        filterAccount()
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

const filterAccount = () => {
    if (COAAccount.value.length) {
        updateDisplayCashFlowStatementList(
            cashFlowStatementList.value.filter((j: any) => COAAccount.value.includes(j.gl_account)),
        )
    } else {
        updateDisplayCashFlowStatementList([])
    }
}

const refresh = async () => {
    try {
        tableLoading.value = true

        await fetchTBList(searchForm)
        filterAccount()
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)

onBeforeMount(async () => {
    console.log(userCompany[0])

    try {
        tableLoading.value = true
        searchForm.company_code = userCompany[0].code
        // searchForm.currency = 'CAD'
        companies.value = _.cloneDeep(_.sortBy(userCompany, 'id'))

        await fetchTBList(searchForm)
        COAAccount.value = COAAccountOptions.value.map((i: any) => i.value)
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
})

const onCheckAllChange = (e: any) => {
    COAAccount.value = e.target.checked ? COAAccountOptions.value.map((i: any) => i.value) : []
    accountChange()
}
watch(
    () => COAAccount.value,
    val => {
        indeterminate.value = !!val.length && val.length < COAAccountOptions.value.length
        selectAll.value = val.length === COAAccountOptions.value.length
    },
)
watch(
    () => dataSource.value,
    val => {
        total.month = numberToCurrency(
            val.reduce((prev: any, curr: any) => {
                return prev + curr.monthNum
            }, 0) || 0,
        )
        total.year = numberToCurrency(
            val.reduce((prev: any, curr: any) => {
                return prev + curr.yearNum
            }, 0) || 0,
        )
    },
)
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="header-title-wrap">
                <div class="header-title">{{ i18n.t('reports.cashFlowStatement') }}</div>
                <div class="header-subtitle">{{ i18n.t('reports.endingAt') }}: {{ today }}</div>
            </div>
            <a-divider type="vertical" style="margin-left: 60px" />
            <div class="selects-wrap">
                <!-- <a-date-picker class="data-select" v-model:value="searchForm.start" :allowClear="false"
                    @change="selectChange" :inputReadOnly="true" :disabled-date="disabledDate" format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD" :placeholder="i18n.t('reports.startDate')" :disabled="tableLoading">
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker> -->
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.end_date"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    :disabled-date="disabledDate"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :placeholder="i18n.t('reports.experationDate')"
                    :disabled="tableLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
                <!-- <a-select
                    class="company-select"
                    v-model:value="searchForm.company"
                    show-search
                    @change="selectChange"
                    :filter-option="filterCompanyOption"
                    :placeholder="i18n.t('reports.company')"
                    :disabled="tableLoading"
                >
                    <a-select-option
                        v-for="item in companies"
                        :key="item.name + uuidv4()"
                        :company_name="item.name"
                        :value="item.code"
                    >
                        {{ item.name }}
                    </a-select-option>
                </a-select> -->
                <!-- <a-select
                    class="coa-account-select"
                    v-model:value="COAAccount"
                    mode="multiple"
                    show-search
                    :filter-option="filterOption"
                    @change="accountChange"
                    :placeholder="i18n.t('reports.COAAccount')"
                    :disabled="tableLoading"
                    :max-tag-count="3"
                >
                    <a-select-option v-for="item in COAAccountOptions" :key="item.id" :value="item.id">{{
                        item.value
                    }}</a-select-option>

                    <template #dropdownRender="{menuNode: menu}">
                        <div class="select-all-item" @mousedown="e => e.preventDefault()">
                            <a-checkbox
                                v-model:checked="selectAll"
                                :indeterminate="indeterminate"
                                @change="onCheckAllChange"
                            >
                                {{ i18n.t('reports.all') }}
                            </a-checkbox>
                        </div>
                        <v-nodes :vnodes="menu" />
                    </template>
                </a-select> -->
            </div>

            <!-- <a-popover class="popover-wrap" trigger="click" placement="bottom" @visibleChange="visibleChange">
                <template #content>
                    <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                        <div class="search-input-group">
                            <a-form-item>
                                <a-radio-group v-model:value="reportsForm.type" name="radioGroup">
                                    <a-radio value="BS">BS</a-radio>
                                    <a-radio value="PL">PL</a-radio>
                                    <a-radio value="AR">AR</a-radio>
                                    <a-radio value="AP">AP</a-radio>
                                    <a-radio value="Sales">Sales</a-radio>
                                    <a-radio value="Purchase">Purchase</a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </div>
                        <div
                            class="search-input-group"
                            v-show="
                                reportsForm.type === 'PL' ||
                                reportsForm.type === 'Sales' ||
                                reportsForm.type === 'Purchase'
                            "
                        >
                            <a-form-item :label="$t('bkApInvoice.date')">
                                <a-date-picker
                                    v-model:value="reportsForm.start_date"
                                    :disabled="tableLoading"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :placeholder="$t('gl.createStartDate')"
                                    style="width: 160px"
                                    clearable
                                >
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </a-form-item>
                            <a-form-item :label="$t('bkApInvoice.to')">
                                <a-date-picker
                                    v-model:value="reportsForm.end_date"
                                    :disabled="tableLoading"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :placeholder="$t('gl.createEndDate')"
                                    style="width: 160px"
                                    clearable
                                >
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </a-form-item>
                        </div>
                        <div
                            class="search-input-group"
                            v-show="
                                !(
                                    reportsForm.type === 'PL' ||
                                    reportsForm.type === 'Sales' ||
                                    reportsForm.type === 'Purchase'
                                )
                            "
                        >
                            <a-form-item :label="$t('connectivity.endDate')">
                                <a-date-picker
                                    v-model:value="reportsForm.end_date"
                                    :disabled="tableLoading"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :placeholder="$t('gl.createEndDate')"
                                    style="width: 160px"
                                    clearable
                                >
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </a-form-item>
                        </div>

                        <a-button type="primary" shape="round" :disabled="tableLoading" @click="exportReports">
                            <template #icon>
                                <export-outlined />
                            </template>
                            {{ i18n.t('workTimeManager.export') }}
                        </a-button>
                    </a-form>
                </template>
                <a-button type="primary" shape="round" class="search-button" :disabled="tableLoading">
                    {{ i18n.t('menu.reports') }}
                </a-button>
            </a-popover> -->

            <a-button type="primary" shape="round" :disabled="tableLoading" @click="exportReport()">
                <export-outlined />
                {{ i18n.t('workTimeManager.export') }}
            </a-button>

            <!-- <a-button type="primary" shape="round" :disabled="tableLoading" @click="refresh">
                {{ i18n.t('reports.refresh') }}
                <template #icon>
                    <sync-outlined />
                </template>
            </a-button> -->
        </div>
        <div class="page-content" ref="tableElWrapRef">
            <a-table
                :dataSource="dataSource"
                :columns="columns"
                :pagination="false"
                :loading="tableLoading"
                :scroll="{y: tableScrollY}"
            >
                <!-- <template #summary>
                    <a-table-summary fixed>
                        <a-table-summary-row class="table-summary-wrap">
                            <a-table-summary-cell class="table-summary-total">
                                <a-typography-text>{{ i18n.t('reports.subTotal') }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.month }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.year }}</a-typography-text>
                            </a-table-summary-cell>
                        </a-table-summary-row>
                    </a-table-summary>
                </template> -->
            </a-table>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.page-wrap {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: #fff;

    .page-header {
        min-height: 84px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        margin-bottom: 14px;

        .header-title-wrap {
            min-width: 200px;
            max-width: 200px;

            .header-title {
                line-height: 26px;
                height: 26px;
                color: #004fc1;
                font-size: 20px;
                font-weight: 700;
                letter-spacing: 0;
            }

            .header-subtitle {
                font-size: 12px;
                color: #262626;
                letter-spacing: 0;
                line-height: 18px;
                font-weight: 400;
            }
        }

        .ant-divider {
            border-left-color: #e2e2ea;
            height: 36px;
            top: 0;
            margin: 0 18px;
        }

        .selects-wrap {
            width: 100%;
            display: flex;
            align-items: center;

            .target-currency-select,
            .company-select,
            .coa-account-select {
                min-width: 175px;
                width: 175px;
                max-width: 500px;
                margin-right: 8px;
                flex: 1;
            }

            .data-select {
                min-width: 145px;
                width: 145px;
                max-width: 145px;
                margin-right: 8px;
                flex: 1;
            }

            .transaction-currency-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.2;
            }

            .coa-account-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.5;
                margin-right: 0;

                .select-all-item {
                    padding: 5px 12px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                    .ant-checkbox-wrapper {
                        width: 100%;
                    }
                }

                :deep(.ant-select-selector) {
                    overflow: hidden;

                    .ant-select-selection-overflow {
                        flex-wrap: nowrap;
                    }
                }
            }
        }

        .ant-btn-round {
            padding: 6px 10px;
            margin-left: 16px;
        }
    }

    .page-content {
        padding: 0 20px;
        overflow: hidden;
        height: calc(100% - 105px);
        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
        }

        // :deep(.ant-table-wrapper .ant-table .ant-table-thead .ant-table-cell) {
        //     text-align: center !important;
        // }
    }

    .table-summary-wrap {
        .table-summary-total {
            text-align: right;
            font-weight: 600;
        }

        .table-summary-text {
            text-align: right;
        }
    }

    .page-footer {
        padding: 5px 8px;
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;
            width: 100%;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                min-width: 45px;
            }

            .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
                min-width: 35px;
            }
        }
    }
}
</style>
