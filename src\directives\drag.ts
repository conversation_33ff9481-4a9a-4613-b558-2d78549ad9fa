/** @format */

const setDragDirective = (app: any) => {
    app.directive('drag', dragDirective)
}

const position = {
    x: 0,
    y: 0,
    right: 0,
    bottom: 0,
    width: 0,
    height: 0,
}
const animateOptions = {
    duration: 100,
    easing: 'linear',
}

// 避免拖动出界的限制
const restoration = (el: HTMLElement) => {
    const elPosition = el.getBoundingClientRect()

    if (elPosition.x <= 0) {
        el.style.right = `${position.right}px`
    }

    if (elPosition.y <= 0) {
        el.style.bottom = `${position.bottom}px`
    }

    el.style.display = 'flex'
}

const dragDirective = {
    mounted(el: HTMLElement) {
        el.ondragover = (event: DragEvent) => {
            event.preventDefault()
        }
        el.ondragstart = (event: DragEvent) => {
            const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
            const height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
            const p = el.getBoundingClientRect()
            position.x = event.clientX
            position.y = event.clientY
            position.right = width - p.right
            position.bottom = height - p.bottom
            position.width = p.width
            position.height = p.height
        }
        el.ondrag = (event: DragEvent) => {
            const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
            const height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
            const xPum = width - event.clientX + position.width / 2 - position.width
            const yPum = height - event.clientY + position.height / 2 - position.height
            el.style.right = `${xPum}px`
            el.style.bottom = `${yPum}px`

            if (event.clientX === 0 && event.clientY === 0) {
                el.style.display = 'none'
            } else {
                el.style.display = 'flex'
            }
        }

        el.ondragend = (event: DragEvent) => {
            const width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
            const height = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
            const xPum = width - event.clientX + position.width / 2 - position.width
            const yPum = height - event.clientY + position.height / 2 - position.height
            el.style.right = `${xPum}px`
            el.style.bottom = `${yPum}px`

            restoration(el)
        }
    },
}

export default setDragDirective
