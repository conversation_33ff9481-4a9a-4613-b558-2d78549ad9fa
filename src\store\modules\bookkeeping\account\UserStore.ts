/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'
import servicev1 from '@/api/requestNew'

const http = service
const httpNew = servicev1

const UserStore = {
    namespaced: true,
    getters: {
        web_roles: () => {
            return [
                {id: 1, key: '1', value: '1', name: 'Admin', label: 'Admin'},
                {id: 3, key: '3', value: '3', name: 'Bookkeeper', label: 'Bookkeeper'},
                {id: 9995, key: '9995', value: '9995', name: 'Operator', label: 'Operator'},
                {id: 9996, key: '9996', value: '9996', name: 'OCR Client', label: 'OCR Client'},
                {id: 9997, key: '9997', value: '9997', name: 'Bill&Report', label: 'Bill&Report'},
            ]
        },
        user_category: () => {
            return [
                {id: 0, key: '0', value: 'web', name: 'Web', label: 'Web'},
                {id: 1, key: '1', value: 'app', name: 'App', label: 'App'},
            ]
        },
    },
    state: {
        userList: [],
        totalNumber: 0,
        companyList: [],
        companyAllList: [],
        companyTotal: 0,
        companyAllTotal: 0,
    },
    mutations: {
        updateUserTotalNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateUserList(state: {userList: any[]}, list: any) {
            state.userList = list
        },
        updateCompanyList(state: {companyList: any[]}, list: any) {
            state.companyList = list
        },
        updateCompanyTotalNumber(state: {companyTotal: any}, num: any) {
            state.companyTotal = num
        },
        updateAllCompanyList(state: {companyAllList: any}, list: any) {
            state.companyAllList = list
        },
        updateAllCompanyTotalNumber(state: {companyAllTotal: any}, num: any) {
            state.companyAllTotal = num
        },
    },
    actions: {
        //获取users  Api
        //TODO: may change to lazy loading
        async getAllUsers(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, query: any) {
            //await httpNew.get(`/users/api/users?$limit=${payload.$limit}&$skip=${payload.$skip}`)
            const response = await httpNew.get(`/users/api/users`, {params: query})
            const list = response.data.data
            const num = response.data.total
            store.commit('updateUserList', list)
            store.commit('updateUserTotalNumber', num)
            return response
        },
        // async getUsers(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, query: any) {
        //     // let response: any = {}
        //     // if (payload.id) {
        //     //     response = await httpNew.get(
        //     //         `/users/api/users?$limit=${payload.$limit}&$skip=${payload.$skip}&id=${payload.id}`,
        //     //     )
        //     // } else if (payload.account) {
        //     //     response = await httpNew.get(
        //     //         `/users/api/users?$limit=${payload.$limit}&$skip=${payload.$skip}&account=${payload.account}`,
        //     //     )
        //     // } else {
        //     //     response = await httpNew.get(`/users/api/users?$limit=${payload.$limit}&$skip=${payload.$skip}`)
        //     // }
        //     const response = await httpNew.get('/users/api/users', {params: query})
        //     const list = response.data.data
        //     const num = response.data.total
        //     store.commit('updateUserList', list)
        //     store.commit('updateUserTotalNumber', num)
        //     return response
        // },
        //获取公司company data
        async getCompanyData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpNew.get(`/users/api/v1/company`, {params: payload})
            const list = response.data.data
            const num = response.data.total
            store.commit('updateCompanyList', list)
            store.commit('updateCompanyTotalNumber', num)
            return response
        },
        // 获取公司全部
        async getAllCompanyData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpNew.get(`/users/api/v1/company`, {params: payload})
            const list = response.data.data
            const num = response.data.total
            store.commit('updateAllCompanyList', list)
            store.commit('updateAllCompanyTotalNumber', num)

            return response
        },
        //更新user负责的公司
        async updateUsersCompany(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            // store.commit('updateCompanyList', list)
            // store.commit('updateCompanyTotalNumber', num)
            return await httpNew.patch(`/users/api/users/${payload.id}`, {
                company_code: payload.company_code,
            })
        },
        // 新增users
        async addUser(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpNew.post('/users/api/users', payload)
            return response
        },
    },
}

export default UserStore
