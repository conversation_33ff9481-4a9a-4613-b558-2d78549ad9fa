<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import SupplierForm from '../../../components/bookkeepingComponents/CommonComponents/SupplierForm.vue'
import {message, Modal, type FormInstance} from 'ant-design-vue'
import {computed, nextTick, onBeforeUnmount, onMounted, ref} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useRouter} from 'vue-router'
import {EditOutlined, SearchOutlined, PlusOutlined} from '@ant-design/icons-vue'
import * as _ from 'lodash'
import {UserCompany, Ap_Integration} from '@/lib/storage'
const apIntegration: any = Ap_Integration.get() ?? 0
const userCompany: any = UserCompany.get() || []
const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const itemList = ref([] as any[])
const formRef = ref<FormInstance>()
const searchForm = ref({
    contact_name: '',
    tel: '',
    email: '',
})

const isFirst = ref(false)
const current = ref({})
const editMode = ref(false)
const accountType = ref('')
const show = ref(false)
const tableLoading = ref(false)
const currentPageNumber = ref(1)
const pageSize = ref(10)
const timer = ref()
// mapActions
const fetchCustomers = (payload: any) => store.dispatch('ContactStore/fetchContacts', payload)
const deleteUser = (payload: any) => store.dispatch('ContactStore/deleteContact', payload)
const fetchSuppliers = (payload: any) => store.dispatch('SupplierStore/fetchSuppliers', payload)
const deleteSupplier = (payload: any) => store.dispatch('SupplierStore/deleteSupplier', payload)
const createDummy = (payload: any) => store.dispatch('ContactStore/createContact', payload)
const companyTaxInfo: any = computed(() => store.state.TaxInfoStore.companyTaxInfo)
const DUMMYCustomers = computed(() =>
    store.state.CommonDropDownStore.customerOptions.filter((item: any) => 'DUMMY' === item.contact_name),
)
// const fetchContactNumber = () => store.dispatch('ContactStore/fetchContactNumber', {})
const fetchCompanyTaxInfo = (query?: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
const queryCustomer = (query?: any) => store.dispatch('CommonDropDownStore/fetchContactDropDown', query)
const fetchAccountDescDropdown = (payload: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', payload)

const updateData = async () => {
    const {contact_name: name, tel, email} = searchForm.value
    const searchObj: any = {
        $limit: pageSize.value,
        $skip: (currentPageNumber.value - 1) * pageSize.value,
    }
    searchObj['$sort[create_time]'] = 'desc'
    if (name) {
        searchObj['contact_name[$like]'] = `%${name}%`
    }
    searchObj['company_code'] = userCompany[0].code
    // const searchFormForCustomer = {
    //     contact_name: name,
    //     ...searchObj,
    // }
    // const searchFormForSupplier = {
    //     supplierName: name,
    //     ...searchObj,
    // }
    try {
        tableLoading.value = true
        // await Promise.all([fetchCustomers(searchFormForCustomer), fetchSuppliers(searchFormForSupplier)])
        await fetchCustomers({...searchObj})
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const add = () => {
    show.value = true
    editMode.value = false
    accountType.value = 'Supplier'
    current.value = {}
}
const edit = (record: any) => {
    show.value = true
    editMode.value = true
    accountType.value = 'Supplier'
    current.value = {...record}
}

const currentRowThing = (record: any) => {
    return {
        onClick: () => {
            edit(record)
        },
    }
}

const showDialog = (bool: any) => {
    show.value = bool
}
const dismiss = () => {
    showDialog(false)
    editMode.value = false
}
const search = async () => {
    currentPageNumber.value = 1
    await updateData()
}
const remove = (record: any) => {
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: `${i18n.t('bkCustomer.receiver')} ` + record.receiver + i18n.t('bkCommonTag.msgDeleteConfirm'),
        async onOk() {
            try {
                const response = await deleteUser(record.id)
                if (response.data.code === 1000) {
                    message.success(i18n.t('ApComponents.success'))
                }
                // else {
                //     message.error({
                //         content: response.data.msg,
                //         duration: 3,
                //     })
                // }
                await updateData()
            } catch (error) {
                console.log(error)
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
        okType: 'danger',
        onCancel() {
            console.log('')
        },
    })
}
const changeCurrentPageNumber = (pageNumber: number) => {
    currentPageNumber.value = pageNumber
    updateData()
}
const changePageSize = (pageSize: any) => {
    pageSize.value = pageSize
    currentPageNumber.value = 1
    updateData()
}
const changePage = () => {
    isFirst.value = false
    updateData()
}
const getFullAddress = (record: any) => {
    if (record) {
        const {office_street, office_city, office_province, office_country, office_postal_code} = record
        const list = [office_street, office_city, office_province, office_country, office_postal_code].filter(
            item => item,
        )
        return list.join(', ')
    }
    return ''
}
// computed mapState
const customersList = computed(() => store.state.ContactStore.contactList)
const customerTotalNumber = computed(() => store.state.ContactStore.totalNumber)
const supplierList = computed(() => store.state.SupplierStore.supplierList)
const supplierTotalNumber = computed(() => store.state.SupplierStore.totalNumber)
// computed mapGetters
const showExpenseTextByCode = (param: any) =>
    computed(() => store.getters.CommonDropDownStore.showExpenseTextByCode(param))
const combineTableData = computed(() => {
    // const table = [...customersList.value, ...supplierList.value]
    // const reformedTable = table.map((item: any) => {
    //     if (Object.prototype.hasOwnProperty.call(item, 'customerName')) {
    //         return {
    //             customerOrSupplierName: item.customerName,
    //             ...item,
    //         }
    //     }
    //     return {
    //         customerOrSupplierName: item.supplierName,
    //         ...item,
    //     }
    // })
    const table = [...customersList.value]
    const sortedTableByUpdateTime = table.sort((a, b) => {
        return a.update_time < b.update_time ? 1 : -1
    })

    console.log('sortedTableByUpdateTime', sortedTableByUpdateTime)
    return sortedTableByUpdateTime
})
const totalNumber = computed(() => store.state.ContactStore.totalNumber)
const modalTitle = computed(() => {
    let title = ''
    title = i18n.t('bkCustomer.createAllReceiverTitle')
    if (editMode.value) {
        title = i18n.t('bkSupplier.editReceiverTitle')
    }
    return title
})
const addTutorial = () => {
    nextTick(() => {
        const addButton: any = document.getElementById('add-button')
        const tutorialHeight = addButton.clientHeight + 20
        const tutorialWidth = addButton.clientWidth + 20
        const bodyRect = document.body.getBoundingClientRect()
        const elemRect = addButton.getBoundingClientRect()
        const topOffset = elemRect.top - bodyRect.top - 10
        const leftOffset = elemRect.left - bodyRect.left - 10

        const appEle: any = document.getElementById('app')
        const tutorialDiv = document.createElement('div')
        tutorialDiv.id = 'turorial'
        const tutorialStr = `
                <div class="tutorial-main-page" style="height: ${tutorialHeight}px; width: ${tutorialWidth}px; top: ${topOffset}px; left: ${leftOffset}px">
                    <div
                        class="inner-content"
                        style="height: ${tutorialHeight - 10}px; width: ${
            tutorialWidth - 10
        }px; top: ${topOffset}px; left: ${leftOffset}px"
                    >
                    </div>
                </div>
                <div
                    class="tutorial-tips"
                    style="top: ${topOffset + tutorialHeight + 15}px; left: ${leftOffset - 120}px; width: ${
            tutorialWidth + 120
        }px"
                >
                    <div class="tips-content">
                        <div class="title">${i18n.t('ContactCustomer.create')}</div>
                        <button class="btn-select" type="link" id="turorialBtn"> ${i18n.t(
                            'ContactCustomer.got',
                        )} </button>
                    </div>
                    <div class="arrow-up"></div>
                </div>
                `
        tutorialDiv.innerHTML = tutorialStr
        appEle.appendChild(tutorialDiv)
    })
}
onMounted(async () => {
    itemList.value = ['common', router.currentRoute.value.meta.title]
    try {
        tableLoading.value = true
        await fetchCompanyTaxInfo({code: userCompany[0].code})
        if (apIntegration != 1) {
            await queryCustomer({company_code: userCompany[0].code, contact_name: 'DUMMY'})

            if (DUMMYCustomers.value.length === 0) {
                // create dummy
                const dummyItem = {
                    contact_name: 'DUMMY',
                    company_code: userCompany[0].code,
                    company_id: userCompany[0].id,
                    gl_account: '', // null check of api
                    office_country: companyTaxInfo.value.country,
                    office_province: companyTaxInfo.value.province,
                    billing_country: companyTaxInfo.value.country,
                    billing_province: companyTaxInfo.value.province,
                    shipping_country: companyTaxInfo.value.country,
                    shipping_province: companyTaxInfo.value.province,
                }
                await createDummy(dummyItem)
            }
        }

        await updateData()
        await fetchAccountDescDropdown({company_code: userCompany[0].code})
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }

    if (_.isEmpty(router.currentRoute.value.query) || !router.currentRoute.value.query.showTutorial) return
    addTutorial()
    setTimeout(() => {
        const _turorialBtn = document.getElementById('turorialBtn')
        const _turorial = document.getElementById('turorial')
        if (_turorialBtn)
            _turorialBtn.addEventListener('click', function () {
                _turorial!.remove()
                router.push({
                    path: '/bookkeeping/ap/uploadInvoice',
                    query: {showTutorial: 'true'},
                })
            })
    }, 100)
})
onBeforeUnmount(async () => {
    const x = document.getElementById('turorial')
    if (x) x.remove()
})
const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer.value)
    timer.value = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer.value)
        }
    }, 1000)
}
</script>
<template>
    <div class="page-container-customer" ref="mainRef">
        <div class="history-page-header">
            <div class="search-group-wrap">
                <a-input
                    v-model:value="searchForm.contact_name"
                    :placeholder="i18n.t('commonTag.search') + i18n.t('bkCustomer.company')"
                    :disabled="tableLoading"
                    class="search-input"
                    @input="inputChange"
                    @pressEnter="search"
                >
                    <template #suffix>
                        <search-outlined />
                    </template>
                </a-input>
                <a-popover trigger="click" placement="bottom">
                    <template #content>
                        <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                            <div class="search-input-group"></div>

                            <a-button type="primary" shape="round" :disabled="tableLoading" @click="search">
                                <template #icon>
                                    <search-outlined />
                                </template>
                                {{ $t('commonTag.search') }}
                            </a-button>
                        </a-form>
                    </template>
                    <!-- chen change -->
                    <!-- <a-button class="search-button" :disabled="tableLoading">
            <template #icon>
              <svg-icon name="icon_filter"></svg-icon>
            </template>
            {{ i18n.t('commonTag.filter') }}
          </a-button> -->
                </a-popover>
            </div>
            <a-button
                v-if="apIntegration != 1"
                type="primary"
                shape="round"
                class="add-button"
                id="add-button"
                :disabled="tableLoading"
                @click="add()"
            >
                <!-- <template #icon>
                    <plus-outlined />
                </template> -->
                {{ i18n.t('commonTag.new') }}
            </a-button>
        </div>

        <div class="history-page-content">
            <a-table
                :dataSource="combineTableData"
                :loading="tableLoading"
                :pagination="false"
                rowKey="id"
                :customRow="currentRowThing"
                :scroll="{y: 'calc(100vh - 300px)'}"
            >
                <a-table-column
                    align="left"
                    :title="i18n.t('bkCustomer.company')"
                    data-index="contact_name"
                    width="160px"
                />
                <a-table-column
                    align="left"
                    :title="i18n.t('bkCustomer.receiver')"
                    data-index="office_receiver"
                    width="160px"
                />
                <a-table-column
                    align="left"
                    :title="i18n.t('bkCustomer.address01')"
                    data-index="address"
                    width="200px"
                    :ellipsis="true"
                >
                    <template #default="{record}">
                        <span>
                            {{ getFullAddress(record) }}
                        </span>
                    </template>
                </a-table-column>
                <a-table-column align="center" :title="i18n.t('bkCustomer.tel')" data-index="tel" width="120px" />
                <a-table-column
                    align="left"
                    :title="i18n.t('bkCustomer.email')"
                    data-index="email"
                    width="120px"
                    :ellipsis="true"
                />
                <!-- <a-table-column
                    align="center"
                    :title="i18n.t('bkCustomer.operation')"
                    key="operation"
                    fixed="right"
                    width="64px"
                >
                    <template #default="{record}">
                        <span>
                            <a-button
                                :title="$t('bkSupplier.editReceiverTitle')"
                                class="btn-txt"
                                type="link"
                                @click="edit(record)"
                            >
                                <edit-outlined />
                            </a-button>
                        </span>
                    </template>
                </a-table-column> -->
            </a-table>
            <div class="pagination-wrap">
                <a-pagination
                    v-model:current="currentPageNumber"
                    v-model:page-size="pageSize"
                    :disabled="tableLoading"
                    :hideOnSinglePage="false"
                    :showSizeChanger="true"
                    :total="totalNumber"
                    @change="changePage"
                />
                <span
                    >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                    {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                >
            </div>
        </div>

        <a-modal
            :title="modalTitle"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            style="width: 80%; height: auto"
            :dialogStyle="{top: '10px'}"
            :bodyStyle="{padding: '10px 14px 14px'}"
        >
            <supplier-form
                :current-customer="current"
                :edit-mode="editMode"
                @updateData="updateData()"
                @dismiss="dismiss"
            ></supplier-form>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
/** @format */

.page-container-customer {
    border-radius: 10px;
    background-color: #fff;

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        height: 100%;
        /* 设置容器的高度为父容器高度的80% */
        overflow-y: hidden;
        /* 隐藏纵向滚动条 */
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 20px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 90px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button + .search-button {
                min-width: 113px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
        }
    }

    .history-page-content {
        padding: 12px 20px;
        overflow: hidden;

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
}
</style>
