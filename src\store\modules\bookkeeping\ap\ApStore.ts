/** @format */

import type {ActionContext} from 'vuex'
import service from '@/api/request'
import serviceV1 from '@/api/requestNew'
import FileSaver from 'file-saver'

const http = service
// v1 is new api.
const httpv1 = serviceV1

const ApStore = {
    namespaced: true,
    state: {
        invoicesList: [],
        invoicesListByPdf: [],
        ocrInvoicesListByPdf: [],
        ocrInvoiceItemByFile: {},
        invoicesHistoryList: [],
        invoiceDetail: {},
        totalNumber: 0,
        apTopCoaList: [],
        activeTabKey: '',
        poData: {},
        sapSupplierList: [],
        sapMasterData: {},
        invoiceFlow: [],
        paymentFlow: [],
        poTopList: [],
        taxCodeList: [],
    },
    mutations: {
        updateInvoicesList(state: {invoicesList: any[]}, list: any) {
            state.invoicesList = [...list]
        },
        updateInvoicesHistoryList(state: {invoicesHistoryList: any[]}, list: any) {
            state.invoicesHistoryList = [...list]
        },
        updateInvoiceDetail(state: {invoiceDetail: any}, detail: any) {
            state.invoiceDetail = {...detail}
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateInvoicesListByPdf(state: {invoicesListByPdf: any[]}, list: any) {
            state.invoicesListByPdf = [...list]
        },
        updateOcrInvoicesList(state: {ocrInvoicesListByPdf: any[]}, list: any) {
            state.ocrInvoicesListByPdf = [...list]
        },
        updateOcrInvoiceItem(state: {ocrInvoiceItemByFile: any}, list: any) {
            if (list && list.length > 0) {
                const {id, ...item} = list[0]
                state.ocrInvoiceItemByFile = {...item}
            } else {
                state.ocrInvoiceItemByFile = {NotExisted: true}
            }
        },
        updateApTopCoa(state: {apTopCoaList: any[]}, list: any) {
            state.apTopCoaList = [...list]
        },
        updateActiveTabKey(state: {activeTabKey: string}, keyName: string) {
            state.activeTabKey = keyName ?? 'pending'
        },
        updatePoData(state: {poData: any}, data: any) {
            state.poData = {...data}
        },
        updateSapSupplierListData(state: {sapSupplierList: any}, list: any) {
            state.sapSupplierList = [...list]
        },
        updateSapMasterData(state: {sapMasterData: any}, data: any) {
            state.sapMasterData = {...data}
        },
        updateInvoiceData(state: {invoiceFlow: any}, data: any) {
            state.invoiceFlow = [...data]
        },
        updatePaymentData(state: {paymentFlow: any}, data: any) {
            state.paymentFlow = [...data]
        },
        updatePoTopListData(state: {poTopList: any}, data: any) {
            state.poTopList = [...data]
        },
        updateTaxCodeList(state: {taxCodeList: any[]}, list: any) {
            state.taxCodeList = {...list}
        },
    },
    actions: {
        async fetchInvoicesList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/file/list', payload)
            store.commit('updateInvoicesList', response.data.data.list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchInvoicesListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get('invoice-statement/api/v1/file', {params: payload})
            const totalNumber = response.data.paginated ? response.data.paginated.total : response.data.data.length
            store.commit('updateInvoicesList', response.data.data)
            store.commit('updateTotalFoundNumber', totalNumber)
            return response
        },
        async fetchInvoicesHistoryList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            // const response = await http.post('/bk/ap/invoice/list', payload)
            // store.commit('updateInvoicesHistoryList', response.data.data.list)
            // store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            // // used for creating item callback to get the item just create.
            // if (response.data.data.list && response.data.data.totalCount === 1) {
            //     store.commit('updateOcrInvoiceItem', response.data.data.list)
            // }
            //  delete payload.page_size
            const response = await httpv1.get('/invoice-statement/api/v1/ap', {params: payload})

            const list = response.data.data ?? []
            const totalNumber = response.data.paginated ? response.data.paginated.total : list.length
            store.commit('updateInvoicesHistoryList', list)
            store.commit('updateTotalFoundNumber', totalNumber)
            // used for creating item callback to get the item just create.
            if (list && list.length === 1) {
                store.commit('updateOcrInvoiceItem', list)
            }

            return response
        },
        async checkReferenceNoRepetition(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await http.post('/bk/ap/invoice/list', payload)
            return response
        },
        async checkReferenceNoRepetitionV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.get('/invoice-statement/api/v1/ap', {params: payload})
            return response
        },
        async fetchApInvoiceDetailWithId(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any},
        ) {
            const response = await http.get(`/bk/ap/invoice/${payload.id}`)
            store.commit('updateInvoiceDetail', response.data.data)
            return response
        },
        async fetchApInvoiceDetailV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.get(`/invoice-statement/api/v1/ap`, {params: payload})
            store.commit('updateInvoiceDetail', response.data.data[0] || null)
            //store.commit('updateOcrInvoiceItem', response.data.data)
            return response
        },
        async deletePdfWithId(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/file/${payload}`)
        },
        async deletePdfWithIdV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpv1.delete(`invoice-statement/api/v1/file/${payload}`)
        },
        async deleteInvoiceWithoutSapIdV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            return httpv1.delete(`invoice-statement/api/v1/ap/${payload}`)
        },
        async uploadApInvoicePdf(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const formData = payload
            const response = await http.post('/bk/file/upload', formData)
            return response
        },
        async uploadApInvoicePdfV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const formData = payload
            const response = await httpv1.post('/invoice-statement/api/v1/file/upload', formData)
            return response
        },
        async uploadApInvoicePdfV1MX(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const formData = payload
            const response = await httpv1.post('/invoice-statement/api/v1/file/upload/xml', formData)
            return response
        },
        async uploadApInvoiceOriginalDocument(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: number; formData: FormData},
        ) {
            const formData = payload.formData
            const response = await httpv1.post(`/invoice-statement/api/v1/file/invoice/${payload.id}/upload`, formData)
            return response
        },
        async fetchApInvoiceListWithPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {fileId: any},
        ) {
            const response = await http.post(`/bk/ap/pdf/${payload.fileId}/invoice/list`)
            store.commit('updateInvoicesListByPdf', response.data.data)
            return response
        },
        async fetchApOcrResultByPdfId(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {fileId: any},
        ) {
            const response = await http.post(`/bk/ocr/pdf/${payload.fileId}/invoice/list`)
            store.commit('updateOcrInvoicesList', response.data.data)
            // always use first item as 'current' in apInvoiceFromPdf component.
            store.commit('updateOcrInvoiceItem', response.data.data)
            return response
        },
        async fetchApOcrResultByPdfIdV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {file_id: any},
        ) {
            // ocr will not enable
            // const response = await http.post(`/bk/ocr/pdf/${payload.fileId}/invoice/list`)
            const response = await httpv1.get('/invoice-statement/api/v1/ocr-invoice', {params: payload})
            store.commit('updateOcrInvoicesList', response.data.data)
            // always use first item as 'current' in apInvoiceFromPdf component.
            store.commit('updateOcrInvoiceItem', response.data.data)
            return response
        },
        async updateFileOcrStatusV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any; status: any},
        ) {
            const response = await httpv1.patch(`/invoice-statement/api/v1/file/${payload.id}/ocr/status`, payload)
            return response
        },
        async createInvoice(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/ap/invoice', payload)
            return response
        },
        async createInvoiceV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post('/invoice-statement/api/v1/ap', payload)
            return response
        },
        async createIntegrationInvoiceV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post('/invoice-statement/api/v1/ap/integration', payload)
            return response
        },
        async createIntegrationInvoiceWithPoV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            // const response = await httpv1.post('/invoice-statement/api/v1/ap/integration', payload)
            const response = await httpv1.post(`/prefectClient/po_fi_doc`, payload)
            return response
        },
        async createIntegrationInvoiceWithoutPoV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post(`/prefectClient/no_po_fi_doc`, payload)
            return response
        },
        async createIntegrationInvoiceWithCmPoV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            // const response = await httpv1.post('/invoice-statement/api/v1/ap/integration', payload)
            const response = await httpv1.post(`/prefectClient/po_cm_fi_doc`, payload)
            return response
        },
        async createIntegrationInvoiceWithCmOPoV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            // const response = await httpv1.post('/invoice-statement/api/v1/ap/integration', payload)
            const response = await httpv1.post(`/prefectClient/po_cm_fi_doc`, payload)
            return response
        },
        async createInvoiceJushi(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post('/invoice-statement/api/v1/ap/jushi/sap/post', payload)
            return response
        },
        async updateInvoiceV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.put(`/invoice-statement/api/v1/ap/${payload.id}`, payload)
            return response
        },
        async updateIntegrationInvoiceStatusV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any; status: any},
        ) {
            const response = await httpv1.patch(
                `/invoice-statement/api/v1/ap/${payload.id}/integration/status`,
                payload,
            )
            return response
        },
        async updateIntegrationSapStatusV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any; status: any},
        ) {
            const response = await httpv1.patch(
                `/invoice-statement/api/v1/ap/${payload.id}/integration/sap/status`,
                payload,
            )
            return response
        },
        async reversePurchaseRequestV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.patch(`/invoice-statement/api/v1/ap/${payload.id}/reverse`, {
                creator: payload?.creator,
                creator_name: payload?.creator_name,
                posting_date: payload?.posting_date,
            })
            return response
        },
        async convertPurchaseRequestV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.patch(`/invoice-statement/api/v1/ap/${payload.id}/cash`, {
                creator: payload?.creator,
                creator_name: payload?.creator_name,
            })
            return response
        },
        async noestatementBR(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/br/reconciliation/noestatement/submit', payload)
            return response
        },
        async fetchApTopCoa(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {clientId: any},
        ) {
            const response = await http.get(`/bk/ap/coa/top?clientId=${payload.clientId}`)
            store.commit('updateApTopCoa', response.data)
            return response
        },
        async fetchApTopCoaV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get(`/invoice-statement/api/v1/ap/coa/top`, {params: payload})
            store.commit('updateApTopCoa', response.data.data)
            return response
        },
        async fetchSapMasterTopV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code, contact_id} = payload
            const response = await httpv1.get(
                `/prefectClient/bp_finc_info?company_code=${company_code}&contact_id=${contact_id}`,
            )
            return response.data
        },
        async fetchSapPoInfoV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code, po_no} = payload
            const response = await httpv1.get(`/prefectClient/po?company_code=${company_code}&po_no=${po_no}`)
            return response
        },
        async fetchColIcaRateV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code, contact_id} = payload
            const response = await httpv1.get(`/prefectClient/col_ica_rate?company_code=${company_code}&contact_id=${contact_id}`)
            return response
        },
        async fetchSapProfomaPoInfoV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code, po_no} = payload
            const response = await httpv1.get(`/prefectClient/ppo?company_code=${company_code}&po_no=${po_no}`)
            return response
        },
        async fetchSapCmPoInfoV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code, po_no} = payload
            const response = await httpv1.get(`/prefectClient/cmpo?company_code=${company_code}&po_no=${po_no}`)
            return response
        },
        async postSapMasterTopV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post(`/prefectClient/bp_finc_info`, payload)
            return response
        },
        async fetchContactFirstUsingV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const {company_code, contact_id} = payload
            const response = await httpv1.get(
                `/prefectClient/eo_bp_count?company_code=${company_code}&contact_id=${contact_id}`,
            )
            return response.data
        },
        async fetchPoData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {po} = payload
            const response = await httpv1.post(`/invoice-statement/api/v1/ap/jushi/sap/get`, {po: po})
            store.commit('updatePoData', response.data.data)
            return response
        },
        async fetchApSapContactData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code, keyword} = payload
            const response = await httpv1.get(`/prefectClient/bp?company_code=${company_code}&query=${keyword}`)
            const sapSupplierList = response.data.data.context.tableParameters.ET_RESULT.map((item: any) => {
                return {
                    ...item,
                    isSapReturned: true,
                    id: item.BP_NUMBER,
                    contact_name: item.BP_NAME,
                    contact_id: item.BP_NUMBER,
                }
            })
            store.commit('updateSapSupplierListData', sapSupplierList)
            return sapSupplierList
        },
        async fetchApSapMasterData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code} = payload
            const response = await httpv1.get(`/prefectClient/master?company_code=${company_code}`)
            store.commit('updateSapMasterData', response.data.data.context.tableParameters)
            return response.data.data.context.tableParameters
        },
        async saveSAPBP(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code, issuer_id} = payload
            console.log('company_code=', company_code)
            console.log('issuer_id=', issuer_id)
            const response = await httpv1.post(
                `/prefectClient/save_bp?company_code=${company_code}&issuer_id=${issuer_id}`,
            )
            return response.data
        },
        async fetchApIntegrationPoTop(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code} = payload
            const response = await httpv1.get(`/prefectClient/po_history?company_code=${company_code}`)
            store.commit('updatePoTopListData', response.data)
            return response.data
        },
        async fetchInvoiceFlowData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {invoice_id} = payload
            const response = await httpv1.get(
                `/invoice-statement/api/v1/approval-flow?invoice_id=${invoice_id}&type=INVOICE`,
            )
            store.commit('updateInvoiceData', response.data.data)
            return response.data.data
        },
        async fetchPaymentFlowData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {invoice_id} = payload
            const response = await httpv1.get(
                `/invoice-statement/api/v1/approval-flow?invoice_id=${invoice_id}&type=PAYMENT`,
            )
            store.commit('updatePaymentData', response.data.data)
            return response.data.data
        },
        async createApprovalFlowData(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post(`/invoice-statement/api/v1/approval-flow/batch`, {
                companyCode: payload?.company_code,
                invoiceId: payload?.id,
                creator: payload?.creator_name,
                po: payload?.po ?? '',
            })
            return response
        },
        async startApprovalFlow(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post(`/prefectClient/invoice/approve_notice?invoice_id=${payload.id}`)
            return response.data
        },
        async resendApprovalFlow(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {id} = payload
            const response = await httpv1.post(
                `/prefectClient/invoice/approve_notice?invoice_id=${id}&approve_status=2`,
            )
            return response.data
        },
        async approveFlow(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {uuid, email, invoice_id} = payload
            const response = await httpv1.get(
                `/prefectClient/invoice/approve?uuid=${uuid}&email=${email}&id=${invoice_id}`,
            )
            return response.data
        },
        async rejectFlow(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {uuid, email, invoice_id} = payload
            const response = await httpv1.get(`/prefectClient/invoice/reject?uuid=${uuid}&email=${email}`)
            return response.data
        },
        async fetchTaxCodeDropdown(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get(`/prefectClient/tax_code/${payload?.company_code}`)
            store.commit('updateTaxCodeList', response.data)
            return response.data
        },
        async reverseIntegrationPurchaseRequestV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.patch(`/invoice-statement/api/v1/ap/${payload.id}/reverse/integration`, {
                creator: payload?.creator,
                creator_name: payload?.creator_name,
                posting_date: payload?.posting_date,
            })
            return response
        },
        async reverseIntegrationPurchaseWithPoRequestV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post(`/prefectClient/reverse_po_fi_doc`, {
                creator: payload?.creator,
                creator_name: payload?.creator_name,
                posting_date: payload?.posting_date,
                invoice: payload?.invoice,
            })
            return response
        },
        async sendApDataToSap(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post(`/prefectClient/fi_doc?inovice_nomber=${payload.invoice_no}`)
            return response
        },
        async sendApReverseDataToSap(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            console.log('===========payload1', payload)
            const response = await httpv1.post(
                `/handleexport/reverse_fi_doc?fidoc_no=${payload.sap_document_id}&posting_date=${payload.posting_date}`,
            )
            return response
        },
        async sendApReverseDataToSapWithCompanyCode(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            console.log('===========payload2', payload)
            const response = await httpv1.post(
                `/prefectClient/reverse_fi_doc_with_company_code?company_code=${payload.company_code}&fidoc_no=${payload.sap_document_id}&posting_date=${payload.posting_date}`,
            )
            return response
        },
        async handleDuplicatedFile(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post(
                `/prefectClient/remove_duplicate_file?company_code=${payload.company_code}`,
            )
            return response
        },
        async exportApInvoiceList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any},
        ) {
            const subPath = `handleExport/ap/${payload.company_code}`
            const response = await httpv1.get(subPath, {
                responseType: 'blob',
            })
            const filename = (response.headers['content-disposition'] || '')
                .split('filename=')[1]
                .replace('"', '')
                .replace('"', '')

            const blob = new Blob([response.data], {
                type: response.headers['content-type'],
                // type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            })

            FileSaver.saveAs(blob, filename)
        },
        async printInvoice(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post(`/invoice-statement/api/v1/check/batch`, payload)
            if (response.status === 201 && response.data.data.length) {
                const subPath = '/web/static1'
                response.data.data.forEach((url: string) => {
                    const link = document.createElement('a')
                    link.download = url.split('/').pop() || ''
                    link.href = `${window.location.protocol}//${window.location.host}${subPath}${url}`
                    console.log(link.href)
                    link.click()
                    link.remove()
                })
            }

            return response
        },
        async updateUploadFileComment(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {comment: string; id: any},
        ) {
            const query = {
                comment: payload.comment,
            }
            return httpv1.patch(`invoice-statement/api/v1/file/${payload.id}`, query)
        },
        async patchApInvoiceData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const id = payload.id
            delete payload.id
            return await httpv1.patch(`invoice-statement/api/v1/ap/${id}`, payload)
        },
        async createChequePdf(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any[]) {
            return await httpv1.post(`koa/pdf-ck15106`, payload)
        },

        async postPgoLoginInfo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.post('/prefectClient/pgo_login_info', payload)
        },
        async getPgoLoginInfo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code} = payload
            return await httpv1.get('/prefectClient/pgo_login_info?company_code=' + company_code)
        },
        async postPgoUrl(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.post('/prefectClient/pgo_url', payload)
        },
        async getPgoUrl(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const {company_code} = payload
            return await httpv1.get('/prefectClient/pgo_url?company_code=' + company_code)
        },
        async postPgoLoginTest(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.post('/prefectClient/test_pgo_login_info', payload)
        },
        async postApRealizeBankReconcile(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.post(`/prefectClient/ap_reconciliation`, payload)
        },
    },
}

export default ApStore
