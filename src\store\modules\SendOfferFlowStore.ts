/** @format */

import service from '../../api/request'
// import {useI18n} from 'vue-i18n'
// const {t} = useI18n()
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import type {ActionContext} from 'vuex'
const i18n: Composer = i18nInstance.global

const state = () => ({
    candidate: {
        interviewPerson: '',
        mdApproveStatus: 0,
        mdName: '',
        mdEmail: '',
        mdApproveRemark: '',
        cooApprove: '0',
        cooApproveRemark: '',
        cooEmail: '',
        cooName: '',
        cooApproveStatus: 0,
        approveEmailContent: '',
        approveEmailTitle: '',
        sendResume: 0,
        offerUrl: '',
        offerFileName: '',
        workTypeStr: '',
    },
    activeStep: 1,
})

const getters = {
    approveStatus: (state: any) => (idx: string | number) => {
        // const status = ['未开始审批', '审批中', '同意', '拒绝']
        const status = [
            i18n.t('sendOfferStore.approvalNotStarted'),
            i18n.t('sendOfferStore.underApprove'),
            i18n.t('sendOfferStore.approved'),
            i18n.t('sendOfferStore.deny'),
        ]
        return status[+idx]
    },
}

const mutations = {
    setCandidate(
        state: {
            candidate: {cooApproveStatus: string; mdApproveStatus: string}
            activeStep: number
        },
        payload: any,
    ) {
        state.candidate = {...payload}
        if (!+state.candidate.cooApproveStatus) {
            if (!state.candidate.mdApproveStatus) {
                state.activeStep = 1
            } else if (state.candidate.mdApproveStatus === '1') {
                state.activeStep = 2
            } else if (state.candidate.mdApproveStatus === '2') {
                state.activeStep = 3
            }
        } else {
            if (state.candidate.cooApproveStatus === '2') {
                state.activeStep = 3
            } else if (state.candidate.mdApproveStatus === '3' || state.candidate.cooApproveStatus === '3') {
                state.activeStep = 1
            } else {
                state.activeStep = 2
            }
        }
    },
    next(state: {activeStep: number}) {
        state.activeStep = state.activeStep + 1
    },
    prev(state: {activeStep: number}) {
        state.activeStep = state.activeStep - 1
    },
}

const actions = {
    async sendOfferApprove(
        ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {[x: string]: string | Blob; files?: any},
    ) {
        const form = new FormData()
        Object.keys(payload)
            .filter(key => key !== 'files')
            .forEach(key => form.append(key, payload[key]))
        payload.files.forEach((f: {raw: string | Blob}) => form.append('files', f.raw))
        return service.post('/recruitment/approveOfferEmail', form)
    },
    async approveOffer(
        ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {[x: string]: string | Blob},
    ) {
        const form = new FormData()
        Object.keys(payload).forEach(key => form.append(key, payload[key]))
        return service.post('/recruitment/approveOfferSubmit', form)
    },
    async sendOfferEmail(ctx: {state: {candidate: {id: string | Blob}}}, payload: {form: any; files: any}) {
        const formData = new FormData()
        const {form, files} = payload
        Object.keys(form).forEach(key => formData.append(key, form[key]))
        files.forEach((file: {raw: string | Blob}) => formData.append('files', file.raw))
        formData.append('id', ctx.state.candidate.id)
        const emails = form.forward.map((f: {toEmailAddressStr: any}) => f.toEmailAddressStr).join(',')
        formData.append('csPersons', emails)
        return service.post('/recruitment/offerEmail', formData)
    },
    async searchStaff(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, name: string | Blob) {
        const formData = new FormData()
        formData.append('name', name)
        return service.post('/recruitment/candidateName', formData)
    },
}

export default {
    state,
    mutations,
    actions,
    getters,
}
