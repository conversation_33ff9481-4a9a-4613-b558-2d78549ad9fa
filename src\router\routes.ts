/** @format */

const routes = [
    {
        path: '/',
        name: 'Login',
        component: () => import('@/views/Login/Login.vue'),
    },
    {
        path: '/bookkeeping',
        component: () => import('@/components/MainLayout.vue'),
        redirect: '/ap/uploadInvoice',
        children: [
            {
                path: '/dashboard',
                name: 'Dashboard',
                component: () => import('@/views/Bookkeeping/Dashboard/Dashboard.vue'),
            },
            {
                path: '/task',
                name: 'Task',
                component: () => import('@/views/Bookkeeping/Task/Task.vue'),
            },
            {
                path: 'ap',
                name: 'AccountPayable',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                // redirect: '/bookkeeping/ap/uploadInvoice',
                children: [
                    {
                        path: 'uploadInvoice',
                        component: () => import('@/views/Bookkeeping/Ap/ApUploadInvoice.vue'),
                        name: 'UploadInvoice',
                        meta: {title: 'uploadInvoice', keepAlive: true},
                    },
                    {
                        path: 'woBillsInvoice',
                        component: () => import('@/views/Bookkeeping/Ap/ApWoBillsInvoice.vue'),
                        name: 'WoBillsInvoice',
                        meta: {title: 'woBillsInvoice', keepAlive: true},
                    },
                    {
                        path: 'invoiceFromPdf',
                        component: () => import('@/views/Bookkeeping/Ap/ApInvoiceFromPdf.vue'),
                        name: 'InvoiceFromPdf',
                        meta: {title: 'InvoiceFromPdf', keepAlive: true},
                    },
                    {
                        path: 'invoiceHistory',
                        component: () => import('@/views/Bookkeeping/Ap/ApInvoiceHistory.vue'),
                        name: 'ApInvoiceHistory',
                        meta: {title: 'invoiceHistory', keepAlive: true},
                    },
                ],
            },
            {
                path: 'ar',
                name: 'AccountReceivable',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'fullInvoice',
                        component: () => import('@/views/Bookkeeping/Ar/ArFullInvoice.vue'),
                        name: 'FullInvoice',
                        meta: {title: 'fullInvoice', keepAlive: true},
                    },
                    {
                        path: 'invoiceHistory',
                        component: () => import('@/views/Bookkeeping/Ar/ArInvoiceHistory.vue'),
                        name: 'ArInvoiceHistory',
                        meta: {title: 'invoiceHistory', keepAlive: true},
                    },
                ],
            },
            {
                path: 'py',
                name: 'AccountPayroll',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'invoiceHistory',
                        component: () => import('@/views/Bookkeeping/Py/PyInvoiceHistory.vue'),
                        name: 'PyInvoiceHistory',
                        meta: {title: 'invoiceHistory', keepAlive: true},
                    },
                ],
            },
            {
                path: 'pmt',
                name: 'AccountPayment',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'invoiceHistory',
                        component: () => import('@/views/Bookkeeping/Pmt/ApInvoiceHistory.vue'),
                        name: 'PmtInvoiceHistory',
                        meta: {title: 'invoiceHistory', keepAlive: true},
                    },
                ],
            },
            {
                path: 'bankReconciliation',
                name: 'BankReconciliation',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'uploadStatement',
                        component: () => import('@/views/Bookkeeping/BankReconciliation/UploadStatement.vue'),
                        name: 'UploadStatement',
                        meta: {title: 'uploadStatement', keepAlive: true},
                    },
                    {
                        path: 'esOcrFromPdf',
                        component: () => import('@/views/Bookkeeping/BankReconciliation/EsOcrFromPdf.vue'),
                        name: 'EStatementFromPdf',
                        meta: {title: 'EStatementFromPdf', keepAlive: true},
                    },
                    {
                        path: 'main',
                        component: () => import('@/views/Bookkeeping/BankReconciliation/BankReconciliation.vue'),
                        name: 'Main',
                        meta: {title: 'main', keepAlive: true},
                    },
                    {
                        path: 'history',
                        component: () => import('@/views/Bookkeeping/BankReconciliation/ReconciliationHistory.vue'),
                        name: 'History',
                        meta: {title: 'history', keepAlive: true},
                    },
                    {
                        path: 'reconciliation',
                        component: () => import('@/views/Bookkeeping/BankReconciliation/ReconciliationDetails.vue'),
                        name: 'Reconciliation',
                        meta: {title: 'bankReconciliation', keepAlive: true},
                    },
                ],
            },
            {
                path: 'gl',
                name: 'Gl',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'glListing',
                        component: () => import('@/views/Bookkeeping/Gl/GlList.vue'),
                        name: 'GlListing',
                        meta: {title: 'glListing', keepAlive: true},
                    },
                    {
                        path: 'glEntry',
                        component: () => import('@/views/Bookkeeping/Gl/GlEntry.vue'),
                        name: 'GlEntry',
                        meta: {title: 'glEntry', keepAlive: true},
                    },
                ],
            },
            {
                path: 'reporting',
                name: 'Reporting',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                redirect: {name: 'demo'},
                children: [
                    {
                        path: '',
                        component: () => import('@/views/Bookkeeping/Reporting/ReportingDemo.vue'),
                        name: 'demo',
                        meta: {title: 'demo', keepAlive: true},
                    },
                    {
                        path: 'realtimeReports',
                        component: () => import('@/views/Bookkeeping/Reporting/RealtimeReports.vue'),
                        name: 'RealtimeReports',
                        meta: {title: 'RealtimeReports', keepAlive: true},
                    },
                ],
            },
            {
                path: 'common',
                name: 'Common',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'customer',
                        component: () => import('@/views/Bookkeeping/Common/ContactCustomer.vue'),
                        name: 'Customer',
                        meta: {title: 'customer', keepAlive: true},
                    },
                    {
                        path: 'taxInformation',
                        component: () => import('@/views/Bookkeeping/Common/TaxInformation.vue'),
                        name: 'TaxInformation',
                        meta: {title: 'taxInformation', keepAlive: true},
                    },
                    {
                        path: 'accountDescription',
                        component: () => import('@/views/Bookkeeping/Common/AccountDescription.vue'),
                        name: 'AccountDescription',
                        meta: {title: 'accountDescription', keepAlive: true},
                    },
                    //TODO:chen COA Mapping
                    {
                        path: 'coaMapping',
                        component: () => import('@/views/Bookkeeping/Common/CoaMappingPage.vue'),
                        name: 'CoaMapping',
                        meta: {title: 'coaMapping', keepAlive: true},
                    },
                    {
                        path: 'productService',
                        component: () => import('@/views/Bookkeeping/Common/ProductServicePage.vue'),
                        name: 'ProductService',
                        meta: {title: 'productService', keepAlive: true},
                    },
                    {
                        path: 'taxCalculation',
                        component: () => import('@/views/Bookkeeping/Common/TaxCalculation.vue'),
                        name: 'TaxCalculation',
                        meta: {title: 'taxCalculation', keepAlive: true},
                    },
                    {
                        path: 'spotCurrency',
                        component: () => import('@/views/Bookkeeping/Common/SpotCurrency.vue'),
                        name: 'SpotCurrency',
                        meta: {title: 'spotCurrency', keepAlive: true},
                    },
                    {
                        path: 'connectivities',
                        component: () => import('@/views/Bookkeeping/Common/SettingConnectivities.vue'),
                        name: 'Connectivities',
                        meta: {title: 'Connectivities', keepAlive: true},
                    },
                ],
            },
            //TODO:Chen
            {
                path: 'help',
                name: 'Help',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'faq',
                        component: () => import('@/views/Bookkeeping/Help/Faq.vue'),
                        name: 'Faq',
                        meta: {title: 'Faq', keepAlive: true},
                    },
                ],
            },
            {
                path: 'account',
                name: 'Account',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'user',
                        component: () => import('@/views/Bookkeeping/Account/User.vue'),
                        name: 'User',
                        meta: {title: 'User', keepAlive: true},
                    },
                ],
            },
            {
                path: 'subscribe',
                name: 'Subscribe',
                component: () => import('@/views/Bookkeeping/BookkeepingView.vue'),
                children: [
                    {
                        path: 'nosubscribe',
                        component: () => import('@/views/Bookkeeping/Subscribe/NoSubscribe.vue'),
                        name: 'NoSubscribe',
                        meta: {title: 'NoSubscribe', keepAlive: true},
                    },
                ],
            },
        ],
    },
    {path: '/:pathMatch(.*)', redirect: '/'},
]
export default routes
