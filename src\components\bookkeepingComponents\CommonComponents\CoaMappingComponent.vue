<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import {computed, onBeforeMount, reactive, ref} from 'vue'
import {message, Modal, type FormInstance} from 'ant-design-vue'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {PlusOutlined} from '@ant-design/icons-vue'

import * as _ from 'lodash'
import {UserCompany, UserInfo} from '@/lib/storage'
import SvgIcon from '@/components/SvgIcon.vue'

const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const i18n: Composer = i18nInstance.global
const store = useStore()
const formRef = ref<FormInstance>()
const tableWrapRef = ref()
const state = reactive({
    form: [] as any[],
})
const isDisable = ref(false)
const formLoading = ref(false)
// const show = ref(false)
// const spot = ref()
// const amount_tc_a = ref('')
// const amount_tc_b = ref('')
// const showSaveButton = ref(false)

// new
const addNumber = ref(0)
const optionType = ref('')
// const category = ref<any>({
//     list: [],
// // })
// const businessKey = ref<any>({
//     list: [],
// })
const tableLoading = ref(false)
const enterAdd = ref(false)

const currentPageNumber = ref(1)
const pageSize = ref(10)

const readonlyMode = ref(true)
const showEditItem = ref(-1)
const editItemList = ref<number[]>([]) // persist edited items id

const businessKeyListOptions = ref([] as any)

// mapActions
//请求list数据
const fetchCoaMappingList = (payload: any) => store.dispatch('CoaMappingStore/fetchCoaMappingList', payload)
//增加Actio
const AddCoaMapping = (payload: any) => store.dispatch('CoaMappingStore/AddCoaMappingItem', payload)
//删除Action
const fetchDelAction = (payload: any) => store.dispatch('CoaMappingStore/fetchDelItemAction', payload)
//修改Action
const updateEditItem = (payload: any) => store.dispatch('CoaMappingStore/updateCoaMappingItem', payload)
//请求静态数据
const fetchBusinessKeyList = (payload: any) => store.dispatch('CoaMappingStore/fetchBusinessKeyList', payload)

const fetchMovementTypeList = (payload: any) => store.dispatch('CoaMappingStore/fetchMovementTypeList', payload)
//请求deb和cre数据
const fetchDebitOrCreditList = (payload: any) => store.dispatch('CoaMappingStore/fetchDebOrCreditList', payload)

// mapMutations

// computed mapState
const categoryList = computed(() => store.getters['CoaMappingStore/coa_mapping_category'])

const businessKeyList = computed(() => store.state.CoaMappingStore.businessKeyList)
const movementTypeList = computed(() => store.state.CoaMappingStore.movementTypeList)
const orderTypeList = computed(() => store.state.CoaMappingStore.orderTypeList)
const debOrCreList = computed(() => store.state.CoaMappingStore.debOrCreList)

const coaMappingList = computed(() =>
    store.state.CoaMappingStore.coaMappingList.map((item: any) => {
        let coa_json: any = _.isArray(item.coa_json)
            ? JSON.stringify(item.coa_json || [])
            : JSON.stringify(JSON.parse(item.coa_json))
        coa_json = !coa_json || coa_json === 'null' ? null : coa_json

        return {...item, coa_json}
    }),
)
const totalNumber = computed(() => store.state.CoaMappingStore.totalNumber)

//change callback, add the item into editItemList if happen
const onChangeCategory = (value: any, index: number) => {
    if (value === '1') {
        //pay item need change businessKey to EI
        businessKeyListOptions.value = businessKeyList.value
        state.form[index].code = businessKeyList.value.find((x: any) => x.desc === 'EI')?.wage_type || ''
    } else if (value === '2') {
        state.form[index].code = null
        businessKeyListOptions.value = movementTypeList.value
    } else if (value === '3') {
        state.form[index].code = null
        businessKeyListOptions.value = orderTypeList.value
    }
    if (state.form[index].id) {
        //editing existed item
        editItemList.value.push(state.form[index].id)
    }
}

const selectFocus = (value: string) => {
    if (value === '1') {
        businessKeyListOptions.value = businessKeyList.value
    } else if (value === '2') {
        businessKeyListOptions.value = movementTypeList.value
    } else if (value === '3') {
        businessKeyListOptions.value = orderTypeList.value
    }
}

const coaJsonChanged = (ev: any, item: any) => {
    if (item.id) {
        //editing existed item
        editItemList.value.push(item.id)
    }
}

const fieldsChanged = (id: any) => {
    if (id) {
        //editing existed item
        editItemList.value.push(id)
    }
}

//methods
//更新页面
const updateData = async () => {
    const searchObj: any = {}
    searchObj['$limit'] = pageSize.value
    searchObj['$skip'] = (currentPageNumber.value - 1) * pageSize.value
    searchObj['$sort[create_time]'] = 'desc'
    searchObj['company_code'] = userCompany[0].code
    try {
        tableLoading.value = true
        initFormData()
        await fetchCoaMappingList(searchObj)
        state.form = _.cloneDeep(coaMappingList.value)
        businessKeyListOptions.value = businessKeyList.value
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const addItem = () => {
    if (!state.form.length) {
        state.form = []
    }
    addNumber.value++
    if (addNumber.value === 1) {
        // 添加items
        enterAdd.value = true
        state.form.push({
            itemNo: state.form.length + 1,
            company_id: userCompany[0].id,
            company_code: userCompany[0].code,
            type: undefined,
            code: undefined,
            code_category: null,
            coa_id: 0,
            coa_code: undefined,
            debit_coa_code: undefined,
            debit_coa_id: 0,
            creator: userInfo?.id,
            coa_json: '[]',
        })
        // formAdd.value.push({
        //     itemNo: form.value.length + 1,
        //     company_id: userCompany[0].id,
        //     company_code: userCompany[0].code,
        //     type: undefined,
        //     code: undefined,
        //     code_category: null,
        //     coa_id: 0,
        //     coa_code: undefined,
        //     debit_coa_code: undefined,
        //     debit_coa_id: 0,
        //     creator: 'string',
        // })
        showEditItem.value = state.form.length
    } else {
        message.warn('Only one can be added at a time')
    }
}

const typeDisplayString = (type: string) => {
    return categoryList.value.find((x: any) => x.id === type)?.title || ''
}

const businessKeyDisplayString = (businessKey: string, type: string) => {
    let desc = ''
    if (type === '1') {
        desc = businessKeyList.value.find((x: any) => x.wage_type === businessKey)?.desc || ''
    } else if (type === '2') {
        desc = movementTypeList.value.find((x: any) => x.code === businessKey)?.description || ''
    } else if (type === '3') {
        desc = orderTypeList.value.find((x: any) => x.code === businessKey)?.description || ''
    }

    return businessKey ? `${businessKey} | ${desc}` : ''
}

const coaDisplayString = (coa: string) => {
    const coaItem = debOrCreList.value.find((x: any) => x.account_code === coa)
    return coa ? `${coa} | ${coaItem?.name || ''}` : ''
}

// //自定义字典查询方法
// const dictCodeFun = (code: number, type: number) => {
//     let itemTitle = ''
//     if (type === 1) {
//         categoryList.value.forEach((item: any) => {
//             if (item.id === code) {
//                 itemTitle = item.title
//             }
//         })
//     } else if (type === 3) {
//         const lines = JSON.parse(JSON.stringify(debOrCreList))
//         for (let index = 0; index < lines?.length; index++) {
//             const elements = lines[index]
//             if (elements.account_code == code) {
//                 itemTitle = elements.account_code + ' | ' + elements.name
//             }
//         }
//     }
//     return itemTitle
// }
//点击选中callback
const customRow = (record: any, index: number) => {
    return {
        onclick: (event: any) => {
            if (enterAdd.value) {
                optionType.value = 'isAdd'
            } else {
                optionType.value = 'isEdit'
            }
            showEditItem.value = index + 1
        },
    }
}
//business 回显

//删除
const remove = (record: any) => {
    if (record.id) {
        Modal.confirm({
            title: i18n.t('bkCommonTag.confirmation'),
            content: i18n.t('bkCommonTag.msgDeleteConfirm'), //the data
            async onOk() {
                try {
                    const response = await fetchDelAction(record.id)
                    if (response.status === 200 || response.status === 201) {
                        message.success(i18n.t('ApComponents.success'))
                        updateData()
                    } else {
                        // message.error({
                        //     content: 'failed',
                        //     duration: 3,
                        // })
                    }
                } catch (error) {
                    console.log(error)
                }
            },
            okText: i18n.t('commonTag.confirm'),
            cancelText: i18n.t('commonTag.cancel'),
            okType: 'danger',
            onCancel() {
                //showEditItem.value = null
                showEditItem.value = 0
            },
        })
    } else {
        state.form.splice(state.form.length - 1, 1)
        addNumber.value = 0
    }
}

const save_edit = () => {
    const ids = [...new Set(editItemList.value)] // remove duplicated
    if (ids.length > 0) {
        const editItemsPromise = state.form
            .filter((item: any) => ids.includes(item.id))
            .map((x: any) => {
                if (x.coa_code) {
                    const coaItem = debOrCreList.value.find((c: any) => c.account_code === x.coa_code)
                    x.coa_id = coaItem?.id
                    x.coa_name = coaItem?.name
                }
                if (x.debit_coa_code) {
                    const coaItem = debOrCreList.value.find((c: any) => c.account_code === x.debit_coa_code)
                    x.debit_coa_id = coaItem?.id
                    x.debit_coa_name = coaItem?.name
                }
                return x
            })
            .map((x: any) => updateEditItem({...x, coa_json: JSON.parse(x.coa_json)}))
        return editItemsPromise
    } else {
        return [null]
    }
}
const save_add = () => {
    const add_form = state.form.find((item: any) => item.id === undefined)

    if (add_form === undefined) return null
    if (add_form.coa_code) {
        const coaItem = debOrCreList.value.find((x: any) => x.account_code === add_form.coa_code)
        add_form.coa_id = coaItem?.id
        add_form.coa_name = coaItem?.name
    }
    if (add_form.debit_coa_code) {
        const coaItem = debOrCreList.value.find((x: any) => x.account_code === add_form.debit_coa_code)
        add_form.debit_coa_id = coaItem?.id
        add_form.debit_coa_name = coaItem?.name
    }
    if (add_form.code && (add_form.coa_code || add_form.debit_coa_code)) {
        return AddCoaMapping({...add_form, coa_json: JSON.parse(add_form.coa_json)})
    } else {
        return null
    }
}

const save_form = async () => {
    if (await formRef.value?.validateFields()) {
        try {
            tableLoading.value = true
            const req = [...save_edit(), save_add()].filter(x => x)
            // if (req.length > 0) {
            const res = await Promise.allSettled(req)
            //map((p: any) => p.catch((e: Error) => e))
            console.log(res)

            if (res.every((r: any) => [200, 201].includes(r.value.status))) {
                message.success(i18n.t('ApComponents.success'))

                editItemList.value = []
            } else {
                // message.error('failed')
            }
            await updateData()
            // } else {
            //     // message.warn('please fill in debit receipt or credit receipt')
            // }
        } catch (e) {
            // message.error('failed')
        } finally {
            tableLoading.value = false
        }
    }
}
//新增 和 编辑
// const save = async () => {
// if (optionType.value === 'isEdit') {
//     const response2 = await fetchEditAction(formEdit.value)
//     if (response2.status === 200 || response2.status === 201) {
//         message.success({
//             content: 'success',
//             duration: 3,
//         })
//     } else {
//         message.error({
//             content: 'failed',
//             duration: 3,
//         })
//     }
//     await updateData()
// } else {
//     const queryForm = form.value.find((item: any) => {
//         return item.id === undefined
//     })
//
//     if (queryForm === undefined) return
//
//     if (
//         // queryForm.code_category !== null &&
//         // queryForm.code_category !== undefined &&
//         queryForm.code &&
//         (queryForm.coa_code || queryForm.debit_coa_code)
//     ) {
//         let response: any = {}
//         try {
//             tableLoading.value = true
//             response = await fetchAddAction(queryForm)
//             if (response.status == 201 || response.status == 200) {
//                 message.success({
//                     content: 'success',
//                     duration: 3,
//                 })
//             } else {
//                 message.error({
//                     content: response.data.errors,
//                     duration: 3,
//                 })
//             }
//             await updateData()
//         } catch (error: any) {
//             message.error({
//                 content: error.response.data.errors,
//                 duration: 3,
//             })
//         } finally {
//             tableLoading.value = false
//         }
//     } else {
//         message.error({
//             content: 'field required',
//             duration: 3,
//         })
//         return false
//     }
// }
//}
//重置
const initFormData = () => {
    //showEditItem.value = null
    showEditItem.value = 0
    readonlyMode.value = true
    enterAdd.value = false
    addNumber.value = 0
    editItemList.value = []
}
//分页change
const changePage = () => {
    updateData()
}
//校验
// const requireRule = (propName: any) => [
//     {
//         required: true,
//         message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
//         trigger: ['blur', 'change'],
//     },
// ]
// const lengthLimitRule = (min = 1, max: number) => {
//     return [
//         {
//             min,
//             max,
//             message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
//             trigger: 'blur',
//         },
//     ]
// }

// :TODO  add validator
const validateCredit = async (_rule: any, value: string) => {
    if (!value) {
        return Promise.reject(' at least select one option in credit or debit ')
    } else {
        return Promise.resolve()
    }
}
const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur'],
        },
    ]
}
const requireCoaJson = (rule: any, value: string | null) => {
    try {
        if (value) {
            JSON.parse(value)
        } else {
            return Promise.reject()
        }
    } catch (error) {
        return Promise.reject()
    }

    return Promise.resolve()
}

const rules = reactive({
    type: [...requireRule('Category')],
    code: [...requireRule('Business Key')],
    coa_json: [
        {
            message: i18n.t('gl.format'),
            validator: requireCoaJson,
            trigger: ['blur', 'change'],
        },
    ],
})

const getPopupContainer = (node: any) => {
    return tableWrapRef.value
}

const test = () => {
    console.log(state.form)
    console.log('first element: ', state.form[0])
}

onBeforeMount(async () => {
    try {
        tableLoading.value = true
        await Promise.all([
            fetchMovementTypeList(userCompany[0].code),
            fetchBusinessKeyList(userCompany[0].code),
            fetchDebitOrCreditList({company: userCompany[0].code}),
            updateData(),
        ])
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
})

//defineExpose({initFormData})
</script>
<template>
    <div class="page-container-ap-invoice-form" id="scroll-box">
        <a-spin :spinning="formLoading">
            <a-form
                ref="formRef"
                :model="state.form"
                :layout="'vertical'"
                label-width="auto"
                label-position="top"
                class="form-box"
            >
                <div class="ap-invoice-block" ref="tableWrapRef">
                    <a-table
                        :dataSource="state.form"
                        :loading="tableLoading"
                        :pagination="false"
                        defaultSortOrder="ascend"
                        :customRow="customRow"
                    >
                        <!-- index -->
                        <a-table-column
                            data-index="id"
                            :title="i18n.t('bkCustomer.itemNo')"
                            align="center"
                            header-align="center"
                            width="10%"
                        >
                            <template #default="{index}">
                                <span>
                                    {{ index + 1 }}
                                </span>
                            </template>
                        </a-table-column>
                        <!-- category -->
                        <a-table-column
                            data-index="type"
                            :title="i18n.t('bkCustomer.category')"
                            align="left"
                            header-align="left"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    style="margin-bottom: 0"
                                    :name="[index, 'type']"
                                    v-if="!readonlyMode || showEditItem == index + 1"
                                >
                                    <a-select
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="record.type"
                                        show-search
                                        :dropdownStyle="{maxHeight: '150px'}"
                                        dropdownMatchSelectWidth
                                        :getPopupContainer="getPopupContainer"
                                        :filter-option="false"
                                        class="table-input"
                                        style="width: 200px; over-flow: hidden"
                                        @change="onChangeCategory(record.type, index)"
                                    >
                                        <a-select-option v-for="item in categoryList" :key="item.id" :value="item.id">{{
                                            item.title
                                        }}</a-select-option>
                                    </a-select>
                                </a-form-item>
                                <span class="" v-else>
                                    {{ typeDisplayString(record.type) }}
                                </span>
                            </template>
                        </a-table-column>
                        <!-- businessKey -->
                        <a-table-column
                            data-index="code"
                            :title="i18n.t('bkCustomer.businessKey')"
                            align="left"
                            header-align="left"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    style="margin-bottom: 0"
                                    :name="[index, 'code']"
                                    v-if="!readonlyMode || showEditItem == index + 1"
                                >
                                    <a-select
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="record.code"
                                        show-search
                                        :dropdownStyle="{maxHeight: '200px'}"
                                        :dropdownMatchSelectWidth="500"
                                        :filter-option="false"
                                        :getPopupContainer="getPopupContainer"
                                        class="table-input"
                                        style="width: 200px; over-flow: hidden"
                                        @dropdownVisibleChange="selectFocus(record.type)"
                                        @change="fieldsChanged(record.id)"
                                        :allow-clear="true"
                                    >
                                        <a-select-option
                                            v-for="item in businessKeyListOptions"
                                            :key="item.id"
                                            :value="item.wage_type || item.code"
                                            >{{
                                                `${item.wage_type || item.code} | ${
                                                    item.desc || item.description || ''
                                                }`
                                            }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                                <span v-else>
                                    {{ businessKeyDisplayString(record.code, record.type) }}
                                </span>
                            </template>
                        </a-table-column>

                        <!-- debitReceipt -->
                        <a-table-column
                            data-index="debit_coa_code"
                            :title="i18n.t('bkCustomer.debitReceipt')"
                            align="left"
                            header-align="left"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    name="debit_coa_code"
                                    style="margin-bottom: 0"
                                    v-if="!readonlyMode || showEditItem == index + 1"
                                >
                                    <a-select
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="record.debit_coa_code"
                                        show-search
                                        :dropdownStyle="{maxHeight: '200px'}"
                                        :dropdownMatchSelectWidth="500"
                                        :filter-option="false"
                                        :getPopupContainer="getPopupContainer"
                                        class="table-input"
                                        style="width: 200px; over-flow: hidden"
                                        @change="fieldsChanged(record.id)"
                                        :allow-clear="true"
                                    >
                                        <a-select-option
                                            v-for="item in debOrCreList"
                                            :key="item.account_code"
                                            :value="item.account_code"
                                            >{{ `${item.account_code} | ${item.name}` }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                                <span v-else>
                                    {{ coaDisplayString(record.debit_coa_code) }}
                                </span>
                            </template>
                        </a-table-column>
                        <!-- creditReceipt -->
                        <a-table-column
                            data-index="coa_code"
                            :title="i18n.t('bkCustomer.creditReceipt')"
                            align="left"
                            header-align="left"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    name="coa_code"
                                    style="margin-bottom: 0"
                                    v-if="!readonlyMode || showEditItem == index + 1"
                                >
                                    <a-select
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="record.coa_code"
                                        show-search
                                        :dropdownStyle="{maxHeight: '200px'}"
                                        :dropdownMatchSelectWidth="500"
                                        :filter-option="false"
                                        :getPopupContainer="getPopupContainer"
                                        class="table-input"
                                        style="width: 200px; over-flow: hidden"
                                        @change="fieldsChanged(record.id)"
                                        :allow-clear="true"
                                    >
                                        <a-select-option
                                            v-for="item in debOrCreList"
                                            :key="item.account_code"
                                            :value="item.account_code"
                                        >
                                            <!-- {{ `${item.account_code.substring(0, 4)} | ${item.name}` }}-->
                                            {{ `${item.account_code} | ${item.name}` }}
                                        </a-select-option>
                                    </a-select>
                                </a-form-item>
                                <span v-else>
                                    {{ coaDisplayString(record.coa_code) }}
                                </span>
                            </template>
                        </a-table-column>
                        <!-- coaJson -->
                        <a-table-column
                            data-index="coa_json"
                            :title="i18n.t('bkCustomer.coaJson')"
                            align="left"
                            header-align="left"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    style="margin-bottom: 0"
                                    :name="[index, 'coa_json']"
                                    :rules="rules['coa_json']"
                                    v-if="!readonlyMode || showEditItem == index + 1"
                                >
                                    <a-input
                                        v-model:value="record.coa_json"
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        class="table-input"
                                        style="width: 200px; over-flow: hidden"
                                        @input="coaJsonChanged($event, record)"
                                    ></a-input>
                                </a-form-item>
                                <span v-else>
                                    {{ record.coa_json }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column key="operation" title="" align="center">
                            <template #default="{record}">
                                <span>
                                    <a-button type="link" danger @click="remove(record)">
                                        <svg-icon name="icon_delete" style="color: #ff0000"></svg-icon>
                                    </a-button>
                                </span>
                            </template>
                        </a-table-column>
                    </a-table>
                    <a-button class="invoice-add" type="primary" ghost @click="addItem" :disabled="isDisable">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        {{ i18n.t('bkAp.addItemBtnTxt') }}
                    </a-button>
                    <div class="pagination-wrap">
                        <a-pagination
                            v-model:current="currentPageNumber"
                            v-model:page-size="pageSize"
                            :disabled="tableLoading"
                            :hideOnSinglePage="false"
                            :showSizeChanger="true"
                            :total="totalNumber"
                            @change="changePage"
                        />
                        <span
                            >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                            {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                        >
                    </div>
                    <div class="ap-invoice-footer">
                        <a-button type="primary" shape="round" @click="save_form" :loading="formLoading">{{
                            i18n.t('bkCustomer.save')
                        }}</a-button>
                    </div>
                    <!--                    <div>-->
                    <!--                        <a-button @click="test">test</a-button>-->
                    <!--                    </div>-->
                </div>
            </a-form>
        </a-spin>
    </div>
</template>
<style lang="scss" scoped>
.ap-invoice-block {
    padding: 30px 20px 24px;
    border-bottom: 1px solid #e2e2ea;
}

:deep(.ap-invoice-block .ant-col.ant-form-item-label) {
    height: 31.5px;
}

.difference_message {
    color: #ff0000;
}

.el-icon-question {
    margin: auto;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #2ead2b;
    margin-left: 5px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}

.ap-invoice-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .reverse-button {
        background-color: #004fc1;
        color: #fff;
    }

    .ant-btn + .ant-btn {
        margin-left: 12px;
    }
}

.inputWidth_f_o {
    width: 200px;
}

.inputWidth_f_t {
    width: 272px;
}

.pagination-wrap {
    display: flex;
    margin-top: 12px;
    justify-content: flex-end;
    align-items: center;

    span {
        font-size: 12px;
        margin-left: 8px;
        line-height: 16px;
        color: #8c8c8c;
    }
}
:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -12px;
    font-size: 12px;
    line-height: 12px;
    min-height: 12px;
}
:deep(.ant-table .ant-table-tbody .ant-table-cell) {
    padding-top: 12px;
    padding-bottom: 12px;
}
</style>
