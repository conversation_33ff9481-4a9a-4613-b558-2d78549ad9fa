<!-- @format -->

<script setup lang="ts">
import {useStore} from 'vuex'
import {computed, onBeforeMount, onMounted, reactive, ref} from 'vue'
import {message} from 'ant-design-vue'
import {useRouter} from 'vue-router'
import ImageViewer from './ImageViewer.vue'
const router = useRouter()
const store = useStore()
const props = defineProps({
    url: {
        type: String,
        required: false,
    },
})

const state = reactive({
    itemList: [],
    isLoading: false,
    pdfFullPath: '',
})

// vuex actions
// const getFileBlobById = async (id: string) => await store.dispatch('ArApBrStore/getFileBlobById', {id: id})
// vuex state
// const currentFileInfo = computed(() => store.state.ArApBrStore.pdfInfo)
// const currentFileUrl = computed(() => currentFileInfo.value.fileUrl)
const currentFileUrl = computed(() => store.state.ArApBrStore.pdfInfo.file_url)

onBeforeMount(() => {
    // pdfFullPath.value = `https://${window.location.hostname}/web/static1${currentFileUrl.value}`
    const subPath = '/web/static1'
    state.pdfFullPath = `${window.location.protocol}//${window.location.host}${subPath}${currentFileUrl.value}`
    console.log('before mounted: ', state)
})

onMounted(async () => {
    // debugger
    console.log('check pdf url', state.pdfFullPath)
    // console.log('check pdf url', window.location.hostname)
    if (!currentFileUrl.value) {
        message.error('please select a file first!')
        return router.go(-1)
    }
    // pdfFullPath.value = window.location.hostname + currentFileUrl.value
})
// may be used later for Blob PdfViewer
// if (!this.currentPdfInfo.id) return
// await this.getFileBlobById({id: this.currentPdfInfo.id})
</script>

<template>
    <div class="page-container-pdf_viewer">
        <image-viewer
            v-if="!state.pdfFullPath.includes('.pdf') && !state.pdfFullPath.includes('.PDF')"
            :url="state.pdfFullPath"
        />
        <iframe v-else :src="state.pdfFullPath + '#view=fitH'" frameborder="0" width="100%" height="100%"></iframe>
    </div>
</template>

<style scoped>
.page-container-pdf_viewer {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}
</style>
