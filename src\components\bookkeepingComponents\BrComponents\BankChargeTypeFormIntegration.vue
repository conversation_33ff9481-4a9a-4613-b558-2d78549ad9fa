<!-- @format -->

<script setup lang="ts">
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {
    UserCompany,
    UserInfo,
    LocalCurrency,
    Ap_Integration,
    SapPayableGl,
    SapPayableWbs,
    SapPayableCostCenter,
    SapPayableInternalOrder,
    SapPayableProfitCenter,
} from '@/lib/storage'
import {computed, onBeforeMount, reactive, ref, watch, getCurrentInstance, createVNode} from 'vue'
import {useStore} from 'vuex'
import {message, type FormInstance} from 'ant-design-vue'
import SupplierForm from '@/components/bookkeepingComponents/CommonComponents/SupplierForm.vue'
import dayjs from 'dayjs'
import {PlusCircleOutlined, QuestionCircleOutlined} from '@ant-design/icons-vue'
import moment from 'moment'
import * as _ from 'lodash'
import Decimal from 'decimal.js'
import InputNumber from 'primevue/inputnumber'
import SvgIcon from '@/components/SvgIcon.vue'

const {appContext} = getCurrentInstance()!
const formatNumber = appContext.config.globalProperties.$formatNumber
const parseNumber = appContext.config.globalProperties.$parseNumber
const decimalFormat = appContext.config.globalProperties.$decimalFormat
const userInfo: any = UserInfo.get() || {}
const localCurrency = LocalCurrency.get() || 'CAD'
const i18n: Composer = i18nInstance.global
const formRef = ref<FormInstance>()
const store = useStore()
const props = defineProps<{
    reconciliation: {[key: string]: any}
}>()
const emits = defineEmits(['update', 'dismiss'])
const userCompany: any = UserCompany.get() || []
const apIntegration: any = Ap_Integration.get() ?? 0

const spot = ref({
    rate: '',
    rate_date: '',
})

const selectedCOA = ref(true)
const spotRateCheck = ref(false)
const taxRateCheck = ref(false)
const isDisable = computed(() => {
    const status = !isReconcileGL.value
        ? !(countryCodeCheck.value && spotRateCheck.value && taxRateCheck.value)
        : !(selectedCOA.value && Boolean(selectedGLType.value))
    // return !(countryCodeCheck.value && spotRateCheck.value && taxRateCheck.value)
    return status
})

const selectedGLType = ref('ap')

const currencyLoading = ref(false)
const isWeekend = ref(false)
const type = ref('')
const userType = ref('')
const postingDate = ref(dayjs().format('YYYY-MM-DD'))
const isReconcileGL = ref(false)
const sapPayableGl: any = SapPayableGl.get() || {} || undefined
const sapPayableProfitCenter: any = SapPayableProfitCenter.get() || {}
const sapPayableWbs: any = SapPayableWbs.get() || {} || undefined
const sapPayableCostCenter: any = SapPayableCostCenter.get() || {}
const sapPayableInternalOrder: any = SapPayableInternalOrder.get() || {}
const autoCalTaxFlag = ref('1') // '1': change value from "Total"; '0': change value from 'item list';'2': change value from bill province selected
const autoCalculateState = ref(true)
const enableTaxExempt = ref(true)
const form_diff = computed(() =>
    Math.abs(
        Number(type.value === 'AP' ? apForm.value.total_fee_local : arForm.value.total_fee_local) -
            Number(
                (
                    Number(type.value === 'AP' ? apForm.value.total_fee || 0 : arForm.value.total_fee || 0) *
                    parseFloat(spot.value.rate || '1')
                ).toFixed(2),
            ),
    ),
)
const supplierList: any = computed(() => store.state.CommonDropDownStore.supplierOptions)
const customersList = computed(() => store.state.CommonDropDownStore.customerOptions)
const apTopCoaList: any = computed(() => store.state.ApStore.apTopCoaList)
const arTopCoaList = computed(() => store.state.ArStore.arTopCoaList)
const glTypeList = computed(() => store.state.CommonDropDownStore.glTypeOptions)
const companyBankList = computed(() => store.state.BankInfoStore.bankList)
const saveGl = (payload: any) => store.dispatch('GlStore/saveGlAction', payload)

const submitEe = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationEE', payload)

const getSpot = (query?: any) => store.dispatch('Utils/getSpotv1', query)

const VNodes = (_: any, {attrs}: any) => {
    return attrs.vnodes
}

const apForm = ref<any>({
    id: null,
    pay_method: '1',
    company_name: undefined as string | undefined,
    company_code: '',
    company_id: '',
    company_email: '',
    company_phone: '',
    issuer_name: null,
    issuer_tel: '',
    issuer_email: '',
    reference_no: '',
    tax_content: [],
    invoice_currency: localCurrency,
    invoice_create_date: '',
    invoice_due_date: '',
    posting_date: '',
    items: [] as any[],
    amount: null as number | null,
    net_amount: null as number | null,
    file_id: '',
    file_url: '',
    file_name: '',
    creator: '',
    engine_document_id: '' || [] || null,
    cash_engine_payment_no: '' || null,
    engine_reverse_document_id: '' || (null as any),
    // amountFee: null as number | null,
    //totalTaxable: null as number | null,
    total_tax: 0 as number | null,
    total_fee: 0 as number | null,
    total_fee_local: 0 as number | null,
    shipping: null,
    // discount: null,
    // balance: null,
    // deposit: null,
    invoice_comments: '',
    issuer_id: '',
    issuer_address: '',
    invoice_no: '',
    br_type: '',
    po: '',
    payment_term_discount: '',
    exchange_rate: null,
    gl_type: undefined,
    sap_gl_account: '',
    sap_wbs: '',
    sap_cost_center: '',
    sap_internal_order: '',
    sap_profit_center: '',
})
const arForm = ref<any>({
    pay_method: '1',
    company_name: '',
    company_code: '',
    company_tel: '',
    company_id: 0,
    reference_no: '',
    invoice_currency: localCurrency,
    invoice_create_date: '',
    invoice_due_date: '',
    posting_date: '',
    items: [] as any[],
    tax_content: [],
    net_amount: null,
    total_tax: 0,
    total_fee: 0,
    total_fee_local: 0,
    shipping: null,
    invoice_comments: '',
    supplierId: '',
    company_email: '',
    fileId: '',
    bill_to_company: '',
    bill_to_customer_id: undefined as string | undefined,
    bill_to_email: '',
    bill_to_receiver: '',
    bill_to_street: '',
    bill_to_city: '',
    bill_to_province: '',
    bill_to_postal_code: '',
    bill_to_tel: '',
    exchange_rate: null as string | null,
    gl_type: undefined,
})
const formLoading = ref(false)
const initLoading = ref(false)
const countryCodeCheck = ref(false)
const poNumberLoading = ref(false)
// const taxRateCheck = ref(false)
// const isDisable = computed(() => {
//     return !(countryCodeCheck.value && taxRateCheck.value)
// })
const current = reactive({
    contact_name: '',
})
const contactPageSize = ref(10)
const contactLoading = ref(false)
const contactKeyword = ref('')
const isShowSupplierForm = ref(false)
const isShowContactForm = ref(false)
const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}

const apRules = reactive({
    issuer_name: [...requireRule(i18n.t('bkAp.companyName'))],
    invoice_currency: [...requireRule(i18n.t('bkAp.currency'))],
    invoice_due_date: [...requireRule(i18n.t('bkAp.dueDate'))],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur', 'change'],
        },
    ],
    requireGLType: [...requireRule('gl_type')],
    requireGLAccount: [...requireRule(i18n.t('bkAp.accountingCategory'))],
    requiredReferenceNo: [...requireRule(i18n.t('bkAp.referenceNo'))],
    requiredReference: [...requireRule(i18n.t('bkApInvoice.referenceNo'))],
    posting_date: [...requireRule(i18n.t('bkAp.postingDate'))],
    invoice_comments: [...lengthLimitRule(1, 200)],
    pay_method: [...requireRule(i18n.t('bkAp.payMethod'))],
})
const arRules = reactive({
    companyGstNo: [...lengthLimitRule(1, 17)],
    companyPstNo: [...lengthLimitRule(1, 18)],
    invoice_currency: [...requireRule(i18n.t('bkAr.currency'))],
    invoice_create_date: [...requireRule(i18n.t('bkAr.date'))],
    invoice_due_date: [...requireRule(i18n.t('bkAr.dueDate'))],
    bill_to_company: [...requireRule(i18n.t('bkAr.billToCompany'))],
    billToEmail: [
        ...requireRule(i18n.t('bkAr.billToEmail')),
        {type: 'email', message: i18n.t('bkCommonTag.msgEmailRule'), trigger: 'blur'},
    ],
    shipToStreet: [...requireRule(i18n.t('bkAr.shipToStreet'))],
    pay_method: [...requireRule(i18n.t('bkAr.payMethod'))],
    requireGLType: [...requireRule('gl_type')],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur', 'change'],
        },
    ],
    requireGLAccount: [...requireRule(i18n.t('bkAr.accountingCategory'))],
    invoice_comments: [...lengthLimitRule(1, 200)],
    posting_date: [...requireRule(i18n.t('bkAr.postingDate'))],
})
const apAddNewContact = () => {
    current.contact_name = apForm.value.issuer_name ? apForm.value.issuer_name : ''
    showSupplierCreationModal(true)
}

const arAddNewContact = () => {
    current.contact_name = arForm.value.bill_to_company ? arForm.value.bill_to_company : ''
    showContactCreationModal(true)
}

const showSupplierCreationModal = (bool = false) => {
    isShowSupplierForm.value = bool
}

const showContactCreationModal = (bool = false) => {
    isShowContactForm.value = bool
}

const apUpdateNewContact = async (response: any) => {
    formRef.value?.clearValidate()
    await fetchSupplierDropDown({...currentUserCompanyQuery})
    // apForm.value.companyEmail = response.data.data.email || ''
    // apForm.value.companyName = response.data.data.supplierName
    // apForm.value.companyPhone = response.data.data.tel || ''
    // apForm.value.supplierId = response.data.data.supplierId
    apForm.value.company_code = userCompany[0].code
    apForm.value.company_id = userCompany[0].id

    apForm.value.issuer_email = response.data.email || ''
    apForm.value.issuer_name = response.data.contact_name || ''
    apForm.value.issuer_tel = response.data.tel || ''
    apForm.value.issuer_id = response.data.contact_id || ''

    await getTaxRates({
        action: 'Purchase',
        companyCode: companyTaxInfo.value.code,
        buyerCountryCode: companyTaxInfo.value.country,
        buyerRegionCode: companyTaxInfo.value.province,
        sellerCountryCode: companyTaxInfo.value.country,
        sellerRegionCode: companyTaxInfo.value.province,
    })

    await getApTopCoa()
}
const handleEnterEvent = (event: any) => {
    event.target.blur()
}
const selectInputValues = (e: FocusEvent) => {
    const target = e.target as HTMLInputElement // 使用类型断言告诉TypeScript不为空
    if (target) {
        target.select()
    }
}
const referenceNoValid = async () => {
    poNumberLoading.value = true
    if (apForm.value.reference_no == null) apForm.value.reference_no = ''
    const query = {
        company_code: userCompany[0].code,
        reference_no: apForm.value.reference_no.trim(),
        br_flag: {$ne: 3},
    }
    if (apForm.value.reference_no !== null && apForm.value.reference_no.length > 0 && apIntegration === 1) {
        const resReferenceValid = await store.dispatch('ApStore/checkReferenceNoRepetitionV1', query)
        if (resReferenceValid.data.data.length > 0) {
            message.error({content: i18n.t('ApComponents.referenceError')})
            poNumberLoading.value = false
            return false
        }
    }
    poNumberLoading.value = false
    return true
}
const arUpdateNewContact = async (response: any) => {
    await fetchCustomerDropDown({...currentUserCompanyQuery})
    // console.log(response)

    arForm.value.bill_to_street = response.data.billing_street || ''
    arForm.value.bill_to_city = response.data.billing_city || ''
    arForm.value.bill_to_province = response.data.billing_province || ''
    arForm.value.bill_to_postal_code = response.data.billing_postal_code || ''
    arForm.value.bill_to_country = response.data.billing_country || ''
    arForm.value.bill_to_email = response.data.email || ''
    arForm.value.bill_to_receiver = response.data.billing_receiver
    arForm.value.bill_to_tel = response.data.tel
    arForm.value.bill_to_company = response.data.contact_name
    arForm.value.bill_to_customer_id = response.data.contact_id

    arForm.value.ship_to_company = response.data.contact_name
    arForm.value.ship_to_street = response.data.shipping_street
    arForm.value.ship_to_city = response.data.shipping_city
    arForm.value.ship_to_province = response.data.shipping_province
    arForm.value.ship_to_postal_code = response.data.shipping_postal_code
    arForm.value.ship_to_country = response.data.shipping_country
    arForm.value.ship_to_receiver = response.data.shipping_receiver
    arForm.value.ship_to_email = response.data.email
    arForm.value.ship_to_tel = response.data.tel

    await getTaxRates({
        action: 'Sale',
        companyCode: companyTaxInfo.value.code,
        buyerCountryCode: response.data.office_country,
        buyerRegionCode: response.data.office_province,
        sellerCountryCode: companyTaxInfo.value.country,
        sellerRegionCode: companyTaxInfo.value.province,
    })

    await getArTopCoa()

    formRef.value?.clearValidate()
}
const updateTaxRates = (data: any) => store.commit('TaxCalculationStore/updateTaxRatesList', data)
const fetchApTopCoa = (query?: any) => store.dispatch('ApStore/fetchApTopCoaV1', query)
const fetchArTopCoa = (payload: any) => store.dispatch('ArStore/fetchArTopCoaV1', payload)
const fetchTaxRates = (query?: any) => store.dispatch('TaxCalculationStore/fetchTaxRates2', query)
const fetchAllBankList = (query?: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', query)
const fetchCustomerDropDown = (query?: any) => store.dispatch('CommonDropDownStore/fetchContactDropDown', query)
const fetchSupplierDropDown = (query?: any) => store.dispatch('CommonDropDownStore/fetchContactDropDown', query)
// const fetchTaxCodeList = (payload: any) => store.dispatch('ApStore/fetchTaxCodeDropdown', payload)

const fetchGlTypeDropDown = () => store.dispatch('CommonDropDownStore/fetchGlTypeInfo')
const fetchAccountDescDropdown = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)
const fetchCompanyTaxInfo = (query?: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
const reversePurchaseRequest = (payload?: any) => store.dispatch('ApStore/reversePurchaseRequestV1', payload)
const reverseSalesRequest = (payload?: any) => store.dispatch('ArStore/reverseSalesRequestV1', payload)
const updateApTopCoa = (data: any) => store.commit('ApStore/updateApTopCoa', data)
const updateGLType = (payload: any) => store.dispatch('BrStore/changeReconcileReportType', payload)
const submitReconcileEE = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationEEV1Integration', payload)
const accountDescList: any = computed(() => store.state.CommonDropDownStore.accountDescList)
const companyTaxInfo: any = computed(() => store.state.TaxInfoStore.companyTaxInfo)
const taxRatesList: any = computed(() => {
    return _.cloneDeep(store.state.TaxCalculationStore.taxRatesList).map((i: any) => {
        return {...i, value: new Decimal(i.value).div(new Decimal(100)).toNumber()}
    })
})
const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}
const credit_accountQuery = {bk_type: 2, company_code: userCompany[0].code, $limit: -1, del_flag: 0}
const debit_accountQuery = {bk_type: 1, company_code: userCompany[0].code, $limit: -1, del_flag: 0}
// const taxCodeList = ref<any>([])

const apHandleCompanyChange = async (flag: string, value: any) => {
    const current = supplierList.value.find((i: any) => i.id === value)
    const found = supplierList.value.filter((item: {id: any}) => item.id === value)
    // reset switch for tax exempt
    autoCalculateState.value = true
    enableTaxExempt.value = true

    apForm.value.issuer_address =
        [
            current.office_receiver || '',
            current.office_street || '',
            current.office_city || '',
            current.office_province || '',
            current.office_country || '',
            current.office_postal_code || '',
        ]
            .filter(i => i)
            .join() || ''
    if (flag === 'ship') {
        // const {shippingReceiver, shippingStreet, shippingCity, shippingProvince, shippingPostalCode} = found[0]
        // this.apForm.value.shipToReceiver = shippingReceiver
        // this.apForm.value.shipToStreet = shippingStreet
        // this.apForm.value.shipToCity = shippingCity
        // this.apForm.value.shipToProvince = shippingProvince
        // this.apForm.value.shipToPostalCode = shippingPostalCode
        // this.companyProvinceCode = found[0].shippingProvince
        apForm.value.company_code = userCompany[0].code
        apForm.value.company_id = userCompany[0].id
        apForm.value.issuer_email = found[0].email
        apForm.value.issuer_name = found[0].contact_name
        // this.apForm.value.companyAddress = found[0].officeStreet + found[0].officeCity + found[0].officeProvince + found[0].officeCountry + found[0].officePostalCode
        apForm.value.issuer_tel = found[0].tel
        apForm.value.issuer_id = found[0].contact_id

        await getTaxRates({
            action: 'Purchase',
            companyCode: companyTaxInfo.value.code,
            buyerCountryCode: companyTaxInfo.value.country,
            buyerRegionCode: companyTaxInfo.value.province,
            sellerCountryCode: found[0].office_country,
            sellerRegionCode: found[0].office_province,
        })

        await getApTopCoa()
    }
}

const arHandleCompanyChange = async (flag: any, value: any) => {
    // reset switch for tax exempt
    autoCalculateState.value = true
    enableTaxExempt.value = true

    // TODO: better to use comany id or code.
    if (!value) return
    const found = customersList.value.filter((item: any) => item.contact_id === value)
    // console.log("flag", flag)
    // console.log("customersList", this.customersList)
    console.log('found', found)
    const {
        company_id,
        office_country,
        office_province,
        contact_name,
        contact_id,
        billing_receiver,
        billing_street,
        billing_city,
        billing_province,
        billing_postal_code,
        billing_country,
        shipping_street,
        shipping_city,
        shipping_province,
        shipping_postal_code,
        shipping_receiver,
        shipping_country,
        email,
        tel,
    } = found[0]
    if (flag === 'bill') {
        arForm.value.bill_to_street = billing_street
        arForm.value.bill_to_city = billing_city
        arForm.value.bill_to_province = billing_province
        arForm.value.bill_to_postal_code = billing_postal_code
        arForm.value.bill_to_country = billing_country
        arForm.value.bill_to_email = email
        arForm.value.bill_to_receiver = billing_receiver
        arForm.value.bill_to_tel = tel
        arForm.value.bill_to_company = contact_name
        arForm.value.bill_to_customer_id = contact_id

        arForm.value.ship_to_company = contact_name
        arForm.value.ship_to_street = shipping_street
        arForm.value.ship_to_city = shipping_city
        arForm.value.ship_to_province = shipping_province
        arForm.value.ship_to_postal_code = shipping_postal_code
        arForm.value.ship_to_country = shipping_country
        arForm.value.ship_to_receiver = shipping_receiver
        arForm.value.ship_to_email = email
        arForm.value.ship_to_tel = tel

        await getTaxRates({
            action: 'Sale',
            companyCode: companyTaxInfo.value.code,
            buyerCountryCode:
                contact_name === 'DUMMY' ? office_country || companyTaxInfo.value.country : office_country,
            buyerRegionCode:
                contact_name === 'DUMMY' ? office_province || companyTaxInfo.value.province : office_province,
            sellerCountryCode: companyTaxInfo.value.country,
            sellerRegionCode: companyTaxInfo.value.province,
        })

        await getArTopCoa()
    }
}

const changeItemListRowExpenseAccount = (value: any, index: number, fieldCode: any) => {
    if (type.value === 'AP') {
        apForm.value.items[index].credit_coa_id = value
        const coaItem = accountDescList.value.find((x: any) => x.id == value)
        apForm.value.items[index].credit_coa_code = coaItem?.account_code
        apForm.value.items[index].credit_coa_name = coaItem?.name
        // apForm.value.items[index].bank_account = selected.bank_account
    } else if (type.value === 'AR') {
        arForm.value.items[index].debit_coa_id = value
        const coaItem = accountDescList.value.find((x: any) => x.id == value)
        arForm.value.items[index].debit_coa_code = coaItem?.account_code
        arForm.value.items[index].debit_coa_name = coaItem?.name
    }
    selectedCOA.value = true
}

const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const getTaxRates = async (query: any) => {
    const {buyerCountryCode, buyerRegionCode, sellerCountryCode, sellerRegionCode} = query

    if (!buyerCountryCode || !buyerRegionCode || !sellerCountryCode || !sellerRegionCode) {
        countryCodeCheck.value = false
        message.error({
            content: i18n.t('taxRates.countryNotSet'),
            duration: 8,
        })

        updateTaxRates([])
        return
    } else {
        countryCodeCheck.value = true
    }
    formLoading.value = true
    await fetchTaxRates({...query, invoiceDate: moment().valueOf(), action: 'Purchase'})
    calculateInvoiceTotal()
    formLoading.value = false
}

const getApTopCoa = async () => {
    try {
        await fetchApTopCoa({company_code: apForm.value.company_code, issuer_id: apForm.value.issuer_id})

        if (
            apForm.value.items.length === 1 &&
            apTopCoaList.value.length > 0 &&
            _.isNull(apForm.value.items[0].credit_coa_id)
        ) {
            const credit_coa_code = apTopCoaList.value[0].credit_coa_code
            const coaItem = accountDescList.value.find((x: any) => x.account_code == credit_coa_code)

            apForm.value.items[0].credit_coa_id = coaItem?.id
            apForm.value.items[0].credit_coa_code = coaItem?.account_code
            apForm.value.items[0].credit_coa_name = coaItem?.name
        }
    } catch (error) {
        console.log(error)
    }
}

const getArTopCoa = async () => {
    try {
        await fetchArTopCoa({
            company_code: arForm.value.company_code,
            bill_to_customer_id: arForm.value.bill_to_customer_id,
        })
        if (
            arForm.value.items.length === 1 &&
            arTopCoaList.value.length > 0 &&
            _.isNull(arForm.value.items[0].debit_coa_id)
        ) {
            const code = arTopCoaList.value[0].debit_coa_code
            const coaItem = accountDescList.value.find((x: any) => x.account_code == code)

            arForm.value.items[0].debit_coa_id = coaItem?.id
            arForm.value.items[0].debit_coa_code = coaItem?.account_code
            arForm.value.items[0].debit_coa_name = coaItem?.name
        }
    } catch (error) {
        console.log(error)
    }
}

const addApItem = () => {
    apForm.value.items = []

    let credit_coa_id = null
    let credit_coa_code = ''
    let credit_coa_name = ''

    if (apTopCoaList.value.length > 0) {
        const id = apTopCoaList.value[0].credit_coa_id
        const code = apTopCoaList.value[0].credit_coa_code

        const coaItem = accountDescList.value.find((x: any) => x.account_code == code)

        credit_coa_id = id
        credit_coa_code = coaItem?.account_code
        credit_coa_name = coaItem?.name
    }

    apForm.value.items.push({
        item_no: apForm.value.items.length + 1,
        model: '',
        description: 'EE',
        qty: 0,
        unit_price: 0,
        total: props.reconciliation.balance,
        type: '',
        dr_cr: 'dr',
        tax_code: '',
        credit_coa_id: credit_coa_id,
        credit_coa_code: credit_coa_code,
        credit_coa_name: credit_coa_name,
    })
}

const addArItem = () => {
    arForm.value.items = []

    let debit_coa_id = null
    let debit_coa_code = ''
    let debit_coa_name = ''

    if (arTopCoaList.value.length > 0) {
        const id = arTopCoaList.value[0].debit_coa_id
        const code = arTopCoaList.value[0].debit_coa_code
        const coaItem = accountDescList.value.find((x: any) => x.account_code == code)

        debit_coa_id = id
        debit_coa_code = coaItem?.account_code
        debit_coa_name = coaItem?.name
    }

    arForm.value.items.push({
        item_no: arForm.value.items.length + 1,
        model: '',
        description: 'EE',
        qty: 0,
        unit_price: 0,
        total: props.reconciliation.balance,
        type: '',
        dr_cr: 'cr',
        debit_coa_code: debit_coa_code,
        debit_coa_id: debit_coa_id,
        debit_coa_name: debit_coa_name,
    })
}

// const taxCodeOptions = [
//     { label: 'I0, A/P 0%', value: 'I0' },
//     { label: 'I3, A/P 5% GST, 9.975% QST', value: 'I3' },
//     { label: 'I4, A/P 13% HST', value: 'I4' },
//     { label: 'I6, A/P 5% GST', value: 'I6' },
//     { label: 'I7, A/P 5% GST 7% PST', value: 'I7' },
//     { label: 'IB, A/P 15% HST', value: 'IB' },
//     { label: '', value: '' } // 空行选项
// ]

const fetchContactList = async () => {
    try {
        contactLoading.value = true
        // state.supplierList = []
        const queryObj: any = {}
        if (contactKeyword.value) {
            queryObj['contact_name[$like]'] = `%${contactKeyword.value.trim()}%`
        }
        queryObj['$limit'] = contactPageSize.value
        queryObj['$skip'] = 0
        await fetchSupplierDropDown({...queryObj, ...currentUserCompanyQuery})
        if (contactKeyword.value) {
            console.log('contactKeyword.value', contactKeyword.value)
            if (contactKeyword.value.trim() === '') {
                state.supplierList = state.localSupplierList
            } else {
                const queryObjSap: any = {
                    company_code: companyTaxInfo.value.code,
                    keyword: contactKeyword.value,
                }
                const sapSupplierListRes = await fetchApSapContactDropDown({...queryObjSap})
                const sapSupplierList = sapSupplierListRes.map((item: any) => {
                    return {
                        ...item,
                        isSapReturned: true,
                        id: item.BP_NUMBER,
                        contact_name: item.BP_NAME,
                        contact_id: item.BP_NUMBER,
                        company_code: companyTaxInfo.value.code,
                        billing_city: item.CITY,
                        billing_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
                        billing_province: item.REGION !== '' ? item.REGION : 'QC',
                        billing_postal_code: item.POSTAL_CODE,
                        billing_street: item.STREET,
                        office_city: item.CITY,
                        office_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
                        office_province: item.REGION !== '' ? item.REGION : 'QC',
                        office_postal_code: item.POSTAL_CODE,
                        office_street: item.STREET,
                        shipping_city: item.CITY,
                        shipping_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
                        shipping_province: item.REGION !== '' ? item.REGION : 'QC',
                        shipping_postal_code: item.POSTAL_CODE,
                        shipping_street: item.STREET,
                        email: item.EMAIL,
                        tel: item.TELEPHONE,
                    }
                })
                state.supplierList = sapSupplierList
            }
        } else if (!contactKeyword.value) {
            state.supplierList = state.localSupplierList
        }
    } catch (e) {
        console.log(e)
    } finally {
        contactLoading.value = false
    }
    return void 0
}

const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}

const contactSearch = _.debounce(async (value: any) => {
    console.log('contactSearch', contactKeyword.value)
    //reset volume
    contactKeyword.value = value
    state.supplierList = []
    contactPageSize.value = 10
    await fetchContactList()
}, 500)

const contactScroll = async (e: any) => {
    const {target} = e
    // when user scoll near bottom
    if (Math.abs(target.scrollTop + target.offsetHeight - target.scrollHeight) < 2 && !contactLoading.value) {
        contactPageSize.value += 10
        await fetchContactList()
    }
}

const apInit = async () => {
    addApItem()
    // apForm.value.reference_no = `EE${moment().format('YYYY-MM-DD')}`
    apForm.value.invoice_create_date = getToday()
    apForm.value.invoice_due_date = getToday()
    apForm.value.posting_date = props.reconciliation.date
    apForm.value.net_amount = props.reconciliation.balance
    apForm.value.total_fee = props.reconciliation.balance
    apForm.value.total_fee_local = props.reconciliation.balance
    apForm.value.invoice_currency = props.reconciliation.currency

    apForm.value.reconcile_balance = props.reconciliation.balance
    try {
        await Promise.all([
            fetchAllBankList({company_code: userCompany[0].code}),
            fetchSupplierDropDown({...currentUserCompanyQuery}),
            fetchAccountDescDropdown({...credit_accountQuery}),
            fetchCompanyTaxInfo({code: userCompany[0].code}),
            fetchSapMasterData({...currentUserCompanyQuery}),
        ])
    } catch (e) {
        console.log(e)
    } finally {
        initLoading.value = false
    }
}

const arInit = async () => {
    addArItem()
    arForm.value.reference_no = `EE${moment().format('YYYY-MM-DD')}`
    arForm.value.invoice_create_date = getToday()
    arForm.value.invoice_due_date = getToday()
    arForm.value.posting_date = props.reconciliation.date
    arForm.value.net_amount = props.reconciliation.balance
    arForm.value.total_fee = props.reconciliation.balance
    arForm.value.total_fee_local = props.reconciliation.balance
    arForm.value.invoice_currency = props.reconciliation.currency

    arForm.value.reconcile_balance = props.reconciliation.balance
    try {
        await Promise.all([
            fetchAllBankList({company_code: userCompany[0].code}),
            fetchCustomerDropDown({...currentUserCompanyQuery}),
            fetchAccountDescDropdown(debit_accountQuery),
            fetchCompanyTaxInfo({code: userCompany[0].code}),
        ])
        arForm.value.company_code = companyTaxInfo.value.code
        arForm.value.company_id = companyTaxInfo.value.id
        arForm.value.company_name = companyTaxInfo.value.name
        arForm.value.company_address = `${companyTaxInfo.value.address_line1} ${
            companyTaxInfo.value.address_line2 ?? ''
        }`
        arForm.value.company_tel = companyTaxInfo.value.phone
        arForm.value.company_email = companyTaxInfo.value.email
    } catch (e) {
        console.log(e)
    } finally {
        initLoading.value = false
    }
}

const getType = (debit: number, credit: number) => {
    if (Math.abs(debit) > 0) {
        return 'AR'
    } else if (Math.abs(credit) > 0) {
        return 'AP'
    } else {
        return ''
    }
}
onBeforeMount(async () => {
    initLoading.value = true
    userType.value = userInfo.roles === '9996' ? 'jushi' : 'common'

    updateTaxRates([])
    updateApTopCoa([])
    // taxCodeList.value = await fetchTaxCodeList({company_code: userCompany[0].code})

    type.value = getType(props.reconciliation.deposit, props.reconciliation.withdrawal)

    if (type.value === 'AP') {
        await apInit()
    } else if (type.value === 'AR') {
        await arInit()
    }
})

const cancel = () => {
    // console.log(countryCodeCheck.value)
    // console.log(spotRateCheck.value)
    // console.log(taxRateCheck.value)

    emits('dismiss')
}

const confirm = async () => {
    if (type.value === 'AP' && !isReconcileGL.value && apForm.value.reference_no === '') {
        message.error({
            content: i18n.t('bkAp.referenceNo'),
            duration: 6,
        }) 
        return
    }
    if (type.value === 'AP' && isReconcileGL.value && apForm.value.reference_no === '') {
        message.error({
            content: i18n.t('bkApInvoice.referenceNo'),
            duration: 6,
        }) 
        return
    }
    const {gl_account} = props.reconciliation
    // empty gl account will lead to reconcileInvoices() method error
    if (!gl_account) {
        message.error({
            content: "Can't proceed because GL Account is empty",
            duration: 6,
        })
        return
    }

    try {
        if (!isReconcileGL.value) {
            // create purchase or sale
            if (type.value === 'AP') {
                await apSave()
            } else if (type.value === 'AR') {
                await arSave()
            }
        } else {
            // create GL
            await glSave()
        }
    } catch (err: any) {
        console.log(err)
    } finally {
        emits('dismiss')
        emits('update')
    }
}
const fetchSapMasterData = (query?: any) => store.dispatch('ApStore/fetchApSapMasterData', query)
const fetchApSapContactDropDown = (query?: any) => store.dispatch('ApStore/fetchApSapContactData', query)
const fetchSapMasterDataTop = (query: any) => store.dispatch('ApStore/fetchSapMasterTopV1', query)
const state = reactive({
    supplierList: apIntegration === 1 ? [] : computed(() => store.state.CommonDropDownStore.supplierOptions),
    localSupplierList: computed(() => store.state.CommonDropDownStore.supplierOptions),
    masterData: computed(() => store.state.ApStore.sapMasterData),
    // invoiceFlowData: computed(() => store.state.ApStore.invoiceFlow),
    // paymentFlowData: computed(() => store.state.ApStore.paymentFlow),
})
const sapMasterDataTopNew = ref({
    gl_account_code: '',
    wbs_code: '',
    cost_center_code: '',
    internal_order_code: '',
    profit_center_code: '',
})
const newIssuerName = ref('')
const isContactKeywordEmpty = computed(() => contactKeyword.value == null || contactKeyword.value.length === 0)
const handleIssuerNameKeyup = (event: any) => {
    newIssuerName.value = event.target.value.toUpperCase()
}
const handleCompanyChange = async (flag: string, value: any) => {
    const current = state.supplierList.find((i: any) => i.id === value)
    const found = state.supplierList.filter((item: {id: any}) => item.id === value)

    // get sap master data top
    sapMasterDataTopNew.value = await fetchSapMasterDataTop({
        company_code: userCompany[0].code,
        contact_id: found[0].contact_id,
    })
    apForm.value.items.forEach((item: any) => {
        item.sap_gl_account = sapMasterDataTopNew.value.gl_account_code
        item.sap_wbs = sapMasterDataTopNew.value.wbs_code
        item.sap_cost_center = sapMasterDataTopNew.value.cost_center_code
        item.sap_internal_order = sapMasterDataTopNew.value.internal_order_code
        item.sap_profit_center = sapMasterDataTopNew.value.profit_center_code
        // item.sap_wbs = sapMasterDataTopNew.value.wbs_code
        // item.sap_cost_center = sapMasterDataTopNew.value.cost_center_code
        // item.sap_internal_order = sapMasterDataTopNew.value.internal_order_code
        // item.sap_profit_center = sapMasterDataTopNew.value.profit_center_code
        selectedCOA.value = true
        selectedGLType.value = 'ap'
    })

    apForm.value.issuer_address =
        [
            current.office_receiver || '',
            current.office_street || '',
            current.office_city || '',
            current.office_province || '',
            current.office_country || '',
            current.office_postal_code || '',
        ]
            .filter(i => i)
            .join() || ''
    if (flag === 'ship') {
        // const {shippingReceiver, shippingStreet, shippingCity, shippingProvince, shippingPostalCode} = found[0]
        // this.form.value.shipToReceiver = shippingReceiver
        // this.form.value.shipToStreet = shippingStreet
        // this.form.value.shipToCity = shippingCity
        // this.form.value.shipToProvince = shippingProvince
        // this.form.value.shipToPostalCode = shippingPostalCode
        // this.companyProvinceCode = found[0].shippingProvince
        apForm.value.company_code = userCompany[0].code
        apForm.value.company_id = userCompany[0].id
        apForm.value.issuer_email = found[0].email
        apForm.value.issuer_name = found[0].contact_name
        // this.form.value.companyAddress = found[0].officeStreet + found[0].officeCity + found[0].officeProvince + found[0].officeCountry + found[0].officePostalCode
        apForm.value.issuer_tel = found[0].tel
        apForm.value.issuer_id = found[0].contact_id
        await getTaxRates({
            companyCode: companyTaxInfo.value.code,
            buyerCountryCode: companyTaxInfo.value.country,
            buyerRegionCode: companyTaxInfo.value.province,
            sellerCountryCode: companyTaxInfo.value.country,
            sellerRegionCode: companyTaxInfo.value.province,
        })

        // await getApTopCoa()

        // calculate taxs by Province
        autoCalTaxFlag.value = '2'
        apForm.value.tax_content = calculateTaxRates(apForm.value.net_amount)
        await calculateInvoiceTotal()
    }
}
const handleFocus = async () => {
    const queryObj: any = {}
    queryObj['$limit'] = contactPageSize.value
    queryObj['$skip'] = 0
    await fetchSupplierDropDown({...queryObj, ...currentUserCompanyQuery})
    state.supplierList = state.localSupplierList
}

const getGLAccount = (bank_acocunt: string) => {
    const match = companyBankList.value.find((x: any) => x.account_no === bank_acocunt)?.gl_account
    return match || ''
}

const createGL = async (payload: any) => {
    await saveGl(payload)
}

const updateReportType = async () => {
    return await updateGLType({
        id: props.reconciliation.id,
        report_type: selectedGLType,
        deleted_time: moment().format('YYYY-MM-DD'),
    })
}

const glSave = async () => {
    const {br_type} = props.reconciliation
    if (await formRef.value?.validateFields()) {
        formLoading.value = true
        // const queryForm = prepareApInvoice()
        try {
            const res = await reconcileInvoices({invoice_detail: null, br_entity_type: br_type})
            if (res.data.statusCode === 200) {
                message.success({content: i18n.t('Success')})
            } else {
                message.error({content: i18n.t('Error')})
            }
        } catch (error) {
            // await reversePurchase(invoice.id)
        } finally {
            formLoading.value = false
        }
    } else {
        message.error({
            content: i18n.t('Error'), //"Can't create invoice without [ Net Amount ]",
            duration: 6,
        })
    }
    formLoading.value = false
}

const apSave = async () => {
    if (!apForm.value.items || (apForm.value.items && apForm.value.items.length === 0)) {
        message.error({
            content: i18n.t('ApComponents.atleast'), //'Invoice must contain at least one [ Item ]',
            duration: 6,
        })
        return
    }
    if (!apForm.value.net_amount) {
        message.error({
            content: i18n.t('ApComponents.lackNetAmount'), //"Can't create invoice without [ Net Amount ]",
            duration: 6,
        })
        return
    }
    if (await formRef.value?.validateFields()) {
        formLoading.value = true
        const queryForm = prepareApInvoice()
        // const invoice = await createApInvoice()
        try {
            const res = await reconcileInvoices({invoice_detail: queryForm, br_entity_type: queryForm.br_type})
            if (res.data.statusCode === 200) {
                message.success({content: i18n.t('Success')})
            } else {
                message.error({content: i18n.t('Error')})
            }
        } catch (error) {
            // await reversePurchase(invoice.id)
        } finally {
            formLoading.value = false
        }
    } else {
        message.error({
            content: i18n.t('Error'), //"Can't create invoice without [ Net Amount ]",
            duration: 6,
        })
    }
}

const arSave = async () => {
    if (!arForm.value.items || (arForm.value.items && arForm.value.items.length === 0)) {
        message.error({
            content: 'Invoice must contain at least one [ Item ]',
            duration: 6,
        })
        return
    }
    if (!arForm.value.net_amount) {
        message.error({
            content: "Can't create invoice without [ Invoice Amount ]",
            duration: 6,
        })
        return
    }
    // if (!arForm.value.bill_to_email) {
    //     message.error({
    //         content: "Can't create invoice without [ Billing Email ]",
    //         duration: 6,
    //     })
    //     return
    // }

    if (await formRef.value?.validateFields()) {
        formLoading.value = true
        const invoice = await createArInvoice()
        try {
            await reconcileInvoices({...invoice, br_entity_type: invoice.br_type})
        } catch (error) {
            await reverseSales(invoice.id)
        } finally {
            formLoading.value = false
        }
    }
}

const prepareApInvoice = () => {
    try {
        const {
            engine_document_id = '',
            engine_reverse_document_id = '',
            NotExisted = false,
            ...queryForm
        } = {..._.cloneDeep(apForm.value)}
        queryForm.items = queryForm.items.filter((i: any) => i.sap_gl_account !== null)
        queryForm.items.forEach((x: any) => {
            if (+x.total < 0) {
                x.dr_cr = 'cr'
            } else {
                if (!x.dr_cr) {
                    x.dr_cr = 'dr'
                }
            }
        })
        queryForm.creator = userInfo?.id
        queryForm.creator_name = userInfo?.account
        queryForm.company_id = userCompany[0].id
        queryForm.company_code = userCompany[0].code
        queryForm.file_id = null
        queryForm.file_name = ''
        queryForm.file_url = ''
        queryForm.company_name = queryForm.company_name?.toUpperCase()
        if (
            apForm.value.total_fee != undefined &&
            apForm.value.total_tax != undefined &&
            apForm.value.total_fee.toFixed(2) !==
                (Number(apForm.value.net_amount) + Number(apForm.value.total_tax)).toFixed(2)
        ) {
            message.error({
                content: i18n.t('ApComponents.notEqual'), //'Net amount plus tax does not equal total',
                duration: 8,
            })
            return
        }
        const {statement_type} = props.reconciliation
        if (statement_type === '1') queryForm.pay_method = '3' // cheque  -> check
        if (statement_type === '2') queryForm.pay_method = '1' // saving  -> bank
        if (statement_type === '3') queryForm.pay_method = '2' // credit  -> credit

        if (queryForm.pay_method === '2') {
            //AR/AP 创建cash paid发票的功能已经好了，创建发票的时候会自动完成对账。需要注意的是，入参中pay_method = ‘2’ (cash paid)，br_type = ‘9’ (cash pay invoice)
            queryForm.br_type = '9'
        } else {
            queryForm.br_type = queryForm.net_amount && queryForm.net_amount < 0 ? '2' : '1'
        }

        // some patch
        if (queryForm.exchange_rate === '') queryForm.exchange_rate = null
        return queryForm
    } catch (error) {
        console.log(error)
    }
}

const createApInvoice = async () => {
    try {
        const {
            engine_document_id = '',
            engine_reverse_document_id = '',
            NotExisted = false,
            ...queryForm
        } = {..._.cloneDeep(apForm.value)}
        queryForm.items = queryForm.items.filter((i: any) => i.credit_coa_id !== null)
        queryForm.items.forEach((x: any) => {
            if (+x.total < 0) {
                x.dr_cr = 'cr'
            } else {
                if (!x.dr_cr) {
                    x.dr_cr = 'dr'
                }
            }
        })
        queryForm.creator = userInfo?.id
        queryForm.creator_name = userInfo?.account
        queryForm.company_id = userCompany[0].id
        queryForm.company_code = userCompany[0].code
        queryForm.file_id = null
        queryForm.file_name = ''
        queryForm.file_url = ''
        queryForm.company_name = queryForm.company_name?.toUpperCase()
        if (
            apForm.value.total_fee != undefined &&
            apForm.value.total_tax != undefined &&
            apForm.value.total_fee.toFixed(2) !==
                (Number(apForm.value.net_amount) + Number(apForm.value.total_tax)).toFixed(2)
        ) {
            message.error({
                content: i18n.t('ApComponents.notEqual'), //'Net amount plus tax does not equal total',
                duration: 8,
            })
            return
        }

        if (queryForm.pay_method === '2') {
            //AR/AP 创建cash paid发票的功能已经好了，创建发票的时候会自动完成对账。需要注意的是，入参中pay_method = ‘2’ (cash paid)，br_type = ‘9’ (cash pay invoice)
            queryForm.br_type = '9'
        } else {
            queryForm.br_type = queryForm.net_amount && queryForm.net_amount < 0 ? '2' : '1'
        }

        // some patch
        if (queryForm.exchange_rate === '') queryForm.exchange_rate = null

        const response = await store.dispatch('ApStore/createInvoiceV1', queryForm)

        if (response.data.statusCode === 200) {
            // message.success('success')

            return response.data.data
        }
    } catch (error) {
        console.log(error)
    }
}

const createArInvoice = async () => {
    try {
        companyBankList.value.forEach((item: any) => {
            if (item.id == arForm.value.bank_id) {
                arForm.value.bank_account = item.account_no
                arForm.value.bank_name = item.name
            }
        })

        const queryForm = _.cloneDeep(arForm.value)

        // when line item total is negative, assign 'dr_cr' to opposite
        // for example ar's dr_cr default is 'cr'
        // when line item amount is negative
        // dr_cr change to 'dr'
        queryForm.items = queryForm.items.filter((i: any) => i.debit_coa_id !== null)
        queryForm.items.forEach((x: any) => {
            if (+x.total < 0) {
                x.dr_cr = 'dr'
            } else {
                if (!x.dr_cr) {
                    x.dr_cr = 'cr'
                }
            }
        })
        queryForm.file_id = null
        queryForm.creator = userInfo?.id
        queryForm.creator_name = userInfo?.account
        queryForm.br_type = queryForm.net_amount && queryForm.net_amount < 0 ? '3' : '0'
        if (queryForm.exchange_rate === '') queryForm.exchange_rate = null

        const response = await store.dispatch('ArStore/createInvoicev1', queryForm)

        if (response.data.statusCode === 200) {
            // message.success('success')

            return response.data.data
        }
    } catch (error) {
        console.log(error)
    }
}

const reconcileInvoices = async (inovice: any) => {
    try {
        const {
            br_type,
            br_entity_type,
            company_code,
            id,
            currency,
            bank_account,
            balance,
            date,
            gl_account,
            reason_code,
        } = props.reconciliation
        if (!gl_account) {
            message.error({
                content: "Can't reconcile invoice because GL Account is empty",
                duration: 6,
            })
            return
        }

        if (isReconcileGL.value) {
            return submitReconcileEE({
                company_code: company_code || userCompany[0].code,
                statement_id: id,
                posting_date: apForm.value.posting_date ?? date,
                currency,
                bank_account,
                balance,
                gl_account,
                gl_reason_code: reason_code,
                br_type,
                br_entity_type,
                creator: userInfo?.id,
                invoice_detail: null,
                sap_gl_account: apForm.value.sap_gl_account,
                sap_wbs: apForm.value.sap_wbs,
                sap_cost_center: apForm.value.sap_cost_center,
                sap_internal_order: apForm.value.sap_internal_order,
                sap_profit_center: apForm.value.sap_profit_center,
                reference: apForm.value.reference_no,
                comments: apForm.value.invoice_comments,
            })
        } else {
            return submitReconcileEE({
                company_code: company_code || userCompany[0].code,
                statement_id: id,
                posting_date: apForm.value.posting_date ?? date,
                currency,
                bank_account,
                balance,
                gl_account,
                gl_reason_code: reason_code,
                br_type,
                br_entity_type,
                creator: userInfo?.id,
                invoice_detail: inovice.invoice_detail,
            })
        }

        // const payload = {
        //     company_code: company_code || userCompany[0].code,
        //     statement_id: id,
        //     posting_date: date,
        //     currency,
        //     bank_account,
        //     balance,
        //     gl_account,
        //     br_type,
        //     br_entity_type,
        //     creator: userInfo?.id,
        //     invoice_detail: inovice.invoice_detail,
        // } as any

        // return submitReconcileEE(payload)
    } catch (e) {
        console.log(e)
    }
}

const reversePurchase = async (id: string) => {
    try {
        const response = await reversePurchaseRequest({
            id: id,
            creator: userInfo?.id,
            creator_name: userInfo?.account,
        })
        // if (response.status === 200) {
        //     message.success(i18n.t('ApComponents.success'))
        // }
    } catch (e: any) {
        console.log(e)
        // message.error(e.response.data.message)
    }
}

const reverseSales = async (id: string) => {
    try {
        const response = await reverseSalesRequest({
            id: id,
            creator: userInfo?.id,
            creator_name: userInfo?.account,
        })
        // if (response.status === 200) {
        //     message.success('success')
        // }
    } catch (e: any) {
        console.log(e)
        // message.error(e.response.data.message)
    }
}

const getSpotInputDateStatus = (date: moment.MomentInput) => {
    const weekOfday = moment(date, 'YYYY-MM-DD').format('E')
    return 5 - +weekOfday
}

const updateSpot = async () => {
    const form = type.value === 'AP' ? apForm : arForm

    if (userType.value === 'jushi') return
    if (form.value.invoice_currency?.toString() === localCurrency) {
        form.value.total_fee_local = form.value.total_fee
        spotRateCheck.value = true
        return
    }

    currencyLoading.value = true
    const baseCurrency = form.value.invoice_currency
    const quoteCurrency = localCurrency

    const weekOfDayDiff = getSpotInputDateStatus(form.value.posting_date)
    isWeekend.value = weekOfDayDiff < 0
    if (!isWeekend.value) {
        postingDate.value = form.value.posting_date
    } else {
        postingDate.value = moment(form.value.posting_date, 'YYYY-MM-DD')
            .add(weekOfDayDiff, 'days')
            .format('YYYY-MM-DD')
    }
    try {
        spot.value = await getSpot({baseCurrency, quoteCurrency, date: postingDate.value})
        form.value.exchange_rate = spot.value.rate
        spotRateCheck.value = spot.value.rate !== ''
    } finally {
        currencyLoading.value = false
    }

    // comments for test
    // postingDate.value = form.value.posting_date
    // isWeekend.value = getSpotInputDateStatus(form.value.posting_date)
    //
    // form.value.total_fee_local = form.value.total_fee != null ? form.value.total_fee * parseFloat(spot.value.rate) : null

    form.value.total_fee_local =
        form.value.total_fee != undefined
            ? Number((form.value.total_fee * parseFloat(spot.value.rate ?? '')).toFixed(2))
            : null
}

const calculateInvoiceTotal = () => {
    const form = type.value === 'AP' ? apForm : arForm

    if (!autoCalculateState.value) {
        const totalFee = form.value.total_fee ? form.value.total_fee : 0.0
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(totalFee).sub(new Decimal(form.value.total_tax)).toDP(2).toNumber()
        form.value.items[0].total = form.value.net_amount
    } else {
        const shipFee = form.value.shipping || 0.0
        // const disCountFee = this.form.value.discount || 0.0
        const amountFee = form.value.net_amount ? form.value.net_amount : 0.0
        const totalTaxableFee = amountFee + shipFee
        // const totalTaxableFee = amountFee + shipFee - disCountFee > 0 ? amountFee + shipFee - disCountFee : 0.0
        const totalTaxFee = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        // const totalFee = new Decimal(totalTaxableFee).add(new Decimal(totalTaxFee)).toDP(2).toNumber()
        const calculated_net_amount = new Decimal(form.value.total_fee)
            .minus(new Decimal(totalTaxFee))
            .toDP(2)
            .toNumber()
        // form.value.amountFee = amountFee
        // form.value.totalTaxable = totalTaxableFee

        form.value.total_tax = totalTaxFee
        form.value.net_amount = calculated_net_amount
        updateSpot()
        // form.value.total_fee_local =
        //     form.value.invoice_currency == '2'
        //         ? form.value.total_fee
        //         : form.value.total_fee * parseFloat(spot.value.rate)
    }
}

const calculateTaxRates = (amount: number | null = null) => {
    if (enableTaxExempt.value) {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: 0}
        })
    }
    // change from item List ( row net amount)
    if (autoCalTaxFlag.value === '0' || autoCalTaxFlag.value === '2') {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: amount ? new Decimal(amount).mul(item.value).toDP(2).toNumber() : 0}
        })
    }

    // change from TOTAL
    const taxTotal = _.reduce(
        taxRatesList.value,
        (sum, item) => {
            return new Decimal(sum).add(new Decimal(item.value || 0)).toNumber()
        },
        0,
    )
    const netTotal = amount ? new Decimal(amount).div(new Decimal(1 + taxTotal)).toNumber() : 0
    return _.map(taxRatesList.value, (item: any) => {
        return {...item, value: netTotal ? new Decimal(netTotal).mul(item.value).toDP(2).toNumber() : 0}
    })
}
const changeAutoCalculateMode = (state: any) => {
    const form = type.value === 'AP' ? apForm : arForm

    if (state) {
        const sum = form.value.items.reduce((prev: any, curr: {total: any}) => {
            return prev + curr.total
        }, 0)

        form.value.net_amount = new Decimal(sum).toDP(2).toNumber()
        form.value.tax_content = calculateTaxRates(sum)
        calculateInvoiceTotal()
    }
}

const revertTaxCal = (total_amount: number | null = null) => {
    const form = type.value === 'AP' ? apForm : arForm

    if (total_amount) {
        // change from TOTAL
        const taxTotal = _.reduce(
            taxRatesList.value,
            (sum, item) => {
                return new Decimal(sum).add(new Decimal(item.value || 0)).toNumber()
            },
            0,
        )
        _.forEach(taxRatesList.value, (item, index) => {
            form.value.tax_content[index].value = new Decimal(total_amount)
                .div(new Decimal(1 + taxTotal))
                .mul(new Decimal(item.value))
                .toDP(2)
                .toNumber()
        })
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(total_amount).sub(new Decimal(form.value.total_tax)).toDP(2).toNumber()
    } else {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_fee = 0
        form.value.total_tax = 0
    }
}

const changeTaxExempt = () => {
    const form = type.value === 'AP' ? apForm : arForm
    form.value.net_amount = form.value.reconcile_balance
    if (enableTaxExempt.value) {
        form.value.total_fee = form.value.net_amount ?? 0
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0
    } else {
        revertTaxCal(form.value.total_fee)
    }

    updateSpot()
}

const handleTaxChange = (current: any, item: any) => {
    console.log(current, item)
    if (current === null) {
        item.value = 0
    } else {
        item.value = current
    }

    calculateInvoiceTotal()
}

const handleShippingChange = (value: any) => {
    const form = type.value === 'AP' ? apForm : arForm

    // if (value === null) this.form.value.shipping = 0.0
    if (form.value.net_amount) {
        form.value.shipping = value
        calculateInvoiceTotal()
    }
}

const updateSpotReverse = async () => {
    const form = type.value === 'AP' ? apForm : arForm

    if (form.value.invoice_currency?.toString() === localCurrency) {
        form.value.total_fee = form.value.total_fee_local
        return
    }

    form.value.total_fee =
        form.value.total_fee_local != undefined
            ? Number((form.value.total_fee_local / parseFloat(spot.value.rate ?? '')).toFixed(2))
            : null
}

const enableReconcileGL = async () => {
    formLoading.value = true
    if (arForm.value.items && arForm.value.items[0]) arForm.value.items[0].debit_coa_id = null
    if (apForm.value.items && apForm.value.items[0]) apForm.value.items[0].credit_coa_id = null
    await fetchGlTypeDropDown()
    formLoading.value = false
}

const changeGLTypeDropdown = () => {
    selectedGLType.value = type.value === 'AP' ? apForm.value.gl_type : arForm.value.gl_type
}

const changeTotalAmount = (value: any, reverse: boolean) => {
    const form = type.value === 'AP' ? apForm : arForm

    autoCalTaxFlag.value = '1'
    if (value && form.value.items.length === 1) {
        if (form.value.invoice_currency !== localCurrency && reverse) {
            updateSpotReverse()
            value = form.value.total_fee ?? value
        }

        form.value.tax_content = calculateTaxRates(value)
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(form.value.total_fee || 0)
            .sub(new Decimal(form.value.total_tax))
            .toDP(2)
            .toNumber()
        form.value.items[0].total = form.value.net_amount
        form.value.items[0].qty = 0
        form.value.items[0].unit_price = 0
    } else if (!value && form.value.items.length === 1) {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0

        form.value.items[0].total = 0
        form.value.items[0].qty = 0
        form.value.items[0].unit_price = 0
    }

    if (form.value.invoice_currency !== localCurrency && !reverse) {
        updateSpot()
    }
}

watch(
    () => taxRatesList.value,
    list => {
        apForm.value.tax_content = []
        arForm.value.tax_content = []
        if (list.length) {
            list.forEach((i: any) => {
                apForm.value.tax_content = [...apForm.value.tax_content, {...i, value: 0}]
                arForm.value.tax_content = [...arForm.value.tax_content, {...i, value: 0}]
            })
            taxRateCheck.value = true
        } else {
            taxRateCheck.value = false
        }
    },
)

watch(
    () => arForm.value.net_amount,
    (count, prevCount) => {
        if (arForm.value.net_amount && arForm.value.items.length > 0) {
            arForm.value.items[0].total = arForm.value.net_amount
        }
    },
)

watch(
    () => apForm.value.net_amount,
    (count, prevCount) => {
        if (apForm.value.net_amount && apForm.value.items.length > 0) {
            apForm.value.items[0].total = apForm.value.net_amount
        }
    },
)
watch(
    () => apForm.value.reference_no,
    (count, prevCount) => {
        apForm.value.reference_no = count.toUpperCase()
    },
)
</script>

<template>
    <a-spin
        :tip="apIntegration === 1 ? i18n.t('commonTag.sapTip') : ''"
        :spinning="initLoading"
        wrapperClassName="custom-spin"
    >
        <a-form
            ref="formRef"
            :layout="'vertical'"
            :model="type === 'AP' ? apForm : arForm"
            :rules="type === 'AP' ? apRules : arRules"
        >
            <div class="title">
                <span v-if="!isReconcileGL">{{ i18n.t('EstatementTable.integrationEEbrTitle') }}</span>
                <span v-if="isReconcileGL">{{ i18n.t('EstatementTable.integrationEEbrTitle') }}</span>
                <p />
                <a-switch
                    class="gl-switch-wrap"
                    v-model:checked="isReconcileGL"
                    size="small"
                    @change="enableReconcileGL"
                />{{ i18n.t('esMain.reconcileGL') }}
            </div>

            <a-form-item 
                v-if="type === 'AP' && !isReconcileGL"
                class="form-item"
                :label="i18n.t('esMain.postingDate')"
                name="posting_date"
            >
                <div class="table-input" style="width: 100%">
                    <a-date-picker v-model:value="apForm.posting_date" :allowClear="false" :inputReadOnly="true"
                        format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" class="table-input" style="width: 100%">
                        <template #suffixIcon>
                            <svg-icon name="icon_date"></svg-icon>
                        </template>
                    </a-date-picker>
                </div>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL"
                class="form-item"
                name="issuer_name"
                :label="i18n.t('bkAp.companyName')"
            >
                <a-config-provider>
                    <template v-if="(isContactKeywordEmpty && !contactLoading) || contactLoading" #renderEmpty>
                        <div v-if="isContactKeywordEmpty && !contactLoading" style="text-align: center">
                            <smile-outlined style="font-size: 20px" />
                            <p>{{ i18n.t('bkApInvoice.issueNameEmpty') }}</p>
                        </div>
                        <div v-if="contactLoading" style="text-align: center">
                            <a-spin
                                :tip="i18n.t('commonTag.sapTip')"
                                :spinning="contactLoading"
                                size="small"
                                wrapperClassName="custom-spin"
                            />
                        </div>
                    </template>
                    <a-select
                        :placeholder="i18n.t('commonTag.msgSelect')"
                        v-model:value="apForm.issuer_name"
                        show-search
                        :filter-option="false"
                        @keyup="handleIssuerNameKeyup($event)"
                        @change="handleCompanyChange('ship', $event)"
                        @search="contactSearch"
                        @popupScroll="contactScroll"
                        @focus="handleFocus"
                    >
                        <a-select-option v-for="item in state.supplierList" :key="item.id" :value="item.id">{{
                            item.contact_name
                        }}</a-select-option>
                    </a-select>
                </a-config-provider>
                <!-- <a-select
                        :placeholder="i18n.t('commonTag.msgSelect')"
                        v-model:value="apForm.issuer_name"
                        show-search
                        :filter-option="false"
                        @change="apHandleCompanyChange('ship', $event)"
                        @search="contactSearch"
                        :dropdownMatchSelectWidth="600"
                        @popupScroll="contactScroll"
                        :loading="contactLoading"
                    >
                        <a-select-option v-for="item in supplierList" :key="item.id" :value="item.id">{{
                            item.contact_name
                        }}</a-select-option>
                        <template #dropdownRender="{menuNode: menu}">
                            <div style="padding: 4px 12px; cursor: pointer; color: #004fc1" @click="apAddNewContact">
                                <plus-circle-outlined />
                                {{ i18n.t('ApComponents.NewBP') }}
                                <a-divider style="margin: 4px 0" />
                            </div>
                            <v-nodes :vnodes="menu" />
                        </template>
                    </a-select> -->
            </a-form-item>
            <a-form-item v-if="type === 'AR' && !isReconcileGL" class="form-item" name="bill_to_company">
                <template v-slot:label>
                    {{ i18n.t('bkAp.billTo') }}
                    <a-tooltip placement="top">
                        <template #title>
                            If there is no existing item corresponding
                            <br />to the entered information, <br />a new item is created
                        </template>
                        <!-- <question-circle-outlined class="el-icon-question" /> -->
                    </a-tooltip>
                </template>
                <a-select
                    :placeholder="i18n.t('workTimeManager.msgInput')"
                    allow-create
                    show-search
                    default-active-first-option
                    :filter-option="false"
                    option-label-prop="label"
                    v-model:value="arForm.bill_to_customer_id"
                    @change="arHandleCompanyChange('bill', $event)"
                    style="width: 100%"
                    allowClear
                    @search="contactSearch"
                    @popupScroll="contactScroll"
                    :dropdownMatchSelectWidth="600"
                    :loading="contactLoading"
                    popper-class="company-name-class"
                    :popper-append-to-body="false"
                    :options="customersList.map((item: any) => ({ key: item.contact_id, value: item.contact_id, label: item.contact_name }))"
                >
                    <template #dropdownRender="{menuNode: menu}">
                        <div style="padding: 4px 8px; cursor: pointer; color: #004fc1" @click="arAddNewContact">
                            <plus-circle-outlined />
                            Add New Business Partner
                            <a-divider style="margin: 4px 0" />
                        </div>
                        <v-nodes :vnodes="menu" />
                    </template>
                </a-select>
            </a-form-item>
            <!-- <a-form-item
                    label="Type"
                    v-if="isReconcileGL && type === 'AP'"
                    class="form-item"
                    name="gl_type"
                    :rules="apRules['requireGLType']"
                >
                    <a-select
                        :placeholder="i18n.t('commonTag.msgSelect')"
                        show-search
                        :dropdownMatchSelectWidth="600"
                        :filter-option="filterOption"
                        class="table-input"
                        v-model:value="apForm.gl_type"
                        @change="changeGLTypeDropdown()"
                        :options="glTypeList.map((item: any) => ({ key: item.id, value: item.name, label: item.name +' - '+ item.mapping }))"
                    >
                    </a-select>
                </a-form-item> -->
            <!-- <a-form-item
                    label="Type"
                    v-if="isReconcileGL && type === 'AR'"
                    class="form-item"
                    name="gl_type"
                    :rules="apRules['requireGLType']"
                >
                    <a-select
                        :placeholder="i18n.t('commonTag.msgSelect')"
                        show-search
                        :dropdownMatchSelectWidth="600"
                        :filter-option="filterOption"
                        class="table-input"
                        v-model:value="arForm.gl_type"
                        @change="changeGLTypeDropdown()"
                        :options="glTypeList.map((item: any) => ({ key: item.id, value: item.name,label: item.name +' - '+ item.mapping }))"
                    >
                    </a-select>
                </a-form-item> -->
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL && sapPayableGl === 1"
                class="form-item"
                :label="i18n.t('bkAp.accountingCategory')"
                :name="['items', 0, 'sap_gl_account']"
                :rules="apRules['requireGLAccount']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.items[0].sap_gl_account"
                    show-search
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_GL_ACCOUNT"
                        :key="item.GL_ACCOUNT + ' | ' + item.DESCRIPTION"
                        :value="item.GL_ACCOUNT"
                        >{{ item.GL_ACCOUNT }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL && sapPayableWbs === 1"
                class="form-item"
                :label="i18n.t('ApComponents.wbs')"
                :name="['items', 0, 'sap_wbs']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.items[0].sap_wbs"
                    show-search
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_WBS"
                        :key="item.WBS + ' | ' + item.DESCRIPTION"
                        :value="item.WBS"
                        >{{ item.WBS }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL && sapPayableCostCenter === 1"
                class="form-item"
                :label="i18n.t('ApComponents.costCenter')"
                :name="['items', 0, 'sap_cost_center']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.items[0].sap_cost_center"
                    show-search
                    allow-clear
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_COST_CENTER"
                        :key="item.COST_CENTER + ' | ' + item.DESCRIPTION"
                        :value="item.COST_CENTER"
                        >{{ item.COST_CENTER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL && sapPayableInternalOrder === 1"
                class="form-item"
                :label="i18n.t('ApComponents.internalOrder')"
                :name="['items', 0, 'sap_internal_order']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.items[0].sap_internal_order"
                    show-search
                    allow-clear
                    :dropdownMatchSelectWidth="200"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_INTERNAL_ORDER"
                        :key="item.INTERNAL_ORDER + ' | ' + item.DESCRIPTION"
                        :value="item.INTERNAL_ORDER"
                    >
                        {{ item.INTERNAL_ORDER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
                <!-- <a-input
                    v-model:value="apForm.items[0].sap_internal_order"
                    :placeholder="i18n.t('commonTag.msgInput')"
                    class="table-input"
                ></a-input> -->
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL && sapPayableProfitCenter === 1"
                class="form-item"
                :label="i18n.t('ApComponents.profitCenter')"
                :name="['items', 0, 'sap_profit_center']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.items[0].sap_profit_center"
                    show-search
                    allow-clear
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_PROFIT_CENTER"
                        :key="item.PROFIT_CENTER + ' | ' + item.DESCRIPTION"
                        :value="item.PROFIT_CENTER"
                        >{{ item.PROFIT_CENTER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL"
                class="form-item"
                :label="i18n.t('bkAp.taxCode')"
                :name="['items', 0, 'tax_code']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.items[0].tax_code"
                    show-search
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_TAX_CODE"
                        :key="item.TAXCODE + ', ' + item.DESCRIPTION"
                        :value="item.TAXCODE"
                        >{{ item.TAXCODE }}, {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL"
                class="form-item"
                :label="i18n.t('bkAp.referenceNo')"
                name="reference_no"
                :rules="apRules['requiredReferenceNo']"
            >
                <a-spin :spinning="poNumberLoading" wrapperClassName="input-spin">
                    <a-input
                        v-model:value="apForm.reference_no"
                        @blur="referenceNoValid()"
                        @keypress.enter="handleEnterEvent"
                        ref="referenceNo"
                        @focus="selectInputValues"
                        :placeholder="i18n.t('commonTag.msgInput')"
                        class="table-input"
                    ></a-input>
                </a-spin>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && !isReconcileGL"
                class="form-item"
                :label="i18n.t('bkAp.invoiceComment')"
                name="invoiceComments"
            >
                <a-textarea
                    class="textarea-wrap"
                    v-model:value="apForm.invoice_comments"
                    :placeholder="i18n.t('commonTag.msgInput')"
                />
            </a-form-item>
            <a-form-item 
                v-if="type === 'AP' && isReconcileGL"
                class="form-item"
                :label="i18n.t('esMain.postingDate')"
                name="posting_date"
            >
                <div class="table-input" style="width: 100%">
                    <a-date-picker v-model:value="apForm.posting_date" :allowClear="false" :inputReadOnly="true"
                        format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" class="table-input" style="width: 100%">
                        <template #suffixIcon>
                            <svg-icon name="icon_date"></svg-icon>
                        </template>
                    </a-date-picker>
                </div>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && isReconcileGL"
                class="form-item"
                :label="i18n.t('bkAp.accountingCategory')"
                name="sap_gl_account"
                :rules="apRules['requireGLAccount']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.sap_gl_account"
                    show-search
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_GL_ACCOUNT"
                        :key="item.GL_ACCOUNT + ' | ' + item.DESCRIPTION"
                        :value="item.GL_ACCOUNT"
                        >{{ item.GL_ACCOUNT }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && isReconcileGL && sapPayableWbs === 1"
                class="form-item"
                :label="i18n.t('ApComponents.wbs')"
                name="sap_wbs"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.sap_wbs"
                    show-search
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_WBS"
                        :key="item.WBS + ' | ' + item.DESCRIPTION"
                        :value="item.WBS"
                        >{{ item.WBS }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && isReconcileGL && sapPayableCostCenter === 1"
                class="form-item"
                :label="i18n.t('ApComponents.costCenter')"
                name="sap_cost_center"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.sap_cost_center"
                    show-search
                    allow-clear
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_COST_CENTER"
                        :key="item.COST_CENTER + ' | ' + item.DESCRIPTION"
                        :value="item.COST_CENTER"
                        >{{ item.COST_CENTER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && isReconcileGL && sapPayableInternalOrder === 1"
                class="form-item"
                :label="i18n.t('ApComponents.internalOrder')"
                name="sap_internal_order"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.sap_internal_order"
                    show-search
                    allow-clear
                    :dropdownMatchSelectWidth="200"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_INTERNAL_ORDER"
                        :key="item.INTERNAL_ORDER + ' | ' + item.DESCRIPTION"
                        :value="item.INTERNAL_ORDER"
                    >
                        {{ item.INTERNAL_ORDER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
                <!-- <a-input
                    v-model:value="apForm.sap_internal_order"
                    :placeholder="i18n.t('commonTag.msgInput')"
                    class="table-input"
                ></a-input> -->
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && isReconcileGL && sapPayableProfitCenter === 1"
                class="form-item"
                :label="i18n.t('ApComponents.profitCenter')"
                name="sap_profit_center"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="apForm.sap_profit_center"
                    show-search
                    :dropdownMatchSelectWidth="400"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_PROFIT_CENTER"
                        :key="item.PROFIT_CENTER + ' | ' + item.DESCRIPTION"
                        :value="item.PROFIT_CENTER"
                        >{{ item.PROFIT_CENTER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && isReconcileGL"
                class="form-item"
                :label="i18n.t('bkApInvoice.referenceNo')"
                name="reference_no"
                :rules="apRules['requiredReference']"
            >
                <a-spin :spinning="poNumberLoading" wrapperClassName="input-spin">
                    <a-input
                        v-model:value="apForm.reference_no"
                        @blur="referenceNoValid()"
                        @keypress.enter="handleEnterEvent"
                        ref="referenceNo"
                        @focus="selectInputValues"
                        :placeholder="i18n.t('commonTag.msgInput')"
                        class="table-input"
                    ></a-input>
                </a-spin>
            </a-form-item>
            <a-form-item
                v-if="type === 'AP' && isReconcileGL"
                class="form-item"
                :label="i18n.t('bkAp.invoiceComment')"
                name="invoiceComments"
            >
                <a-textarea
                    class="textarea-wrap"
                    v-model:value="apForm.invoice_comments"
                    :placeholder="i18n.t('commonTag.msgInput')"
                />
            </a-form-item>
            <a-form-item
                v-if="type === 'AR'"
                class="form-item"
                :label="i18n.t('bkAr.accountingCategory')"
                :name="['items', 0, 'debit_coa_id']"
                :rules="arRules['requireGLAccount']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="arForm.items[0].debit_coa_id"
                    show-search
                    :dropdownMatchSelectWidth="600"
                    :filter-option="filterOption"
                    class="table-input"
                    @change="changeItemListRowExpenseAccount(arForm.items[0].debit_coa_id, 0, arForm.items[0])"
                >
                    <a-select-option
                        v-for="item in accountDescList"
                        :key="item.account_code + ' | ' + item.name"
                        :value="item.id"
                        >{{ item.account_code.substring(0, 4) + ' | ' + item.name }}</a-select-option
                    >
                </a-select>
            </a-form-item>
            <a-divider class="footer-divider"></a-divider>

            <div class="ap-invoice-amount-block" v-if="!isReconcileGL">
                <!-- <div class="ap-invoice-amount-block-left">
                                <date-delay v-if="!readonlyMode" @update="delayUpdate"></date-delay>
                            </div> -->
                <div class="ap-invoice-amount-block-right">
                    <div class="amount-block">
                        <div v-show="false" v-if="userType !== 'jushi'" class="title">
                            <a-switch
                                class="switch-wrap"
                                v-model:checked="autoCalculateState"
                                size="small"
                                @change="changeAutoCalculateMode"
                            />{{ i18n.t('ApComponents.auto') }}
                        </div>
                        <div v-if="userType !== 'jushi'" class="title">
                            <a-switch
                                class="switch-wrap"
                                size="small"
                                v-model:checked="enableTaxExempt"
                                @change="changeTaxExempt"
                            />{{ i18n.t('ApComponents.exempt') }}
                        </div>
                        <div v-if="userType === 'jushi'" class="amount-item-wrap">
                            <div class="amount-item">
                                <div class="amount-lable bold">{{ i18n.t('ApComponents.amount') }}</div>
                                <a-input-number
                                    v-if="type === 'AP'"
                                    v-model:value="apForm.total_fee"
                                    :disabled="true"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                >
                                </a-input-number>
                                <a-input-number
                                    v-if="type === 'AR'"
                                    v-model:value="arForm.total_fee"
                                    :disabled="true"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                >
                                </a-input-number>
                            </div>
                        </div>
                        <div v-else class="amount-item-wrap">
                            <div class="amount-item">
                                <div class="amount-lable">{{ i18n.t('ApComponents.netAmount') }}</div>
                                <a-input-number
                                    v-if="type === 'AP'"
                                    v-model:value="apForm.net_amount"
                                    :disabled="true"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                >
                                </a-input-number>
                                <a-input-number
                                    v-if="type === 'AR'"
                                    v-model:value="arForm.net_amount"
                                    :disabled="true"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                >
                                </a-input-number>
                            </div>
                            <!-- taxRatesList item-block  -->
                            <div
                                v-if="type === 'AP'"
                                class="amount-item"
                                v-for="item in apForm.tax_content || []"
                                :key="item.fieldName"
                            >
                                <div class="amount-lable">{{ item.alias }}</div>
                                <!-- <a-input-number
                                    v-model:value="item.value"
                                    :disabled="enableTaxExempt"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                    @change="handleTaxChange($event, item)"
                                >
                                </a-input-number> -->
                                <InputNumber
                                    class="amount-input-prime"
                                    v-model="item.value"
                                    :controls="false"
                                    :disabled="enableTaxExempt"
                                    :minFractionDigits="2"
                                    :maxFractionDigits="2"
                                    :locale="decimalFormat()"
                                    fluid
                                    @value-change="handleTaxChange($event, item)"
                                />
                            </div>

                            <div
                                v-if="type === 'AR'"
                                class="amount-item"
                                v-for="item in arForm.tax_content || []"
                                :key="item.fieldName"
                            >
                                <div class="amount-lable">{{ item.alias }}</div>
                                <!-- <a-input-number
                                    v-model:value="item.value"
                                    :disabled="enableTaxExempt"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                    @change="handleTaxChange($event, item)"
                                >
                                </a-input-number> -->
                                <InputNumber
                                    class="amount-input-prime"
                                    v-model="item.value"
                                    :controls="false"
                                    :disabled="enableTaxExempt"
                                    :minFractionDigits="2"
                                    :maxFractionDigits="2"
                                    :locale="decimalFormat()"
                                    fluid
                                    @value-change="handleTaxChange($event, item)"
                                />
                            </div>

                            <!--                            <div class="amount-item" v-show="false">-->
                            <!--                                <div class="amount-lable">{{ i18n.t('bkAp.shipping') }}</div>-->
                            <!--                                <a-input-number-->
                            <!--                                    v-if="type === 'AP'"-->
                            <!--                                    v-model:value="apForm.shipping"-->
                            <!--                                    :disabled="readonlyMode"-->
                            <!--                                    :controls="false"-->
                            <!--                                    :precision="2"-->
                            <!--                                    class="amount-input"-->
                            <!--                                    @change="handleShippingChange"-->
                            <!--                                >-->
                            <!--                                </a-input-number>-->

                            <!--                                <a-input-number-->
                            <!--                                    v-if="type === 'AR'"-->
                            <!--                                    v-model:value="arForm.shipping"-->
                            <!--                                    :disabled="readonlyMode"-->
                            <!--                                    :controls="false"-->
                            <!--                                    :precision="2"-->
                            <!--                                    class="amount-input"-->
                            <!--                                    @change="handleShippingChange"-->
                            <!--                                >-->
                            <!--                                </a-input-number>-->
                            <!--                            </div>-->
                            <div class="amount-item">
                                <div class="amount-lable">{{ i18n.t('ApComponents.subtotal') }}</div>
                                <a-input-number
                                    v-if="type === 'AP'"
                                    v-model:value="apForm.total_tax"
                                    :disabled="true"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                >
                                </a-input-number>

                                <a-input-number
                                    v-if="type === 'AR'"
                                    v-model:value="arForm.total_tax"
                                    :disabled="true"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                >
                                </a-input-number>
                            </div>
                            <!--                            <div v-show="false" class="amount-item">-->
                            <!--                                <div class="amount-lable">{{ i18n.t('ApComponents.taxableSubtotal') }}</div>-->
                            <!--                                <a-input-number-->
                            <!--                                    v-if="type === 'AP'"-->
                            <!--                                    v-model:value="apForm.net_amount"-->
                            <!--                                    :disabled="true"-->
                            <!--                                    :controls="false"-->
                            <!--                                    :precision="2"-->
                            <!--                                    class="amount-input"-->
                            <!--                                >-->
                            <!--                                </a-input-number>-->

                            <!--                                <a-input-number-->
                            <!--                                    v-if="type === 'AR'"-->
                            <!--                                    v-model:value="arForm.net_amount"-->
                            <!--                                    :disabled="true"-->
                            <!--                                    :controls="false"-->
                            <!--                                    :precision="2"-->
                            <!--                                    class="amount-input"-->
                            <!--                                >-->
                            <!--                                </a-input-number>-->
                            <!--                            </div>-->
                            <div class="amount-item">
                                <div class="amount-lable bold">
                                    {{ i18n.t('ApComponents.total') }}
                                    {{ type === 'AP' ? apForm.invoice_currency : arForm.invoice_currency }}
                                </div>
                                <a-input-number
                                    v-if="type === 'AP'"
                                    v-model:value="apForm.total_fee"
                                    :disabled="autoCalculateState"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                    @change="changeTotalAmount($event, false)"
                                >
                                </a-input-number>

                                <a-input-number
                                    v-if="type === 'AR'"
                                    v-model:value="arForm.total_fee"
                                    :disabled="autoCalculateState"
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    class="amount-input"
                                    @change="changeTotalAmount($event, false)"
                                >
                                </a-input-number>
                            </div>
                            <!--         @change="changeTotalAmount($event, true)"-->
                            <div v-if="type === 'AP'">
                                <div class="amount-item" v-if="apForm.invoice_currency !== localCurrency">
                                    <div class="amount-lable bold">
                                        {{ i18n.t('ApComponents.total') }} {{ localCurrency }}
                                    </div>
                                    <a-input-number
                                        v-model:value="apForm.total_fee_local"
                                        :disabled="false"
                                        :controls="false"
                                        :precision="2"
                                        :formatter="formatNumber"
                                        :parser="parseNumber"
                                        class="amount-input"
                                    >
                                    </a-input-number>
                                </div>
                                <div class="amount-item" v-if="apForm.invoice_currency !== localCurrency">
                                    <div class="amount-lable bold">{{ i18n.t('ApComponents.difference') }}</div>
                                    <a-input-number
                                        v-model:value="form_diff"
                                        :disabled="true"
                                        :controls="false"
                                        :precision="2"
                                        :formatter="formatNumber"
                                        :parser="parseNumber"
                                        class="amount-display-alert"
                                    >
                                    </a-input-number>
                                </div>
                            </div>

                            <div v-if="type === 'AR'">
                                <div class="amount-item" v-if="arForm.invoice_currency !== localCurrency">
                                    <div class="amount-lable bold">
                                        {{ i18n.t('ApComponents.total') }} {{ localCurrency }}
                                    </div>
                                    <a-input-number
                                        v-model:value="arForm.total_fee_local"
                                        :disabled="false"
                                        :controls="false"
                                        :precision="2"
                                        :formatter="formatNumber"
                                        :parser="parseNumber"
                                        class="amount-input"
                                    >
                                    </a-input-number>
                                </div>
                                <div class="amount-item" v-if="arForm.invoice_currency !== localCurrency">
                                    <div class="amount-lable bold">{{ i18n.t('ApComponents.difference') }}</div>
                                    <a-input-number
                                        v-model:value="form_diff"
                                        :disabled="true"
                                        :controls="false"
                                        :precision="2"
                                        :formatter="formatNumber"
                                        :parser="parseNumber"
                                        class="amount-display-alert"
                                    >
                                    </a-input-number>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <a-divider class="footer-divider"></a-divider>

            <footer>
                <a-button shape="round" class="cancel-button" @click="cancel" size="small">
                    {{ i18n.t('commonTag.cancel') }}
                </a-button>
                <a-button
                    type="primary"
                    :loading="formLoading"
                    :disabled="isDisable"
                    size="small"
                    shape="round"
                    @click="confirm"
                    >{{ i18n.t('commonTag.confirm') }}
                </a-button>
            </footer>
        </a-form>
    </a-spin>
    
    <a-modal
        title="Create New Supplier"
        v-model:visible="isShowSupplierForm"
        :footer="null"
        destroyOnClose
        :closeable="true"
        :width="820"
        :wrapClassName="'modal-wrap'"
    >
        <supplier-form
            :current-customer="current"
            :edit-mode="false"
            :origin="'outside'"
            client-type="SUPPLIER"
            @updateNewContact="apUpdateNewContact"
            @dismiss="showSupplierCreationModal(false)"
        ></supplier-form>
    </a-modal>
    <a-modal
        title="Create New Customer"
        v-model:visible="isShowContactForm"
        :footer="null"
        destroyOnClose
        :closeable="true"
        :width="820"
        :bodyStyle="{padding: '10px 14px 14px'}"
        :z-index="2902"
        :wrapClassName="'modal-wrap'"
    >
        <supplier-form
            :current-customer="current"
            :edit-mode="false"
            :origin="'outside'"
            client-type="CUSTOMER"
            @updateNewContact="arUpdateNewContact"
            @dismiss="showContactCreationModal(false)"
        ></supplier-form>
    </a-modal>
</template>

<style scoped lang="scss">
.title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 24px;
    margin-left: 24px;
}

.form-item {
    margin-left: 24px;
    width: 80%;
}

.gl-switch-wrap {
    margin-right: 8px;
}

.ap-invoice-amount-block {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 0 0;
    margin-left: 24px;

    .ap-invoice-amount-block-left {
        width: 450px;
    }

    .ap-invoice-amount-block-right {
        width: 450px;
        min-width: 450px;
        padding-bottom: 30px;

        // max-width: calc(100% - 285px);
        .amount-block {
            width: 100%;

            .title {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                text-align: right;
            }

            .switch-wrap {
                margin-right: 8px;
            }

            .amount-item-wrap {
                padding: 20px 16px;
                width: 100%;
                background-color: #f5f7f9;
                border-radius: 8px;

                .amount-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0px;
                    }

                    .amount-lable {
                        width: calc(100% - 151px - 8px);
                        min-width: 70px;
                        text-align: right;

                        &.bold {
                            font-weight: 700;
                        }
                    }

                    .amount-input {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: #fff;
                        }
                    }

                    .amount-display-alert {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: rgba(255, 0, 0, 0.2);
                            color: red;
                        }
                    }
                }
            }
        }
    }
}

.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}

footer {
    padding: 24px 0px 0;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn {
        min-width: 65px;
    }

    .ant-btn + .ant-btn {
        margin-left: 8px;
    }
}

.amount-input-prime {
    width: 151px;
    height: 33px;
    border-radius: 6px;
    color: #333;
    background-color: #fff;
    &.item-amount {
        max-width: 100%;
        background-color: #f5f7f9;
    }
}

:deep(.p-inputnumber input) {
    font-size: 14px;
    padding-left: 10px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border: 1px solid #4096ff;
        box-shadow: 0 0 0 2px #0591ff1a;
    }
}

:deep(.p-inputnumber.item-amount input) {
    border-color: #f5f7f9;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border-color: #216fcf;
    }
}

:deep(.p-inputnumber.item-amount input::placeholder) {
    color: #bbb;
}
.textarea-wrap {
    min-height: 100px;
    height: 100%;
    margin-bottom: 15px;
}
</style>
