<!-- @format -->

<script lang="ts" setup>
import {UserCompany} from '@/lib/storage'
import i18nInstance from '@/locales/i18n'
import {type FormInstance, message} from 'ant-design-vue'
import {computed, nextTick, onBeforeMount, onMounted, reactive, ref, watch} from 'vue'
import type {Composer} from 'vue-i18n'
import {useStore} from 'vuex'

import * as _ from 'lodash'

const i18n: Composer = i18nInstance.global
const store = useStore()
const props = defineProps({
    currentProductService: {
        type: Object,
    },
    editMode: {
        type: Boolean,
        default: false,
    },
})
const formRef = ref<FormInstance>()
const userCompany: any = UserCompany.get() || []
const form = ref<any>({
    company_id: userCompany[0].id,
    company_code: userCompany[0].code,
    product_service: '',
    description: '',
    unit_price: 0,
    coa: '',
})

const formLoading = ref(false)
const accountQuery = {bk_type: 1, company_code: userCompany[0].code, $limit: -1, del_flag: 0}

//mapMutations
const accountDescList: any = computed(() => store.state.CommonDropDownStore.accountDescList)

// mapActions
const createProductService = (payload: any) => store.dispatch('ProductServiceStore/createProductService', payload)
const updateProductService = (payload: any) => store.dispatch('ProductServiceStore/updateProductService', payload)
const fetchAccountDescDropdown = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)
const emit = defineEmits(['updateData', 'fetchProductServiceList', 'dismiss'])
const save = async () => {
    if (await formRef.value?.validateFields()) {
        let response
        try {
            formLoading.value = true
            if (!props.editMode) {
                response = await createProductService(form.value)
            } else {
                response = await updateProductService(form.value)
            }
            if (response.status === 201 || response.status === 200) {
                message.success(i18n.t('ApComponents.success'))
                emit('fetchProductServiceList')
                cancel()
            } else {
                // message.error({
                //     content: response.statusText || 'failed',
                //     duration: 3,
                // })
            }
        } catch (err: any) {
            console.log(err)
            // message.error({
            //     content: err.response.data.message,
            //     duration: 3,
            // })
        } finally {
            formLoading.value = false
        }
    } else {
        return false
    }
}
const cancel = () => {
    emit('dismiss')
}

// computed mapState
// const provinceOptions = computed(() => store.state.CommonDropDownStore.provinceOptions)

// const inputChange = (key: string, event: any) => {
//     inputState[key] = _.isString(event.data) || _.isString(event)
// }
const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}
const unitPriceValidator = async (rule: any, value: any) => {
    if(isNaN(value)) {
        return Promise.reject(i18n.t('bkCommonTag.msgNumberRule'))
    }
    return Promise.resolve()
    // console.log(value);
    // console.log(_.isNumber(value));
    // console.log(typeof value);
    // console.log(isNaN(value));
}
const rules = reactive({
    product_service: [...requireRule(i18n.t('bkAr.modelNumber')), ...lengthLimitRule(1, 255)],
    description: [...lengthLimitRule(1, 255)],
    unit_price: [...requireRule(i18n.t('bkAr.unitPrice')), {validator: unitPriceValidator, trigger: 'blur'}],
    // coa: [...lengthLimitRule(1, 255)],
})
onBeforeMount(async () => {
    fetchAccountDescDropdown(accountQuery)
})
onMounted(async () => {
    if (props.editMode) {
        form.value = props.currentProductService
    }
})
const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}
</script>
<template>
    <div class="product-service-form-wrap">
        <a-form
            ref="formRef"
            :model="form"
            :layout="'vertical'"
            :rules="rules"
            label-width="auto"
            label-position="top"
            class="form-box"
            :loading="formLoading"
        >
            <a-row :gutter="[24, 24]" class="product-service-form-block">
                <a-col :span="12">
                    <a-form-item name="product_service" :label="i18n.t('bkAr.modelNumber')">
                        <a-input v-model:value="form.product_service"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item name="description" :label="i18n.t('bkAr.description')">
                        <a-input v-model:value="form.description"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item name="unit_price" :label="i18n.t('bkAr.unitPrice')">
                        <a-input v-model:value="form.unit_price"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <!-- <template #default="{index}"> -->
                        <a-form-item name="coa" :label="i18n.t('bkAp.accountingCategory')">
                            <!-- <a-input v-model:value="form.coa"></a-input> -->
                            <a-select
                                :placeholder="i18n.t('workTimeManager.msgInput')"
                                v-model:value="form.coa"
                                show-search
                                :dropdownMatchSelectWidth="400"
                                :filter-option="filterOption"
                                :options="accountDescList.map((item: any) => ({ 
                                    key: item.account_code + '|' + item.name,
                                    value: item.account_code,
                                    label: item.account_code.substring(0, 4) + ' | ' + item.name 
                                }))"
                            >
                                <!-- <a-select-option
                                    v-for="item in accountDescList"
                                    :key="item.account_code + ' | ' + item.name"
                                    :value="item.id"
                                    >{{
                                        item.account_code.substring(0, 4) + ' | ' + item.name
                                    }}</a-select-option
                                > -->
                            </a-select>
                        </a-form-item>
                    <!-- </template> -->
                </a-col>
            </a-row> 
        </a-form>
        <footer>
            <a-button @click="cancel" class="cancel-button" shape="round">
                {{ i18n.t('commonTag.cancel') }}
            </a-button>
            <a-button type="primary" shape="round" :loading="formLoading" @click="save">
                {{ editMode ? i18n.t('commonTag.save') : i18n.t('commonTag.save') }}
            </a-button>
        </footer>
    </div>
</template>
<style lang="scss" scoped>
.product-service-form-wrap {
    padding: 20px 24px 0px;

    .product-service-form-block {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        :deep(.ant-collapse-item) {
            .ant-collapse-header {
                padding-left: 0;
                padding-right: 28px;
                font-size: 16px;
                border-bottom: 1px solid #e2e2ea;
                border-radius: 0;
            }

            .ant-collapse-content-box {
                padding-left: 0;
                padding-right: 0;
                border-bottom: 1px solid #e2e2ea;
            }
        }
    }

    .product-service-form-divider {
        margin-top: 0px;
        margin-bottom: 0px;
        border-color: #e2e2ea;
    }

    footer {
        padding: 12px 0px;
        text-align: right;

        .cancel-button {
            border-color: #004fc1;
            color: #004fc1;
        }

        .ant-btn {
            min-width: 65px;
        }

        .ant-btn + .ant-btn {
            margin-left: 8px;
            min-width: 75px;
        }
    }
}
</style>
