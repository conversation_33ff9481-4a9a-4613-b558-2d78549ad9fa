<!-- @format -->

<script lang="ts" setup>
import { useStore } from 'vuex'
import GlComponent from '../../../components/bookkeepingComponents/GlComponents/GlComponent.vue'
import CustomColumns from '../../../components/bookkeepingComponents/CommonComponents/CustomColumns.vue'
import moment from 'moment'
import { message, Modal, notification, type TableProps } from 'ant-design-vue'
import { computed, onBeforeMount, createVNode, onMounted, onBeforeUnmount, reactive, ref } from 'vue'
import { GlCustomizeTable, UserCompany, UserInfo, FinancialYear } from '@/lib/storage'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import {
    EditOutlined,
    DeleteOutlined,
    MailOutlined,
    SearchOutlined,
    FilterOutlined,
    LayoutOutlined,
    PlusOutlined,
    ExportOutlined,
} from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import SvgIcon from '../../../components/SvgIcon.vue'
import * as _ from 'lodash'
import dayjs, { Dayjs } from 'dayjs'

const userCompany: any = UserCompany.get() || []
const financialYear = FinancialYear.get() ? moment(FinancialYear.get()).month() : null
const userInfo: any = UserInfo.get() || {}
const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const timer = ref()
const defaltPageType: any = router.currentRoute.value.query.pageType
const showColumns = ref(false)
let defaultTable = ['narration', 'journalEntry', 'createDate', 'totalDebit', 'totalCredit', 'postingDate', 'status']
const saveColumns = (list: any) => {
    customizeTable.value = list
    showColumns.value = false
    GlCustomizeTable.set(list)
}
const itemList = ref([] as any[])
const tableLoading = ref(false)
const activeTabName = ref('1')
const isCustomize = ref(false)
const customizeTable: any = ref(
    GlCustomizeTable.get() || [
        'narration',
        'journalEntry',
        'createDate',
        'totalDebit',
        'totalCredit',
        'postingDate',
        'status',
    ],
)
const searchForm = reactive({
    posting_date_start: '',
    posting_date_end: '',
    revers_filter: false,
    narration: '',

    total_amount_min: null,
    total_amount_max: null,
})
const exportForm = reactive({
    start: null,
    end: null,
    revers_filter: false,
    company: userCompany[0].code,
})
const current = ref({})
const show = ref(false)
const readonlyMode = ref(false)
const operationMode = ref('editing')
const pageQuery = reactive({
    page_index: 1,
    page_size: 10,
    // currentPageNumber: 1,
})
type SortQuery = {
    [key: string]: string // 定义 sortQuery 的属性为字符串类型
}

const sortQuery: SortQuery = reactive({})

// const currentPageNumber = ref()
const isSwitchTab = ref(false)

const showConfirm = ref(false)
const confirmationWrap = ref<HTMLElement | null>(null)
const confirmText = i18n.t('ApComponents.confirm') //'Please confirm to reverse'
const showReverseLoading = ref(false)
const reverseDt = ref('')

const confirmationConfirm = async () => {
    showReverseLoading.value = true
    const alist = JSON.parse(JSON.stringify(current.value))
    // reverseDt = alist['posting_date']
    const queryParam = {
        document_no: alist['document_no'],
        posting_date: reverseDt.value, //alist['posting_date'],
        creator: userInfo?.id || '',
    }
    await reverse(queryParam)
    showReverseLoading.value = false
    showConfirm.value = false
}

const popConfirmModal = () => {
    const { posting_date } = current.value as any
    reverseDt.value = posting_date
    showConfirm.value = true
}

const replaceKey = (array: string[], key: string, replaceKey: string) => {
    const index = array.findIndex(i => i === key)
    if (index >= 0) {
        array.splice(index, 1, replaceKey)
    }

    return array
}

onBeforeMount(async () => {
    if (defaltPageType !== undefined) {
        activeTabName.value = defaltPageType
    } else {
        activeTabName.value = '1'
    }
    if (activeTabName.value === '1') {
        defaultTable = replaceKey(defaultTable, 'draftDate', 'postingDate')
        customizeTable.value = replaceKey(customizeTable.value, 'draftDate', 'postingDate')
    } else {
        defaultTable = replaceKey(defaultTable, 'postingDate', 'draftDate')
        customizeTable.value = replaceKey(customizeTable.value, 'postingDate', 'draftDate')
    }
})
// mapActions
const fetchGlList = (payload: any) => store.dispatch('GlStore/fetchGlListByInvoiceHistoryV1', payload)
const editNewGl = (payload: any) => store.dispatch('GlStore/editGl', payload)
const deleteGl = (payload: any) => store.dispatch('GlStore/deleteGl', payload)
const postGl = (payload: any) => store.dispatch('GlStore/saveGlAction', payload)
const fetchReverse = (payload: any) => store.dispatch('GlStore/fetchReverseAction', payload)

const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}
const updateTable = async () => {
    try {
        tableLoading.value = true
        console.log('userCompanyuserCompanyuserCompany', userCompany[0].code)

        const query = {
            ...sortQuery,
            ...searchForm, //搜索时form值
            ...pageQuery,
            company: userCompany[0].code,
            status: activeTabName.value,
        }
        await fetchGlList(query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}
const search = async () => {
    clearInterval(timer.value)
    const query = {
        company: userCompany[0].code,
        status: activeTabName.value,
        ...searchForm,
    }
    pageQuery.page_index = 1
    // currentPageNumber.value = 1
    tableLoading.value = true
    await fetchGlList(query)
    tableLoading.value = false
    // await updateTable()
}
const filterSearch = async () => {
    pageQuery.page_index = 1
    await updateTable()
}

const isInFiscalYear = (start: string, end: string) => {
    const year =
        moment().year(moment(start).year()).month(financialYear!).startOf('month') <= moment(start)
            ? moment(start).year()
            : moment(start).year() - 1
    return (
        moment().year(year).month(financialYear!).startOf('month') <= moment(start) &&
        moment(end) <
        moment()
            .year(year + 1)
            .month(financialYear!)
            .startOf('month')
    )
}

const disabledMinDate = (current: Dayjs) => {
    if (exportForm.start) {
        return current && dayjs(current).endOf('day') < dayjs(exportForm.start).endOf('day')
    }
    return false
}

const disabledMaxDate = (current: Dayjs) => {
    if (exportForm.end) {
        return current && dayjs(current).endOf('day') > dayjs(exportForm.end).endOf('day')
    }
    return false
}

const exportCsv = async () => {
    if (financialYear === null) {
        message.error({
            content: 'Your company has not set Financial Year Start',
            duration: 5,
        })
        return
    }

    if (exportForm.start && exportForm.end && !isInFiscalYear(exportForm.start, exportForm.end)) {
        message.error({
            content: 'Date period must be within one fiscal year',
            duration: 5,
        })
        return
    }

    try {
        tableLoading.value = true
        const query = {
            ...exportForm,
            start:
                exportForm.start ||
                (moment().month(financialYear).startOf('month') <= moment()
                    ? moment().month(financialYear).startOf('month').format('YYYY-MM-DD')
                    : moment().month(financialYear).add(-1, 'year').startOf('month').format('YYYY-MM-DD')),
            end: exportForm.end || moment().format('YYYY-MM-DD'),
            revers_filter: exportForm.revers_filter,
            name: userCompany[0],
        }
        await store.dispatch('GlStore/exportGlInvoiceList', query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}
const onClickRow = (record: any, index: any) => {
    if (activeTabName.value === '1') {
        return {
            onclick: (event: any) => {
                // debugger

                current.value = _.cloneDeep(record)
                readonlyMode.value = true
                operationMode.value = 'detail'
                showDialog(true)
            },
        }
    } else {
        return {
            onclick: (event: any) => {
                current.value = _.cloneDeep(record)
                readonlyMode.value = false
                operationMode.value = 'editing'
                showDialog(true)
            },
        }
    }
}

const onChange = async (pagination: any, filters: any, sorter: any) => {
    console.log('params', pagination, filters, sorter)
    if (sorter.field && sorter.order) {
        //先移除全部字段
        for (const prop in sortQuery) {
            delete sortQuery[prop]
        }
        const field = `$sort[${sorter.field}]`
        sortQuery[field] = sorter.order === 'ascend' ? '1' : '0'
    } else {
        //移除全部字段
        for (const prop in sortQuery) {
            delete sortQuery[prop]
        }
    }

    await updateTable()
}

const edit = (row: any) => {
    current.value = _.cloneDeep(row)
    readonlyMode.value = false
    operationMode.value = 'editing'
    showDialog(true)
}
const view = (row: any) => {
    current.value = { ...row }
    readonlyMode.value = true
    showDialog(true)
}
const remove = () => {
    console.log(selectedRowKeysState.selectedRowKeys, selectedRowKeysState.selectedRows)

    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: i18n.t('gl.msgGlDelete'),
        async onOk() {
            try {
                if (selectedRowKeysState.selectedRows.length > 0) {
                    for (const record of selectedRowKeysState.selectedRows) {
                        const response = await deleteGl(record.document_no)
                        if (response.data.draft === 'ok') {
                            message.success(i18n.t('ApComponents.success'))
                        }
                    }

                    await updateTable()
                }
            } catch (error) {
                console.log(error)
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
        okType: 'danger',
        onCancel() {
            console.log('canceled')
        },
    })
}

const saveDraft = async (form: any) => {
    let response: any = {}
    form.draft = 1
    try {
        tableLoading.value = true
        response = await postGl(form)
        if (response.data.data.success) {
            message.success(i18n.t('ApComponents.success'))
            await updateTable()
        } else {
            // message.error({
            //     content: 'failed',
            //     duration: 5,
            // })
        }
    } catch (error: any) {
        // message.error({
        //     content: error.response.data.errors.message ? error.response.data.errors.message : 'failed',
        //     duration: 5,
        // })
    } finally {
        tableLoading.value = false
    }
}

const editDraft = async (form: any) => {
    let response = {} as any
    try {
        tableLoading.value = true
        response = await editNewGl(form)
        if (response.data.ok === 'ok') {
            message.success(i18n.t('ApComponents.success'))
            updateTable()
        }
        //  else {
        //     message.error({
        //         content: response.data.errors,
        //         duration: 3,
        //     })
        // }
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const post = async (form: any) => {
    let response = {} as any
    try {
        tableLoading.value = true
        response = await postGl(form)
        if (response.data.data.success) {
            message.success(i18n.t('ApComponents.success'))
            updateTable()
        }
    } catch (error: any) {
        // message.error({
        //     content: error.response.data.errors || 'failed',
        //     duration: 5,
        // })
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}

const reverse = async (obj: any) => {
    let response = {} as any
    try {
        tableLoading.value = true
        response = await fetchReverse(obj)
        if (response.data.data.success) {
            message.success(i18n.t('ApComponents.success'))

            showDialog(false)
            updateTable()
        }
        // else {
        //     message.error({
        //         content: response.data.errors,
        //         duration: 5,
        //     })
        // }
    } catch (error: any) {
        // showConfirm.value = false
        console.error(error)
        // message.error({
        //     content: error.response.data.errors || 'failed',
        //     duration: 5,
        // })
    } finally {
        showReverseLoading.value = false
        tableLoading.value = false
    }
}

const confirmationCancel = () => {
    showConfirm.value = false
    return
}

const enterShowConfirm = () => {
    // Modal.confirm({
    //     title: i18n.t('bkCommonTag.confirmation'),
    //     icon: createVNode(ExclamationCircleFilled),
    //     content: i18n.t('ApComponents.confirm'), //'Please confirm to reverse',
    //     style: `margin-top:10%`,
    //     okText: i18n.t('commonTag.confirm'),
    //     okType: 'primary',
    //     cancelText: i18n.t('commonTag.cancel'),
    //     onOk() {
    //         const alist = JSON.parse(JSON.stringify(current.value))
    //         const queryParam = {
    //             document_no: alist['document_no'],
    //             posting_date: alist['posting_date'],
    //             creator: userInfo?.id || '',
    //         }
    //         reverse(queryParam)
    //         showDialog(false)
    //     },
    //     onCancel() {
    //         console.log('Cancel')
    //     },
    // })
    popConfirmModal()
}

const showDialog = (bool: boolean) => {
    show.value = bool
    if (!bool) {
        current.value = {}
    }
}
const dismiss = () => {
    showDialog(false)
    readonlyMode.value = false
}
const changeCurrentPageNumber = (pageNumber: number) => {
    pageQuery.page_index = pageNumber
    // pageQuery.currentPageNumber = pageNumber
    updateTable()
}
const changePageSize = (pageSize: number) => {
    pageQuery.page_size = pageSize
    // pageQuery.currentPageNumber = 1
    updateTable()
}
const switchTab = (e: any) => {
    isSwitchTab.value = true
    activeTabName.value = e

    if (activeTabName.value === '1') {
        defaultTable = replaceKey(defaultTable, 'draftDate', 'postingDate')
        customizeTable.value = replaceKey(customizeTable.value, 'draftDate', 'postingDate')
    } else {
        defaultTable = replaceKey(defaultTable, 'postingDate', 'draftDate')
        customizeTable.value = replaceKey(customizeTable.value, 'postingDate', 'draftDate')
    }

    pageQuery.page_index = 1
    // pageQuery.currentPageNumber = 1
    searchForm.posting_date_start = ''
    searchForm.posting_date_end = ''
    searchForm.revers_filter = false
    updateTable()
}
const customize = () => {
    isCustomize.value = !isCustomize.value
    if (!isCustomize.value) {
        localStorage.setItem('glCustomizeTable', JSON.stringify(customizeTable.value))
    }
}
const changeCustomize = (key: any) => {
    const index = customizeTable.value.findIndex((i: any) => i == key)
    if (index >= 0) {
        customizeTable.value = customizeTable.value.filter((i: any) => i != key)
    } else {
        customizeTable.value.push(key)
    }
}
// computed mapState
//invoicesList.slice((paging.index - 1) * paging.size, paging.index * paging.size)
const glList = computed(() =>
    store.state.GlStore.glList.map((i: any) => {
        return { ...i, created_on: moment(i.created_on).format('YYYY-MM-DD') }
    }),
)

const totalNumber = computed(() => store.state.GlStore.totalNumber)
const pageIndex = computed(() => store.state.pageType)
const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)

const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer.value)
    timer.value = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer.value)
        }
    }, 1000)
}

onMounted(async () => {
    itemList.value = ['gl', router.currentRoute.value.meta.title]
    await updateTable()
})

const changePage = () => {
    updateTable()
}
const dismissColumns = () => {
    showColumns.value = false
}
const toAddGl = () => {
    readonlyMode.value = false
    operationMode.value = 'creating'
    showDialog(true)
}
onBeforeUnmount(() => {
    const newQuery = JSON.parse(JSON.stringify(router.currentRoute.value.query)) // 深拷贝
    delete newQuery.pageType
    router.replace({ query: newQuery })
})

const selectedRowKeysState = reactive({
    selectedRowKeys: [] as number[],
    selectedRows: [] as any[],
})

const rowSelection = computed(() => {
    return {
        selectedRowKeys: selectedRowKeysState.selectedRowKeys,
        onChange: (selectedRowKeys: number[], selectedRows: any[]) => {
            selectedRowKeysState.selectedRowKeys = selectedRowKeys
            selectedRowKeysState.selectedRows = selectedRows
        },
    }
})

const setRowKey = (record: { id: number }) => record.id

// const rowSelection = computed(() => {
//     return {
//         // type: 'radio', // 是否为单选
//         selectedRowKeys: selectedRowKeys,
//         onChange: (selectedRowKeys, selectedRows, event) => {
//             console.log(selectedRowKeys, selectedRows, event)

//             //selectedRowKeys 为你点击选框时这一页选中的所有key
//             //selectedRows 为你点击选框时这一页选中的所有数据
//         },
//         onSelect: (record, selected, selectedRows, nativeEvent) => {
//             //record 点击某一条的所有数据
//             //selected 点击的一条是否被选中
//         },
//         onSelectAll: (selected, selectedRows, changeRows) => {
//             console.log('onSelectAll-----', selected, selectedRows, changeRows)

//             //selected 点击全选是否选中
//             //selectedRows 点击全选判断所有的选中数据
//             //changeRows 所有改变选中状态的数据
//         },
//         getCheckboxProps: record => ({
//             props: {
//                 // 全部默认禁止选中
//                 // disabled: true,
//                 // 某几项默认禁止选中(R: 当state等于1时)
//                 // disabled: record.state == 1,
//                 // 某几项默认选中(R: 当state等于1时)
//                 defaultChecked: record.state === 1,
//             },
//         }),
//     }
// })
</script>
<template>
    <div class="page-container-gl_list">
        <div class="gl-list-tabs" style="display: flex; justify-content: space-between">
            <a-tabs class="header-tabs" v-model:activeKey="activeTabName" @change="switchTab">
                <a-tab-pane :disabled="tableLoading || isCustomize" :tab="i18n.t('gl.posted')" key="1"></a-tab-pane>
                <a-tab-pane :disabled="tableLoading || isCustomize" :tab="i18n.t('gl.draft')" key="0"></a-tab-pane>
            </a-tabs>

            <!-- 顶部搜索框 过滤 设置columns是否可见 -->
            <div class="history-page-header">
                <div class="search-group-wrap">
                    <!-- v-model:value="searchForm.companyName"
              @input="inputChange" -->
                    <a-input :placeholder="$t('gl.search')" :disabled="tableLoading || isCustomize"
                        v-model:value="searchForm.narration" class="search-input" @input="inputChange"
                        @pressEnter="search">
                        <template #suffix>
                            <svg-icon name="icon_search"></svg-icon>
                        </template>
                    </a-input>
                    <a-popover class="popover-wrap" trigger="click" placement="bottom" @visibleChange="visibleChange">
                        <template #content>
                            <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                                <div class="search-input-group">
                                    <a-form-item :label="$t('gl.date')">
                                        <a-date-picker v-model:value="searchForm.posting_date_start"
                                            :disabled="isCustomize" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                            :placeholder="$t('gl.createStartDate')" style="width: 160px" clearable>
                                        </a-date-picker>
                                    </a-form-item>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    <a-form-item>{{ $t('gl.to') }}
                                        <a-date-picker v-model:value="searchForm.posting_date_end"
                                            :disabled="isCustomize" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                            :placeholder="$t('gl.createEndDate')" style="width: 160px" clearable>
                                        </a-date-picker>
                                    </a-form-item>
                                </div>
                                <div class="search-input-group">
                                    <a-form-item :label="$t('gl.totalCol')">
                                        <a-input-number v-model:value="searchForm.total_amount_min" :controls="false"
                                            :disabled="isCustomize" :min="0 || null" :placeholder="$t('gl.minFee')"
                                            style="width: 160px"></a-input-number>
                                    </a-form-item>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    <a-form-item>{{ $t('gl.to') }}
                                        <a-input-number v-model:value="searchForm.total_amount_max" :controls="false"
                                            :disabled="isCustomize" :min="0 || null" :placeholder="$t('gl.maxFee')"
                                            style="width: 160px"></a-input-number>
                                    </a-form-item>
                                </div>

                                <div class="search-input-group">
                                    <!-- <a-form-item :label="$t('gl.totalCol')"> -->
                                    <a-form-item>
                                        <a-switch class="gl-switch-wrap" v-model:checked="searchForm.revers_filter" />
                                        {{ i18n.t('commonTag.wihtreverse') }}
                                    </a-form-item>
                                </div>

                                <a-button type="primary" shape="round" :disabled="tableLoading || isCustomize"
                                    @click="filterSearch">
                                    <template #icon>
                                        <search-outlined />
                                    </template>
                                    {{ $t('commonTag.search') }}
                                </a-button>
                            </a-form>
                        </template>
                        <a-button class="search-button" :disabled="tableLoading">
                            <template #icon>
                                <svg-icon name="icon_filter"></svg-icon>
                            </template>
                            <!-- {{ i18n.t('commonTag.filter') }} -->
                        </a-button>
                    </a-popover>

                    <a-button class="search-button" :disabled="tableLoading" @click="showColumns = !showColumns">
                        <template #icon>
                            <svg-icon name="icon_columns"></svg-icon>
                        </template>
                        <!-- {{ i18n.t('commonTag.columns') }} -->
                    </a-button>

                    <a-popover class="popover-wrap" v-if="activeTabName === '1'" trigger="click" placement="bottom"
                        @visibleChange="visibleChange">
                        <template #content>
                            <a-form :model="exportForm" ref="searchRef" class="search-input-form">
                                <div class="search-input-group">
                                    <a-form-item :label="$t('gl.date')">
                                        <a-date-picker v-model:value="exportForm.start" :disabled="isCustomize"
                                            :disabledDate="disabledMaxDate" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                            :placeholder="$t('gl.createStartDate')" style="width: 160px" clearable>
                                        </a-date-picker>
                                    </a-form-item>
                                    <a-form-item :label="$t('gl.to')">
                                        <a-date-picker v-model:value="exportForm.end" :disabled="isCustomize"
                                            format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" :disabledDate="disabledMinDate"
                                            :placeholder="$t('gl.createEndDate')" style="width: 160px" clearable>
                                        </a-date-picker>
                                    </a-form-item>
                                </div>

                                <div class="search-input-group">
                                    <!-- <a-form-item :label="$t('gl.totalCol')"> -->
                                    <a-form-item>
                                        <a-switch class="gl-switch-wrap" v-model:checked="exportForm.revers_filter" />
                                        {{ i18n.t('commonTag.wihtreverse') }}
                                    </a-form-item>
                                </div>

                                <a-button type="primary" shape="round" :disabled="tableLoading ||
                isCustomize ||
                (!exportForm.start && exportForm.end) ||
                (exportForm.start && !exportForm.end)
                " @click="exportCsv">
                                    OK
                                </a-button>
                            </a-form>
                        </template>
                        <a-button class="search-button" :disabled="tableLoading">
                            <export-outlined />
                            <!-- {{ i18n.t('workTimeManager.export') }} -->
                        </a-button>
                    </a-popover>
                </div>
                <a-button type="primary" shape="round" class="add-button" @click="toAddGl()">
                    <template #icon>
                        <plus-outlined />
                    </template>
                    {{ i18n.t('commonTag.new') }}
                </a-button>
                <a-button danger :disabled="selectedRowKeysState.selectedRowKeys.length === 0" :ghost="true"
                    shape="round" type="primary" @click="remove" class="del-button" v-if="activeTabName === '0'">
                    <delete-outlined />
                    {{ i18n.t('bkApInvoice.del') }}
                </a-button>
            </div>
        </div>

        <!-- table数据 -->
        <div class="table-data-section">
            <div class="table">
                <a-table :dataSource="glList" :loading="tableLoading" height="100%" :pagination="false"
                    style="width: 100%" :customRow="onClickRow" @change="onChange" :rowKey="setRowKey"
                    :row-selection="rowSelection"
                    :scroll="customizeTable.length > 5 ? { x: 1350, y: 'calc(100vh - 300px)' } : { x: 'auto' }">
                    <a-table-column align="center" :title="i18n.t('gl.narration')" data-index="narration" width="14%"
                        :ellipsis="true" :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="customizeTable.includes('narration') || isCustomize" />
                    <a-table-column align="center" :title="i18n.t('gl.module')" data-index="module" width="12%"
                        :ellipsis="true" :sorter="false" />
                    <a-table-column align="center" :title="i18n.t('gl.journalEntry')" data-index="document_no"
                        width="20%" :ellipsis="true" :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="customizeTable.includes('journalEntry') || isCustomize" />
                    <a-table-column align="center" :title="i18n.t('gl.createDate')" data-index="created_on" width="12%"
                        :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="customizeTable.includes('createDate') || isCustomize">
                    </a-table-column>
                    <a-table-column align="center" :title="i18n.t('gl.totalDebit')" data-index="totalDrTc" width="12%"
                        :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="customizeTable.includes('totalDebit') || isCustomize">
                        <template #default="{ record }">
                            <span>
                                <!-- {{ Number(record.totalDrTc).toFixed(2) }} -->
                                {{ $formatNumber(Number(record.totalDrTc)) }}
                            </span>
                        </template>
                    </a-table-column>
                    <a-table-column align="center" :title="i18n.t('gl.totalCredit')" data-index="totalCrTc" width="12%"
                        :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="customizeTable.includes('totalCredit') || isCustomize">
                        <template #default="{ record }">
                            <span>
                                <!-- {{ Number(record.totalCrTc).toFixed(2) }} -->
                                {{ $formatNumber(Number(record.totalCrTc)) }}
                            </span>
                        </template>
                    </a-table-column>
                    <a-table-column align="center" :title="i18n.t('gl.postingDate')" data-index="posting_date"
                        width="12%" :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="(customizeTable.includes('postingDate') || isCustomize) && activeTabName === '1'">
                    </a-table-column>
                    <a-table-column align="center" :title="i18n.t('gl.draftDate')" data-index="posting_date" width="12%"
                        :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="(customizeTable.includes('draftDate') || isCustomize) && activeTabName === '0'">
                    </a-table-column>
                    <a-table-column align="center" :title="i18n.t('gl.status')" data-index="status" width="8%"
                        :sorter="true" :sortDirections="['ascend', 'descend']"
                        v-if="customizeTable.includes('status') || isCustomize">
                        <template #default="{ record }">
                            <span v-if="record.status === 0">
                                <a-tag class="tag-red">{{ i18n.t('gl.status0') }}</a-tag>
                            </span>
                            <span v-else-if="record.status === 1">
                                <a-tag class="tag-green">{{ i18n.t('gl.status1') }}</a-tag>
                            </span>
                            <span v-else-if="record.status === 2">
                                <a-tag class="tag-gray">{{ i18n.t('gl.status2') }}</a-tag>
                            </span>
                        </template>
                    </a-table-column>
                    <!-- <a-table-column
                        align="center"
                        :title="i18n.t('gl.operation')"
                        key="operation"
                        fixed="right"
                        width="12%"
                        v-if="activeTabName === '0'"
                    >
                        <template #default="{record}">
                            <span v-if="record.status === 0">
                                <a-button
                                    :title="i18n.t('gl.editGl')"
                                    class="btn-txt"
                                    type="link"
                                    :disabled="isCustomize"
                                    @click="edit(record)"
                                >
                                    <edit-outlined />
                                </a-button>
                                <a-divider type="vertical" />
                                <a-button
                                    :title="i18n.t('gl.delGl')"
                                    class="btn-txt"
                                    type="link"
                                    :disabled="isCustomize"
                                    @click="remove(record)"
                                >
                                    <svg-icon name="icon_delete" style="color: #ff0000"></svg-icon>
                                </a-button>
                            </span>
                        </template>
                    </a-table-column> -->
                </a-table>
                <div class="pagination-wrap">
                    <a-pagination v-model:current="pageQuery.page_index" v-model:page-size="pageQuery.page_size"
                        :disabled="tableLoading || isCustomize" :hideOnSinglePage="false" :showSizeChanger="true"
                        :total="totalNumber" @change="changeCurrentPageNumber" />
                    <span>{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                        {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span>
                </div>
            </div>
        </div>

        <a-modal :title="i18n.t('columns.modalTitle')" v-model:visible="showColumns" :footer="null" destroyOnClose
            :closeable="true" :width="480" :wrapClassName="'modal-wrap'">
            <custom-columns :defaultTable="defaultTable" :customizeTable="customizeTable" prefix="gl"
                @dismiss="dismissColumns" @save="saveColumns"></custom-columns>
        </a-modal>

        <a-modal :title="readonlyMode
                ? i18n.t('gl.readonly')
                : operationMode === 'editing'
                    ? i18n.t('gl.edit')
                    : i18n.t('gl.create')
                " v-model:visible="show" :footer="null" destroyOnClose :closeable="true" :width="1000"
            style="z-index: 999" :dialogStyle="{ top: '10px' }" :bodyStyle="{ padding: '10px 24px 24px' }">
            <gl-component :current-invoice="current" :readonly-mode="readonlyMode" :operation-mode="operationMode"
                @reverse="popConfirmModal" @save="editDraft" @post="post" @dismiss="dismiss"
                @saveDraft="saveDraft"></gl-component>
        </a-modal>
    </div>

    <div ref="confirmationWrap">
        <a-modal v-model:visible="showConfirm" centered destroyOnClose :get-container="confirmationWrap" :width="480"
            :closable="false" :confirm-loading="showReverseLoading" :wrapClassName="'modal-wrap'"
            :ok-text="i18n.t('commonTag.confirm')" :ok-type="'primary'" :ok-button-props="{ shape: 'round' }"
            :cancel-text="i18n.t('commonTag.cancel')"
            :cancel-button-props="{ shape: 'round', ghost: true, type: 'primary' }" @ok="confirmationConfirm"
            @cancel="confirmationCancel" :z-index="1999" :mask="false">
            <template #title>
                <div class="confirm-title-wrap">
                    <div class="confirm-title-text">
                        <ExclamationCircleFilled class="confirm-title-icon" />
                        {{ i18n.t('bkCommonTag.confirmation') }}
                    </div>
                </div>
            </template>
            <div class="confirmation-content-text">{{ confirmText }}</div>
            <div class="date-select">
                <a-date-picker v-model:value="reverseDt" :allowClear="false" :inputReadOnly="true" format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD">
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
            </div>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.page-container-gl_list {
    border-radius: 10px;
    background-color: #fff;

    .gl-list-tabs {
        padding: 10px 20px 0px 20px;

        .header-tabs {
            :global(.ant-tabs-nav-operations) {
                display: none !important;
            }
        }
    }
}

.history-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    /* background: #fff; */
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;

    .search-group-wrap {
        display: flex;
        padding: 8px 8px 25px 0;

        .search-input {
            width: 400px;
            border-radius: 4px;
            color: #676d7c;
        }

        .search-button {
            min-width: 60px;
            margin-left: 8px;
            font-size: 16px;
        }

        .search-button+.search-button {
            min-width: 60px;
        }

        .popover-wrap:deep(.ant-popover-placement-bottom) {
            width: 432px;
        }
    }

    .add-button {
        font-size: 16px;
        padding: 6px 16px;
        margin-bottom: 18px;
    }

    .del-button {
        font-size: 16px;
        padding: 6px 16px;
        margin-bottom: 18px;
        margin-left: 8px;
    }
}

.table-data-section {
    background-color: #fff;
    padding-bottom: 12px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;

    .table {
        /* padding-top: 20px; */
        padding-left: 20px;
        padding-right: 20px;
    }
}

.pagination-wrap {
    display: flex;
    margin-top: 12px;
    padding: 0 20px;
    justify-content: flex-end;
    align-items: center;

    span {
        font-size: 12px;
        margin-left: 8px;
        line-height: 16px;
        color: #8c8c8c;
    }
}

// }

.confirm-title-wrap {
    padding-top: 15px;
    padding-bottom: 5px;

    .confirm-title-icon {
        color: #faad14 !important;
        font-size: 21px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .confirm-title-text {
        //  font-family: Calibri;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        font-weight: 400;
    }
}

.confirmation-content-text {
    // font-family: Calibri;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    font-weight: 400;
    padding-left: 64px;
    padding-top: 5px;
    // height: 75px;
}

.date-select {
    color: rgba(0, 0, 0, 0.65);
    min-width: 145px;
    width: 260px;
    max-width: 300px;
    padding-top: 5px;
    padding-left: 64px;
    flex: 1;
}

.search-input-form {
    display: flex;
    flex-direction: column;
    align-items: end;

    .search-input-group {
        display: flex;

        .ant-form-item {
            margin-bottom: 12px;
        }

        :deep(.ant-form-item-label) {
            width: 45px;
        }

        .ant-form-item+.ant-form-item :deep(.ant-form-item-label) {
            width: 35px;
        }
    }
}

.search-input-form2 {
    display: flex;
    flex-direction: column;
    align-items: start;
}

:deep(td) {
    height: 36px !important;
    line-height: 2.5 !important;
    padding: 8px 0 !important;
}

.ant-modal-confirm-btns {
    :deep(.ant-btn span) {
        color: red !important;
    }
}

.br-icon {
    width: 20px;
}

.gl-switch-wrap {
    margin-right: 8px;
}
</style>
