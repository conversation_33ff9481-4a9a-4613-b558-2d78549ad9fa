{"login": {"title": "<PERSON><PERSON><PERSON><PERSON>", "logIn": "Se connecter", "logOut": "Se déconnecter", "english": "<PERSON><PERSON><PERSON>", "chinese": "<PERSON><PERSON>", "language": "<PERSON><PERSON>", "username": "Nom d'utilisateur", "password": "Mot de passe", "msgInputUsername": "Veuillez entrer votre nom d'utilisateur", "msgInputPassword": "Veuillez entrer votre mot de passe", "msgInputAccount": "Veuillez entrer votre compte", "serverError": "<PERSON><PERSON><PERSON> <PERSON>", "changePwd": "Réinitialiser le mot de passe", "gQr": "Authentificateur Google", "newpassword": "Mot de passe", "repassword": "<PERSON><PERSON><PERSON> le mot de passe", "pwdRule": "Doit comprendre au moins 1 majuscule, 1 minuscule, 1 chiffre, et 1 caractère spécial.", "pwdRule1": "Veuillez entrer des caractères sans espaces", "inconsistentPassword": "Mot de passe non cohérent", "repasswordInput": "Veuillez rentrer le mot de passe"}, "mainPage": {"noLogin": "Non connecté"}, "homepage": {"times1": "Paie électronique'", "times2": "Version administrative", "title": "Commencez facilement à payer votre employé, en ligne avec nous.", "info": "L'application NT Digital Payroll offre une couverture complète de l'organisation à la paie, un processus hyper-automatisé permet l'opération commerciale de base pour maximiser la valeur de votre capital humain.", "titleBk1": "OPÉRATION ESSENTIELLE", "titleBk2": "", "titleBk": "Comptabilité hyper-automatisée", "infoBk": "Notre solution financière est conçue pour les petites entreprises d'Amérique du Nord. Que vous soyez propriétaire d'une petite entreprise ou que vous travailliez à votre compte, nous vous aidons à mieux organiser vos finances. Vous pouvez ainsi prendre de meilleures décisions commerciales, vous concentrer sur la croissance et réduire le stress lié à l'administration et à la conformité.", "link": "En savoir plus"}, "router": {"uploadInvoice": "Processus massif", "woBillsInvoice": "Réservation manuelle", "invoiceFromPdf": "Facture au format pdf", "invoiceHistory": "Liste", "accountReceivable": "Vente", "fullInvoice": "Réservation manuelle", "bankReconciliation": "Réconciliation bancaire", "uploadStatement": "Connecter", "EStatementFromPdf": "Déclaration électronique à partir de CSV", "main": "Réconcilier", "common": "Paramètres", "customer": "Contact", "taxInformation": "Profil", "bankInformation": "Informations bancaires", "accountDescription": "CoA", "taxCalculation": "Tableau des impôts", "spotCurrency": "Monnaie courante", "exchangeRate": "Taux de <PERSON>", "supplier": "Fournisseur", "payStubs": "Fiches de paie", "history": "L'histoire", "commonCompany": "Entreprise", "commonAccount": "<PERSON><PERSON><PERSON>", "gl": "Grand livre", "glListing": "Liste", "glEntry": "En<PERSON><PERSON> man<PERSON>le", "fy": "Début de l'année financière", "localCurrency": "Monnaie locale"}, "commonTag": {"tip": "Indice", "confirm": "Confirmer", "cancel": "Annuler", "search": "<PERSON><PERSON><PERSON>", "new": "Ajouter", "save": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "action": "Opération", "actions": "Actions", "view": "Voir", "remark": "<PERSON><PERSON><PERSON>", "back": "Retour", "close": "<PERSON><PERSON><PERSON>", "serial": "Numéro de série", "status": "Statut", "submit": "So<PERSON><PERSON><PERSON>", "send": "Envoyer", "resend": "<PERSON><PERSON><PERSON>", "download": "Télécharger", "sendEmail": "Envoyer un courriel", "columns": "Colonnes", "filter": "Filtre", "msgInput": "Veuillez entrer", "msgSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "reverse": "Inverser", "reverseAndRedo": "Reverse & Redo", "realize": "<PERSON><PERSON><PERSON><PERSON>", "print": "打印", "wihtreverse": "Without reverse", "enable": "Activer", "disable": "Désactiver"}, "columns": {"modalTitle": "Colonnes", "title": "Titre", "operation": "Opération"}, "bkApInvoice": {"to": " ", "readonly": "<PERSON><PERSON><PERSON><PERSON> le dé<PERSON> de la facture", "create": "<PERSON><PERSON>er une facture", "edit": "Modification de la facture", "date": "Date:", "totalCol": "Total ", "minFee": "Frais minimum", "maxFee": "Frais maximum", "invoiceNo": "N° de facture", "invoiceComment": "Commentaires", "creator": "c<PERSON><PERSON><PERSON>", "createTime": "c<PERSON>er du temps", "createDate": "Date de création", "issuerCol": "Éditeur:", "invoiceType": "Type de facture", "issuer": "<PERSON><PERSON><PERSON>", "total": "Total", "type": "Type", "br": "BR", "status": "Statut", "sapStatus": "SAP Statut", "sapNotSent": "Pas envoy<PERSON>", "sapPending": "En attente", "sapSending": "Envoi en cours", "sapSentSuccess": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>", "sapSentFail": "Échec de l'envoi", "sapReversing": "INVERSATION", "sapReverseSuccess": "SUCCÈS DU RENVERSEMENT", "sapReverseFail": "ÉCHEC INVERSE", "referenceNo": "Référence", "dueDate": "Date d'échéance", "postingDate": "Date de facturation", "balance": "Solde", "operation": "Action", "checkNo": "Numéro de chèque", "checkPrintTime": "Temps d'exécution", "printStatus": "Print Status", "printStatus0": "Not Printed", "printStatus1": "Printed", "brStatus0": "Non payé", "brStatus": "<PERSON>ut", "brStatus2": "Paiements en attente", "parked": "Pré-enregistr<PERSON>", "posted": "Comptabilisé", "notPaid": "Non payé", "pmntApproved": "Paiement approuvé", "pmntExecuted": "Paiement exécuté", "paid": "<PERSON><PERSON>", "partialPaid": "Partiellement payé", "reversed": "<PERSON><PERSON><PERSON>", "captured": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "scan": "scanner", "fetch": "<PERSON><PERSON><PERSON><PERSON>", "upload": "Télécharger", "del": "<PERSON><PERSON><PERSON><PERSON>", "level": "Level", "emailList": "Email List", "approvalStatus": "Approval Status", "statusPending": "Pending", "statusApproved": "Approved", "statusRejected": "Rejected", "payMethod": "Méthode de paiement", "printApCheckWarning": "Votre paiement a été effectué, veuillez vérifier.", "issueNameEmpty": "Please input issuer name"}, "bkApUpload": {"fileName": "Nom du dossier", "updateTime": "<PERSON><PERSON> de la mise à jour", "createTime": "c<PERSON>er du temps", "creator": "c<PERSON><PERSON><PERSON>", "xmlStatus": "XML Statut", "payMethod": "Méthode de paiement", "comment": "commentaire", "ocrStatus": "OCR Statut", "scanned": "<PERSON><PERSON><PERSON>", "nonScanned": "Non scanné", "pending": "En attente", "delFile": "Supp<PERSON>er le dossier", "edit": "É<PERSON>er", "createFile": "<PERSON><PERSON>er une facture", "analyzeFile": "Analyser le dossier", "viewDetail": "Voir le détail", "downloadInvoice": "Télécharger la facture", "editComment": "Modifier le commentaire", "editCommentPlaceholder": "Veuillez entrer un commentaire"}, "bkCustomer": {"company": "Entreprise", "tel": "Tél.", "email": "<PERSON><PERSON><PERSON>", "receiver": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Office)", "receiver01": "<PERSON><PERSON> de contact", "address": "<PERSON><PERSON><PERSON>", "address01": "<PERSON><PERSON><PERSON> (Bureau)", "street": "Rue", "city": "Ville", "province": "Province", "country": "Pays", "postalCode": "Code postal", "expenseAccount": "<PERSON><PERSON><PERSON>", "operation": "Action", "createReceiverTitle": "<PERSON><PERSON><PERSON> un client", "createAllReceiverTitle": "<PERSON><PERSON><PERSON> un contact", "accountType": "Type", "editReceiverTitle": "Modifier les coordonnées du contact", "msgPhrSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "officeAddress": "Adresse du bureau", "shippingAddress": "Adresse d'expédition", "billingAddress": "Adresse de facturation", "general": "Général", "sameAsOffice": "Identique au bureau", "itemNo": "#", "category": "<PERSON><PERSON><PERSON><PERSON>", "businessKey": "Clé de l'entreprise", "debitReceipt": "Re<PERSON>u de <PERSON>", "creditReceipt": "Reçu de crédit", "save": "<PERSON><PERSON><PERSON><PERSON>", "caterogyRule": "Veuillez sélectionner une catégorie", "coaJson": "Mapping Code"}, "bkAp": {"invoiceHeader": "<PERSON>-t<PERSON><PERSON> de la facture", "companyName": "Délivré par", "companyAddr": "Adresse de l'entreprise", "companyTel": "Entreprise Tél.", "companyEmail": "<PERSON><PERSON><PERSON> de l'entreprise", "companyGst": "GST No.", "companyQst": "QST No.", "companyGstHst": "GST/HST", "companyPst": "PST (QST/--)", "invoiceNo": "N° de facture", "referenceNo": "Réfé<PERSON><PERSON> de la facture", "purpose": "Objectif", "purposeStandard": "Standard", "purposeProforma": "Formulaire", "purposeCreditMemo": "Note de crédit", "purposeSubsequentCreditMemo": "Note de crédit ultérieure", "poPlaceholder": "Choisissez d'abord votre objectif", "purchaseOrder": "<PERSON> de commande", "currency": "Monnaie", "date": "Date de création", "dueDate": "Date d'échéance", "fixedDate": "Date fixe", "afterDays": "Après les jours", "customerInfo": "Informations sur le client", "billToCompany": "Facture à l'entreprise", "billTo": "Facture à", "billToReceiver": "Facture au récepteur", "billingStreet": "Facture à la Rue", "billToCity": "Facture à la Ville", "billToProvince": "Facture à la Province", "billToZip": "Facture à l'Adresse Postale", "billToTel": "Facture au Tél.", "billToEmail": "Facture au Courriel", "shipToSameAddr": "Envoyer à la même adresse", "shipToCompany": "Envoyer à l'entreprise", "shipToReceiver": "Envoyer au récepteur", "shipToStreet": "Envoyer à la Rue", "shipToCity": "Envoyer à la Ville", "shipToProvince": "Envoyer à la Province", "shipToZip": "Envoyer à l'Adresse Postale", "shipToTel": "Envoyer au Tél.", "shipToEmail": "Envoyer au Courriel", "itemsDetail": "Détail de l'article", "itemNo": "#", "modelNumber": "PRODUIT/SERVICE", "description": "DESCRIPTION", "payMethod": "Méthode de paiement", "addPayMethod": "ajouter un mode de paiement", "qty": "QTY", "unitPrice": " PRIX UNITAIRE", "total": "MONTANT NET", "type": "Type", "bankAccount": "Compte bancaire", "trevenueAccount": "Compte de recettes", "accountingCategory": "COMPTE G/L", "amountAndTax": "Montant et taxe", "amount": "MONTANT NET", "amountRule": "<PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON>", "discount": "<PERSON><PERSON><PERSON>", "totalTaxable": "Total des impôts", "tps": "GST/HST", "tvq": "QST", "tvp": "PST", "totalTax": "Total de l'impôt", "totalCad": "Total(CAD)", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "balance": "Solde", "accountAndType": "Compte et type", "invoiceComment": "Commentaires", "createArInvoice": "<PERSON><PERSON>er une facture", "viewInvoice": "Consulter la facture", "preview": "Prévue", "postingDate": "Date de la facturation", "apIntegrationPostingDate": "Date d'affichage", "create": "sauver", "totalFee": "Total", "msgReferenceNoExisted": "Le numéro de référence existe déjà!", "msgReferenceCheckFail": "La vérification de la répétition du numéro de référence a échoué. Veuillez réessayer!", "from": "À partir de", "addItemBtnTxt": "Ajouter", "addItemBtnTxtInvoice": "Ajouter une facture approuvée", "addItemBtnTxtPayment": "Ajouter une approbation de paiement", "msgCreditMemoInvoiceBeNegative": "Le montant d'une facture de type Avoir doit être négatif.", "paymentDelayDate": "Date de paiement", "paymentDelaySelect": "Sélectionner la durée du paiement", "originalDocument": "Document original", "costObject": "Objet de coût", "taxCode": "Code de taxe", "assigmentSapCostObject": "SAP Cost Object", "assigmentGlAccount": "G/L Account", "assigmentWbs": "WBS", "assigmentCostCenter": "Cost Center", "assigmentInternalOrder": "Internal Order", "assigmentProfitCenter": "Profit Center"}, "bkCommonTag": {"sapTip": "Sychronizing with SAP", "confirmation": "Confirmation", "msgDeleteConfirm": "les données seront supprimées!", "msgRequireRule": "Veuillez entrer ", "msgLengthRule": "La longueur doit être de ", "msgEmailRule": "Veuillez saisir le <PERSON> corriel", "msgSelectRule": "请选择", "msgDeleteSelectConfirm": "Les fichiers PDF sélectionnés seront supprimés !"}, "bkArInvoice": {"to": " ", "readonly": "<PERSON><PERSON><PERSON><PERSON> le dé<PERSON> de la facture", "create": "<PERSON><PERSON><PERSON> la facture", "date": "Date:", "totalCol": "Total ", "minFee": "Frais minimum", "maxFee": "Frais maximum", "invoiceNo": "N° de facture", "invoiceComment": "Commentaires", "creator": "c<PERSON><PERSON><PERSON>", "createTime": "c<PERSON>er du temps", "createDate": "Date de création", "issuerCol": "Émetteur:", "issuer": "Facture à", "total": "Total", "type": "Type", "br": "Statut", "referenceNo": "Référence", "dueDate": "Date d'échéance", "postingDate": "Date de la facture", "balance": "Solde", "operation": "Action", "payerPayee": "Payeur/bénéficiaire:", "brStatus0": "Paiement en attente", "brStatus1": "Partielle payé", "brStatus2": "<PERSON>ut", "brStatus3": "<PERSON>ut", "payMethod": "Mode de paiement", "sapStatus": "Statut de réservation", "search": "Rechercher à facturer", "billing": "Customer Billing", "bill2Customer": "Facturation au client", "pb": "Réservation régulière", "billingNumber": "Numéro de facturation", "referenceNumber": "Numéro de référence", "currency": "Monnaie", "paymentDue": "<PERSON><PERSON><PERSON><PERSON>ai<PERSON>", "postDate": "Date de publication", "billingDate": "Date de facturation", "originalBillingNumber": "Original Billing Number", "uuid": "UUID"}, "workTimeManager": {"projectName": "Nom du projet: ", "msgInput": "Veuillez entrer", "applicant": "Employé: ", "applicationDate": "Date d'entrée en service: ", "approvalDate": "Date d'approbation: ", "status": "Statut: ", "memberNature": "Personnel Nature: ", "company": "Entreprise: ", "export": "Exportation", "workingHourCode": "Numéro de commande d'heures de travail", "applicantName": "Nom du candidat", "applicantPhone": "Numéro de téléphone mobile du rapporteur", "consultantType": "Type de consultants", "projectNameIntern": "Nom du projet", "wbs": "WBS", "whMonths": "<PERSON>is d'heures de travail", "totalWHPerDay": "Total des heures de travail par jour", "preTotalIncome": "Total des recettes estimé", "applicationTime": "Temps de travail", "approvalTime": "Temps de traitement", "emailApprover": "Approbateur", "approvalStatus": "État d'approbation", "passed": "passé", "toBeSubmitted": "So<PERSON><PERSON><PERSON>", "toBeApproved": "En attente", "failed": "Non adopté", "abandoned": "<PERSON><PERSON><PERSON>", "canceled": "Annule", "msgCancelReimburseInfo1": "Après avoir été invalidé, le rapport du temps devient invalide et le système envoie automatiquement un courriel de notification d'invalidité à", "msgCancelReimburseInfo2": "et la personne en charge de l'approbation. Cette opération est irréversible, confirmez la poursuite de l'opération?", "cancelReimburse": "Le rapport du temps perdu", "msgCancelSuccess": "Démantèlement avec succès!"}, "bkAr": {"invoiceHeader": "<PERSON>-t<PERSON><PERSON> de la facture", "companyName": "<PERSON><PERSON><PERSON>", "companyAddr": "<PERSON><PERSON><PERSON>", "companyTel": "Tél.", "companyEmail": "<PERSON><PERSON><PERSON>", "companyGst": "GST No.", "companyQst": "QST No.", "companyGstHst": "GST/HST", "companyPst": "PST (QST/--)", "invoiceNo": "N° de facture", "currency": "Monnaie", "date": "Date de création", "dueDate": "Date d'échéance", "fixedDate": "Date fixe", "afterDays": "Après les jours", "customerInfo": "Informations sur les clients", "billToCompany": "Facture à l'entreprise", "billToReceiver": "Facture à la N° de récepteur", "billingStreet": "Rue", "billToCity": "Ville", "billToProvince": "Province", "billToZip": "Facture à l'adresse hostale", "billToTel": "Facture au tél.", "billToEmail": "Facture au Courriel", "shipToSameAddr": "Envoyer à la même adresse", "shipToCompany": "Envoyer à l'entreprise", "shipToReceiver": "Envoyer au récepteur", "shipToStreet": "Envoyer à la Rue", "shipToCity": "Envoyer à la Ville", "shipToProvince": "Envoyer à la Province", "shipToZip": "Envoyer à l'Adresse Postale", "shipToTel": "Envoyer au Tél.", "shipToEmail": "Envoyer au Courriel", "itemsDetail": "Détail de l'article", "itemNo": "#", "modelNumber": "PRODUCIT/SERVICE", "to": "À", "description": "DESCRIPTION", "accountingCategory": "CATÉGORIE DE LA COMPTABILITÉ", "payMethod": "Mode de paiement", "qty": "QTY", "unitPrice": "PRIX UNITAIRE", "total": "<PERSON><PERSON> net", "type": "Type", "bankAccount": "Compte bancaire", "amountAndTax": "Montant et impôt", "amount": "MONTANT NET", "amountRule": "<PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON>", "discount": "<PERSON><PERSON><PERSON>", "totalTaxable": "Total imposable", "tps": "TPS/TVH", "tvq": "TVQ", "tvp": "TVP", "totalTax": "Total de l'impôt", "totalCad": "Total(CAD)", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "balance": "Solde", "accountAndType": "Compte et type", "invoiceComment": "Commentaires", "createArInvoice": "<PERSON><PERSON>er une facture", "viewInvoice": "Consulter la facture", "preview": "Prevue", "create": "c<PERSON><PERSON>", "postingDate": "Date de publication", "totalFee": "Total", "msgReferenceNoExisted": "Le numéro de référence existe déjà!", "msgReferenceCheckFail": "La vérification de la répétition du numéro de référence a échoué. Veuillez réessayer!", "from": "À partir de", "addItemBtnTxt": "Ajouter un article", "billingDoc": "Billing Doc", "referenceNo": "Réfé<PERSON><PERSON> de la facture"}, "esUpload": {"bankAccount": "Compte bancaire", "bankMonth": "Période de déclaration", "buttonSubmited": "<PERSON><PERSON><PERSON>", "buttonEdit": "É<PERSON>er"}, "bankInfo": {"bankName": "Banque", "bankAccount": "Compte bancaire", "accountType": "Type de banque", "currency": "Monnaie", "coa": "CoA", "operation": "Action", "createBankTitle": "<PERSON><PERSON>er une nouvelle banque", "editBankTitle": "Modifier les informations bancaires", "msgPhrSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "authorizaiton": "Autorisation", "fetchFrom": "Rechercher à partir de"}, "esMain": {"date ": "Date", "description": "Description", "inOutType": "Entrée/sortie", "amount": "<PERSON><PERSON>", "bankAccount": "Compte bancaire", "statementPeriod": "Période de déclaration", "cashIn": "Encaissement", "cashOut": "Sorties de fonds", "createCashStatementTitle": "<PERSON><PERSON>er un tableau de financement", "editCashStatementTitle": "Modifier le détail du tableau de financement", "cashStatement": "Le tableau de financement", "withdrawal": "retrait", "withdraw": "retirer", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postingDate": "Date de publication", "reference": "Référence", "payerPayee": "Payeur/bénéficiaire", "debit": "<PERSON><PERSON><PERSON>r", "debitManu": "Débit", "credit": "<PERSON><PERSON><PERSON>", "creditManu": "Crédit", "sendSapStatus": "État des sèves", "viewInvoice": "Voir la facture", "invoiceDetail": "<PERSON><PERSON><PERSON> de la facture", "balance": "Solde", "chargeFee": "Frais de facturation", "chargeCoa": "CoA de facturation", "reasonMsg": "Code de raison", "currency": "Monnaie", "brType": "Type", "transactionDate": "Date de la transaction", "delWarn": "La transaction ne peut pas être restaurée après la suppression.", "reconcileGL": "Reconcile G/L", "autoReconcile": "Auto Reconcile", "autoReconcileOn": "Auto Reconcile On", "autoReconcileOff": "Auto Reconcile Off"}, "chartOfAccount": {"coa": "CoA", "btnLoadCoa": "Chargement du référentiel CoA", "btnReloadCoa": "<PERSON><PERSON><PERSON> le référentiel CoA", "fieldCode": "Code du champ", "account": "Code du compte", "accountDes": "Description", "alias": "Nom du compte", "operation": "Action", "fullCoa": "Dépôt de la CoA", "createCoaTitle": "<PERSON><PERSON><PERSON> un plan comptable", "editCoaTitle": "Modifier le plan comptable", "msgCoaDelete01": "Compte avec description: ", "msgCoaDelete02": " sera supprimé!", "msgCoaPause01": "Compte avec description: ", "msgCoaPause02": " sera désactivé!", "msgCoaActive01": "Compte avec description: ", "msgCoaActive02": " sera réactivé!", "yes": "O<PERSON>", "no": "Non", "groupName": "Groupe"}, "taxCalculation": {"msgWarning": "La méthode de calcul du taux d'imposition fournie automatiquement par le système ne sert qu'à faciliter l'utilisation et ne garantit pas une exactitude totale. Les utilisateurs doivent vérifier manuellement l'exactitude du calcul du taux de change au cours du processus d'émission de la facture.", "provinceName": "Nom de la province", "provinceCode": "Code de la province", "applicableSalesTax": "Taxe de vente applicable", "total": "Total"}, "spotCurrency": {"msgWarning": "Le taux de change publié par la banque.", "date": "Date", "currency": "Monnaie(USD/CAD)", "operation": "Action", "createTitle": "<PERSON><PERSON><PERSON> un taux de change", "editTitle": "Modifier le taux de change", "selectTime": "Veuillez choisir la date", "msgInputRate": "Veuillez saisir le taux de change"}, "connectivity": {"sourceHolder": "Veuillez <PERSON>", "source": "À partir de", "username": "Nom d'utilisateur", "password": "Mot de passe", "startDate": "Date de démarrage", "endDate": "Date de fin", "powerAutomate": "automatiser la puissance", "server": "Ser<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "emailHost": "<PERSON><PERSON><PERSON>", "disk_secret": "Secret", "type": "Taper", "disk_app_id": "ID d'application", "disk_key": "Clé"}, "gl": {"search": "Rechercher Narration / Entrée de journal", "glNo": "#", "narration": "Narration", "module": "<PERSON><PERSON><PERSON>", "journalEntry": "Entrée de journal", "createDate": "Date de création", "total": "Total", "totalDebit": "Total Débit", "totalCredit": "Total Crédit", "to": " ", "readonly": "Détail G/L", "edit": "Editer G/L", "create": "<PERSON><PERSON><PERSON>", "descriptionxxx": "Description, xxx", "post": "Poste", "date": "Date:", "totalCol": "Total ", "minFee": "Frais minimum", "maxFee": "Frais maximum", "operation": "Action", "postingDate": "Date de publication", "draftDate": "Date du projet", "currency": "Monnaie", "itemNo": "#", "description": "DESCRIPTION", "debit": "DÉBIT", "credit": "CRÉDIT", "saveDraft": "Sauvegarder le projet", "glAccount": "COMPTE G/L", "createStartDate": "Démarrage", "createEndDate": "Fin", "status": "Statut", "sapStatus": "État des sèves", "editGl": "Editer G/L", "delGl": "Supprimer le G/L", "viewGl": "Voir le détail du G/L", "draft": "Brouillon", "posted": "<PERSON><PERSON><PERSON><PERSON>", "failure ": "Échec", "msgTotalDebitCannotNull": "Le débit total ne peut pas être nul, veuillez vérifier à nouveau.", "msgTotalCreditCannotNull": "Le crédit total ne peut être nul, veuillez vérifier à nouveau", "msgNotMatch": "Le total du crédit ne correspond pas au total du débit, veuillez vérifier à nouveau.", "msgNumberLimited": "La plage de numéros d'entrée est limitée à 0-13 chiffres.", "msgGlDelete": "Ce G/L sera supprimé !", "transactionCurrency": "monnaie locale - ", "status0": "Brouillon", "status1": "<PERSON><PERSON>", "status2": "Renversé", "format": "フォーマットエラー"}, "glEntry": {"totalDebit": "TOTAL DÉBIT", "totalCredit": "TOTAL CRÉDIT", "msgAtLeastOne": "Le G/L doit contenir au moins un [ Article ]", "transactionCurrency": "monnaie locale - "}, "bkSupplier": {"company": "Entreprise", "tel": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "receiver": "Fournisseur", "receiver01": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Bureau)", "supplierAddr": "Personne à contacter", "address": "<PERSON><PERSON><PERSON>", "address01": "<PERSON><PERSON><PERSON> (Bureau)", "street": "Rue", "city": "Ville", "province": "Province", "country": "Pays", "postalCode": "Code postal", "expenseAccount": "<PERSON><PERSON><PERSON>", "operation": "Action", "createReceiverTitle": "<PERSON><PERSON><PERSON> un fournis<PERSON>ur", "editReceiverTitle": "Modifier les détails du contact", "msgPhrSelect": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "sameAsOffice": "Identique à celui du bureau"}, "taxInfo": {"companyLogo": "Logo", "companyName": "Nom", "companyAddress": "<PERSON><PERSON><PERSON>", "companyEmail": "<PERSON><PERSON><PERSON>", "companyPhone": "Tél.", "gstNo": "TPS / HST No.", "qstNo": "QST / PST No.", "limit": "Remarque : seuls les fichiers jpg/png peuvent être téléchargés, dans la limite de 2M.", "editLogoBtn": "Modifier le logo", "rmLogoBtn": "Supprimer le logo"}, "account": {"userId": "<PERSON><PERSON><PERSON>", "password": "Mot de passe", "cate": "<PERSON><PERSON><PERSON><PERSON>", "role": "R<PERSON><PERSON>", "add": "Ajouter un utilisateur"}, "taxRates": {"countryNotSet": "Please set the Country and/or Province of the vendor(or customer)"}, "ApComponents": {"notpaid": "NON PAYÉ", "bank": "Compte Débit Bancaire", "credit": "<PERSON><PERSON>", "cashpaid": "CASH", "check": "Chèque", "confirm": "Confirm Reversing Date", "confirm2": "Cette opération annulera également le rapprochement.", "confirm3": "Veuillez d'abord annuler le rapprochement bancaire", "referenceError": "<PERSON>um<PERSON><PERSON> de réf<PERSON><PERSON><PERSON> du<PERSON>", "contactFirstUsing": "Le nouveau renseignement commercial doit mettre à jour les informations bancaires.", "atleast": "La facture doit contenir au moins un [ Article ]", "mustone": "La facture ne doit contenir qu'un seul [ Article ]", "lackNetAmount": "Impossible de créer une facture sans [ Montant net ]", "notEqual": "Le montant net plus la taxe n'est pas égal au total", "success": "<PERSON><PERSON><PERSON>", "referenceNo": "Numéro de référence dans les reçus.", "spotCurrency": "La devise au comptant est {rate} sur {date}", "contactAdmin": "Veuillez contacter l'administrateur", "NewBP": "Ajouter un nouveau partenaire", "subtotal": "Sous-total d'impôt ", "taxableSubtotal": "Total partiel d'impôt ", "total": "TOTAL CAD", "difference": "<PERSON>ff<PERSON><PERSON><PERSON>", "drag": "Faites glisser le fichier ici, ou cliquez sur Télécharger", "uploading": "Télécharger", "sizeExceed": "Note : la taille du fichier est limitée à 10Mb.", "MEAT": "CHAMPS MÉTA", "oriDoc": "Document original", "download": "Télécharger", "auto": "Mode automatique", "exempt": "Exonération fiscale", "amount": "MONTANT TOTAL", "netAmount": "<PERSON><PERSON> net", "GST": "TPS/TVH", "QST": "TVQ", "PST": "TVP", "search": "Rechercher un émetteur", "pay": "Payer", "uploadConfirm": "Le document original sera remplacé!", "wbs": "WBS", "costCenter": "centre de coûts", "costObject": "Objet de coût", "internalOrder": "Ordre interne", "profitCenter": "Centre de profit", "payableGL": "G/L payable", "testGetPgoTokenSuccess": "Obt<PERSON>r le succès du jeton Pgo", "testGetPgoTokenFail": "Échec de l'obtention du jeton Pgo"}, "ArComponents": {"inavailable": "Le fichier de la facture n'est pas disponible.", "notExist": "Le client n'existe pas, vous voulez ajouter un nouveau client ?", "postingDt": "Date d'enregistrement", "billTo": "Facturer à", "shipTo": "Envoyer à", "netAmount": "<PERSON><PERSON> net", "shipping": "Expédition", "discount": "Décompte", "GST": "TPS/TVH", "QST": "TVQ", "PST": "TVP", "USD": "TOTAL USD", "CAD": "TOTAL CAD", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "balance": "Solde", "JE": "Entrées du journal", "SAP": "SAP Document", "file": "Fichier de factures", "download": "Télécharger", "cancel": "Annuler", "convert": "Pay by Cash", "confirmConvert": "Original invoice will be removed and cannot be recovered."}, "PeriodicalBooking": {"notFound": "DUMMY n'a pas été trouvé !"}, "EstatementTable": {"selectBank": "Veuillez sélectionner une banque", "required": "Champ Compte bancaire obligatoire # ", "inovice": "Facture", "fetch": "<PERSON><PERSON><PERSON>", "upload": "Télécharger", "acceptAll": "Accepter tout", "autoOn": "Le rapprochement automatique est activé. Cliquez dessus pour accepter le rapprochement.", "autoOff": "Le rapprochement automatique est désactivé. Cliquez dessus pour activer le rapprochement automatique.", "reject": "Cliquez sur ce bouton pour rejeter le rapprochement automatique recommandé et passer à la page de rapprochement manuel.", "RP": "RP [Achats réguliers]", "RS": "RS [Ventes régulières]", "PR": "PR [Remboursement d'achat]", "SR": "SR [Remboursement des ventes]", "FT": "FT [Transfert de fonds]", "FX": "FX [Échange de fonds] ", "PY": "PY [<PERSON><PERSON>]", "BC": "EE [Express Entry]", "SP": "SP [Stripe]", "PP": "PP [Prepayment]", "BCMessage": "Invoice will be created and reconciled, please select vendor (or customer) and G/L account.", "GLMessage": "Please choose Type and Accounting Category.", "integrationEEbrTitle": "Express Entry", "account": "Compte bancaire", "cancel": "Annuler", "OK": "OK", "uploadES": "Télécharger le fichier de relevé électronique", "reconcileAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "payoutIdMsgInput": "Please enter payoutId", "confirm": "Confirmer", "updateChargeFee": "Frais de facturation"}, "RcInfoTable": {"account": "Compte bancaire", "date": "Date de paiement", "desc": "Description de l'opération", "debit": "Débit", "credit": "Crédit", "balance": "Solde", "invoiceNo": "N° de facture", "comments": "Commentaires", "paymentDt": "Date de paiement", "fee": "Total des frais", "amount": "Montant BR", "brDoc": "BR Document"}, "CustomerForm": {"USA": "Code postal des États-Unis : 12345 ou 12345-1234", "CA": "Code postal canadien : H1H 2H2"}, "PyComponents": {"total": "Total"}, "FileUploadResult": {"failed": "Le téléchargement du fichier a échoué. Veuillez réessayer."}, "UploadFileComp": {"notPaid": "Achat non payé", "cash": "Achat au comptant", "reconcile": "<PERSON><PERSON><PERSON><PERSON>", "uploadLimit": "Limite de téléchargement de fichiers :", "select": "Veuillez sélectionner un fichier", "perfileLimit": "<PERSON><PERSON> de <PERSON> de <PERSON> :", "uploadFile": "<PERSON><PERSON><PERSON>z télécharger le fichier {type}", "success": "succès de téléchargement", "error_two_files": "Vous devez télécharger deux fichiers en même temps, dont un fichier XML", "retry": "erreurs de téléchargement [{err}]. ve<PERSON><PERSON>z réessayer", "selectBtn": "SÉLECTIONNER", "type": "Taper", "note": "Remarque: taille limite {fileSize} Mo par fichier. {fileLimit} fichier(s) en même temps."}, "mainLayout": {"setFY": "Définir la devise et l'exercice financier.", "createBP": "Créer des partenaires commerciaux.", "createChart": "<PERSON><PERSON><PERSON> un plan comptable.", "fillin": "<PERSON><PERSON><PERSON><PERSON> le solde d'ouverture.", "careteBank": "C<PERSON>er des banques.", "setup": "Veuillez suivre les étapes suivantes pour créer votre entreprise"}, "userGuide": {"setFY": "Définir la devise et l'exercice financier.", "createBP": "Créer des partenaires commerciaux.", "createChart": "<PERSON><PERSON><PERSON> un plan comptable.", "fillin": "<PERSON><PERSON><PERSON><PERSON> le solde d'ouverture.", "careteBank": "C<PERSON>er des banques."}, "userPage": {"selectUser": "<PERSON><PERSON> sélectionner un utilisateur", "success": "<PERSON><PERSON><PERSON>", "error": "erreur", "companyWithDesc": "Entreprise avec description :", "companyCd": "Code de l'entreprise", "companyNm": "Nom de l'entreprise", "AddAll": "Ajouter toutes les entreprises", "cdnm": "Company Code/Name"}, "ApInvoiceFormPdf": {"invoice": "fichier de factures", "fetch": "Récupérer des informations", "notPaid": "Non payé", "partialPaid": "<PERSON><PERSON> partiel<PERSON>", "paid": "Payée", "reversed": "ren<PERSON><PERSON>"}, "ApUploadInvoice": {"upload": "Téléchargement d'un fichier de facture", "OCR": "Procédure d'analyse OCR", "file": "<PERSON><PERSON>er sélectionné :", "fillError": "Le fichier de la facture n'est pas disponible.", "analyzing": "Analyse ( via {type} ) ...", "error": "<PERSON><PERSON><PERSON>", "confirm": "Confirmer"}, "ArInvoiceHistory": {"invoice": "Le fichier de la facture n'est pas disponible", "Send Invoice": "Envoyer la facture"}, "ReconciliationDetails": {"title": "Bank Reconciliation Detail", "invoiceNo": "N° de facture", "select": "<PERSON><PERSON><PERSON><PERSON> sélectionner la facture", "search": "Recher<PERSON> le solde", "totals": "Totaux ${amt}, Montants sélectionnés ${amt2}, Diff. ${amt3}.", "reconcile": "<PERSON><PERSON><PERSON><PERSON>", "manual": "Réconciliation manuelle", "manualIntegration": "Réconciliation manuelle Pour Integration", "payerAndPayee": "Rechercher Payer/Payee"}, "AccountDescription": {"importAll": "Voulez-vous importer tous les Coa du groupe sélectionné ?", "add": "V<PERSON><PERSON>z-vous ajouter une nouvelle sous-catégorie ?", "primary": "primaire", "create": "Veuillez créer un plan comptable.", "step1": "Étape 1 : <PERSON><PERSON> les informations", "next": "Etape suivante", "step2": "Étape 2 : <PERSON><PERSON><PERSON> sur le bouton", "step3": "Étape 3 : C<PERSON>z sur le lien Sélectionner", "got": "L'information obtenu", "importGroup": "Importer le groupe", "pause": "Pause", "active": "Actif", "copy": "<PERSON><PERSON><PERSON>", "cdnm": "Code de compte ou nom"}, "bankInformation": {"BOM": "Banque de Montréal", "CIBC": "Banque Canadienne Impériale de Commerce", "NBC": "Banque Nationale du Canada", "RBC": "Banque Royale du Canada", "Desjardins": "<PERSON><PERSON>", "TDB": "Banque Toronto-Dominion", "ASPIRE": "Banque Aspire", "ICBC": "Industrial And Commercial Bank Of China", "BONJ": "Bank of NanJing", "SANTANDER": "Santander Bank", "BASEINET": "BASEinet Bank", "MONEX": "Monex Bank", "BBVA": "BBVA Bank", "email": "Veuillez définir votre adresse électronique.", "create": "Veuillez créer des banques."}, "ContactCustomer": {"create": "Veuillez créer des partenaires commerciaux.", "got": "L'information obtenu"}, "SettingConnectivities": {"importBilling": "Importer la facturation", "import": "Importer", "save": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "pass": "mot de passe de messagerie", "channel": "Intégration omnicanal", "SMTP": "Société SMTP", "Netdist": "Distillateur net"}, "TaxInformation": {"removed": "succès pour enlever le logo de l'entreprise", "error": "<PERSON><PERSON>ur de téléchargement", "recorrrect": "<PERSON><PERSON><PERSON><PERSON> saisir le `{msg}` correctement, ex : {desc}", "phoneNo": "veuil<PERSON><PERSON> saisir le numéro de téléphone correctement. ex : **********", "FY": "Veuillez définir la devise et l'année fiscale. "}, "reports": {"balanceSheet": "Bilan", "trialBalance": "Balance de vérification", "cashFlowStatement": "État des flux de trésorerie", "purchaseReport": "Rapport d'achat", "incomeStatement": "État des résultats", "salesReport": "Rapport de ventes", "apReport": "Rapport des fournisseurs", "arReport": "Rapport des clients", "mxElectronicAccountingCoaReport": "Electronic Accounting - COA", "mxElectronicAccountingTrialBalanceReport": "Electronic Accounting - Trial Balance", "mxInformativeDeclarationReport": "Informative Declaration of Operations with Third Parties (DIOT)", "mxValueAddedTaxReport": "Value Added Tax", "mxColumnSeqNo": "Seq. No.", "mxColumnGlAccountDetails": "G/L Account Details", "mxColumnGlAccount": "G/L Account", "mxColumnStartingBalanceAmount": "Starting Balance Amount", "mxColumnDebitAmount": "Debit Amount", "mxColumnCreditAmount": "Credit Amount", "mxColumnEndingBalanceAmount": "Ending Balance Amount", "mxColumnSatCode": "SAT Code", "mxColumnGlAccountDescription": "G/L Account Description", "mxColumnLevel": "Level", "mxColumnNatureOfAccount": "Nature Of Account", "mxDiotColumn1": "Third party type", "mxDiotColumn2": "Tipo de operación", "mxDiotColumn3": "RFC", "mxDiotColumn4": "Tax identification number", "mxDiotColumn5": "Foreigner's name", "mxDiotColumn6": "Country or jurisdiction of tax residence", "mxDiotColumn7": "Specify place of tax jurisdiction", "mxDiotColumn8": "Total value of paid events or activities / Paid events or activities in the northern border region", "mxDiotColumn9": "Refunds, discounts and bonuses / Paid events or activities in the northern border region", "mxDiotColumn10": "Total value of paid events or activities / Paid events or activities in the southern border region", "mxDiotColumn11": "Refunds, discounts and bonuses / Paid events or activities in the southern border region", "mxDiotColumn12": "Total value of paid events or activities / Total events or activities paid at the 16% VAT rate", "mxDiotColumn13": "Refunds, discounts and bonuses / Total events or activities paid at the rate of 16% VAT", "mxDiotColumn14": "Total value of acts or activities paid / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn15": "Refunds, discounts and bonuses / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn16": "Total value of acts or activities paid / Acts or activities paid in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn17": "Refunds, discounts and bonuses / Acts or activities paid for the import of intangible goods and services at the rate of 16% VAT", "mxDiotColumn18": "Exclusively for taxed activities / Acts or activities paid in the northern border region", "mxDiotColumn19": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn20": "Exclusively for taxed activities / Acts or activities paid in the southern border region", "mxDiotColumn21": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn22": "Exclusively from taxable activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn23": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn24": "Exclusively for taxed activities / Acts or activities paid for in the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn25": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn26": "Exclusively for taxed activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn27": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn28": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn29": "Associated with activities that do not meet requirements / Paid events or activities in the northern border region", "mxDiotColumn30": "Associated with exempt activities / Paid acts or activities in the northern border region", "mxDiotColumn31": "Associated with non-object activities / Paid acts or activities in the northern border region", "mxDiotColumn32": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn33": "Associated with activities that do not meet requirements / Paid events or activities in the southern border region", "mxDiotColumn34": "Associated with exempt activities / Paid events or activities in the southern border region", "mxDiotColumn35": "Associated with non-object activities / Paid acts or activities in the southern border region", "mxDiotColumn36": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn37": "Associated with activities that do not meet requirements / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn38": "Associated with exempt activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn39": "Associated with non-object activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn40": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn41": "Associated with activities that do not comply with requirements / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn42": "Associated with exempt activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn43": "Associated with non-object activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn44": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn45": "Associated with activities that do not comply with requirements / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn46": "Associated with exempt activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn47": "Associated with non-object activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn48": "VAT withheld by the taxpayer", "mxDiotColumn49": "Acts or activities paid for in the importation of goods and services for which VAT is not paid (Exempt)", "mxDiotColumn50": "Paid acts or activities for which VAT will not be paid (Exempt)", "mxDiotColumn51": "Other acts or activities paid at the 0% VAT rate", "mxDiotColumn52": "Acts or activities not subject to VAT carried out in national territory", "mxDiotColumn53": "Acts or activities not subject to VAT due to not having an establishment in national territory", "mxDiotColumn54": "I declare that fiscal effects were given to the receipts that support the operations carried out with the supplier", "closingBalance": "Solde de clôture", "itemsDetail": "Détail des articles", "startDate": "Date de début", "experationDate": "Date d'expiration", "company": "Société", "targetCurrency": "<PERSON><PERSON> cible", "transactionCurrency": "Devise de transaction", "COAAccount": "Compte du plan comptable", "refresh": "Actualiser", "category": "<PERSON><PERSON><PERSON><PERSON>", "accountCode": "Code de compte", "nameGLCategory": "Nom de la catégorie G/L", "endDebit": "Débit final", "endCredit": "Crédit final", "all": "Tous", "endingAt": "Se terminant le", "subTotal": "Sous-total", "updatedOn": "Mis à jour le", "postingDate": "Date de publication", "documentNo": "Numéro de document", "invoiceNo": "Numéro de facture", "supplier": "Fournisseur", "customer": "Client", "netAmount": "<PERSON><PERSON> net", "currency": "<PERSON><PERSON>", "PST": "TPS", "GST": "TVA/GST", "QST": "TVQ", "VAT": "VAT", "Discount": "Discount", "IsrWithholding": "ISR Withholding", "VatWithholding": "VAT Withholding", "totalTaxAmount": "Montant total des taxes", "totalAmount": "Montant total", "invoiceAmount": "<PERSON><PERSON> de la facture", "payment": "Paiement", "totalDue": "Total dû", "businessPartner": "Partenaire commercial", "invoiceReference": "Réfé<PERSON><PERSON> de la facture", "transactionType": "Type de transaction", "current": "Actuel", "31To60": "31 à 60", "61To90": "61 à 90", "91+": "91+", "contactName": "Nom du contact", "narration": "Narration", "cashFlowName": "Éléments de flux de trésorerie", "cashFlowMonth": "Mois en cours", "cashFlowYear": "Année en cours", "vatChargeable": "VAT chargeable", "valueOfActsOrActivitiesTaxedAtTheRateOf16": "Value of acts or activities taxed at the rate of 16%", "valueOfActsOrActivitiesTaxedAtThe0ExportRate": "Value of acts or activities taxed at the 0% export rate", "valueOfActsOrActivitiesTaxedAtTheRateOf0Others": "Value of acts or activities taxed at the rate of 0% others", "sumOfTheTaxedActsOrActivities": "Sum of the taxed acts or activities", "valueOfActsOrActivitiesForWhichTaxIsNotPayable": "Value of acts or activities for which tax is not payable (exempt)", "valueOfActsOrActivitiesNotSubjectToTax": "Value of acts or activities not subject to tax", "vatChargeableAtTheRateOf16": "VAT chargeable at the rate of 16%", "vatCharged": "VAT charged", "updatedAmountToBeReimbursedDerivedFromTheAdjustment": "Updated amount to be reimbursed derived from the adjustment", "totalVatDue": "Total VAT due", "btCapture": "Capture", "vatCreditable": "VAT creditable", "amountOfPaidEventsOrActivities": "Amount of paid events or activities", "totalActsPaid16Percent": "Total of acts or activities paid at the 16% VAT rate", "totalActsPaidImport16Percent": "Total of acts or activities paid for in the import of goods and services at the 16% VAT rate", "totalActsPaid0Percent": "Total of other acts or activities paid at the 0% VAT rate", "totalPaidActsExempt": "Total of paid acts or activities for which VAT will not be paid (exempt)", "determinationCreditableVAT": "Determination of the creditable Value Added Tax", "vatOnActsPaid16Percent": "VAT on acts or activities paid at the rate of 16%", "vatOnImportPaid16Percent": "VAT on acts or activities paid on the import of goods and services at the rate of 16%", "totalVATTransferred": "Total VAT transferred to the taxpayer (Effectively paid)", "updatedCreditableAmount": "Updated creditable amount to increase derived from the adjustment", "totalCreditableVAT": "Total creditable VAT", "determination": "Determination", "vatWithheld": "VAT withheld", "totalCreditableVat": "Total creditable VAT", "otherAmountsPayableByTheTaxpayer": "Other amounts payable by the taxpayer", "otherAmountsInFavorOfTheTaxpayer": "Other amounts in favor of the taxpayer", "amountDue": "Amount due", "creditingOfTheBalanceInFavorOfPreviousPeriods": "Crediting of the balance in favor of previous periods (Without exceeding the amount due)", "taxDue": "Tax due", "amountToBeDetailed1": "Amount to be detailed", "interestChargedAtRate16": "Interest charged at a rate of 16%", "royaltiesBetweenRelatedPartiesAtRate16": "Royalties between related parties at the rate of 16%", "otherActsOrActivitiesTaxedAtRate16": "Other acts or activities taxed at the rate of 16%", "amountToBeDetailed2": "Amount to be detailed", "agriculturalLivestockForestryFishingActivitiesTaxedAtRate0": "Agricultural, livestock, forestry or fishing activities taxed at a rate of 0%", "otherActsOrActivitiesTaxedAtRate0": "Other acts or activities taxed at a rate of 0%", "amountToBeDetailed3": "Amount to be detailed", "alienationOfLandAndBuildingsForResidentialHousing": "Alienation of land and buildings attached to the land, intended or used for residential housing", "saleOfBooksNewspapersMagazinesNotByTaxpayer": "Sale of books, newspapers and magazines (not published by the taxpayer)", "royaltiesChargedByAuthors": "Royalties charged by authors", "disposalOfUsedMovablePropertyExceptByCompanies": "Disposal of used movable property, except those disposed of by companies", "alienationOfLotteryTicketsAndReceipts": "Alienation of tickets and other receipts from lotteries, raffles, drawings or games with bets and contests of all kinds", "teachingServices": "Teaching services", "publicLandTransportationServiceForPeople": "Public land transportation service for people", "derivativeFinancialTransactions": "Derivative financial transactions", "ticketSalesForPublicShows": "Ticket sales for public shows", "professionalMedicalServices": "Professional medical services", "temporaryUseOfRealEstateForResidentialOrFarming": "Temporary use or enjoyment of real estate for residential purposes and for farms for agricultural or livestock purposes", "otherIncomeExemptFromVat": "Other income exempt from VAT"}, "menu": {"task": "<PERSON><PERSON><PERSON>", "purchase": "<PERSON><PERSON><PERSON>", "massiveProcess": "Processus massif", "invoices": "Factures", "sales": "<PERSON><PERSON><PERSON>", "billing": "Facturation", "payroll": "<PERSON><PERSON>", "payrollRecord": "Fiche de paie", "bankReconciliation": "Rapprochement bancaire", "reconcile": "<PERSON><PERSON><PERSON><PERSON>", "history": "Historique", "generalLedger": "Grand livre", "journalEntries": "Opérations comptables", "setting": "Réglages", "contact": "Contact", "profile": "Profil", "coA": "Plan de compte", "coAMapping": "Mapping du plan de compte", "connectivities": "Connexions", "reporting": "Rapports", "reports": " Business Intelligent", "exportTrialBalance": "Reports", "help": "Aide", "userGuide": "Guide utilisateur", "FAQ": "FAQ (Foire aux questions)", "account": "<PERSON><PERSON><PERSON>", "user": "Utilisa<PERSON>ur", "dashboard": "Dashboard"}, "fileTypeList": {"salesNotPaid": "Non payé", "notPaid": "Non Payé", "salesCash": "Paiement", "cashPaid": "Cash", "ES": "Relevé bancaire", "yearEnd": "rapport de fin d'année"}, "task": {"name": "<PERSON><PERSON><PERSON>", "companyCode": "Code de la société", "companyName": "Nom de la société", "last": "<PERSON><PERSON>", "dueDate": "<PERSON><PERSON>", "estimatedHour": "<PERSON><PERSON><PERSON><PERSON> (heure)", "actualHour": "<PERSON><PERSON><PERSON> (heure)", "status": "Statut", "assignedTo": "Attribué à", "email": "E-mail", "tag": "<PERSON><PERSON><PERSON><PERSON>", "priority": "Priorité", "createTime": "Heure de création", "updateTime": "<PERSON><PERSON> de mise à jour", "action": "Action", "placeholderSearch": "Nom de la tâche de recherche", "placeholderStarting": "Démarrage", "placeholderEnding": "Fin", "placeholderMinHour": "Min heure", "placeholderMaxHour": "Heure maximale", "searchTitleEstimated": "<PERSON><PERSON><PERSON><PERSON>", "statusToDo": "À faire", "statusDoing": "En cours", "statusDone": "<PERSON><PERSON><PERSON><PERSON>", "statusDeleted": "Supprimé"}, "update": {"items": "items", "item": "item", "assigment": "ASSIGMENT", "paymentDueLater": "Payment due later", "withinDay7": "Within 7 days", "withinDay15": "Within 15 days", "withinDay30": "Within 30 days", "withinDayOther": "Fixed date", "other": "Other", "searchByBankAccount": "Search by bank account", "searchByFaq": "Search", "sapInstance": "Instance", "sapApplicationNumber": "Application Number"}}