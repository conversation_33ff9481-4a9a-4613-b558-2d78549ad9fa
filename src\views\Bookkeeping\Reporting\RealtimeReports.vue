<!-- @format -->

<script lang="ts" setup>
import {ref} from 'vue'
import BalanceSheet from '@/components/bookkeepingComponents/ReportComponents/BalanceSheet.vue'
import IncomeStatement from '@/components/bookkeepingComponents/ReportComponents/IncomeStatement.vue'
import TrialBalance from '@/components/bookkeepingComponents/ReportComponents/TrialBalance.vue'
import PurchaseReport from '@/components/bookkeepingComponents/ReportComponents/PurchaseReport.vue'
import SalesReport from '@/components/bookkeepingComponents/ReportComponents/SalesReport.vue'
import ApReport from '@/components/bookkeepingComponents/ReportComponents/ApReport.vue'
import ArReport from '@/components/bookkeepingComponents/ReportComponents/ArReport.vue'
import MxPurchaseReport from '@/components/bookkeepingComponents/ReportComponents/MxPurchaseReport.vue'
import MxSalesReport from '@/components/bookkeepingComponents/ReportComponents/MxSalesReport.vue'
import MxElectronicAccountingCoaReport from '@/components/bookkeepingComponents/ReportComponents/MxElectronicAccountingCoaReport.vue'
import MxElectronicAccountingTrialBalanceReport from '@/components/bookkeepingComponents/ReportComponents/MxElectronicAccountingTrialBalanceReport.vue'
import MxInformativeDeclarationReport from '@/components/bookkeepingComponents/ReportComponents/MxInformativeDeclarationReport.vue'
import MxValueAddedTaxReport from '@/components/bookkeepingComponents/ReportComponents/MxValueAddedTaxReport.vue'
import CashFlowStatement from '@/components/bookkeepingComponents/ReportComponents/CashFlowStatement.vue'
import {UserCompany} from '@/lib/storage'

const userCompany: any = UserCompany.get() || []
const activeKey = userCompany[0].country === 'MX' ? ref('41') : ref('1')
</script>
<template>
    <div class="report-page-wrap">
        <a-tabs v-model:activeKey="activeKey" :destroyInactiveTabPane="true">
            <a-tab-pane v-if="userCompany[0].country !== 'MX'" key="1" :tab="$t('reports.balanceSheet')">
                <div class="report-content-wrap">
                    <BalanceSheet></BalanceSheet>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country !== 'MX'" key="2" :tab="$t('reports.incomeStatement')">
                <div class="report-content-wrap">
                    <IncomeStatement></IncomeStatement>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country !== 'MX'" key="3" :tab="$t('reports.trialBalance')">
                <div class="report-content-wrap">
                    <TrialBalance></TrialBalance>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country !== 'MX'" key="4" :tab="$t('reports.purchaseReport')">
                <div class="report-content-wrap">
                    <PurchaseReport></PurchaseReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country !== 'MX'" key="5" :tab="$t('reports.salesReport')">
                <div class="report-content-wrap">
                    <SalesReport></SalesReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country !== 'MX'" key="6" :tab="$t('reports.apReport')">
                <div class="report-content-wrap">
                    <ApReport></ApReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country !== 'MX'" key="7" :tab="$t('reports.arReport')">
                <div class="report-content-wrap">
                    <ArReport></ArReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].code == '2605'" key="8" :tab="$t('reports.cashFlowStatement')">
                <div class="report-content-wrap">
                    <CashFlowStatement></CashFlowStatement>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country === 'MX'" key="41" :tab="$t('reports.purchaseReport')">
                <div class="report-content-wrap">
                    <MxPurchaseReport></MxPurchaseReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country === 'MX'" key="51" :tab="$t('reports.salesReport')">
                <div class="report-content-wrap">
                    <MxSalesReport></MxSalesReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country === 'MX'" key="9" :tab="$t('reports.mxElectronicAccountingCoaReport')">
                <div class="report-content-wrap">
                    <MxElectronicAccountingCoaReport></MxElectronicAccountingCoaReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country === 'MX'" key="10" :tab="$t('reports.mxElectronicAccountingTrialBalanceReport')">
                <div class="report-content-wrap">
                    <MxElectronicAccountingTrialBalanceReport></MxElectronicAccountingTrialBalanceReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country === 'MX'" key="11" :tab="$t('reports.mxInformativeDeclarationReport')">
                <div class="report-content-wrap">
                    <MxInformativeDeclarationReport></MxInformativeDeclarationReport>
                </div>
            </a-tab-pane>
            <a-tab-pane v-if="userCompany[0].country === 'MX'" key="12" :tab="$t('reports.mxValueAddedTaxReport')">
                <div class="report-content-wrap">
                    <MxValueAddedTaxReport></MxValueAddedTaxReport>
                </div>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<style lang="scss" scoped>
.report-page-wrap {
    width: 100%;
    height: 100%;
    .ant-tabs {
        height: 100%;
        :deep(.ant-tabs-content) {
            height: 100%;
        }
    }
}
.report-content-wrap {
    background-color: #fff;
    height: 100%;
    border-radius: 12px;
}
</style>
