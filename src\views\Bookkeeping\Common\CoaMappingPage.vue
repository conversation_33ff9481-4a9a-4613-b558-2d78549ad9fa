<!-- @format -->

<script lang="ts" setup>
import CoaMappingComponentVue from '@/components/bookkeepingComponents/CommonComponents/CoaMappingComponent.vue'
</script>
<template>
    <div class="page-container-customer">
        <CoaMappingComponentVue />
    </div>
</template>
<style lang="scss" scoped>
/** @format */

.page-container-customer {
    max-height: 70vh; /* 设置容器的最大高度为当前视口高度的70%（根据需要进行调整）*/
    border-radius: 10px;
    background-color: #fff;
    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 24px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 90px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button + .search-button {
                min-width: 113px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
        }
    }
    .invoice-add {
        width: 100%;
        margin-top: 8px;
        border-radius: 2px;
        border-style: dashed;
    }

    .history-page-content {
        padding: 12px 20px;

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
    .ap-invoice-footer {
        margin-top: 20px;
        text-align: right;

        .cancel-button {
            border-color: #004fc1;
            color: #004fc1;
        }

        .ant-btn + .ant-btn {
            margin-left: 12px;
        }
    }
    .table-input {
        width: 200px;
    }
}
</style>
