<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import ProductServiceForm from '@/components/bookkeepingComponents/CommonComponents/ProductServiceForm.vue'
import {computed, onMounted, ref} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useRouter} from 'vue-router'
import {SearchOutlined, PlusOutlined} from '@ant-design/icons-vue'
import * as _ from 'lodash'
import {UserCompany} from '@/lib/storage'
const userCompany: any = UserCompany.get() || []
const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const itemList = ref([] as any[])
const searchForm = ref({
    product_service: '',
})

const isFirst = ref(false)
const current = ref({})
const editMode = ref(false)
const show = ref(false)
const tableLoading = ref(false)
const currentPageNumber = ref(1)
const pageSize = ref(10)
const timer = ref()
// mapActions
const fetchProductServiceList = (payload?: any) => store.dispatch('ProductServiceStore/fetchProductServiceList', payload)

// computed mapState
const productServiceList = computed(() => store.state.ProductServiceStore.ProductServiceList)
const combineTableData = computed(() => {
    const table = [...productServiceList.value]
    const sortedTableByUpdateTime = table.sort((a, b) => {
        return a.update_time < b.update_time ? 1 : -1
    })

    console.log('sortedTableByUpdateTime', sortedTableByUpdateTime)
    return sortedTableByUpdateTime
})
const totalNumber = computed(() => store.state.ProductServiceStore.totalNumber)
const modalTitle = computed(() => {
    let title = ''
    title = i18n.t('productService.createTitle')
    if (editMode.value) {
        title = i18n.t('productService.editTitle')
    }
    return title
})

const updateData = async () => {
    const {product_service: name} = searchForm.value
    const searchObj: any = {
        $limit: pageSize.value,
        $skip: (currentPageNumber.value - 1) * pageSize.value,
    }
    searchObj['$sort[create_time]'] = 'desc'
    if (name) {
        searchObj['product_service[$like]'] = `%${name}%`
    }
    searchObj['company_code'] = userCompany[0].code
    try {
        tableLoading.value = true
        await fetchProductServiceList({...searchObj})
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const add = () => {
    show.value = true
    editMode.value = false
    current.value = {}
}
const edit = (record: any) => {
    show.value = true
    editMode.value = true
    current.value = {...record}
}

const currentRowThing = (record: any) => {
    return {
        onClick: () => {
            edit(record)
        },
    }
}

const showDialog = (bool: any) => {
    show.value = bool
}
const dismiss = () => {
    showDialog(false)
    editMode.value = false
}
const search = async () => {
    currentPageNumber.value = 1
    await updateData()
}
const changePage = () => {
    isFirst.value = false
    updateData()
}

onMounted(async () => {
    itemList.value = ['common', router.currentRoute.value.meta.title]
    try {
        tableLoading.value = true
        await updateData()
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }

    if (_.isEmpty(router.currentRoute.value.query) || !router.currentRoute.value.query.showTutorial) return
})
const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer.value)
    timer.value = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer.value)
        }
    }, 1000)
}
</script>
<template>
    <div class="page-container-customer" ref="mainRef">
        <div class="history-page-header">
            <div class="search-group-wrap">
                <a-input
                    v-model:value="searchForm.product_service"
                    :placeholder="i18n.t('commonTag.search') + i18n.t('bkAp.modelNumber')"
                    :disabled="tableLoading"
                    class="search-input"
                    @input="inputChange"
                    @pressEnter="search"
                >
                    <template #suffix>
                        <search-outlined />
                    </template>
                </a-input>
                <a-popover trigger="click" placement="bottom">
                    <template #content>
                        <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                            <div class="search-input-group"></div>

                            <a-button type="primary" shape="round" :disabled="tableLoading" @click="search">
                                <template #icon>
                                    <search-outlined />
                                </template>
                                {{ $t('commonTag.search') }}
                            </a-button>
                        </a-form>
                    </template>
                    <!-- chen change -->
                    <!-- <a-button class="search-button" :disabled="tableLoading">
            <template #icon>
              <svg-icon name="icon_filter"></svg-icon>
            </template>
            {{ i18n.t('commonTag.filter') }}
          </a-button> -->
                </a-popover>
            </div>
            <a-button
                type="primary"
                shape="round"
                class="add-button"
                id="add-button"
                :disabled="tableLoading"
                @click="add()"
            >
                <template #icon>
                    <plus-outlined />
                </template>
                {{ i18n.t('commonTag.new') }}
            </a-button>
        </div>

        <div class="history-page-content">
            <a-table
                :dataSource="combineTableData"
                :loading="tableLoading"
                :pagination="false"
                rowKey="id"
                :customRow="currentRowThing"
                :scroll="{y: 'calc(100vh - 300px)'}"
            >
                <a-table-column
                    align="left"
                    :title="i18n.t('bkAp.modelNumber')"
                    data-index="product_service"
                    width="160px"
                />
                <a-table-column
                    align="left"
                    :title="i18n.t('bkAp.description')"
                    data-index="description"
                    width="160px"
                />
                <!-- <a-table-column
                    align="left"
                    :title="i18n.t('bkAp.qty')"
                    data-index="address"
                    width="200px"
                    :ellipsis="true"
                >
                    <template #default="{record}">
                        <span>
                            {{ getFullAddress(record) }}
                        </span>
                    </template>
                </a-table-column> -->
                <a-table-column
                    align="left"
                    :title="i18n.t('bkAp.unitPrice')"
                    data-index="unit_price"
                    width="120px"
                    :ellipsis="true"
                />
                <a-table-column
                    align="center" 
                    :title="i18n.t('bkAp.accountingCategory')" 
                    data-index="coa" 
                    width="120px"
                />
                <!-- <a-table-column
                    align="center"
                    :title="i18n.t('bkCustomer.operation')"
                    key="operation"
                    fixed="right"
                    width="64px"
                >
                    <template #default="{record}">
                        <span>
                            <a-button
                                :title="$t('bkSupplier.editReceiverTitle')"
                                class="btn-txt"
                                type="link"
                                @click="edit(record)"
                            >
                                <edit-outlined />
                            </a-button>
                        </span>
                    </template>
                </a-table-column> -->
            </a-table>
            <div class="pagination-wrap">
                <a-pagination
                    v-model:current="currentPageNumber"
                    v-model:page-size="pageSize"
                    :disabled="tableLoading"
                    :hideOnSinglePage="false"
                    :showSizeChanger="true"
                    :total="totalNumber"
                    @change="changePage"
                />
                <span
                    >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                    {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                >
            </div>
        </div>

        <!--Create Product/Service Form Pop-up-->
        <a-modal
            :title="modalTitle"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="820"
            :bodyStyle="{padding: '10px 14px 14px'}"
            :z-index="2902"
        >
            <product-service-form
                :current-product-service="current"
                :edit-mode="editMode"
                @fetchProductServiceList="updateData"
                @dismiss="dismiss"
            ></product-service-form>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
/** @format */

.page-container-customer {
    border-radius: 10px;
    background-color: #fff;

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        height: 100%; /* 设置容器的高度为父容器高度的80% */
        overflow-y: hidden; /* 隐藏纵向滚动条 */
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 20px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 90px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button + .search-button {
                min-width: 113px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
        }
    }

    .history-page-content {
        padding: 12px 20px;
        overflow: hidden;

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
}
</style>
