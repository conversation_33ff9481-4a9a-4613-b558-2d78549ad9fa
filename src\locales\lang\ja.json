{"login": {"title": "はじめに", "logIn": "ログイン", "logOut": "ログアウト", "english": "英語", "chinese": "中国語", "language": "言語", "username": "ユーザー名", "password": "パスワード", "msgInputUsername": "ユーザー名を入力してください", "msgInputPassword": "パスワードを入力してください", "msgInputAccount": "アカウントを入力してください", "serverError": "サーバーエラー", "changePwd": "パスワードリセット", "newpassword": "パスワード", "repassword": "パスワード再入力", "pwdRule": "大文字、小文字、数字、特殊文字を少なくとも1つ含める必要があります", "pwdRule1": "スペースなしの文字を入力してください", "inconsistentPassword": "パスワードが一致しません", "repasswordInput": "パスワードを再入力してください"}, "mainPage": {"noLogin": "ログインしていません"}, "homepage": {"times1": "デジタル給与明細", "times2": "管理者版", "title": "弊社と一緒に簡単に従業員の給与支払いをオンラインで開始しましょう。", "info": "NTデジタル給与明細アプリケーションは、組織から給与までを完全にカバーし、ハイパー自動化プロセスにより人的資本価値を最大化するためのコアビジネスオペレーションを実現します。", "titleBk1": "基本操作", "titleBk2": "", "titleBk": "ハイパーオートメーションの帳簿管理", "infoBk": "当社の財務ソリューションは、北米の中小企業向けに作られており、小規模ビジネスオーナーや自営業者であれ、財務状況をよりよく整理することができます。そのため、より良いビジネス上の決定を下し、成長に集中し、管理やコンプライアンスに関するストレスを軽減することができます。", "link": "詳細を見る"}, "router": {"uploadInvoice": "一括処理", "woBillsInvoice": "手動予約", "invoiceFromPdf": "PDFからの請求書", "invoiceHistory": "リスト", "accountReceivable": "売掛金", "fullInvoice": "手動予約", "bankReconciliation": "銀行調整", "uploadStatement": "接続", "EStatementFromPdf": "CSVからのE-ステートメント", "main": "調整", "common": "設定", "customer": "顧客情報", "taxInformation": "プロファイル", "bankInformation": "銀行情報", "accountDescription": "勘定科目", "taxCalculation": "税金表", "spotCurrency": "スポット為替レート", "exchangeRate": "為替レート", "supplier": "サプライヤー", "payStubs": "給与明細書", "history": "履歴", "commonCompany": "企業", "commonAccount": "アカウント", "gl": "汎用仕訳帳", "glListing": "リスト", "glEntry": "手動仕訳", "fy": "会計年度", "localCurrency": "現地通貨"}, "commonTag": {"tip": "ヒント", "confirm": "確認", "cancel": "キャンセル", "search": "検索", "new": "追加", "save": "保存", "edit": "編集", "delete": "削除", "action": "操作", "actions": "Actions", "view": "表示", "remark": "備考", "back": "戻る", "close": "閉じる", "serial": "シリアル番号", "status": "ステータス", "submit": "送信", "send": "送信", "resend": "再送信", "download": "ダウンロード", "sendEmail": "メールを送信する", "columns": "列", "filter": "フィルター", "msgInput": "入力してください", "msgSelect": "選択してください", "reverse": "反転", "reverseAndRedo": "Reverse & Redo", "realize": "達成", "print": "打印", "wihtreverse": "逆転なし"}, "columns": {"modalTitle": "列", "title": "标题", "operation": "操作"}, "bkApInvoice": {"to": " ", "readonly": "請求書詳細を表示する", "create": "請求書作成", "edit": "請求書編集", "date": "日付：", "totalCol": "合計：", "minFee": "最低手数料", "maxFee": "最高手数料", "invoiceNo": "請求書番号", "invoiceComment": "コメント", "creator": "作成者", "createTime": "作成時間", "createDate": "作成日付", "issuerCol": "発行者：", "invoiceType": "請求書タイプ", "issuer": "発行者", "total": "合計", "type": "タイプ", "br": "BR", "status": "ステータス", "payMethod": "支払方法", "sapStatus": "SAP ステータス", "sapNotSent": "送信されません", "sapPending": "保留中", "sapSending": "送信", "sapSentSuccess": "送信成功", "sapSentFail": "送信失敗", "sapReversing": "リバース", "sapReverseSuccess": "逆転の成功", "sapReverseFail": "リバースフェイル", "referenceNo": "参照番号", "dueDate": "支払期限", "postingDate": "請求日", "balance": "残高", "operation": "操作", "checkNo": "番号", "checkPrintTime": "実行時間", "printStatus": "Print Status", "printStatus0": "Not Printed", "printStatus1": "Printed", "brStatus0": "未払い", "brStatus": "全て", "brStatus2": "支払い保留中", "parked": "仮伝票", "posted": "記帳済み", "notPaid": "未支払", "pmntApproved": "支払承認済み", "pmntExecuted": "支払実行済み", "paid": "支払済み", "partialPaid": "部分支払済み", "reversed": "取り消し済み", "captured": "スキャン", "created": "作成されました", "scan": "スキャン", "fetch": "取得", "upload": "アップロード", "del": "削除", "level": "Level", "emailList": "Email List", "approvalStatus": "Approval Status", "statusPending": "Pending", "statusApproved": "Approved", "statusRejected": "Rejected", "printApCheckWarning": "お支払いが実行されました。ご確認ください。", "issueNameEmpty": "Please input issuer name"}, "bkApUpload": {"fileName": "ファイル名", "updateTime": "更新日時", "createTime": "作成日時", "creator": "作成者", "xmlStatus": "XMLステータス", "payMethod": "支払方法", "comment": "コメント", "ocrStatus": "OCRステータス", "scanned": "スキャン済み", "nonScanned": "未スキャン", "pending": "保留中", "delFile": "ファイルを削除", "edit": "編集", "createFile": "請求書を作成", "analyzeFile": "ファイルを分析", "viewDetail": "詳細を表示", "downloadInvoice": "請求書をダウンロード", "editComment": "コメントの編集", "editCommentPlaceholder": "コメントを入力してください"}, "bkCustomer": {"company": "会社名", "tel": "電話番号", "email": "メールアドレス", "receiver": "受取人（事務所）", "receiver01": "連絡担当者", "address": "住所", "address01": "事務所住所", "street": "番地", "city": "市区町村", "province": "都道府県", "country": "国", "postalCode": "郵便番号", "expenseAccount": "経費科目", "operation": "操作", "createReceiverTitle": "顧客作成", "createAllReceiverTitle": "連絡先作成", "accountType": "タイプ", "editReceiverTitle": "連絡先詳細の編集", "msgPhrSelect": "選択してください", "officeAddress": "事務所の住所", "shippingAddress": "配送先住所", "billingAddress": "請求先住所", "general": "一般", "sameAsOffice": "事務所と同じ", "itemNo": "＃", "category": "カテゴリー", "businessKey": "ビジネスキー", "debitReceipt": "借方領収書", "creditReceipt": "貸方領収書", "save": "保存", "caterogyRule": "カテゴリを選択してください", "coaJson": "Mapping Code"}, "bkAp": {"invoiceHeader": "請求書ヘッダー", "companyName": "発行元", "companyAddr": "発行元住所", "companyTel": "発行元電話番号", "companyEmail": "発行元メールアドレス", "companyGst": "GST番号", "companyQst": "QST番号", "companyGstHst": "GST/HST", "companyPst": "PST (QST/--)", "invoiceNo": "請求書番号", "referenceNo": "請求書リファレンス", "purpose": "目的", "purposeStandard": "標準", "purposeProforma": "プロフォーマ", "purposeCreditMemo": "クレジットメモ", "purposeSubsequentCreditMemo": "クレジットメモ", "poPlaceholder": "まず目的を選択する", "purchaseOrder": "注文書", "currency": "通貨", "date": "作成日", "dueDate": "支払期限日", "fixedDate": "固定日", "afterDays": "日数後", "customerInfo": "顧客情報", "billToCompany": "請求先会社", "billTo": "請求先", "billToReceiver": "請求先受取人", "billingStreet": "請求先住所", "billToCity": "請求先市区町村", "billToProvince": "請求先都道府県", "billToZip": "請求先郵便番号", "billToTel": "請求先電話番号", "billToEmail": "請求先メールアドレス", "shipToSameAddr": "同じ住所に配送する", "shipToCompany": "配送先会社", "shipToReceiver": "配送先受取人", "shipToStreet": "配送先住所", "shipToCity": "配送先市区町村", "shipToProvince": "配送先都道府県", "shipToZip": "配送先郵便番号", "shipToTel": "配送先電話番号", "shipToEmail": "配送先メールアドレス", "itemsDetail": "商品詳細", "itemNo": "＃", "modelNumber": "商品名/サービス名", "description": "商品詳細", "payMethod": "支払い方法", "qty": "数量", "unitPrice": "単価", "total": "小計", "type": "タイプ", "bankAccount": "銀行口座", "trevenueAccount": "収益科目", "accountingCategory": "預金項目", "amountAndTax": "金額と税金", "amount": "小計", "amountRule": "小計", "shipping": "配送料", "discount": "割引", "totalTaxable": "課税対象合計", "tps": "GST/HST", "tvq": "QST", "tvp": "PST", "totalTax": "税金合計", "totalCad": "合計(CAD)", "deposit": "預り金", "balance": "残高", "accountAndType": "口座とタイプ", "invoiceComment": "コメント", "createArInvoice": "AR請求書を作成", "viewInvoice": "請求書を表示", "preview": "プレビュー", "postingDate": "請求日", "apIntegrationPostingDate": "郵送日", "create": "保存", "totalFee": "合計", "msgReferenceNoExisted": "参照番号が既に存在しています！", "msgReferenceCheckFail": "参照番号重複チェックに失敗しました。もう一度お試しください。", "from": "From", "addItemBtnTxt": "追加", "addItemBtnTxtInvoice": "請求書の追加承認", "addItemBtnTxtPayment": "支払いの追加承認", "msgCreditMemoInvoiceBeNegative": "クレジットメモタイプの請求書の金額は負の値でなければなりません。", "paymentDelayDate": "支払い日", "paymentDelaySelect": "支払い条件を選択", "originalDocument": "原本", "costObject": "コストオブジェクト", "taxCode": "税コード", "assigmentSapCostObject": "SAP Cost Object", "assigmentGlAccount": "G/L Account", "assigmentWbs": "WBS", "assigmentCostCenter": "Cost Center", "assigmentInternalOrder": "Internal Order", "assigmentProfitCenter": "Profit Center"}, "bkCommonTag": {"sapTip": "Sychronizing with SAP", "confirmation": "確認", "msgDeleteConfirm": "數據將被刪除！", "msgRequireRule": "入力してください", "msgLengthRule": "長さは", "msgEmailRule": "正しいメールアドレスを入力してください", "msgSelectRule": "请选择", "msgDeleteSelectConfirm": "選択されたPDFファイルは削除されます！"}, "bkArInvoice": {"to": "まで", "readonly": "請求書詳細を表示", "create": "請求書作成", "date": "日付：", "totalCol": "合計：", "minFee": "最小料金", "maxFee": "最大料金", "invoiceNo": "請求書", "invoiceComment": "コメント", "creator": "作成者", "createTime": "作成日時", "createDate": "作成日", "issuerCol": "発行者：", "issuer": "請求先", "total": "合計", "type": "タイプ", "br": "状態", "referenceNo": "参照番号", "dueDate": "期日", "postingDate": "請求日", "balance": "バランス", "operation": "アクション", "payerPayee": "支払い人/受取人：", "brStatus0": "未払い", "brStatus1": "一部支払い済み", "brStatus2": "全額", "brStatus3": "全額", "sapStatus": "予約ステータス", "search": "請求先を検索", "billing": "カスタム請求", "bill2Customer": "顧客への課金", "pb": "定期購読", "billingNumber": "請求書", "referenceNumber": "参照＃", "currency": "通貨", "paymentDue": "支払い締切日です", "postDate": "ポストデート", "billingDate": "請求日", "originalBillingNumber": "Original Billing Number", "uuid": "UUID"}, "workTimeManager": {"projectName": "プロジェクト名：", "msgInput": "を入力してください", "applicant": "従業員：", "applicationDate": "勤務日：", "approvalDate": "承認日：", "status": "状態：", "memberNature": "人員性質：", "company": "会社：", "export": "エクスポート", "workingHourCode": "労働時間オーダー番号", "applicantName": "申請者名", "applicantPhone": "申請者の携帯電話番号", "consultantType": "コンサルタントタイプ", "projectNameIntern": "プロジェクト名", "wbs": "WBS", "whMonths": "労働時間の月", "totalWHPerDay": "1日あたりの総労働時間", "preTotalIncome": "見込み総収入", "applicationTime": "勤務時間", "approvalTime": "処理時間", "emailApprover": "承認者", "approvalStatus": "承認ステータス", "passed": "承認済み", "toBeSubmitted": "未提出", "toBeApproved": "承認待ち", "failed": "未承認", "abandoned": "無効化", "canceled": "キャンセル", "msgCancelReimburseInfo1": "無効化された後、勤務時間報告書は無効になり、システムは", "msgCancelReimburseInfo2": "と承認担当者に無効通知メールを自動的に送信します。この操作は元に戻せません。続行してよろしいですか？", "cancelReimburse": "勤務時間報告書の無効化", "msgCancelSuccess": "正常に取り消されました！"}, "bkAr": {"invoiceHeader": "請求書ヘッダー", "companyName": "請求先", "companyAddr": "住所", "companyTel": "電話番号", "companyEmail": "メールアドレス", "companyGst": "GST番号", "companyQst": "QST番号", "companyGstHst": "GST/HST", "companyPst": "PST(QST/--)", "invoiceNo": "請求書番号", "currency": "通貨", "date": "発行日", "dueDate": "支払期限", "fixedDate": "固定日", "afterDays": "日数後", "customerInfo": "顧客情報", "billToCompany": "請求先会社", "billToReceiver": "請求先受取人", "billingStreet": "番地", "billToCity": "市区町村", "billToProvince": "都道府県", "billToZip": "請求先郵便番号", "billToTel": "請求先電話番号", "billToEmail": "メールアドレス", "shipToSameAddr": "同じ住所に配送", "shipToCompany": "配送先会社", "shipToReceiver": "配送先受取人", "shipToStreet": "配送先住所", "shipToCity": "配送先市区町村", "shipToProvince": "配送先都道府県", "shipToZip": "配送先郵便番号", "shipToTel": "配送先電話番号", "shipToEmail": "配送先メールアドレス", "itemsDetail": "アイテム詳細", "itemNo": "#", "modelNumber": "商品/サービス", "to": "To", "description": "説明", "accountingCategory": "会計カテゴリー", "payMethod": "支払い方法", "qty": "数量", "unitPrice": "単価", "total": "正味金額", "type": "タイプ", "bankAccount": "銀行口座", "amountAndTax": "金額と税金", "amount": "正味金額", "amountRule": "金額", "shipping": "運送", "discount": "割引", "totalTaxable": "課税合計", "tps": "GST/HST", "tvq": "QST", "tvp": "PST", "totalTax": "税金合計", "totalCad": "合計(CAD)", "deposit": "預り金", "balance": "残高", "accountAndType": "口座と種類", "invoiceComment": "コメント", "createArInvoice": "AR請求書を作成する。", "viewInvoice": "請求書を表示する。", "preview": "プレビュー。", "create": "作成する。", "postingDate": "仕訳帳入力日", "totalFee": "合計。", "msgReferenceNoExisted": "参照番号はすでに存在しています！", "msgReferenceCheckFail": "参照番号の重複チェックに失敗しました。もう一度お試しください！", "from": "差出人。", "addItemBtnTxt": "アイテムを追加する。", "billingDoc": "Billing Doc", "referenceNo": "請求書の参照番号"}, "esUpload": {"bankAccount": "銀行口座", "bankMonth": "請求書の期間", "buttonSubmited": "送信済み", "buttonEdit": "編集"}, "bankInfo": {"bankName": "銀行名", "bankAccount": "銀行口座", "accountType": "口座種別", "currency": "通貨", "coa": "CoA", "operation": "操作", "createBankTitle": "新しい銀行を作成する", "editBankTitle": "銀行詳細を編集する", "msgPhrSelect": "選択してください", "authorizaiton": "認証", "fetchFrom": "から取得"}, "esMain": {"date ": "日付", "description": "説明", "inOutType": "入金/出金", "amount": "金額", "bankAccount": "銀行口座", "statementPeriod": "取引期間", "cashIn": "入金", "cashOut": "出金", "createCashStatementTitle": "現金取引明細書を作成する", "editCashStatementTitle": "現金取引明細書の詳細を編集する", "cashStatement": "現金取引明細書", "withdrawal": "引き出し", "deposit": "預り金", "postingDate": "仕訳帳入力日", "reference": "参照", "payerPayee": "支払人/受取人", "debit": "入金", "debitManu": "デビット", "credit": "出金", "creditManu": "クレジット", "sendSapStatus": "SAP ステータス", "viewInvoice": "請求書を表示", "invoiceDetail": "請求書の詳細", "balance": "残高", "chargeFee": "チャージ料金", "chargeCoa": "チャージCoA", "reasonMsg": "理由コード", "currency": "通貨", "brType": "タイプ", "transactionDate": "取引日", "delWarn": "削除後にトランザクションをリカバリできません。", "reconcileGL": "Reconcile G/L", "autoReconcile": "Auto Reconcile", "autoReconcileOn": "Auto Reconcile On", "autoReconcileOff": "Auto Reconcile Off"}, "chartOfAccount": {"coa": "勘定科目", "btnLoadCoa": "勘定科目レポジトリを読み込む", "btnReloadCoa": "勘定科目レポジトリを閉じる", "fieldCode": "フィールドコード", "account": "アカウントコード", "accountDes": "説明", "alias": "アカウント名", "operation": "操作", "fullCoa": "勘定科目レポジトリ", "createCoaTitle": "勘定科目表を作成する", "editCoaTitle": "勘定科目表を編集する", "msgCoaDelete01": "説明付きアカウント: ", "msgCoaDelete02": " 削除される!", "msgCoaPause01": "説明付きアカウント: ", "msgCoaPause02": " 無効になる!", "msgCoaActive01": "説明付きアカウント: ", "msgCoaActive02": " 再アクティブ化される!", "yes": "はい", "no": "いいえ", "groupName": "グループ"}, "taxCalculation": {"msgWarning": "システムが自動的に提供する税率計算方法は利便性のためだけであり、完全な正確性を保証するものではありません。ユーザーは請求書発行プロセス中に為替レート計算の正確性を手動で確認する必要があります。", "provinceName": "都道府県名", "provinceCode": "都道府県コード", "applicableSalesTax": "適用消費税", "total": "合計"}, "spotCurrency": {"msgWarning": "銀行が公表した為替レートである。", "date": "日付", "currency": "通貨（USD/CAD）", "operation": "操作", "createTitle": "為替レート作成", "editTitle": "為替レート編集", "selectTime": "日付を選択してください", "msgInputRate": "為替レートを入力してください"}, "connectivity": {"sourceHolder": "入力してください", "source": "から", "username": "ユーザー名", "password": "パスワード", "startDate": "開始日", "endDate": "終了日", "powerAutomate": "パワーオートメーション", "server": "サーバー", "email": "メール", "emailHost": "ホスト", "disk_secret": "シークレット", "type": "タイプ", "disk_app_id": "アプリID", "disk_key": "キー", "application_number": "申請番号", "instance_number": "インスタンス番号", "userId": "ユーザーID", "version": "バージョン", "receivable_integration": "売掛金統合", "payable_integration": "買掛金統合", "payment_integration": "支払い実行", "reconciliation_integration": "照合統合", "fts_integration": "FTS統合", "sap_instance": "インスタンス", "sap_general": "全般", "sap_setting": "設定", "payable_approval_procedure": "請求書承認フロー", "payment_approval_procedure": "支払い承認フロー"}, "gl": {"search": "検索摘要/仕訳帳", "glNo": "#", "narration": "摘要", "module": "モジュール", "journalEntry": "仕訳帳", "createDate": "作成日", "total": "合計", "totalDebit": "合計借方", "totalCredit": "合計貸方", "to": " ", "readonly": "詳細 G/L", "edit": "G/L を編集する", "create": "作成", "descriptionxxx": "説明、xxx", "post": "記帳", "date": "日付：", "totalCol": "合計：", "minFee": "最小手数料", "maxFee": "最大手数料", "operation": "操作", "postingDate": "仕訳帳入力日", "draftDate": "下書き日", "currency": "通貨", "itemNo": "#", "description": "説明", "debit": "借方", "credit": "貸方", "saveDraft": "下書きを保存する", "glAccount": "G/L 口座", "createStartDate": "開始", "createEndDate": "終了", "status": "ステータス", "sapStatus": "SAP ステータス", "editGl": "G/L を編集する", "delGl": "G/L を削除する", "viewGl": "G/L 詳細を表示する", "draft": "下書き", "posted": "記帳済み", "failure ": "失敗", "msgTotalDebitCannotNull": "借方合計は空欄にできません、再度確認してください", "msgTotalCreditCannotNull": "貸方合計は空欄にできません、再度確認してください", "msgNotMatch": "借方合計が貸方合計と一致しません、再度確認してください", "msgNumberLimited": "入力可能な数字の範囲は0-13桁に制限されています", "msgGlDelete": "この G/L は削除されます！", "transactionCurrency": "当地货币 - ", "status0": "下書き", "status1": "提出された", "status2": "逆転した", "format": "フォーマットエラー"}, "glEntry": {"totalDebit": "合計借方", "totalCredit": "合計貸方", "msgAtLeastOne": "G/Lには少なくとも1つの[項目]が含まれている必要がある", "transactionCurrency": "現地通貨 - "}, "bkSupplier": {"company": "会社", "tel": "電話番号", "email": "メールアドレス", "receiver": "サプライヤー", "receiver01": "受信者（オフィス）", "supplierAddr": "担当者名", "address": "住所", "address01": "住所（オフィス）", "street": "番地", "city": "市区町村", "province": "都道府県", "country": "国", "postalCode": "郵便番号", "expenseAccount": "費用科目", "operation": "操作", "createReceiverTitle": "サプライヤーの新規作成", "editReceiverTitle": "連絡先詳細の編集", "msgPhrSelect": "選択してください", "sameAsOffice": "オフィスと同じ"}, "taxInfo": {"companyLogo": "ロゴ", "companyName": "名称", "companyAddress": "住所", "companyEmail": "メールアドレス", "companyPhone": "電話", "gstNo": "GST / HST 番号", "qstNo": "QST / PST 番号", "limit": "注意：jpg/pngファイルのみアップロード可能で、2Mを超えないようにしてください。", "editLogoBtn": "ロゴを編集する", "rmLogoBtn": "ロゴを削除する"}, "account": {"userId": "アカウント", "password": "パスワード", "cate": "カテゴリー", "role": "役割", "add": "ユーザーを追加する"}, "taxRates": {"countryNotSet": "Please set the Country and/or Province of the vendor(or customer)"}, "ApComponents": {"notpaid": "未払い", "bank": "銀行口座", "credit": "クレジットカード", "cashpaid": "現金", "check": "チェック", "confirm": "Confirm Reversing Date", "confirm2": "この操作は和解を逆転させます。", "confirm3": "まず銀行の調整を取り消してください", "referenceError": "重複参照番号", "contactFirstUsing": "新しい連絡先は銀行情報を更新する必要があります。", "atleast": "請求書には少なくとも 1 つの [アイテム] が含まれている必要があります", "mustone": "請求書は 1 つのみ [アイテム] にする必要があります", "lackNetAmount": "[正味金額] がないと請求書を作成できません", "notEqual": "正味金額と税金が合計と等しくない", "success": "成功", "referenceNo": "領収書の参照番号。", "spotCurrency": "{rate} のスポット通貨は {date} です", "contactAdmin": "アドミニストレータに連絡してください", "NewBP": "新しい取引先を追加", "subtotal": "消費税", "taxableSubtotal": "課税小計", "total": "合計", "difference": "違い", "drag": "ここにファイルをドラッグするか、[アップロード] をクリックします", "uploading": "アップロード中", "sizeExceed": "注: ファイルあたりのサイズ制限は 10Mb です。", "MEAT": "メタフィールド", "oriDoc": "オリジナルのドキュメント", "download": "ダウンロード", "auto": "自動モード", "exempt": "非課税", "amount": "合計金額", "netAmount": "税抜金額", "GST": "GST/HST", "QST": "QST", "PST": "PST", "search": "発行者の検索", "pay": "支付", "uploadConfirm": "元の文書は置き換えられます!", "wbs": "WBS", "costCenter": "コストセンター", "costObject": "コストオブジェクト", "internalOrder": "内部注文", "profitCenter": "利益センター", "payableGL": "支払可能G/L"}, "ArComponents": {"inavailable": "請求書ファイルは利用できません。", "notExist": "顧客が存在しません。新しい顧客を追加しますか?", "postingDt": "郵送日", "billTo": "請求書送付先", "shipTo": "配送先", "netAmount": "税抜金額", "shipping": "運送", "discount": "ディスカウト", "GST": "GST/HST", "QST": "QST", "PST": "PST", "USD": "合計米ドル", "CAD": "合計カナダドル", "deposit": "デポジット", "balance": "バランス", "JE": "仕訳", "SAP": "SAP Document", "file": "請求書ファイル", "download": "ダウンロード", "cancel": "キャンセル", "convert": "Pay by Cash", "confirmConvert": "Original invoice will be removed and cannot be recovered."}, "PeriodicalBooking": {"notFound": "ダミーが見つかりません！"}, "EstatementTable": {"selectBank": "銀行を選択してください", "required": "銀行口座欄必須No.", "inovice": "請求書", "fetch": "取得", "upload": "アップロード", "acceptAll": "アップロード", "autoOn": "すべて受け入れる", "autoOff": "自動調整はオンです。 クリックして調整を受け入れます。", "reject": "クリックして、推奨される自動調整を拒否し、手動調整ページに移動します", "RP": "RP【定期購入】", "RS": "RS【定期販売】", "PR": "PR【購入返金】", "SR": "SR【売上還元】", "FT": "FT【資金移動】", "FX": "FX【資金取引所】", "PY": "PY【給与】", "BC": "EE [Express Entry]", "SP": "SP [Stripe]", "PP": "PP [Prepayment]", "BCMessage": "Invoice will be created and reconciled, please select vendor (or customer) and G/L account.", "GLMessage": "Please choose Type and Accounting Category.", "integrationEEbrTitle": "Express Entry", "account": "銀行口座", "cancel": "キャンセル", "OK": "OK", "uploadES": "電子明細ファイルのアップロード", "reconcileAll": "すべての照合", "payoutIdMsgInput": "Please enter payoutId", "confirm": "確認", "updateChargeFee": "チャージ料金"}, "RcInfoTable": {"account": "銀行口座", "date": "支払日", "desc": "説明", "debit": "デビット", "credit": "クレジット", "balance": "残高", "invoiceNo": "請求書番号", "comments": "コメント", "paymentDt": "支払日", "fee": "合計料金", "amount": "BR金額", "brDoc": "BR Document"}, "CustomerForm": {"USA": "アメリカの郵便番号：12345または12345-1234", "CA": "カナダの郵便番号：H1H 2H2"}, "PyComponents": {"total": "合計"}, "FileUploadResult": {"failed": "ファイルのアップロードに失敗しました。もう一度お試しください。"}, "UploadFileComp": {"notPaid": "未払いの購買", "cash": "現金で購入", "reconcile": "調整する", "uploadLimit": "ファイルのアップロード制限：", "select": "ファイルを選択してください", "perfileLimit": "ファイルサイズの制限：", "uploadFile": "{type}ファイルをアップロードしてください", "success": "アップロードに成功しました", "error_two_files": "XMLファイルを含む2つのファイルを同時にアップロードする必要があります", "retry": "アップロードエラー [{err}]。再試行してください。", "selectBtn": "選択する", "type": "タイプ", "note": "注: 1 つのファイルが以下を超えることはできません:{fileSize}Mb。 毎回アップロードできるファイルは {fileLimit} 個までです。"}, "mainLayout": {"setFY": "通貨と財務年度を設定してください。", "createBP": "ビジネスパートナーを作成してください。", "createChart": "勘定科目を作成してください。", "fillin": "期首残高を入力してください。", "careteBank": "銀行を作成してください。", "setup": "会社を設定するために以下のステップを実行してください。"}, "userGuide": {"setFY": "通貨と財務年度を設定してください。", "createBP": "ビジネスパートナーを作成してください。", "createChart": "勘定科目を作成してください。", "fillin": "期首残高を入力してください。", "careteBank": "銀行を作成してください。"}, "userPage": {"selectUser": "ユーザーを選択する必要があります", "success": "成功", "error": "エラー", "companyWithDesc": "説明のある会社", "companyCd": "会社コード", "companyNm": "会社名", "AddAll": "すべての会社を追加する", "cdnm": "会社コード/会社名"}, "ApInvoiceFormPdf": {"invoice": "請求書ファイル", "fetch": "情報を取得する", "notPaid": "未払い", "partialPaid": "一部支払い済み", "paid": "支払い済み", "reversed": "取り消されました"}, "ApUploadInvoice": {"upload": "請求書ファイルをアップロードしてください。", "OCR": "OCR分析手順", "file": "選択されたファイル:", "fillError": "請求書ファイルは利用できません。", "analyzing": "分析中 ( {type} 経由) ...", "error": "エラー", "confirm": "確認"}, "ArInvoiceHistory": {"invoice": "請求書ファイルは利用できません。", "Send Invoice": "請求書を送ります。"}, "ReconciliationDetails": {"invoiceNo": "請求書番号", "select": "請求書を選択してください。", "search": "残高検索", "totals": "合計金額 ${amt}、選択金額 ${amt2}、差額 ${amt3}。", "reconcile": "調整する", "manual": "手動照合", "manualIntegration": "手動照合 for Integration"}, "AccountDescription": {"importAll": "選択されたグループからすべての勘定科目をインポートしますか？", "add": "新しいサブカテゴリを追加しますか？", "primary": "プライマリ", "create": "勘定科目を作成してください。", "step1": "ステップ1：情報を入力してください。", "next": "次のステップ", "step2": "ステップ2：ボタンをクリックしてください。", "step3": "ステップ 3: [選択] リンクをクリックします。", "got": "とった", "importGroup": "グループをインポートする", "pause": "一時停止", "active": "アクティブ", "copy": "コピー", "cdnm": "アカウントコードまたは名前"}, "bankInformation": {"BMO": "モントリオール銀行", "CIBC": "カナダ帝国商業銀行", "NBC": "カナダ国立銀行", "RBC": "ロイヤル バンク カナダ", "Desjardins": "デジャルダン銀行", "TDB": "トロント・ドミニオン銀行", "JPCB": "JPMorgan Chase Bank", "FB": "Fremont Bank", "SEB": "South East Bank", "EWB": "East West Bank", "CITI": "CITI Bank", "BOC": "Bank Of China", "CMB": "China Merchants Bank", "ASPIRE": "アスパイア銀行", "ICBC": "Industrial And Commercial Bank Of China", "BONJ": "Bank of NanJing", "SANTANDER": "Santander Bank", "BASEINET": "BASEinet Bank", "MONEX": "Monex Bank", "BBVA": "BBVA Bank", "email": "メールアドレスを設定してください。", "create": "銀行を作成してください。"}, "ContactCustomer": {"create": "ビジネスパートナーを作成してください。", "got": "とった"}, "SettingConnectivities": {"importBilling": "請求書をインポートする", "import": "インポート", "save": "保存", "cancel": "キャンセル", "pass": "メールパスワード", "channel": "全チャネル統合", "SMTP": "メールサーバー", "Netdist": "ネットワークディスク", "approvers": "承認者", "lowCode": "ローコード"}, "TaxInformation": {"removed": "企業ロゴの削除に成功しました。", "error": "アップロードエラー", "recorrrect": "正しい`{msg}`を入力してください。例：{desc}", "phoneNo": "正しい電話番号を入力してください。例：**********", "FY": "通貨と会計年度を設定してください。"}, "reports": {"balanceSheet": "貸借対照表", "trialBalance": "試算表", "cashFlowStatement": "キャッシュフロー計算書", "purchaseReport": "購入レポート", "incomeStatement": "損益計算書", "salesReport": "売上レポート", "apReport": "売掛金レポート", "arReport": "買掛金レポート", "mxElectronicAccountingCoaReport": "Electronic Accounting - COA", "mxElectronicAccountingTrialBalanceReport": "Electronic Accounting - Trial Balance", "mxInformativeDeclarationReport": "Informative Declaration of Operations with Third Parties (DIOT)", "mxValueAddedTaxReport": "Value Added Tax", "mxColumnSeqNo": "Seq. No.", "mxColumnGlAccountDetails": "G/L Account Details", "mxColumnGlAccount": "G/L Account", "mxColumnStartingBalanceAmount": "Starting Balance Amount", "mxColumnDebitAmount": "Debit Amount", "mxColumnCreditAmount": "Credit Amount", "mxColumnEndingBalanceAmount": "Ending Balance Amount", "mxColumnSatCode": "SAT Code", "mxColumnGlAccountDescription": "G/L Account Description", "mxColumnLevel": "Level", "mxColumnNatureOfAccount": "Nature Of Account", "mxDiotColumn1": "Third party type", "mxDiotColumn2": "Tipo de operación", "mxDiotColumn3": "RFC", "mxDiotColumn4": "Tax identification number", "mxDiotColumn5": "Foreigner's name", "mxDiotColumn6": "Country or jurisdiction of tax residence", "mxDiotColumn7": "Specify place of tax jurisdiction", "mxDiotColumn8": "Total value of paid events or activities / Paid events or activities in the northern border region", "mxDiotColumn9": "Refunds, discounts and bonuses / Paid events or activities in the northern border region", "mxDiotColumn10": "Total value of paid events or activities / Paid events or activities in the southern border region", "mxDiotColumn11": "Refunds, discounts and bonuses / Paid events or activities in the southern border region", "mxDiotColumn12": "Total value of paid events or activities / Total events or activities paid at the 16% VAT rate", "mxDiotColumn13": "Refunds, discounts and bonuses / Total events or activities paid at the rate of 16% VAT", "mxDiotColumn14": "Total value of acts or activities paid / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn15": "Refunds, discounts and bonuses / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn16": "Total value of acts or activities paid / Acts or activities paid in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn17": "Refunds, discounts and bonuses / Acts or activities paid for the import of intangible goods and services at the rate of 16% VAT", "mxDiotColumn18": "Exclusively for taxed activities / Acts or activities paid in the northern border region", "mxDiotColumn19": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn20": "Exclusively for taxed activities / Acts or activities paid in the southern border region", "mxDiotColumn21": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn22": "Exclusively from taxable activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn23": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn24": "Exclusively for taxed activities / Acts or activities paid for in the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn25": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn26": "Exclusively for taxed activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn27": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn28": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn29": "Associated with activities that do not meet requirements / Paid events or activities in the northern border region", "mxDiotColumn30": "Associated with exempt activities / Paid acts or activities in the northern border region", "mxDiotColumn31": "Associated with non-object activities / Paid acts or activities in the northern border region", "mxDiotColumn32": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn33": "Associated with activities that do not meet requirements / Paid events or activities in the southern border region", "mxDiotColumn34": "Associated with exempt activities / Paid events or activities in the southern border region", "mxDiotColumn35": "Associated with non-object activities / Paid acts or activities in the southern border region", "mxDiotColumn36": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn37": "Associated with activities that do not meet requirements / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn38": "Associated with exempt activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn39": "Associated with non-object activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn40": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn41": "Associated with activities that do not comply with requirements / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn42": "Associated with exempt activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn43": "Associated with non-object activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn44": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn45": "Associated with activities that do not comply with requirements / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn46": "Associated with exempt activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn47": "Associated with non-object activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn48": "VAT withheld by the taxpayer", "mxDiotColumn49": "Acts or activities paid for in the importation of goods and services for which VAT is not paid (Exempt)", "mxDiotColumn50": "Paid acts or activities for which VAT will not be paid (Exempt)", "mxDiotColumn51": "Other acts or activities paid at the 0% VAT rate", "mxDiotColumn52": "Acts or activities not subject to VAT carried out in national territory", "mxDiotColumn53": "Acts or activities not subject to VAT due to not having an establishment in national territory", "mxDiotColumn54": "I declare that fiscal effects were given to the receipts that support the operations carried out with the supplier", "closingBalance": "期末残高", "itemsDetail": "項目詳細", "startDate": "開始日", "experationDate": "有効期限", "company": "会社", "targetCurrency": "目標通貨", "transactionCurrency": "取引通貨", "COAAccount": "総勘定元帳", "refresh": "更新", "category": "カテゴリー", "accountCode": "アカウントコード", "nameGLCategory": "G/Lカテゴリ名", "endDebit": "借方", "endCredit": "貸方", "all": "すべて", "endingAt": "終了日", "subTotal": "小計", "updatedOn": "更新日", "postingDate": "ポスティング日", "documentNo": "文書番号", "invoiceNo": "請求書番号", "supplier": "サプライヤー", "customer": "顧客", "netAmount": "税抜金額", "currency": "通貨", "PST": "PST", "GST": "GST/HST", "QST": "QST", "VAT": "VAT", "Discount": "Discount", "IsrWithholding": "ISR Withholding", "VatWithholding": "VAT Withholding", "totalTaxAmount": "総税額", "totalAmount": "合計金額", "invoiceAmount": "請求金額", "payment": "支払い", "totalDue": "合計額", "businessPartner": "ビジネスパートナー", "invoiceReference": "請求書リファレンス", "transactionType": "取引タイプ", "current": "現在", "31To60": "31から60", "61To90": "61から90", "91+": "91以上", "contactName": "連絡先氏名", "narration": "ナレーション", "cashFlowName": "キャッシュフロー項目", "cashFlowMonth": "現在の月", "cashFlowYear": "現在の年", "vatChargeable": "VAT chargeable", "valueOfActsOrActivitiesTaxedAtTheRateOf16": "Value of acts or activities taxed at the rate of 16%", "valueOfActsOrActivitiesTaxedAtThe0ExportRate": "Value of acts or activities taxed at the 0% export rate", "valueOfActsOrActivitiesTaxedAtTheRateOf0Others": "Value of acts or activities taxed at the rate of 0% others", "sumOfTheTaxedActsOrActivities": "Sum of the taxed acts or activities", "valueOfActsOrActivitiesForWhichTaxIsNotPayable": "Value of acts or activities for which tax is not payable (exempt", "valueOfActsOrActivitiesNotSubjectToTax": "Value of acts or activities not subject to tax", "vatChargeableAtTheRateOf16": "VAT chargeable at the rate of 16%", "vatCharged": "VAT charged", "updatedAmountToBeReimbursedDerivedFromTheAdjustment": "Updated amount to be reimbursed derived from the adjustment", "totalVatDue": "Total VAT due", "btCapture": "Capture", "vatCreditable": "VAT creditable", "amountOfPaidEventsOrActivities": "Amount of paid events or activities", "totalActsPaid16Percent": "Total of acts or activities paid at the 16% VAT rate", "totalActsPaidImport16Percent": "Total of acts or activities paid for in the import of goods and services at the 16% VAT rate", "totalActsPaid0Percent": "Total of other acts or activities paid at the 0% VAT rate", "totalPaidActsExempt": "Total of paid acts or activities for which VAT will not be paid (exempt)", "determinationCreditableVAT": "Determination of the creditable Value Added Tax", "vatOnActsPaid16Percent": "VAT on acts or activities paid at the rate of 16%", "vatOnImportPaid16Percent": "VAT on acts or activities paid on the import of goods and services at the rate of 16%", "totalVATTransferred": "Total VAT transferred to the taxpayer (Effectively paid)", "updatedCreditableAmount": "Updated creditable amount to increase derived from the adjustment", "totalCreditableVAT": "Total creditable VAT", "determination": "Determination", "vatWithheld": "VAT withheld", "totalCreditableVat": "Total creditable VAT", "otherAmountsPayableByTheTaxpayer": "Other amounts payable by the taxpayer", "otherAmountsInFavorOfTheTaxpayer": "Other amounts in favor of the taxpayer", "amountDue": "Amount due", "creditingOfTheBalanceInFavorOfPreviousPeriods": "Crediting of the balance in favor of previous periods (Without exceeding the amount due)", "taxDue": "Tax due", "amountToBeDetailed1": "Amount to be detailed", "interestChargedAtRate16": "Interest charged at a rate of 16%", "royaltiesBetweenRelatedPartiesAtRate16": "Royalties between related parties at the rate of 16%", "otherActsOrActivitiesTaxedAtRate16": "Other acts or activities taxed at the rate of 16%", "amountToBeDetailed2": "Amount to be detailed", "agriculturalLivestockForestryFishingActivitiesTaxedAtRate0": "Agricultural, livestock, forestry or fishing activities taxed at a rate of 0%", "otherActsOrActivitiesTaxedAtRate0": "Other acts or activities taxed at a rate of 0%", "amountToBeDetailed3": "Amount to be detailed", "alienationOfLandAndBuildingsForResidentialHousing": "Alienation of land and buildings attached to the land, intended or used for residential housing", "saleOfBooksNewspapersMagazinesNotByTaxpayer": "Sale of books, newspapers and magazines (not published by the taxpayer)", "royaltiesChargedByAuthors": "Royalties charged by authors", "disposalOfUsedMovablePropertyExceptByCompanies": "Disposal of used movable property, except those disposed of by companies", "alienationOfLotteryTicketsAndReceipts": "Alienation of tickets and other receipts from lotteries, raffles, drawings or games with bets and contests of all kinds", "teachingServices": "Teaching services", "publicLandTransportationServiceForPeople": "Public land transportation service for people", "derivativeFinancialTransactions": "Derivative financial transactions", "ticketSalesForPublicShows": "Ticket sales for public shows", "professionalMedicalServices": "Professional medical services", "temporaryUseOfRealEstateForResidentialOrFarming": "Temporary use or enjoyment of real estate for residential purposes and for farms for agricultural or livestock purposes", "otherIncomeExemptFromVat": "Other income exempt from VAT"}, "menu": {"task": "タスク", "purchase": "購入", "massiveProcess": "大規模処理", "invoices": "請求書", "sales": "販売", "billing": "請求", "payroll": "給与計算", "payrollRecord": "給与記録", "bankReconciliation": "銀行勘定照合", "reconcile": "照合", "history": "履歴", "generalLedger": "総勘定元帳", "journalEntries": "仕訳", "setting": "設定", "contact": "連絡先", "profile": "プロフィール", "coA": "科目一覧", "coAMapping": "科目マッピング", "connectivities": "接続性", "reporting": "レポート", "reports": "BI", "exportTrialBalance": "レポート", "help": "ヘルプ", "userGuide": "ユーザーガイド", "FAQ": "FAQ", "account": "アカウント", "user": "ユーザー", "dashboard": "Dashboard"}, "fileTypeList": {"salesNotPaid": "未払い", "notPaid": "未払い", "salesCash": "現金", "cashPaid": "現金", "ES": "銀行取引明細書", "yearEnd": "年末報告書"}, "task": {"name": "タスク", "companyCode": "会社コード", "companyName": "会社名", "last": "最新", "dueDate": "期限", "estimatedHour": "推定 (時間)", "actualHour": "実際 (時間)", "status": "ステータス", "assignedTo": "担当者", "email": "Eメール", "tag": "鬼ごっこ", "priority": "優先度", "createTime": "作成時間", "updateTime": "更新時間", "action": "アクション", "placeholderSearch": "検索タスク名", "placeholderStarting": "開始中", "placeholderEnding": "エンディング", "placeholderMinHour": "分時", "placeholderMaxHour": "最大時間", "searchTitleEstimated": "推定", "statusToDo": "藤堂", "statusDoing": "実行中", "statusDone": "完了", "statusDeleted": "削除されました"}, "update": {"items": "項目", "item": "項目", "assigment": "割当て", "paymentDueLater": "後で支払う", "withinDay7": "7日以内", "withinDay15": "15日以内", "withinDay30": "30日以内", "withinDayOther": "固定日", "other": "その他", "searchByBankAccount": "銀行口座から探す", "searchByFaq": "検索してください"}}