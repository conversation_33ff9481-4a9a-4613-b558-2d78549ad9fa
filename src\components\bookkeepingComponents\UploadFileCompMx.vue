<!-- @format -->

<template>
    <div class="">
        <div v-show="props.pageType.toString() !== '7'" v-if="!ApIntegration.get()">
            <a-row :gutter="24">
                <a-col :span="24">
                    {{ i18n.t('UploadFileComp.type') }}
                    <a-select
                        :placeholder="i18n.t('workTimeManager.msgInput')"
                        v-model:value="selectedType"
                        :options="props.pageType.toString() === '7' ? reconcileOptions : fileTypeOptions"
                        :dropdownStyle="{maxHeight: '150px'}"
                        dropdownMatchSelectWidth
                        :filter-option="false"
                        :disabled="props.isUploadInvoiceOriginalDocument"
                        class="table-input"
                        @change="fileTypeChange"
                        style="width: 150px; overflow: hidden"
                    >
                    </a-select>
                </a-col>
            </a-row>
        </div>
        <a-upload
            ref="upload"
            action="string"
            :multiple="true"
            name="file"
            :file-list="state.fileList"
            @change="uploadChange"
            @remove="uploadRemove"
            :before-upload="beforeUpload"
            :on-success="uploadSuccess"
            :on-error="logoUploadError"
            :max-count="fileLimit"
            :show-upload-list="true"
            :accept="fileTypeString"
            :custom-request="executeFile"
        >
            <a-button class="btn-select" type="txt">
                <template #icon>
                    <svg-icon name="icon_select"></svg-icon>
                </template>
                {{ i18n.t('UploadFileComp.selectBtn') }}
            </a-button>
            <template #iconRender>
                <folder-open-outlined style="font-size: 14px; color: #aab9cb" />
            </template>
            <template #removeIcon>
                <close-circle-filled style="font-size: 14px; color: #aab9cb" />
            </template>
            <!-- eslint-disable vue/no-deprecated-slot-attribute -->
            <div slot="tip" class="tip-color">
                <exclamation-circle-filled />
                <!-- Note: size limit {{ fileSize }}Mb per file. {{ fileLimit }} file(s) at the same time. -->
                {{
                    i18n.t('UploadFileComp.note', {
                        fileSize: props.fileSize,
                        fileLimit: props.fileLimit,
                    })
                }}
            </div>
        </a-upload>

        <div v-if="uploadProcessing" class="process-bar-wrapper">
            <SyncOutlined spin :style="{color: '#1a94d0', 'font-size': '16px'}" />
            <span class="bar-txt">Uploading ...</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import {message} from 'ant-design-vue'
import type {UploadChangeParam, UploadProps, SelectProps} from 'ant-design-vue'
import {ExclamationCircleFilled, SyncOutlined, FolderOpenOutlined, CloseCircleFilled} from '@ant-design/icons-vue'
import {reactive, ref, type PropType, computed, onBeforeMount} from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useStore} from 'vuex'
import {UserCompany, UserInfo, ApIntegration} from '@/lib/storage'
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const i18n: Composer = i18nInstance.global
const store = useStore()
const props = defineProps({
    fileType: {
        type: Array as PropType<string[]>,
        default: () => ['png', 'jpg', 'jpeg', 'xlsx', 'xls', 'pdf', 'csv', 'xml'],
    },
    fileSize: {
        type: Number,
        default: 10,
    },
    fileLimit: {
        type: Number,
        default: 8,
    },
    uploadProcessing: {
        type: Boolean,
        default: false,
    },
    pageType: {
        type: String,
        default: '',
    },
    bankInfo: {
        type: String,
        default: '',
    },
    bankInfoDetail: {
        type: Object,
    },
    isUploadInvoiceOriginalDocument: {
        type: Boolean,
        default: false,
    },
    invoiceId: {
        type: Number,
        default: 0,
    },
    selectedType: {
        type: String,
        default: '',
    },
})

const fileTypeOptions = ref<SelectProps['options']>([
    // {
    //     value: '0',
    //     label: 'Sales Not Paid',
    // },
    {
        value: '1',
        label: i18n.t('UploadFileComp.notPaid'), //'Purchase Not Paid',
    },
    // {
    //     value: '2',
    //     label: 'Sales Cash',
    // },
    {
        value: '3',
        label: i18n.t('UploadFileComp.cash'), //Purchase Cash',
    },
    // {
    //     value: '7',
    //     label: 'ES',
    // },
    // {
    //     value: '8',
    //     label: 'Year End Document',
    // },
])
const reconcileOptions = ref<SelectProps['options']>([
    {
        value: '7',
        label: i18n.t('UploadFileComp.reconcile'), //'Reconcile',
    },
])

const state = reactive({
    fileList: [] as NonNullable<UploadProps['fileList']>,
    // uploadFile: [] as NonNullable<UploadProps['fileList']>,
})
const selectedFileCount = ref(0)
const selectedType = ref('')
const isAlertShow = ref(false)

const fileTypeString = computed(() => props.fileType?.join(','))

const uploadApInvoicePdf = (data: FormData) => store.dispatch('ApStore/uploadApInvoicePdfV1', data)
const uploadApInvoicePdfMX = (data: FormData) => store.dispatch('ApStore/uploadApInvoicePdfV1MX', data)
const uploadApInvoiceOriginalDocument = (data: {id: number; formData: FormData}) =>
    store.dispatch('ApStore/uploadApInvoiceOriginalDocument', data)
// always return false to disable autoUpload
const beforeUpload = (file: any, filelist: any[]): boolean => {
    // file count rule
    if (filelist.findIndex(x => x.uid === file.uid) === 0) {
        isAlertShow.value = false // reset every select file action
        selectedFileCount.value = state.fileList.length
    }
    if (filelist.length + selectedFileCount.value > props.fileLimit) {
        if (!isAlertShow.value) {
            // only show one time alert for max file count
            message.error({
                content: `${i18n.t('UploadFileComp.uploadLimit')} ${props.fileLimit}`,
            })
            isAlertShow.value = true
        }
        return false
    }

    if (filelist.length === 0) {
        if (!isAlertShow.value) {
            // only show one time alert for max file count
            message.error({
                content: i18n.t('UploadFileComp.select'), //`Please Select File`,
            })
            isAlertShow.value = true
        }
        return false
    }

    if ((file.size as number) > props.fileSize * 1000 * 1000) {
        // file size rule
        if (!isAlertShow.value) {
            // only show one time alert for max file count
            message.error({
                content: `${i18n.t('UploadFileComp.perfileLimit')} ${props.fileSize}MB`,
            })
            isAlertShow.value = true
        }
        return false
    }
    // file duplicated name rule
    const hasDuplicatedName = state.fileList.find((i: any) => i.name === file.name && i.uid !== file.uid)
    if (hasDuplicatedName) {
        if (!isAlertShow.value) {
            // only show one time alert for max file count
            message.error({
                content: `${i18n.t('UploadFileComp.perfileLimit')} ${props.fileSize}MB`,
            })
            isAlertShow.value = true
        }
        return false
    }
    // file type rule
    if (props.fileType.length > 0) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
            fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase()
        }
        const isValidType = props.fileType.some(type => {
            return fileExtension && fileExtension.indexOf(type) > -1
        })
        if (!isValidType) {
            if (!isAlertShow.value) {
                // only show one time alert for max file count
                message.error({
                    content: i18n.t('UploadFileComp.uploadFile', {type: props.fileType.join('/ ')}),
                })
                isAlertShow.value = true
            }
            return false
        }
    }

    state.fileList.push(file)
    return false
}

const uploadChange = () => {
    console.log('file changed')
}

const uploadSuccess = () => {
    message.success({
        content: i18n.t('UploadFileComp.success'), //'success',
    })
}

const uploadRemove = (file: any) => {
    state.fileList.forEach((item, index) => {
        if (item.name === file.name) {
            state.fileList.splice(index, 1)
        }
    })
}

const fileTypeChange = () => {
    console.log('check current', selectedType.value)
}

const logoUploadError = (err: string) => {
    message.error(i18n.t('UploadFileComp.retry', {err: err}))
}

const executeFile = () => console.log('http-request executed.')

const uploadFiles = async () => {
    const uploadFileList = state.fileList

    const formData = new FormData()
    const jsonStr = [] as any[]
    if (uploadFileList.length === 0) {
        if (!isAlertShow.value) {
            // only show one time alert for max file count
            message.error({
                content: `Please Select File`,
            })
        }
        return null
    }
    const arr = props.bankInfo.split('|')
    uploadFileList.forEach(file => {
        const file_type =
            props.pageType.toString() === '7' ? props.pageType : ApIntegration.get() ? '1' : selectedType.value
        const baseObj = {
            company_id: userCompany[0].id,
            company_code: userCompany[0].code,
            file_type: file_type,
            file_name: file.name,
            file_currency: arr[1] ? arr[1] : '',
            bank_account: arr[0] ? arr[0] : '',
            bank_month: '',
            bank_type: props.bankInfoDetail?.bank_type,
            bank_code: props.bankInfoDetail?.code,
            creator: userInfo?.id,
        }
        if (file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase() !== 'xml') {
            jsonStr.push(baseObj)
        }
        formData.append('file', file as any)
    })
    formData.append('jsonStr', JSON.stringify(jsonStr))

    try {
        return props.isUploadInvoiceOriginalDocument
            ? await uploadApInvoiceOriginalDocument({formData, id: props.invoiceId})
            : await uploadApInvoicePdfMX(formData)
    } catch (error) {
        return null
    }
}

onBeforeMount(() => {
    selectedType.value = props.selectedType
    if (props.pageType.toString() === '7') {
        selectedType.value = props.pageType.toString()
    }
})

defineExpose({
    uploadFiles,
})
</script>

<style lang="scss" scoped>
.footer-divider {
    margin-top: 14px;
    margin-bottom: 14px;
}

.process-bar-wrapper {
    margin-top: 12px;

    .bar-txt {
        color: #1a94d0;
        margin-left: 8px;
    }
}

@keyframes move {
    100% {
        background-position: 20px 0;
    }
}

.tip-color {
    padding-left: 10px;
    font-size: 14px;
    color: #aab9cb;
    line-height: 20px;
    font-weight: 400;
    margin-bottom: 10px;
}

footer {
    text-align: right;
}

.btn-select {
    border: none;
    box-shadow: none;
    font-size: 16px;
    color: #004fc1;
    line-height: 22px;
    font-weight: 400;
    padding-left: 8px;

    .btn-select-icon {
        width: 20px;
        height: 20px;
        margin-right: 4px;
        display: inline-block;
        vertical-align: middle;
        background-size: cover;
        background-repeat: no-repeat;
        background-image: url('@/assets/image/icon/icon_Upload_Select_blue.png');
    }
}

:deep(.ant-upload-list-item-info) :hover {
    color: #262626;
}

:deep(.ant-upload-list-item-list-type-text) {
    color: #aab9cb;
}

:deep(.ant-upload-list-text-container) {
    padding-left: 10px;
}
</style>
