<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount, watch} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {numberToCurrency} from '@/lib/utils'
import {ExportOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'

import * as _ from 'lodash'
import moment from 'moment'
import type {Dayjs} from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'

const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global

const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

const today = ref(moment().format('YYYY-MM-DD'))
const tableLoading = ref(false)
const dataSource = computed(
    () =>
        store.state.ReportStore.mxInformativeDeclarationReport.map((i: any) => {
            return {
                ...i,
                tax_identification_number: i.tax_category === 'MX1' ? '' : i.rfc,
                rfc_data: i.tax_category === 'MX1' ? i.rfc : '',
                contact_name_data: i.tax_category === 'MX1' ? '' : i.contact_name,
                VAT_withheld_by_the_taxpayer:
                    i.VAT_withheld_by_the_taxpayer === 0 ? null : numberToCurrency(i.VAT_withheld_by_the_taxpayer),
                vat_20: i.vat_20 === 0 ? null : numberToCurrency(i.vat_20),
                vat_0: i.vat_0 === 0 ? null : numberToCurrency(i.vat_0),
                vat_exmt: i.vat_exmt === 0 ? null : numberToCurrency(i.vat_exmt),
                column_16: i.column_16 === 0 ? null : numberToCurrency(i.column_16),
                column_14: i.column_14 === 0 ? null : numberToCurrency(i.column_14),
                column_49: i.column_49 === 0 ? null : numberToCurrency(i.column_49),
            }
        }) || [],
)
const total = computed(() => [])

const searchForm = reactive<{[key: string]: string | undefined}>({
    month: moment().format('YYYY-MM'),
    company_code: undefined,
})

const fetchReportsList = (payload: any) => store.dispatch('ReportStore/getMxInformativeDeclarationReport', payload)

const disabledDate = (current: Dayjs) => {
    return current && current > moment().endOf('day')
}

const tableScrollY = computed(() => {
    return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight - headerHeight * total.value.length
})

const columns: ColumnsType = [
    {
        title: i18n.t('reports.mxDiotColumn1'),
        dataIndex: 'third_party_type',
        key: 'third_party_type',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn2'),
        dataIndex: 'type_operation',
        key: 'type_operation',
        align: 'center',
        width: 100,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn3'),
        dataIndex: 'rfc_data',
        key: 'rfc_data',
        align: 'center',
        width: 100,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn4'),
        dataIndex: 'tax_identification_number',
        key: 'tax_identification_number',
        align: 'center',
        width: 120,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn5'),
        dataIndex: 'contact_name_data',
        key: 'contact_name_data',
        align: 'center',
        width: 120,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn6'),
        dataIndex: 'mxDiotColumn6',
        key: 'mxDiotColumn6',
        align: 'center',
        width: 120,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn7'),
        dataIndex: 'specify_place_of_tax_jurisdiction',
        key: 'specify_place_of_tax_jurisdiction',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn8'),
        dataIndex: 'mxDiotColumn8',
        key: 'mxDiotColumn8',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn9'),
        dataIndex: 'mxDiotColumn9',
        key: 'mxDiotColumn9',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn10'),
        dataIndex: 'mxDiotColumn10',
        key: 'mxDiotColumn10',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn11'),
        dataIndex: 'mxDiotColumn11',
        key: 'mxDiotColumn11',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn12'),
        dataIndex: 'vat_20',
        key: 'vat_20',
        align: 'right',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn13'),
        dataIndex: 'mxDiotColumn13',
        key: 'mxDiotColumn13',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn14'),
        dataIndex: 'column_14',
        key: 'column_14',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn15'),
        dataIndex: 'mxDiotColumn15',
        key: 'mxDiotColumn15',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn16'),
        dataIndex: 'column_16',
        key: 'column_16',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn17'),
        dataIndex: 'mxDiotColumn17',
        key: 'mxDiotColumn17',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn18'),
        dataIndex: 'mxDiotColumn18',
        key: 'mxDiotColumn18',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn19'),
        dataIndex: 'mxDiotColumn19',
        key: 'mxDiotColumn19',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn20'),
        dataIndex: 'mxDiotColumn20',
        key: 'mxDiotColumn20',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn21'),
        dataIndex: 'mxDiotColumn21',
        key: 'mxDiotColumn21',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn22'),
        dataIndex: 'mxDiotColumn22',
        key: 'mxDiotColumn22',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn23'),
        dataIndex: 'mxDiotColumn23',
        key: 'mxDiotColumn23',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn24'),
        dataIndex: 'mxDiotColumn24',
        key: 'mxDiotColumn24',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn25'),
        dataIndex: 'mxDiotColumn25',
        key: 'mxDiotColumn25',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn26'),
        dataIndex: 'mxDiotColumn26',
        key: 'mxDiotColumn26',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn27'),
        dataIndex: 'mxDiotColumn27',
        key: 'mxDiotColumn27',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn28'),
        dataIndex: 'mxDiotColumn28',
        key: 'mxDiotColumn28',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn29'),
        dataIndex: 'mxDiotColumn29',
        key: 'mxDiotColumn29',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn30'),
        dataIndex: 'mxDiotColumn30',
        key: 'mxDiotColumn30',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn31'),
        dataIndex: 'mxDiotColumn31',
        key: 'mxDiotColumn31',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn32'),
        dataIndex: 'mxDiotColumn32',
        key: 'mxDiotColumn32',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn33'),
        dataIndex: 'mxDiotColumn33',
        key: 'mxDiotColumn33',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn34'),
        dataIndex: 'mxDiotColumn34',
        key: 'mxDiotColumn34',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn35'),
        dataIndex: 'mxDiotColumn35',
        key: 'mxDiotColumn35',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn36'),
        dataIndex: 'mxDiotColumn36',
        key: 'mxDiotColumn36',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn37'),
        dataIndex: 'mxDiotColumn37',
        key: 'mxDiotColumn37',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn38'),
        dataIndex: 'mxDiotColumn38',
        key: 'mxDiotColumn38',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn39'),
        dataIndex: 'mxDiotColumn39',
        key: 'mxDiotColumn39',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn40'),
        dataIndex: 'mxDiotColumn40',
        key: 'mxDiotColumn40',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn41'),
        dataIndex: 'mxDiotColumn41',
        key: 'mxDiotColumn41',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn42'),
        dataIndex: 'mxDiotColumn42',
        key: 'mxDiotColumn42',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn43'),
        dataIndex: 'mxDiotColumn43',
        key: 'mxDiotColumn43',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn44'),
        dataIndex: 'mxDiotColumn44',
        key: 'mxDiotColumn44',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn45'),
        dataIndex: 'mxDiotColumn45',
        key: 'mxDiotColumn45',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn46'),
        dataIndex: 'mxDiotColumn46',
        key: 'mxDiotColumn46',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn47'),
        dataIndex: 'mxDiotColumn47',
        key: 'mxDiotColumn47',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn48'),
        dataIndex: 'VAT_withheld_by_the_taxpayer',
        key: 'VAT_withheld_by_the_taxpayer',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn49'),
        dataIndex: 'column_49',
        key: 'column_49',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn50'),
        dataIndex: 'vat_exmt',
        key: 'vat_exmt',
        align: 'right',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn51'),
        dataIndex: 'vat_0',
        key: 'vat_0',
        align: 'right',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn52'),
        dataIndex: 'mxDiotColumn52',
        key: 'mxDiotColumn52',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn53'),
        dataIndex: 'mxDiotColumn53',
        key: 'mxDiotColumn53',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxDiotColumn54'),
        dataIndex: 'column_54',
        key: 'column_54',
        align: 'center',
        width: 140,
        ellipsis: false,
    },
]
const formatAmount = (value: number | string | null): string => {
    if (!value || value === 0) return ''
    const num = typeof value === 'string' ? Number(value.replace(/[,\s]/g, '')) : Number(value)
    return isNaN(num) || num === 0 ? '' : num.toString()
}
const formatString = (value: string | null): string => {
    if (value === null || value === undefined || value === '') return ''
    return String(value).trim()
}
const exportReport = async () => {
    try {
        tableLoading.value = true

        // Get the data from dataSource
        const data = dataSource.value // store.state.ReportStore.mxInformativeDeclarationReport

        if (!data || data.length === 0) {
            console.log('No data to export')
            return
        }

        // Create text content with pipe-separated values
        let textContent = ''

        // Process each row of data
        data.forEach((item: any) => {
            /**
             * columns:
             * 1. third_party_type
             * 2. type_operation
             * 3. rfc_data
             * 4. tax_identification_number
             * 5. contact_name_data
             * 7. specify_place_of_tax_jurisdiction
             * 12. vat_20
             * 14. column_14
             * 16. column_16
             * 48. VAT_withheld_by_the_taxpayer
             * 49. column_49
             * 50. vat_exmt
             * 51. vat_0
             * 54. column_54(固定值01)
             */
            if (!item) return
            let line = ''
            // Add the line data for first 7 columns
            line += `${formatString(item.third_party_type)}|${formatString(item.type_operation)}|${formatString(item.rfc_data)}|${formatString(item.tax_identification_number)}|${formatString(item.contact_name_data)}|${formatString(item.mxDiotColumn6)}|${formatString(item.specify_place_of_tax_jurisdiction)}|`
            // Add the line data for 4 empty columns
            line += '||||'
            // Add the line data for the vat_20， column_14, column_16 column
            line += `${formatAmount(item.vat_20)}||${formatAmount(item.column_14)}||${formatAmount(item.column_16)}|`
            // Add the line data for the empty 32 columns
            line += '|||||||||||||||||||||||||||||||'
            // Add the line data for the VAT_withheld_by_the_taxpayer, column_49, vat_exmt, vat_0 column
            line += `${formatAmount(item.VAT_withheld_by_the_taxpayer)}|${formatAmount(item.column_49)}|${formatAmount(item.vat_exmt)}|${formatAmount(item.vat_0)}|`
            // Add the line data for the last 3 columns
            line += `||${formatString(item.column_54)}`
            // Add the line to the text content
            textContent += line + '\n'
        })

        // Create a Blob with the text content
        const blob = new Blob([textContent], {type: 'text/plain;charset=utf-8'})

        // Generate filename with current date
        const currentDate = moment().format('YYYYMMDD')
        const filename = `InformativeDeclaration_${currentDate}.txt`

        // Create a download link and trigger the download
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()

        // Clean up
        setTimeout(() => {
            document.body.removeChild(link)
            URL.revokeObjectURL(link.href)
        }, 100)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}

const selectChange = async (date: string) => {
    if (date) {
        searchForm.month = date
    }
    await updateList()
}

const updateList = async () => {
    try {
        tableLoading.value = true

        await fetchReportsList({
            params: {
                company_code: searchForm.company_code,
                month: searchForm.month,
            },
        })
        tableLoading.value = false
        console.log('dataSource', dataSource.value)
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

onBeforeMount(async () => {
    searchForm.company_name = userCompany[0].name
    searchForm.company_code = userCompany[0].code

    await updateList()
})
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="header-title-wrap">
                <div class="header-title">{{ i18n.t('reports.mxInformativeDeclarationReport') }}</div>
                <!-- <div class="header-subtitle">{{ i18n.t('reports.updatedOn') }}: {{ today }}</div> -->
            </div>
            <a-divider type="vertical" style="margin-left: 460px" />
            <div class="selects-wrap">
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.month"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    picker="month"
                    format="YYYY-MM"
                    valueFormat="YYYY-MM"
                    :placeholder="i18n.t('reports.experationDate')"
                    :disabled="tableLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
            </div>

            <a-button
                type="primary"
                shape="round"
                :disabled="tableLoading || dataSource.length === 0"
                @click="exportReport()"
            >
                <export-outlined />
                {{ i18n.t('workTimeManager.export') }}
            </a-button>
        </div>
        <div class="page-content" ref="tableElWrapRef">
            <a-table
                :dataSource="dataSource"
                :columns="columns"
                :pagination="false"
                :loading="tableLoading"
                :scroll="{y: tableScrollY, x: '100%'}"
                :sticky="{ offsetHeader: 0 }"
                class="table-with-bottom-space"
            >
            </a-table>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.page-wrap {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    .page-header {
        min-height: 84px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        margin-bottom: 14px;
        flex-shrink: 0;

        .header-title-wrap {
            min-width: 148px;
            max-width: 148px;

            .header-title {
                white-space: nowrap;
                line-height: 26px;
                height: 26px;
                color: #004fc1;
                font-size: 20px;
                font-weight: 700;
                letter-spacing: 0;
            }

            .header-subtitle {
                white-space: nowrap;
                font-size: 12px;
                color: #262626;
                letter-spacing: 0;
                line-height: 18px;
                font-weight: 400;
            }
        }

        .ant-divider {
            border-left-color: #e2e2ea;
            height: 36px;
            top: 0;
            margin: 0 18px;
        }

        .selects-wrap {
            width: 100%;
            display: flex;
            align-items: center;

            .target-currency-select,
            .company-select,
            .coa-account-select {
                min-width: 175px;
                width: 175px;
                max-width: 500px;
                margin-right: 8px;
                flex: 1;
            }

            .data-select {
                min-width: 145px;
                width: 145px;
                max-width: 145px;
                margin-right: 8px;
                flex: 1;
            }

            .transaction-currency-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.2;
            }

            .coa-account-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.5;
                margin-right: 0;

                .select-all-item {
                    padding: 5px 12px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                    .ant-checkbox-wrapper {
                        width: 100%;
                    }
                }

                :deep(.ant-select-selector) {
                    overflow: hidden;

                    .ant-select-selection-overflow {
                        flex-wrap: nowrap;
                    }
                }
            }
        }

        .ant-btn-round {
            padding: 6px 10px;
            margin-left: 16px;
        }
    }

    .page-content {
        padding: 0 20px 20px;
        overflow: hidden;
        height: calc(100% - 105px);
        display: flex;
        flex-direction: column;
        flex: 1;
        position: relative;

        :deep(.ant-table-wrapper) {
            overflow: hidden;
            flex: 1;
            display: flex;
            flex-direction: column;
            
            .ant-spin-nested-loading {
                flex: 1;
                overflow: hidden;
                
                .ant-spin-container {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    
                    .ant-table {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        overflow: hidden;
                        
                        .ant-table-container {
                            display: flex;
                            flex-direction: column;
                            height: 100%;
                            overflow: hidden;
                            
                            .ant-table-header {
                                overflow: hidden !important;
                                position: sticky;
                                top: 0;
                                z-index: 2;
                            }
                            
                            .ant-table-body {
                                overflow-x: auto !important;
                                overflow-y: auto !important;
                                flex: 1;
                                min-height: 100px;
                                padding-bottom: 20px;
                                /* 确保只有这一层有滚动条 */
                            }
                        }
                    }
                }
            }
        }

        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
        }
        .table-with-bottom-space {
            margin-bottom: 0;
            position: relative;
            height: 100%;
            
            &::after {
                display: none;
            }
            
            :deep(.ant-table-body) {
                &::-webkit-scrollbar {
                    height: 10px;
                    display: block !important;
                }
                
                &::-webkit-scrollbar-thumb {
                    background-color: #c1c1c1;
                    border-radius: 5px;
                }
                
                &::-webkit-scrollbar-track {
                    background-color: #f1f1f1;
                }
            }
        }
        // :deep(.ant-table-wrapper .ant-table .ant-table-thead .ant-table-cell) {
        //     text-align: center !important;
        // }
    }

    .table-summary-wrap {
        .table-summary-total {
            text-align: left;
            font-weight: 600;
        }

        .table-summary-text {
            text-align: right;
            &.center {
                text-align: center;
            }
        }
    }

    .page-footer {
        padding: 5px 8px;
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;
            width: 100%;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                min-width: 45px;
            }

            .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
                min-width: 35px;
            }
        }
    }
}
</style>
