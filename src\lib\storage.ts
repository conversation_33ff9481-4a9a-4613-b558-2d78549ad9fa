/** @format */

import {Session, Local} from './knight-storage'

// 登录令牌
export const Token = new Session('Token')
export const TokenV1 = new Session('TokenV1')
export const TokenPGO = new Session('TokenPGO')
export const UserCompany = new Session('UserCompany')
export const UserInfo = new Session('UserInfo')
export const LocalCurrency = new Session('LocalCurrency')
export const CurrencyCountry = new Session('CurrencyCountry')
export const CurrencyLanguage = new Session('CurrencyLanguage')
export const FinancialYear = new Session('FinancialYear')
export const ShowTutorial = new Session('ShowTutorial')
export const LoginTimeout = new Session('LoginTimeout')
// 菜单
export const FirstListMenu = new Session('FirstListMenu')
export const FlatMenuListObj = new Session('FlatMenuListObj')
export const BookkeepingMenu = new Session('BookkeepingMenu')
export const KycMenu = new Session('KycMenu')
export const CurrentOpenKeys = new Session('CurrentOpenKeys')
export const CurrentSidebarItem = new Session('CurrentSidebarItem')
export const Org_Id = new Session('Org_Id')

export const JushiFetchInfo = new Session('JushiFetchInfo')
export const Ar_Integration = new Session('ar_integration')
export const Ap_Integration = new Session('ap_integration')
export const Payment_Integration = new Session('PaymentIntegration')

// 多语言信息
export const Language = new Local('Language')

// 自定义表格
export const ApCustomizeTable = new Local('ApCustomizeTable')
export const ArCustomizeTable = new Local('ArCustomizeTable')
export const BrHisCustomizeTable = new Local('BrHisCustomizeTable')
export const BrRecCustomizeTable = new Local('BrRecCustomizeTable')
export const PyCustomizeTable = new Local('PyCustomizeTable')
export const TaskCustomizeTable = new Local('TaskCustomizeTable')
export const GlCustomizeTable = new Local('GlCustomizeTable')

//远程登录
export const RemoteLogin = new Session('RemoteLogin')

export const ArIntegration = new Session('ar_integration') // AP Integration
export const ApIntegration = new Session('ap_integration') // AP Integration
export const FtsIntegration = new Session('FtsIntegration') // AP Integration
export const PaymentIntegration = new Session('PaymentIntegration')
export const ReconciliationIntegration = new Session('ReconciliationIntegration')
export const ReceivableIntegration = new Session('ReceivableIntegration')
export const ApplicationNumber = new Session('ApplicationNumber')
export const InstanceNumber = new Session('InstanceNumber')
export const UserId = new Session('UserId')
export const Password = new Session('Password')
export const Version = new Session('Version')

export const SapPayableGl = new Session('sapPayableGl')
export const SapPayableWbs = new Session('sapPayableWbs')
export const SapPayableCostCenter = new Session('sapPayableCostCenter')
export const SapPayableInternalOrder = new Session('sapPayableInternalOrder')
export const SapPayableProfitCenter = new Session('sapPayableProfitCenter')
