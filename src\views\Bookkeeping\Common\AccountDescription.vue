<!-- @format -->

<script lang="ts" setup>
import {
    computed,
    createVNode,
    nextTick,
    onBeforeMount,
    onBeforeUnmount,
    onMounted,
    reactive,
    ref,
    unref,
    watch,
} from 'vue'
import {useStore} from 'vuex'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {message, Modal} from 'ant-design-vue'
import {LeftOutlined, ExclamationCircleFilled} from '@ant-design/icons-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import dayjs from 'dayjs'
import AccountDescForm from '@/components/bookkeepingComponents/CommonComponents/AccountDescForm.vue'
import * as _ from 'lodash'
import {useRouter} from 'vue-router'
import {UserCompany} from '@/lib/storage'

const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const userCompany: any = UserCompany.get() || []
const leftContent = ref<HTMLBaseElement>()
const rightContent = ref<HTMLBaseElement>()
const leftTable = ref<HTMLBaseElement>()
const rightTable = ref<HTMLBaseElement>()
let timer: any = undefined
const accountFormLoading = ref(false)
const selectedRowKeys: any = ref([])
const current: any = ref({
    field_code: '',
    account_des: '',
    alias: '',
})
const tableLoading = ref(false)
const fullAccountTableLoading = ref(false)
const currentFullAccountSearch = reactive({keywords: '', group_id: '1'})
const currentCompanyAccountSearch = reactive({keywords: '', group_id: '1'})
const saveGroup: any = ref(null)
const saveGroup2: any = ref(null)
const switchFullAccountDescTable = ref(false)
const currentAccountAddedPageNumber = ref(1)
const accountAddedPageSize = ref(10)
const fullAccountPageNumber = ref(1)
const companyAccountPageNumber = ref(1)
const fullAccountPageSize = ref(10)
const companyAccountPageSize = ref(10)
const selectionRow: any = ref({})
const editMode = ref(false)
const show = ref(false)
// const currentSortColumn = ref<string | null>(null)
// const currentSortOrder = ref<string | null>(null)

const otherGroupAlreadyImported = ref(false)

const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}

const accountDescList = computed(() => store.state.AccountDescriptionStore.accountDescList)
const accountAddedTotalNumber = computed(() => store.state.AccountDescriptionStore.accountAddedTotalNumber)
const fullAccountDescList = computed(() => store.state.AccountDescriptionStore.fullAccountDescList)
const filteredAccountDescList = computed(() => store.state.AccountDescriptionStore.filteredAccountDescList)
const extendedAccountDescList = computed(() => store.state.AccountDescriptionStore.extendedAccountDescList)
const fullAccountTotalNumber = computed(() => store.state.AccountDescriptionStore.fullAccountTotalNumber)
const coaGroupListOptions = computed(() => store.state.AccountDescriptionStore.coaGroupList)
const coaGroupListOptions2 = computed(() => store.state.AccountDescriptionStore.coaGroupList2)

const companyTaxInfo: any = computed(() => store.state.TaxInfoStore.companyTaxInfo)

const fetchCompanyTaxInfo = (query?: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
// const fetchFullAccountDescNumber = () => store.dispatch('AccountDescriptionStore/fetchFullAccountDescTotalNumberV1')
const fetchAccountDesc = (query: any) => store.dispatch('AccountDescriptionStore/fetchAccountDescV2', query)
const fetchFullAccountDesc = (query: any) => store.dispatch('AccountDescriptionStore/fetchFullAccountDescV2', query)
const deleteChartOfAccount = (query: any) => store.dispatch('AccountDescriptionStore/deleteChartOfAccountV1', query)
const clearBankInfoOfCoA = (payload: any) => store.dispatch('BankInfoStore/clearBankInfoOfCoA', payload)
//const setChartOfAccountState = (query: any) => store.dispatch('AccountDescriptionStore/setChartOfAccountState', query)
const setChartOfAccountState = (query: any) => store.dispatch('AccountDescriptionStore/setChartOfAccountStatev1', query)
const createChartOfAccount = (query: any) => store.dispatch('AccountDescriptionStore/createChartOfAccountV1', query)
const fetchCoaGroup = (country: string) => store.dispatch('AccountDescriptionStore/fetchCoaGroupV1', country)
const fetchCoaGroup2 = (country: string) => store.dispatch('AccountDescriptionStore/fetchCoaGroup2V1', country)
const fetchFilteredAccountDesc = (query: any) =>
    store.dispatch('AccountDescriptionStore/fetchFilteredAccountDescV2', query)
const fetchExtendedAccountDesc = (query: any) =>
    store.dispatch('AccountDescriptionStore/fetchExtendedAccountDescV1', query)
const updateAccountDesc = async (data = {}) => {
    console.log('--------------------')
    const searchObj = {
        ...data,
        is_archive: 1,
        company: currentUserCompanyQuery.company_code,
        page_index: currentAccountAddedPageNumber.value,
        page_size: accountAddedPageSize.value,
        // $limit: currentAccountAddedPageNumber.value * accountAddedPageSize.value,
        // $skip: (currentAccountAddedPageNumber.value - 1) * accountAddedPageSize.value,
    } as any
    // console.log('currentSortColumn.value', currentSortColumn)
    // console.log('currentSortOrder.value', currentSortOrder)
    // if (currentSortColumn && currentSortOrder) {
    //     searchObj['$sort'] = {
    //         [currentSortColumn]: currentSortOrder,
    //     }
    // }
    console.log(searchObj)
    try {
        tableLoading.value = true
        await fetchAccountDesc(searchObj)
        await fetchExtendedAccountDesc({company: userCompany[0].code})
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
// const handleTableChange = (pagination, filters, sorter) => {
//     console.log('pagination', pagination)
//     console.log('filters', filters)
//     console.log('sorter', sorter)
//     // sorter 会包含排序相关的信息
//     if (sorter.field && sorter.order) {
//         handleSortChange(sorter.field, sorter.order)
//     }
// }
// const handleSortChange = (column, order) => {
//     // currentSortColumn = column
//     // currentSortOrder = order
//     // console.log('currentSortColumn.value', currentSortColumn)
//     // console.log('currentSortOrder.value', currentSortOrder)
//     // 调用 updateCompanyAccountDesc 从后台获取排序后的数据
//     // updateCompanyAccountDesc({
//     //     sortColumn: column,
//     //     sortOrder: order,
//     // })
//     updateAccountDesc()
// }
// const handleTableChange = (column: any) => {
//     console.log(column)
//     // 调用 updateCompanyAccountDesc 从后台获取排序后的数据
//     updateFullAccountDesc({
//         // 传递排序相关的参数，例如 column, sortOrder 等
//         sortColumn: column,
//         sortOrder: 'asc', // 你可以根据实际情况调整排序顺序
//     })
// }
const updateFullAccountDesc = async (data = {} as any) => {
    const searchObj = {
        company: userCompany[0].code,
        page_index: fullAccountPageNumber.value,
        page_size: fullAccountPageSize.value,
        template_name: saveGroup2.value,
        country: companyTaxInfo.value.country,
        // ...data,
        // pageIndex: fullAccountPageNumber.value,
        // pageSize: fullAccountPageSize.value,
        // limit: fullAccountPageNumber.value * fullAccountPageSize.value,
        // skip: (fullAccountPageNumber.value - 1) * fullAccountPageSize.value,
    } as any
    if (data.keywords) {
        // searchObj['$or[0][account_des][$like]'] = `%${data.keywords}%`
        // searchObj['$or[1][field_code][$like]'] = `%${data.keywords}%`
        searchObj.query = data.keywords
    } else {
        // searchObj = {
        //     // ...data,
        //     // $limit: fullAccountPageNumber.value * fullAccountPageSize.value,
        //     // $skip: (fullAccountPageNumber.value - 1) * fullAccountPageSize.value,
        // }
    }
    // searchObj['$sort[create_time]'] = 'desc'
    // account_des
    // field_code
    try {
        fullAccountTableLoading.value = true
        await fetchFullAccountDesc(searchObj)
    } catch (error) {
        console.log(error)
    } finally {
        fullAccountTableLoading.value = false
    }
}
const updateCompanyAccountDesc = async (data = {} as any) => {
    const searchObj = {
        company: userCompany[0].code,
        page_index: companyAccountPageNumber.value,
        page_size: companyAccountPageSize.value,
        template_name: saveGroup.value,
        country: companyTaxInfo.value.country,
        // ...data,
        // pageIndex: fullAccountPageNumber.value,
        // pageSize: fullAccountPageSize.value,
        // limit: fullAccountPageNumber.value * fullAccountPageSize.value,
        // skip: (fullAccountPageNumber.value - 1) * fullAccountPageSize.value,
    } as any
    if (data.keywords) {
        // searchObj['$or[0][account_des][$like]'] = `%${data.keywords}%`
        // searchObj['$or[1][field_code][$like]'] = `%${data.keywords}%`
        searchObj.query = data.keywords
    } else {
        // searchObj = {
        //     // ...data,
        //     // $limit: fullAccountPageNumber.value * fullAccountPageSize.value,
        //     // $skip: (fullAccountPageNumber.value - 1) * fullAccountPageSize.value,
        // }
    }
    // searchObj['$sort[create_time]'] = 'desc'
    // account_des
    // field_code
    // if (data.sortColumn && data.sortOrder) {
    //     console.log('data.sortColumn', data.sortColumn)
    //     console.log('data.sortOrder', data.sortOrder)
    //     // 处理排序相关的参数
    //     searchObj['$sort'] = {
    //         [data.sortColumn]: data.sortOrder,
    //     }
    // }
    // 传递当前的排序信息
    // if (currentSortColumn.value && currentSortOrder.value) {
    //     searchObj['$sort'] = {
    //         [currentSortColumn.value]: currentSortOrder.value,
    //     }
    // }
    try {
        tableLoading.value = true
        await fetchAccountDesc(searchObj)
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const searchFullAccount = async () => {
    clearInterval(timer)
    currentFullAccountSearch.group_id = '1'
    fullAccountPageNumber.value = 1
    await updateFullAccountDesc(currentFullAccountSearch)
}
const searchCompanyAccount = async () => {
    clearInterval(timer)
    currentCompanyAccountSearch.group_id = '1'
    companyAccountPageNumber.value = 1
    await updateCompanyAccountDesc(currentCompanyAccountSearch)
}
const changeAccountAddedPage = () => {
    updateAccountDesc()
}
const changeCurrentAccountAddedPageNumber = (pageNumber: number) => {
    currentAccountAddedPageNumber.value = pageNumber
    updateAccountDesc()
}
const changeAccountAddedPageSize = (pageSize: number) => {
    accountAddedPageSize.value = pageSize
    currentAccountAddedPageNumber.value = 1
    updateAccountDesc()
}
const changeFullAccountAddedPage = () => {
    updateFullAccountDesc(currentFullAccountSearch)
}
const changeFullAccountPageNumber = (pageNumber: number) => {
    fullAccountPageNumber.value = pageNumber
    updateFullAccountDesc(currentFullAccountSearch)
}
const changeFullAccountPageSize = (pageSize: number) => {
    fullAccountPageSize.value = pageSize
    fullAccountPageNumber.value = 1
    updateFullAccountDesc(currentFullAccountSearch)
}
const showFullAccountDesc = async () => {
    switchFullAccountDescTable.value = !switchFullAccountDescTable.value
    if (!switchFullAccountDescTable.value) return
    currentFullAccountSearch.group_id = '1'
    await updateFullAccountDesc(currentFullAccountSearch)
    if (_.isEmpty(router.currentRoute.value.query) || !router.currentRoute.value.query.showTutorial) return
    addTutorialInLoadedPage()
    setTimeout(() => {
        const _turorial3Btn = document.getElementById('turorial3Btn')
        const _turorial3 = document.getElementById('turorial3')
        if (_turorial3Btn)
            _turorial3Btn.addEventListener('click', function () {
                _turorial3!.remove()
                router.push({
                    path: '/bookkeeping/ap/uploadInvoice',
                    query: {showTutorial: 'true'},
                })
            })
    }, 100)
}
const add = () => {
    show.value = true
    accountFormLoading.value = true
    editMode.value = false
    const existedCount = extendedAccountDescList.value.filter(
        (x: any) => x.account_code.substring(0, 4) === selectionRow.value.account_code.substring(0, 4),
    ).length

    current.value = {...selectionRow.value}
    current.value.account_code = getAccountCode(selectionRow.value.account_code, existedCount)
}
// TODO and TOTEST
const importCoaGroup = () => {
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: i18n.t('AccountDescription.importAll'), //'Do you want to import all CoA from the selected group?',
        icon: createVNode(ExclamationCircleFilled),
        cancelButtonProps: {
            shape: 'round',
            size: 'small',
        },
        okButtonProps: {
            type: 'primary',
            shape: 'round',
            size: 'small',
        },
        async onOk() {
            const requestList = []
            await fetchFilteredAccountDesc({template_name: saveGroup.value, country: companyTaxInfo.value.country})
            for (const item of filteredAccountDescList.value) {
                const {account_code, id, ...rest} = item
                const existedCount = extendedAccountDescList.value.filter(
                    (x: any) => x.account_code.substring(0, 4) === account_code.substring(0, 4),
                ).length
                const query = {
                    account_code: getAccountCode(account_code, existedCount),
                    company: userCompany[0].code,
                    ...rest,
                }
                requestList.push(createChartOfAccount(query))
            }

            try {
                await Promise.all(requestList)
            } catch (error) {
                console.log(error)
            } finally {
                await updateAccountDesc()
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
    })
}

const importCoaGroup2 = async () => {
    currentFullAccountSearch.group_id = '1'
    fullAccountPageNumber.value = 1
    await updateFullAccountDesc(currentFullAccountSearch)
}
const edit = (record: any) => {
    show.value = true
    editMode.value = true
    current.value = {...record}
}
const pauseCoa = (record: {is_archive: boolean; name: any; account_code: any}) => {
    // const setStatusFlag = record.is_archive === 0 || record.is_archive === null ? 1 : 0
    const setStatusFlag = !record.is_archive
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content:
            !record.is_archive || record.is_archive === null
                ? i18n.t('chartOfAccount.msgCoaPause01') + record.name + i18n.t('chartOfAccount.msgCoaPause02')
                : i18n.t('chartOfAccount.msgCoaActive01') + record.name + i18n.t('chartOfAccount.msgCoaActive02'),
        icon: createVNode(ExclamationCircleFilled),
        cancelButtonProps: {
            shape: 'round',
            size: 'small',
        },
        okButtonProps: {
            type: 'primary',
            shape: 'round',
            size: 'small',
        },
        async onOk() {
            try {
                const query = {
                    company: userCompany[0].code,
                    account_code: record.account_code,
                }
                const response = await deleteChartOfAccount(query)
                if (response.status === 200) {
                    // debugger;
                    const param = {
                        company_code: userCompany[0].code,
                        gl_account: record.account_code,
                    }
                    await clearBankInfoOfCoA(param)

                    message.success(i18n.t('ApComponents.success'))
                }
                // else {
                //     message.error({
                //         content: 'failed',
                //         duration: 3,
                //     })
                // }
                await updateAccountDesc()
            } catch (error) {
                console.log(error)
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
    })
}

// const field_mapping = (source: any) => {
//     const payload = {} as any
//     payload['company'] = source.company_code || userCompany[0].code
//     payload['account_code'] = source.account_code || ''
//     payload['name'] = source.name || ''
//     payload['category'] = source.category || ''
//     return payload
// }

const getAccountCode = (sourceCode: string, order = 0): string => {
    //common account code should in 'baseCode-company_code-order'
    // '9220-3002-2'
    let [_base, _company_code, _order] = sourceCode.split('-')
    _base = _base.toString()
    // _order = accountDescList.value.filter((x: any) => x.account_code.substring(0, 4) === _base).length
    _company_code = _company_code ?? userCompany[0].code
    // _order = order
    //     ? order.toString()
    //     : (+(Number(_order) || 0) + 1).toLocaleString('en-US', {
    //           minimumIntegerDigits: 2,
    //           useGrouping: false,
    //       })
    _order = (order + 1).toLocaleString('en-US', {
        minimumIntegerDigits: 2,
        useGrouping: false,
    })
    return `${_base}-${_company_code}-${_order}`
}

const copy = (record: {[x: string]: any; alias: any}) => {
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: i18n.t('AccountDescription.add'), //'Do you want to add a new sub category?',
        icon: createVNode(ExclamationCircleFilled),
        cancelButtonProps: {
            shape: 'round',
            size: 'small',
        },
        okButtonProps: {
            type: 'primary',
            shape: 'round',
            size: 'small',
        },
        async onOk() {
            try {
                const {id, name, company_code, account_code, created_on, updated_on, duplicate, ...rest} = record
                const existedCount = extendedAccountDescList.value.filter(
                    (x: any) => x.account_code.substring(0, 4) === account_code.substring(0, 4),
                ).length
                console.log(record)
                const query: any = {
                    name: name + ' - copy',
                    company: company_code,
                    account_code: getAccountCode(account_code, existedCount),
                    create_on: dayjs().toISOString(),
                    updated_on: dayjs().toISOString(),
                    duplicate: duplicate,
                    ...rest,
                }
                //test
                // query.bk_type = 1
                console.log('test-copy: ', query)
                const response = await createChartOfAccount(query)
                if (response.status === 200 || response.status === 201) {
                    message.success(i18n.t('ApComponents.success'))
                }
                // else {
                //     message.error({
                //         content: response.data.message,
                //         duration: 5,
                //     })
                // }
                await updateAccountDesc()
            } catch (error: any) {
                console.log(error)
                // message.error({
                //     content: error.response.data.errors || 'failed',
                //     duration: 5,
                // })
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
    })
}
const remove = (record: {alias: string; id: any}) => {
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: i18n.t('chartOfAccount.msgCoaDelete01') + record.alias + i18n.t('chartOfAccount.msgCoaDelete02'),
        icon: createVNode(ExclamationCircleFilled),
        cancelButtonProps: {
            shape: 'round',
            size: 'small',
        },
        okButtonProps: {
            type: 'primary',
            shape: 'round',
            size: 'small',
        },
        async onOk() {
            try {
                const response = await deleteChartOfAccount(record.id)
                if (response.status === 200) {
                    message.success(i18n.t('ApComponents.success'))
                }
                // else {
                //     message.error({
                //         content: response.data.message,
                //     })
                // }
                await updateAccountDesc()
            } catch (error) {
                console.log(error)
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
    })
}

const inputChange = () => {
    delayTimer(0)
}
const inputChangeCompanyCOA = () => {
    delayTimerCompanyCOA(0)
}
const delayTimerCompanyCOA = (i: number) => {
    const _timer = 2
    clearInterval(timer)
    timer = setInterval(() => {
        ++i
        if (i == _timer) {
            searchCompanyAccount() //改成 search company account
            clearInterval(timer)
        }
    }, 1000)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer)
    timer = setInterval(() => {
        ++i
        if (i == _timer) {
            searchFullAccount()
            clearInterval(timer)
        }
    }, 1000)
}
const showDialog = (bool: boolean) => {
    show.value = bool
}
const dismiss = () => {
    showDialog(false)
    editMode.value = false
}
const clearSelection = () => {
    selectedRowKeys.value = []
    selectionRow.value = {}
}
const handleSelectionChange = (rows: any[]) => {
    const key = rows.pop()
    selectedRowKeys.value = [key]
    selectionRow.value = fullAccountDescList.value.find((i: any) => i.id === key) || {}
}
const previewCoaGroup = async (value: string) => {
    currentFullAccountSearch.group_id = value
    currentFullAccountSearch.keywords = ''
}
const changeCoaGroup = async (value: string) => {
    currentFullAccountSearch.group_id = value
    currentFullAccountSearch.keywords = ''
    await updateFullAccountDesc(currentFullAccountSearch)
}
// deletebutton() {
//   switchFullAccountDescTable.value = false
//   updateFullAccountDesc(currentFullAccountSearch)
// }

watch(
    () => accountDescList,
    () => {
        const filteredList = accountDescList.value.filter((item: {template_name: string}) => !!item.template_name)
        if (filteredList.length > 0) {
            saveGroup.value = filteredList[0].template_name
            // set to false for test import
            otherGroupAlreadyImported.value = true
        } else {
            otherGroupAlreadyImported.value = false
        }
    },
    {
        deep: true,
        immediate: true,
    },
)

watch(
    () => coaGroupListOptions2.value,
    () => {
        saveGroup2.value = (_.last(coaGroupListOptions2.value) as any)?.group_name
    },
    {immediate: true},
)

onBeforeMount(async () => {
    saveGroup.value = null
    saveGroup2.value = null
    await fetchCompanyTaxInfo({code: userCompany[0].code})
    await Promise.all([
        updateAccountDesc(),
        fetchCoaGroup(companyTaxInfo.value.country),
        fetchCoaGroup2(companyTaxInfo.value.country),
    ])
})

onMounted(() => {
    console.log(coaGroupListOptions.value)
})
const addTutorial = () => {
    nextTick(() => {
        const importCoaGroup: any = document.getElementById('import-coa-group')
        const showFullAccountDesc: any = document.getElementById('show-full-account-desc')
        const appEle: any = document.getElementById('app')
        // step1
        const tutorialHeight1 = importCoaGroup.clientHeight + 20
        const tutorialWidth1 = importCoaGroup.clientWidth + 20
        const bodyRect1 = document.body.getBoundingClientRect()
        const elemRect1 = importCoaGroup.getBoundingClientRect()
        const topOffset1 = elemRect1.top - bodyRect1.top - 10
        const leftOffset1 = elemRect1.left - bodyRect1.left - 10

        const tutorialDiv1 = document.createElement('div')
        tutorialDiv1.id = 'turorial1'
        const tutorialStr1 = `
                <div class="tutorial-main-page" style="height: ${tutorialHeight1}px; width: ${tutorialWidth1}px; top: ${topOffset1}px; left: ${leftOffset1}px">
                    <div
                        class="inner-content"
                        style="height: ${tutorialHeight1 - 10}px; width: ${
            tutorialWidth1 - 10
        }px; top: ${topOffset1}px; left: ${leftOffset1}px"
                    >
                    </div>
                </div>
                <div
                    class="tutorial-tips"
                    style="top: ${topOffset1 + tutorialHeight1 + 15}px; left: ${leftOffset1 + 80}px; width: ${
            tutorialWidth1 + 70
        }px"
                >
                    <div class="tips-content">
                        <div class="content-wrap">
                            <div class="title">${i18n.t('AccountDescription.create')}</div>
                            <div class="description">${i18n.t('AccountDescription.step1')}</div>
                        </div>
                        <button class="btn-select" type="link" onclick="var x = document.getElementById('turorial1');x.remove();var y = document.getElementById('turorial2');y.style.display = 'block';"> ${i18n.t(
                            'AccountDescription.next',
                        )} </button>
                    </div>
                    <div class="arrow-up-left"></div>
                </div>
                `
        tutorialDiv1.innerHTML = tutorialStr1
        appEle.appendChild(tutorialDiv1)
        // step2
        const tutorialHeight2 = showFullAccountDesc.clientHeight + 20
        const tutorialWidth2 = showFullAccountDesc.clientWidth + 20
        const bodyRect2 = document.body.getBoundingClientRect()
        const elemRect2 = showFullAccountDesc.getBoundingClientRect()
        const topOffset2 = elemRect2.top - bodyRect2.top - 10
        const leftOffset2 = elemRect2.left - bodyRect2.left - 10

        const tutorialDiv2 = document.createElement('div')
        tutorialDiv2.id = 'turorial2'
        const tutorialStr2 = `
                <div class="tutorial-main-page" style="height: ${tutorialHeight2}px; width: ${tutorialWidth2}px; top: ${topOffset2}px; left: ${leftOffset2}px">
                    <div
                        class="inner-content"
                        style="height: ${tutorialHeight2 - 10}px; width: ${
            tutorialWidth2 - 10
        }px; top: ${topOffset2}px; left: ${leftOffset2}px"
                    >
                    </div>
                </div>
                <div
                    class="tutorial-tips"
                    style="top: ${topOffset2 + tutorialHeight2 + 15}px; left: ${leftOffset2 + 80}px; width: ${
            tutorialWidth2 + 70
        }px"
                >
                    <div class="tips-content">
                        <div class="content-wrap">
                            <div class="title">${i18n.t('AccountDescription.create')}</div>
                            <div class="description">${i18n.t('AccountDescription.step2')}</div>
                        </div>
                        <button class="btn-select" type="link" onclick="var x = document.getElementById('turorial2');x.remove();"> Next step </button>
                    </div>
                    <div class="arrow-up-left"></div>
                </div>
                `
        tutorialDiv2.innerHTML = tutorialStr2
        tutorialDiv2.style.display = 'none'
        appEle.appendChild(tutorialDiv2)
    })
}
const addTutorialInLoadedPage = () => {
    nextTick(() => {
        const coaRepoTable: any = document.getElementById('coa-repo-table')
        const appEle: any = document.getElementById('app')
        // step3
        const tutorialHeight3 = coaRepoTable.clientHeight - 60 + 20
        const tutorialWidth3 = 52
        const bodyRect3 = document.body.getBoundingClientRect()
        const elemRect3 = coaRepoTable.getBoundingClientRect()
        const topOffset3 = elemRect3.top - bodyRect3.top + 52 - 10
        const leftOffset3 = elemRect3.left - bodyRect3.left - 10

        const tutorialDiv3 = document.createElement('div')
        tutorialDiv3.id = 'turorial3'
        const tutorialStr3 = `
                <div class="tutorial-main-page" style="height: ${tutorialHeight3}px; width: ${tutorialWidth3}px; top: ${topOffset3}px; left: ${leftOffset3}px">
                    <div
                        class="inner-content"
                        style="height: ${tutorialHeight3 - 10}px; width: ${
            tutorialWidth3 - 10
        }px; top: ${topOffset3}px; left: ${leftOffset3}px"
                    >
                    </div>
                </div>
                <div
                    class="tutorial-tips"
                    style="top: ${topOffset3 + tutorialHeight3 / 10}px; left: ${
            leftOffset3 - tutorialWidth3 - 220
        }px; width: ${tutorialWidth3 + 205}px"
                >
                    <div class="tips-content">
                        <div class="content-wrap">
                            <div class="title">${i18n.t('AccountDescription.create')}</div>
                            <div class="description">${i18n.t('AccountDescription.step3')}</div>
                        </div>
                        <button class="btn-select" type="link" id="turorial3Btn"> ${i18n.t(
                            'AccountDescription.got',
                        )} </button>
                    </div>
                    <div class="arrow-up"></div>
                </div>
                `
        tutorialDiv3.innerHTML = tutorialStr3
        appEle.appendChild(tutorialDiv3)
    })
}
onMounted(() => {
    if (_.isEmpty(router.currentRoute.value.query) || !router.currentRoute.value.query.showTutorial) return
    addTutorial()
})
onBeforeUnmount(() => {
    const x = document.getElementById('turorial1')
    const y = document.getElementById('turorial2')
    const z = document.getElementById('turorial3')
    if (x) x.remove()
    if (y) y.remove()
    if (z) z.remove()
})
</script>
<template>
    <div class="account-description-wrap">
        <div class="content-box">
            <div class="content-box-left">
                <div class="content-box-header">
                    <div class="title">{{ i18n.t('chartOfAccount.coa') }}</div>
                    <div class="content">
                        <div id="import-coa-group">
                            <a-select
                                :disabled="otherGroupAlreadyImported"
                                :placeholder="i18n.t('AccountDescription.importGroup')"
                                v-model:value="saveGroup"
                                @change="importCoaGroup"
                            >
                                <a-select-option
                                    v-for="item in coaGroupListOptions"
                                    :key="item.group_id"
                                    :value="item.group_name"
                                >
                                    {{ item.group_name }}
                                </a-select-option>
                            </a-select>
                        </div>
                        <a-input
                            v-model:value="currentCompanyAccountSearch.keywords"
                            :placeholder="$t('AccountDescription.cdnm')"
                            autocomplete="off"
                            clearable
                            class="input-wrap"
                            @input="inputChangeCompanyCOA"
                            @pressEnter="searchCompanyAccount"
                            style="margin-right: 10px"
                        >
                            <template #suffix>
                                <svg-icon name="icon_search"></svg-icon>
                            </template>
                        </a-input>
                        <a-button
                            :type="switchFullAccountDescTable ? 'danger' : 'primary'"
                            @click="showFullAccountDesc"
                            shape="round"
                            :disabled="tableLoading || fullAccountTableLoading"
                            id="show-full-account-desc"
                        >
                            {{
                                switchFullAccountDescTable
                                    ? i18n.t('chartOfAccount.btnReloadCoa')
                                    : i18n.t('chartOfAccount.btnLoadCoa')
                            }}
                        </a-button>
                    </div>
                </div>
                <div class="content-box-content" ref="leftContent">
                    <div ref="leftTable">
                        <a-table
                            :dataSource="accountDescList"
                            :loading="tableLoading"
                            :pagination="false"
                            :scroll="
                                !(leftTable?.offsetHeight || 0) ||
                                !(leftContent?.offsetHeight || 0) ||
                                (leftTable?.offsetHeight || 0) < (leftContent?.offsetHeight || 0)
                                    ? {}
                                    : {y: leftContent ? leftContent?.offsetHeight - 52 - 34 : 440}
                            "
                        >
                            <!-- <a-table-column
                      data-index="fieldCode"
                      :title="i18n.t('chartOfAccount.fieldCode')"
                      align="center"
                      min-width="20%"
                    >
                    </a-table-column> -->
                            <a-table-column
                                data-index="name"
                                :title="i18n.t('chartOfAccount.alias')"
                                :ellipsis="true"
                                width="150px"
                                align="center"
                            />

                            <a-table-column
                                data-index="account_code"
                                :title="i18n.t('chartOfAccount.account')"
                                width="100px"
                                :ellipsis="true"
                                align="center"
                            />
                            <a-table-column
                                v-if="false"
                                data-index="name"
                                :title="i18n.t('chartOfAccount.accountDes')"
                                :ellipsis="true"
                                width="200px"
                                align="center"
                            />
                            <a-table-column
                                :title="i18n.t('chartOfAccount.operation')"
                                key="operation"
                                align="center"
                                width="120px"
                            >
                                <template #default="{record}">
                                    <span>
                                        <a-button title="Edit" class="btn-txt" type="link" @click="edit(record)">
                                            <svg-icon name="icon_edit2"></svg-icon>
                                        </a-button>
                                        <a-button
                                            class="btn-txt"
                                            type="link"
                                            :title="i18n.t('AccountDescription.pause')"
                                            @click="pauseCoa(record)"
                                            v-if="!record.is_archive || false"
                                        >
                                            <svg-icon name="icon_pause" style="color: #f5222d"></svg-icon>
                                        </a-button>
                                        <a-button
                                            v-else-if="record.is_archive"
                                            class="btn-txt"
                                            type="link"
                                            :title="i18n.t('AccountDescription.active')"
                                            @click="pauseCoa(record)"
                                        >
                                            <svg-icon name="icon_active"></svg-icon>
                                        </a-button>
                                        <a-button
                                            class="btn-txt"
                                            type="link"
                                            :title="i18n.t('AccountDescription.copy')"
                                            @click="copy(record)"
                                        >
                                            <svg-icon name="icon_edit1"></svg-icon>
                                        </a-button>
                                        <!-- <a-button class="btn-txt" type="link" @click="remove(record)">
                                      <svg-icon name="icon_delete" style="color: #f5222d"></svg-icon>
                                    </a-button> -->
                                    </span>
                                </template>
                            </a-table-column>
                        </a-table>
                    </div>
                </div>

                <div class="content-box-footer pagination-wrap">
                    <a-pagination
                        v-model:current="currentAccountAddedPageNumber"
                        v-model:page-size="accountAddedPageSize"
                        :disabled="tableLoading"
                        :pageSizeOptions="['1', '2', '10', '20', '30', '50']"
                        :hideOnSinglePage="false"
                        :showSizeChanger="true"
                        :total="accountAddedTotalNumber"
                        @change="changeAccountAddedPage"
                    ></a-pagination>
                    <span
                        >{{ i18n.t('bkApInvoice.total') }} {{ accountAddedTotalNumber }}
                        {{ accountAddedTotalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                    >
                </div>
            </div>

            <div class="content-box-middle">
                <a-button v-show="switchFullAccountDescTable" type="primary" :disabled="!selectionRow.id" @click="add">
                    <template #icon>
                        <left-outlined />
                    </template>
                </a-button>
            </div>

            <div class="content-box-right" v-if="switchFullAccountDescTable">
                <div class="content-box-header">
                    <!-- <div class="title">{{ i18n.t('chartOfAccount.fullCoa') }}</div> -->
                    <a-select
                        :placeholder="$t('AccountDescription.importGroup')"
                        v-model:value="saveGroup2"
                        @change="importCoaGroup2"
                    >
                        <a-select-option
                            v-for="item in coaGroupListOptions2"
                            :key="item.group_id"
                            :value="item.group_name"
                        >
                            {{ item.group_name }}
                        </a-select-option>
                    </a-select>
                    <a-input
                        v-model:value="currentFullAccountSearch.keywords"
                        :placeholder="$t('AccountDescription.cdnm')"
                        autocomplete="off"
                        clearable
                        class="input-wrap"
                        @input="inputChange"
                        @pressEnter="searchFullAccount"
                    >
                        <template #suffix>
                            <svg-icon name="icon_search"></svg-icon>
                        </template>
                    </a-input>
                </div>
                <div class="content-box-content" ref="rightContent">
                    <div ref="rightTable">
                        <a-table
                            :dataSource="fullAccountDescList"
                            :loading="fullAccountTableLoading"
                            rowKey="id"
                            :row-selection="{
                                onChange: handleSelectionChange,
                                hideSelectAll: true,
                                selectedRowKeys: unref(selectedRowKeys),
                            }"
                            :pagination="false"
                            :scroll="
                                !(rightTable?.offsetHeight || 0) ||
                                !(rightContent?.offsetHeight || 0) ||
                                (rightTable?.offsetHeight || 0) < (rightContent?.offsetHeight || 0)
                                    ? {}
                                    : {y: rightContent ? rightContent?.offsetHeight - 52 - 34 : 440, x: false}
                            "
                            id="coa-repo-table"
                        >
                            <a-table-column
                                data-index="account_code"
                                :title="i18n.t('chartOfAccount.account')"
                                width="30%"
                                align="center"
                            />
                            <!--                          :title="i18n.t('chartOfAccount.alias')"-->
                            <a-table-column
                                data-index="name"
                                :title="i18n.t('chartOfAccount.alias')"
                                :ellipsis="true"
                                width="70%"
                                align="center"
                            />
                        </a-table>
                    </div>
                </div>

                <div class="content-box-footer pagination-wrap">
                    <a-pagination
                        v-model:page-size="fullAccountPageSize"
                        v-model:current="fullAccountPageNumber"
                        :total="fullAccountTotalNumber"
                        :hideOnSinglePage="false"
                        :showSizeChanger="true"
                        simple
                        @change="changeFullAccountAddedPage"
                    ></a-pagination>
                    <span
                        >{{ i18n.t('bkApInvoice.total') }} {{ fullAccountTotalNumber }}
                        {{ fullAccountTotalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                    >
                </div>
            </div>
        </div>

        <a-modal
            :title="editMode ? i18n.t('chartOfAccount.editCoaTitle') : i18n.t('chartOfAccount.createCoaTitle')"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="680"
            :wrapClassName="'modal-wrap'"
        >
            <account-desc-form
                :current-account-desc="current"
                :edit-mode="editMode"
                @updateData="updateAccountDesc({})"
                @dismiss="dismiss"
                @clean="clearSelection"
            ></account-desc-form>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
.account-description-wrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    .content-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        .content-box-header {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px 10px;
            align-items: center;
            border-bottom: 1px solid #e2e2ea;
            .title {
                font-size: 20px;
                line-height: 26px;
                font-weight: 700;
            }

            .content {
                display: flex;
            }

            .ant-select {
                margin-right: 8px;
                min-width: 130px;
            }
            .ant-btn {
                min-width: 167px;
            }

            .input-wrap {
                width: 200px;
                color: #676d7c;
            }
        }
        .content-box-content {
            padding: 24px 20px 10px;
            height: calc(100% - 60px - 68px);
            overflow: hidden;
            :deep(.ant-table-tbody .ant-table-cell) {
                height: 52px;
                padding: 8px 10px;
            }
        }

        .content-box-footer {
            padding: 16px 20px;
            &.pagination-wrap {
                display: flex;
                justify-content: flex-end;
                align-items: center;

                span {
                    font-size: 12px;
                    margin-left: 8px;
                    line-height: 16px;
                    color: #8c8c8c;
                }
                :deep(.ant-pagination-simple) {
                    .ant-pagination-next,
                    .ant-pagination-prev {
                        height: 32px;
                        line-height: 30px;
                        vertical-align: middle;
                        .ant-pagination-item-link {
                            height: 32px;
                        }
                    }
                    .ant-pagination-simple-pager {
                        height: 25px;
                        line-height: 25px;
                        input {
                            border-radius: 12px;
                        }
                    }
                }
            }
        }
        .content-box-left {
            width: calc(100% - 454px - 28px - 8px);
            height: 100%;
            background-color: #fff;
            border-radius: 12px;
        }
        .content-box-right {
            width: 454px;
            height: 100%;
            background-color: #fff;
            border-radius: 12px;

            .content-box-content:deep(.ant-table) {
                .ant-table-thead .ant-table-cell {
                    padding: 12px 8px;
                }
            }
        }
        .content-box-middle {
            width: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            button {
                width: 28px;
                height: 28px;
                padding: 0;
            }
        }
    }
    .btn-txt {
        padding-left: 0;
        padding-right: 0;
    }
    .btn-txt + .btn-txt {
        margin-left: 12px;
    }
}
</style>
