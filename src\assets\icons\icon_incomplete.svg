<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon_incomplete@3x</title>
    <g id="页面-svg" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1.2Purchase_Invoices1" transform="translate(-1236.000000, -480.000000)">
            <g id="编组-22备份" transform="translate(316.000000, 98.000000)">
                <g id="编组-22备份-5" transform="translate(20.000000, 166.000000)">
                    <g id="编组-9备份-10" transform="translate(0.000000, 200.000000)">
                        <g id="编组-3" transform="translate(900.000000, 16.000000)">
                            <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="20" height="20"></rect>
                            <circle id="椭圆形" stroke="#FF372F" stroke-width="1.2" stroke-linecap="round" cx="10" cy="10" r="6"></circle>
                            <g id="编组-17" transform="translate(7.600000, 7.600000)" stroke="#FF372F" stroke-linecap="round" stroke-width="1.2">
                                <polyline id="路径" transform="translate(2.404163, 2.404163) rotate(-45.000000) translate(-2.404163, -2.404163) " points="-0.495836944 2.40416306 5.30416306 2.40416306 5.30416306 2.40416306"></polyline>
                                <polyline id="路径" transform="translate(2.404163, 2.404163) rotate(-315.000000) translate(-2.404163, -2.404163) " points="-0.495836944 2.40416306 5.30416306 2.40416306 5.30416306 2.40416306"></polyline>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>