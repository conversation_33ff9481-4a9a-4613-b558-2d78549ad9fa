/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'

const http = service

const SupplierStore = {
    namespaced: true,
    state: {
        supplierList: [],
        totalNumber: 0,
    },
    mutations: {
        updateSupplierList(state: {supplierList: any[]}, list: any) {
            state.supplierList = [...list]
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
    },
    actions: {
        async fetchSuppliers(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/supplier/list', payload)
            const list = response.data.data.list.map((item: any) => {
                return {
                    ...item,
                    accountType: 'Supplier',
                }
            })
            store.commit('updateSupplierList', list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async createSupplier(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.post('/bk/supplier', payload)
        },
        async updateSupplier(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.put('/bk/supplier', payload)
        },
        async deleteSupplier(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/supplier/${payload}`)
        },
    },
}

export default SupplierStore
