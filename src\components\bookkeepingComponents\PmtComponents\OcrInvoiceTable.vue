<!-- @format -->

<template>
    <div>
        <div class="content-box-right-inner">
            <a-spin :spinning="state.invoiceTableLoading">
                <a-table ref="multipleTable" :dataSource="tableData" :pagination="false" :max-height="'500px'">
                    <a-table-column
                        dataIndex="filePageIndex"
                        :title="'Index'"
                        align="center"
                        header-align="center"
                        min-width="10%"
                    ></a-table-column>
                    <a-table-column
                        dataIndex="invoiceNo"
                        :title="'Invoice No.'"
                        align="center"
                        header-align="center"
                        min-width="32%"
                        :show-overflow-tooltip="true"
                    >
                    </a-table-column>
                    <a-table-column
                        dataIndex="totalFee"
                        :title="'Total'"
                        align="center"
                        header-align="center"
                        min-width="14%"
                    >
                    </a-table-column>
                    <a-table-column
                        dataIndex="totalTax"
                        :title="'Total Tax'"
                        align="center"
                        header-align="center"
                        min-width="14%"
                    >
                    </a-table-column>
                    <!--                    <a-table-column-->
                    <!--                        dataIndex="invoiceCreatedStatus"-->
                    <!--                        :title="'Status'"-->
                    <!--                        align="center"-->
                    <!--                        header-align="center"-->
                    <!--                        min-width="20%"-->
                    <!--                    >-->
                    <!--                        <template #default="{record}">-->
                    <!--                            <span v-if="record.invoiceCreatedStatus === '1'">-->
                    <!--                                <a-tag type="success">Created</a-tag>-->
                    <!--                            </span>-->
                    <!--                            <span v-else>-->
                    <!--                                <a-tag type="danger">Pending</a-tag>-->
                    <!--                            </span>-->
                    <!--                        </template>-->
                    <!--                    </a-table-column>-->
                    <!--                    <a-table-column :title="'Operation'" align="center" header-align="center" min-width="12%">-->
                    <!--                        <template #default="{record}">-->
                    <!--                            <span v-if="record.invoiceCreatedStatus === '0'">-->
                    <!--                                <a-button-->
                    <!--                                    class="btn-txt"-->
                    <!--                                    type="link"-->
                    <!--                                    :title="'Create Invoice'"-->
                    <!--                                    @click="editDetail(scope.row)"-->
                    <!--                                    ><file-add-outlined />-->
                    <!--                                    &lt;!&ndash;                                    <a-icon type="form" />&ndash;&gt;-->
                    <!--                                </a-button>-->
                    <!--                                &lt;!&ndash; <a-divider type="vertical" /> &ndash;&gt;-->
                    <!--                            </span>-->
                    <!--                            <span v-else>-->
                    <!--                                <a-tooltip-->
                    <!--                                    class="item"-->
                    <!--                                    effect="dark"-->
                    <!--                                    content="More details, see [AP Invoice History ] page"-->
                    <!--                                    placement="top-end"-->
                    <!--                                >-->
                    <!--                                    <InfoCircleOutlined />-->
                    <!--                                </a-tooltip>-->
                    <!--                            </span>-->
                    <!--                            &lt;!&ndash;                            <a-button v-show="false" class="btn-txt" type="link" @click="abandon(scope.row)">&ndash;&gt;-->
                    <!--                            &lt;!&ndash;                                <a-icon type="delete" style="color: #f5222d" />&ndash;&gt;-->
                    <!--                            &lt;!&ndash;                            </a-button>&ndash;&gt;-->
                    <!--                        </template>-->
                    <!--                    </a-table-column>-->
                </a-table>
            </a-spin>
        </div>

        <div class="page">
            <a-pagination
                class="paginpage table-pagination"
                :page-size="state.size"
                :pageSizeOptions="['2/page', '10/page', '20/page', '30/page']"
                :showSizeChanger="true"
                v-model:current="state.currentPage"
                :total="totalCount"
                layout="total, sizes, prev, pager, next, jumper"
                background
                small
                @change="handleCurrentChange"
                @showSizeChange="handleSizeChange"
            ></a-pagination>
            <span
                >{{ i18n.t('bkApInvoice.total') }} {{ totalCount }}
                {{ totalCount > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
            >
        </div>
    </div>
</template>

<script setup lang="ts">
import {useStore} from 'vuex'
import {computed, onMounted, reactive} from 'vue'
import {useRoute} from 'vue-router'
import {InfoCircleOutlined, FileAddOutlined} from '@ant-design/icons-vue'
import i18nInstance from '@/locales/i18n'
import type { Composer } from 'vue-i18n'

const route = useRoute()
const store = useStore()
const i18n: Composer = i18nInstance.global

const props = defineProps({
    readonlyMode: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['edit', 'view'])

const state = reactive({
    tableData: [],
    invoiceTableLoading: false,
    currentPage: 1,
    size: 10,
    currentPageNumber: 1,
})

const tableData = computed(() =>
    store.state.ApStore.ocrInvoicesListByPdf.slice(
        (state.currentPage - 1) * state.size,
        state.currentPage * state.size,
    ),
)
const ocrInvoicesListByPdf = computed(() => store.state.ApStore.ocrInvoicesListByPdf)
const totalCount = computed(() => {
    return ocrInvoicesListByPdf.value.length > 0 ? ocrInvoicesListByPdf.value.length : 0
})
const currentPdfInfo = computed(() => store.state.ArApBrStore.pdfInfo)
const currentFileId = computed(() => route.query.fileId)
const editDetail = (record: any) => emit('edit', record)

const viewDetail = (record: any) => emit('view', record)

const handleCurrentChange = (pageNumber: number) => {
    // TODO: consider change array without call api again
    state.currentPage = pageNumber
    updateInvoiceList()
}

const handleSizeChange = (pageSize: number) => {
    state.size = pageSize
    state.currentPage = 1
    updateInvoiceList()
}
const abandon = (data: any) => console.log('item abandoned')

const fetchApOcrResultByPdfId = (data: {file_id: string}) => store.dispatch('ApStore/fetchApOcrResultByPdfIdV1', data)
const updateInvoiceList = async () => {
    if (!currentPdfInfo.value.id) return
    try {
        state.invoiceTableLoading = true
        await fetchApOcrResultByPdfId({file_id: currentPdfInfo.value.id})
    } catch (error) {
        console.log(error)
    } finally {
        state.invoiceTableLoading = false
    }
}

onMounted(async () => {
    await updateInvoiceList()
    console.log(totalCount.value)
})
</script>

<style lang="scss" scoped>
.page {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-height: 38px;
    margin-top: 10px;

    .table-pagination {
        padding: 0 20px 0 0;
        margin: 0;
    }
}

.btn-txt {
    padding-left: 0;
    padding-right: 0;
    font-size: 16px;
}

.info-icon {
    cursor: help;
}
</style>
