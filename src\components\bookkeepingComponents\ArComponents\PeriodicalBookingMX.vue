<!-- @format -->

<script lang="ts" setup>
import {computed, nextTick, onBeforeMount, onMounted, onUnmounted, reactive, ref, watch, getCurrentInstance} from 'vue'
import {useStore} from 'vuex'
import moment from 'moment'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import * as _ from 'lodash'
import {message, type FormInstance} from 'ant-design-vue'
import {QuestionCircleOutlined, PlusOutlined, PlusCircleOutlined} from '@ant-design/icons-vue'
import {UserCompany, UserInfo, LocalCurrency} from '@/lib/storage'
import ProductServiceForm from '@/components/bookkeepingComponents/CommonComponents/ProductServiceForm.vue'

import dayjs from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'
import Decimal from 'decimal.js'
import InputNumber from 'primevue/inputnumber'

const {appContext} = getCurrentInstance()!
const formatNumber = appContext.config.globalProperties.$formatNumber
const parseNumber = appContext.config.globalProperties.$parseNumber
const decimalFormat = appContext.config.globalProperties.$decimalFormat
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const localCurrency = LocalCurrency.get() || 'CAD'
const i18n: Composer = i18nInstance.global
const store = useStore()
interface ProductItem {
    item_no: number
    model: string
    description: string
    qty: number | null
    unit_price: number | null
    total: number | null
    type?: string
    dr_cr: string // ar -> debit & dr
    debit_coa_id: number | null
    debit_coa_code: string
    debit_coa_name: string
}

interface PeriodicalInvoiceForm {
    pay_method: string
    company_name: string
    company_code: string
    company_tel: string
    company_id: number
    reference_no: string
    invoice_currency: string
    invoice_create_date: string
    invoice_due_date: string
    posting_date: string
    items: Array<ProductItem>
    tax_content: {[key: string]: any}[]
    net_amount: number | null
    total_tax: number | null
    total_fee: number | null
    total_fee_local: number | null
    invoice_comments: string
    supplierId: string
    bill_to_company: string
    bill_to_customer_id: string
    bill_to_email: string
    bill_to_receiver: string
    bill_to_street: string
    bill_to_city: string
    bill_to_province: string
    bill_to_postal_code: string
    bill_to_tel: string
    company_email: string
    fileId: string | null
    shipping: string | null
    exchange_rate: string | null
}

const props = withDefaults(
    defineProps<{
        currentInvoice: any
        readonlyMode: boolean
        operationMode: string
        from: string
    }>(),
    {
        readonlyMode: false,
        operationMode: 'creating',
        from: '',
    },
)

const formRef = ref<FormInstance>()
const tableWrapRef = ref()
const form = ref<PeriodicalInvoiceForm>({
    pay_method: '1',
    company_name: '',
    company_code: '',
    company_tel: '',
    company_id: 0,
    reference_no: '',
    invoice_currency: localCurrency,
    invoice_create_date: '',
    invoice_due_date: '',
    posting_date: '',
    items: [] as ProductItem[],
    tax_content: [],
    net_amount: null,
    total_tax: 0,
    total_fee: 0,
    total_fee_local: 0,
    shipping: null,
    invoice_comments: '',
    supplierId: '',
    company_email: '',
    fileId: '',
    bill_to_company: '',
    bill_to_customer_id: '',
    bill_to_email: '',
    bill_to_receiver: '',
    bill_to_street: '',
    bill_to_city: '',
    bill_to_province: '',
    bill_to_postal_code: '',
    bill_to_tel: '',
    exchange_rate: null as string | null,
})
const show = ref(false)
const payMethodOptions = reactive([
    {
        value: '1',
        label: i18n.t('ApComponents.bank'), //'NOT PAID',
    },
    {
        value: '2',
        label: i18n.t('ApComponents.cashpaid'), //'CASH PAID',
    },
    // {
    //     value: '3',
    //     label: 'FUNDING TRANSFER',
    // },
    // {
    //     value: '4',
    //     label: 'INTERCOM',
    // },
])
const isWeekend = ref(false)
const spotPostingDate = ref(dayjs().format('YYYY-MM-DD'))
const countryCodeCheck = ref(false)
const spotRateCheck = ref(false)
const taxRateCheck = ref(false)
const isDisable = computed(() => {
    return !(countryCodeCheck.value && spotRateCheck.value && taxRateCheck.value)
})
const fixedDate = ref(0)
const formLoading = ref(false)
const referenceNoLoading = ref(false)
const enableTaxExempt = ref(false)
const autoCalTaxFlag = ref('1') // '1': change value from "Total"; '0': change value from 'item list')
const autoCalculateState = ref(true)
const spot = ref({
    rate: '',
    rate_date: '',
})
const accountQuery = {bk_type: 1, company_code: userCompany[0].code, $limit: -1, del_flag: 0}
const emits = defineEmits(['save', 'dismiss'])

const preDefinedDummy = ref<any>({
    contact_name: 'DUMMY',
    company_code: userCompany[0].code,
    company_id: userCompany[0].id,
    gl_account: '', // null check of api
})
// const productServicePageSize = ref(10)
// const productServiceLoading = ref(false)
// const productServiceKeyword = ref('')

// mapActions

const createDummy = (payload: any) => store.dispatch('ContactStore/createContact', payload)
const fetchCustomerDropDown = (query?: any) => store.dispatch('CommonDropDownStore/fetchContactDropDown', query)
const fetchAccountDescDropdown = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)
const fetchAllBankList = (query?: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', query)
const fetchTaxRates = (query?: any) => store.dispatch('TaxCalculationStore/fetchTaxRates2', query)
const checkReferenceNoRepetition = (query?: any) => store.dispatch('ArStore/checkReferenceNoRepetitionV1', query)
const fetchCompanyTaxInfo = (query?: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
const getSpot = (query?: any) => store.dispatch('Utils/getSpotv1', query)
const fetchArTopCoa = (payload: any) => store.dispatch('ArStore/fetchArTopCoaV1', payload)
const updateArTopCoa = (data: any) => store.commit('ArStore/updateArTopCoa', data)
const updateTaxRates = (data: any) => store.commit('TaxCalculationStore/updateTaxRatesList', data)
const fetchProductServiceList = (payload?: any) =>
    store.dispatch('ProductServiceStore/fetchProductServiceList', payload)

const currentCustomers = computed(() =>
    store.state.CommonDropDownStore.customerOptions.filter((item: any) => 'DUMMY' === item.contact_name),
)

const addItem = () => {
    if (!form.value.items) {
        form.value.items = []
    }

    let debit_coa_id = null
    let debit_coa_code = ''
    let debit_coa_name = ''

    if (arTopCoaList.value.length > 0) {
        const id = arTopCoaList.value[0].debit_coa_id
        const coaItem = accountDescList.value.find((x: any) => x.id == id)

        debit_coa_id = id
        debit_coa_code = coaItem?.account_code
        debit_coa_name = coaItem?.name
    }

    form.value.items.push({
        item_no: form.value.items.length + 1,
        model: '',
        description: '',
        qty: null,
        unit_price: null,
        total: 0,
        type: '',
        dr_cr: 'cr',
        debit_coa_code: debit_coa_code,
        debit_coa_id: debit_coa_id,
        debit_coa_name: debit_coa_name,
    } as ProductItem)
}
const remove = (index: number) => {
    form.value.items.splice(index, 1)
    form.value.items.forEach((item, index) => {
        item.item_no = index + 1
    })
}
const save = async () => {
    if (!form.value.items || (form.value.items && form.value.items.length === 0)) {
        message.error({
            content: 'Invoice must contain at least one [ Item ]',
            duration: 6,
        })
        return
    }
    if (!form.value.net_amount) {
        message.error({
            content: "Can't create invoice without [ Net Amount ]",
            duration: 6,
        })
        return
    }

    const dummyItem = currentCustomers.value.length
        ? _.cloneDeep(currentCustomers.value[0])
        : {...preDefinedDummy.value}

    form.value.bill_to_company = dummyItem?.contact_name
    form.value.bill_to_customer_id = dummyItem?.contact_id
    form.value.bill_to_email = dummyItem?.email
    form.value.bill_to_receiver = dummyItem?.billing_receiver
    form.value.bill_to_street = dummyItem?.billing_street
    form.value.bill_to_city = dummyItem?.billing_city
    form.value.bill_to_province = dummyItem?.billing_province
    form.value.bill_to_postal_code = dummyItem?.billing_postal_code
    form.value.bill_to_tel = dummyItem?.tel
    form.value.company_email = companyTaxInfo.value.email
    form.value.company_name = companyTaxInfo.value.name
    form.value.company_tel = companyTaxInfo.value.phone
    form.value.company_id = companyTaxInfo.value.id
    form.value.company_code = companyTaxInfo.value.code

    if (await formRef.value?.validateFields()) {
        const queryForm: any = {..._.cloneDeep(form.value)}
        // when line item total is negative, assign 'dr_cr' to opposite
        // for example ar's dr_cr default is 'cr'
        // when line item amount is negative
        // dr_cr change to 'dr'
        queryForm.items.forEach((item: any) => {
            item.debit_coa_code = accountDescList.value.filter((i: any) => i.id === item.debit_coa_id)[0].account_code
            if (+item.total < 0) {
                item.dr_cr = 'dr'
            } else {
                if (!item.dr_cr) {
                    item.dr_cr = 'cr'
                }
            }
        })
        queryForm.creator = userInfo?.id
        queryForm.creator_name = userInfo?.account
        queryForm.company_name = queryForm.company_name.toUpperCase()
        if (fixedDate.value) {
            queryForm.invoice_due_date = moment(queryForm.invoice_due_date, 'YYYY-MM-DD')
                .add(fixedDate.value, 'days')
                .format('YYYY-MM-DD')
        }
        if (form.value.total_fee!.toFixed(2) !== (form.value.net_amount + Number(form.value.total_tax)).toFixed(2)) {
            message.error({
                content: 'Net amount plus tax does not equal total',
                duration: 8,
            })
            return
        }

        if (queryForm.pay_method === '2') {
            //AR/AP 创建cash paid发票的功能已经好了，创建发票的时候会自动完成对账。需要注意的是，入参中pay_method = ‘2’ (cash paid)，br_type = ‘9’ (cash pay invoice)
            queryForm.br_type = '9'
        } else {
            queryForm.br_type = queryForm.net_amount && queryForm.net_amount < 0 ? '3' : '0'
        }

        // patch
        if (queryForm.exchange_rate === '') queryForm.exchange_rate = null
        emits('save', queryForm)
        form.value.invoice_comments = ''
        form.value.reference_no = ''
        form.value.items = []
    } else {
        return false
    }
}

const resetFormField = () => {
    formRef.value?.resetFields()
}
const cancel = () => {
    emits('dismiss')
}
const showProductServiceModal = (isShow = false) => {
    show.value = isShow
}
const FormItemModelChange = (value: string, index: number, record: any) => {
    const productServiceItem = productServiceList.value
        .filter((item: any) => item.product_service == record.model)
        .shift()
    form.value.items[index].description = productServiceItem?.description
    if (productServiceItem.coa) {
        form.value.items[index].debit_coa_code = productServiceItem?.coa
        const coaItem = accountDescList.value.filter((x: any) => x.account_code == productServiceItem?.coa).shift()
        form.value.items[index].debit_coa_id = coaItem?.id
        form.value.items[index].debit_coa_name = coaItem?.name
    }

    form.value.items[index].unit_price = productServiceItem?.unit_price
}
const handleQtyChange = (index: number) => {
    autoCalTaxFlag.value = '0'
    calItemTotal(index)
}
const handleUnitPriceChange = (index: number) => {
    autoCalTaxFlag.value = '0'
    calItemTotal(index)
}
const calItemTotal = (index: number) => {
    if (form.value.items[index].qty && form.value.items[index].unit_price) {
        form.value.items[index].total = new Decimal(form.value.items[index].qty || 0)
            .mul(form.value.items[index].unit_price || 0)
            .toDP(2)
            .toNumber()
    } else {
        form.value.items[index].total = 0.0
    }
}
const handleItemTotalChange = (row: any, index: number) => {
    autoCalTaxFlag.value = '0'
    if (row.total) {
        form.value.items[index].qty = 0
        form.value.items[index].unit_price = 0
    }
}
const blurItemTotalChange = (row: any, index: number) => {
    autoCalTaxFlag.value = '0'
    if (!autoCalculateState.value) {
        form.value.items[0].total = form.value.net_amount
    }
}

const handlePayMethodChange = async (flag: any, value: any) => {
    // if (value) {
    //     isDisable.value = false
    // } else {
    //     isDisable.value = true
    // }
    return
}

const handleShippingChange = (value: any) => {
    if (form.value.net_amount) {
        form.value.shipping = value
        calculateInvoiceTotal()
    }
}
const calculateInvoiceTotal = () => {
    if (!autoCalculateState.value) {
        const totalFee = form.value.total_fee ? form.value.total_fee : 0.0
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )

        form.value.net_amount = new Decimal(totalFee).sub(new Decimal(form.value.total_tax)).toDP(2).toNumber()
        form.value.items[0].total = form.value.net_amount
    } else {
        const amountFee = form.value.net_amount ? form.value.net_amount : 0.0

        const totalTaxFee = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        const totalFee = new Decimal(amountFee).add(new Decimal(totalTaxFee)).toDP(2).toNumber()

        form.value.total_tax = totalTaxFee
        form.value.total_fee = totalFee
        form.value.total_fee_local = new Decimal(form.value.total_fee)
            .mul(spot.value.rate || '1')
            .toDP(2)
            .toNumber()
    }
}
const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}
const initFormData = () => {
    formRef.value?.resetFields()
    form.value.tax_content = []
    form.value.total_tax = 0
    form.value.total_fee = 0
    form.value.total_fee_local = 0
    form.value.net_amount = 0
    form.value.items = []
    addItem()
}

const handleTaxChange = (current: any, item: any) => {
    if (current === null) {
        item.value = 0
    } else {
        item.value = current
    }

    calculateInvoiceTotal()
}
const referenceNoChange = async (value: any) => {
    if (form.value.reference_no) {
        await checkRefNoRepetition(form.value.reference_no)
    }
}
const checkRefNoRepetition = async (value: any) => {
    const query = {
        company_code: userCompany[0].code,
        reference_no: form.value.reference_no,
        page_index: 1,
        page_size: 10,
    }
    try {
        referenceNoLoading.value = true
        const response = await checkReferenceNoRepetition(query)
        if (response.data.statusCode === 200) {
            if (response.data.data.length > 0) {
                message.warning({
                    content: i18n.t('bkAp.msgReferenceNoExisted'),
                    duration: 8,
                })
            }
        }
    } catch (error) {
        console.log(error)
    } finally {
        referenceNoLoading.value = false
    }
}

const changeItemListRowExpenseAccount = (value: any, index: any) => {
    form.value.items[index].debit_coa_id = value
    const coaItem = accountDescList.value.filter((x: any) => x.id == value).shift()
    form.value.items[index].debit_coa_code = coaItem?.account_code
    form.value.items[index].debit_coa_name = coaItem?.name
}

const expenseAccountAlias = (row: any, accountList: any) => {
    const item = accountList.find((item: any) => item.account_code === row.debit_coa_code)
    return `${row.debit_coa_code.substring(0, 4)} | ${item?.name || ''}`
}

const changeTotalAmount = (value: any, reverse: boolean) => {
    autoCalTaxFlag.value = '1'
    if (value && form.value.items.length === 1) {
        if (form.value.invoice_currency !== localCurrency && reverse) {
            updateSpotReverse()
            value = form.value.total_fee ?? value
        }
        form.value.tax_content = calculateTaxRates(value)

        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(form.value.total_fee || 0)
            .sub(new Decimal(form.value.total_tax))
            .toDP(2)
            .toNumber()
        form.value.items[0].total = form.value.net_amount
        form.value.items[0].qty = null
        form.value.items[0].unit_price = null
    } else if (!value && form.value.items.length === 1) {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0

        form.value.items[0].total = 0
        form.value.items[0].qty = 0
        form.value.items[0].unit_price = 0
    }
    if (form.value.invoice_currency !== localCurrency && !reverse) {
        updateSpot()
    }
}

const revertTaxCal = (amount: number | null = null) => {
    if (amount) {
        _.forEach(taxRatesList.value, (item, index: number) => {
            form.value.tax_content[index].value = new Decimal(amount).mul(item.value).toDP(2).toNumber()
        })
        form.value.total_fee = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            amount,
        )
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
    } else {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_fee = 0
        form.value.total_tax = 0
    }
}

const changeTaxExempt = () => {
    if (enableTaxExempt.value) {
        form.value.total_fee = form.value.net_amount ?? 0
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0
    } else {
        revertTaxCal(form.value.net_amount)
    }

    updateSpot()
}

const calculateTaxRates = (amount: number | null) => {
    if (enableTaxExempt.value) {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: 0}
        })
    }

    if (autoCalTaxFlag.value === '0') {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: amount ? new Decimal(amount).mul(item.value).toDP(2).toNumber() : 0}
        })
    }

    // change from TOTAL
    const taxTotal = _.reduce(
        taxRatesList.value,
        (sum, item) => {
            return new Decimal(sum).add(new Decimal(item.value || 0)).toNumber()
        },
        0,
    )

    const netTotal = amount ? new Decimal(amount).div(new Decimal(1 + taxTotal)).toNumber() : 0.0
    return _.map(taxRatesList.value, (item: any) => {
        return {...item, value: netTotal ? new Decimal(netTotal).mul(item.value).toDP(2).toNumber() : 0}
    })
}
const changeAutoCalculateMode = (state: any) => {
    if (state) {
        const sum = form.value.items.reduce((prev, curr) => {
            return prev + (curr.total as number)
        }, 0)

        form.value.net_amount = sum
        form.value.tax_content = calculateTaxRates(sum)
        calculateInvoiceTotal()
    }
}

const updateSpotReverse = async () => {
    if (form.value.invoice_currency.toString() === localCurrency) {
        form.value.total_fee = form.value.total_fee_local
        return
    }

    form.value.total_fee =
        form.value.total_fee_local != undefined
            ? Number((form.value.total_fee_local / parseFloat(spot.value.rate ?? '')).toFixed(2))
            : null
}

const getSpotInputDateStatus = (date: moment.MomentInput): number => {
    const weekOfDay = moment(date, 'YYYY-MM-DD').format('E')
    return 5 - +weekOfDay
}

const updateSpot = async () => {
    if (form.value.invoice_currency.toString() === localCurrency) {
        form.value.total_fee_local = form.value.total_fee
        spotRateCheck.value = true
        return
    }

    const baseCurrency = form.value.invoice_currency
    const quoteCurrency = localCurrency

    const weekOfDayDiff = getSpotInputDateStatus(form.value.posting_date)
    isWeekend.value = weekOfDayDiff < 0
    if (!isWeekend.value) {
        spotPostingDate.value = form.value.posting_date
    } else {
        spotPostingDate.value = moment(form.value.posting_date, 'YYYY-MM-DD')
            .add(weekOfDayDiff, 'days')
            .format('YYYY-MM-DD')
    }
    try {
        spot.value = await getSpot({baseCurrency, quoteCurrency, date: spotPostingDate.value})
        form.value.exchange_rate = spot.value.rate
        spotRateCheck.value = spot.value.rate !== ''
    } catch (e) {
        console.log(e)
    }
    form.value.total_fee_local =
        form.value.total_fee != undefined
            ? Number((form.value.total_fee * parseFloat(spot.value.rate)).toFixed(2))
            : null
}

const form_diff = computed(() =>
    Math.abs(
        Number(form.value.total_fee_local) -
            Number((Number(form.value.total_fee || 0) * parseFloat(spot.value.rate || '1')).toFixed(2)),
    ),
)

const accountDescList: any = computed(() => store.state.CommonDropDownStore.accountDescList)
const accountCurrencyOptions: any = computed(() => store.state.CommonDropDownStore.bankCurrencyOptions)
const arTopCoaList: any = ref([])
const taxRatesList: any = computed(() => {
    return _.cloneDeep(store.state.TaxCalculationStore.taxRatesList).map((i: any) => {
        return {...i, value: new Decimal(i.value).div(new Decimal(100)).toNumber()}
    })
})
const companyTaxInfo: any = computed(() => store.state.TaxInfoStore.companyTaxInfo)
const productServiceList: any = computed(() => store.state.ProductServiceStore.ProductServiceList)

const discountMaxValue = () => {
    const amount = form.value.net_amount || 0.0
    const shipping = form.value.shipping || 0.0
    return (amount as number) + (shipping as number)
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}
const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}

const rules = reactive({
    company_name: [...requireRule(i18n.t('bkAp.companyName'))],
    company_gst_no: [...requireRule(i18n.t('bkAp.companyGst'))],
    companyPstNo: [...requireRule(i18n.t('bkAp.companyQst'))],
    gst: [...requireRule(i18n.t('bkAp.companyGstHst'))],
    pst: [...requireRule(i18n.t('bkAp.companyPst'))],
    invoiceCurrency: [...requireRule(i18n.t('bkAp.currency'))],
    invoiceDueDate: [...requireRule(i18n.t('bkAp.dueDate'))],
    billToReceiver: [...requireRule(i18n.t('bkAp.billToReceiver'))],
    billToCity: [...requireRule(i18n.t('bkAp.billToCity'))],
    billToProvince: [...requireRule(i18n.t('bkAp.billToProvince'))],
    billToTel: [...requireRule(i18n.t('bkAp.billToTel'))],
    billToEmail: [
        ...requireRule(i18n.t('bkAp.billToEmail')),
        {type: 'email', message: i18n.t('bkCommonTag.msgEmailRule'), trigger: 'blur'},
    ],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur', 'change'],
        },
    ],
    posting_date: [...requireRule(i18n.t('bkAr.postingDate'))],
    invoice_comments: [...lengthLimitRule(1, 200000)],
    pay_method: [...requireRule(i18n.t('bkAr.payMethod'))],
    requireGLAccount: [
        {
            required: true,
            message: 'Selection required',
            trigger: ['blur', 'change'],
        },
    ],
})

const getPopupContainer = (node: any) => {
    return tableWrapRef.value
}

const getArTopCoa = async (company_code: string, bill_to_customer_id: string) => {
    try {
        const {data} = await fetchArTopCoa({
            company_code: company_code,
            bill_to_customer_id: bill_to_customer_id,
        })
        arTopCoaList.value = data.data
        if (
            form.value.items.length === 1 &&
            arTopCoaList.value.length > 0 &&
            _.isNull(form.value.items[0].debit_coa_id)
        ) {
            const code = arTopCoaList.value[0].debit_coa_code
            const coaItem = accountDescList.value.find((x: any) => x.account_code == code)

            form.value.items[0].debit_coa_id = coaItem?.id
            form.value.items[0].debit_coa_code = coaItem?.account_code
            form.value.items[0].debit_coa_name = coaItem?.name
        }
    } catch (error) {
        console.log(error)
    }
}

const getTaxRates = async (query: any) => {
    const {buyerCountryCode, buyerRegionCode, sellerCountryCode, sellerRegionCode} = query

    if (!buyerCountryCode || !buyerRegionCode || !sellerCountryCode || !sellerRegionCode) {
        countryCodeCheck.value = false
        message.error({
            content: i18n.t('taxRates.countryNotSet'),
            duration: 8,
        })

        updateTaxRates([])
        return
    } else {
        countryCodeCheck.value = true
    }

    await fetchTaxRates({...query, invoiceDate: moment().valueOf(), action: 'Sale'})
}

// const productServiceSearch = _.debounce(async (value: any) => {
//     //reset volume
//     productServiceKeyword.value = value
//     productServicePageSize.value = 10
//     await fetchContactList()
// }, 500)

// const fetchContactList = async () => {
//     try {
//         productServiceLoading.value = true
//         const queryObj: any = {}
//         if (productServiceKeyword.value) {
//             queryObj['contact_name[$like]'] = `%${productServiceKeyword.value}%`
//         }
//         queryObj['$limit'] = productServicePageSize.value
//         queryObj['$skip'] = 0
//         await fetchCustomerDropDown({...queryObj, company_code: userCompany[0].code})
//     } catch (e) {
//         console.log(e)
//     } finally {
//         productServiceLoading.value = false
//     }
//     return void 0
// }

// const contactScroll = async (e: any) => {
//     const {target} = e
//     // when user scoll near bottom
//     if (Math.abs(target.scrollTop + target.offsetHeight - target.scrollHeight) < 2 && !productServiceLoading.value) {
//         productServicePageSize.value += 10
//         await fetchContactList()
//     }
// }

watch(
    () => taxRatesList.value,
    list => {
        if (props.readonlyMode) return
        form.value.tax_content = []
        if (list.length) {
            list.forEach((i: any) => {
                form.value.tax_content = [...form.value.tax_content, {...i, value: 0}]
            })
            taxRateCheck.value = true
        } else {
            taxRateCheck.value = false
        }
    },
)

watch(
    () => form.value.reference_no,
    (count, prevCount) => {
        if (count && !props.readonlyMode) {
            form.value.reference_no = count.toUpperCase()
        }
    },
)
watch(
    () => form.value.items,
    (count, prevCount) => {
        if (!autoCalculateState.value) return
        if (props.readonlyMode || !count) return
        const sum = count.reduce((prev, curr: any) => {
            return prev + curr.total
        }, 0)
        if (form.value.net_amount === sum) return
        //this.form.amount = sum ? sum : undefined
        form.value.net_amount = sum
        if (autoCalTaxFlag.value === '0') {
            form.value.tax_content = calculateTaxRates(form.value.net_amount as number)
            calculateInvoiceTotal()
        }
    },
    {deep: true},
)
onBeforeMount(async () => {
    updateTaxRates([])
    if (props.operationMode === 'editing' || props.readonlyMode) {
        form.value = _.cloneDeep(props.currentInvoice)
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee, balance
        // const {amount, totalTaxable, totalTax, totalFee, balance} = JSON.parse(JSON.stringify(this.currentInvoice))
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee
        const {net_amount, totalTaxable, total_tax, total_fee} = _.cloneDeep(props.currentInvoice)
        nextTick(() => {
            form.value.net_amount = net_amount ? net_amount : 0.0
            form.value.total_tax = total_tax ? total_tax : 0.0
            form.value.total_fee = total_fee ? total_fee : 0.0
        })
    }
    if (props.from === 'copy') {
        form.value = _.cloneDeep(props.currentInvoice)
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee, balance
        // const {amount, totalTaxable, totalTax, totalFee, balance} = JSON.parse(JSON.stringify(this.currentInvoice))
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee
        const {net_amount, totalTaxable, total_tax, total_fee} = _.cloneDeep(props.currentInvoice)
        nextTick(() => {
            form.value.net_amount = net_amount ? net_amount : 0.0
            form.value.total_tax = total_tax ? total_tax : 0.0
            form.value.total_fee = total_fee ? total_fee : 0.0
        })
    } else {
        if (!props.readonlyMode && props.operationMode === 'creating' && form.value.items.length === 0) {
            addItem()
        }
    }

    if (!props.readonlyMode) {
        form.value.company_name = ''
        form.value.invoice_create_date = getToday()
        form.value.invoice_due_date = getToday()
        form.value.posting_date = getToday()
    }
    const contactQuery = {} as any
    contactQuery.company_code = userCompany[0].code
    contactQuery.contact_name = 'DUMMY'
    formLoading.value = true
    await Promise.all([
        fetchCustomerDropDown({...contactQuery}),
        fetchAllBankList({company_code: userCompany[0].code}),
        fetchAccountDescDropdown(accountQuery),
        fetchCompanyTaxInfo({code: userCompany[0].code}),
        fetchProductServiceList({company_code: userCompany[0].code, $limit: -1}),
        updateSpot(),
    ])
    if (form.value.reference_no && !props.readonlyMode) {
        await checkRefNoRepetition(form.value.reference_no)
    }
    // if current company has no dummy, then create one for it.

    let contact_id = ''
    const company_code = companyTaxInfo.value.code || ''

    if (currentCustomers.value.length === 0) {
        // create dummy
        try {
            const dummyItem = {
                ...preDefinedDummy.value,
                office_country: companyTaxInfo.value.country,
                office_province: companyTaxInfo.value.province,
                billing_country: companyTaxInfo.value.country,
                billing_province: companyTaxInfo.value.province,
                shipping_country: companyTaxInfo.value.country,
                shipping_province: companyTaxInfo.value.province,
            }
            const res = await createDummy(dummyItem)
            preDefinedDummy.value = {...res.data}
            contact_id = preDefinedDummy.value.contact_id || ''
        } catch (e: any) {
            message.warn('DUMMY creat error, please refresh')
        }
        // await fetchCustomerDropDown({...contactQuery})
    } else {
        contact_id = currentCustomers.value[0].contact_id || ''
    }

    const dummyItem = currentCustomers.value.length
        ? _.cloneDeep(currentCustomers.value[0])
        : {...preDefinedDummy.value}

    await getTaxRates({
        companyCode: companyTaxInfo.value.code,
        buyerCountryCode: dummyItem.office_country || companyTaxInfo.value.country,
        buyerRegionCode: dummyItem.office_province || companyTaxInfo.value.province,
        sellerCountryCode: companyTaxInfo.value.country,
        sellerRegionCode: companyTaxInfo.value.province,
    })

    await getArTopCoa(company_code, contact_id)
    formLoading.value = false
})
onMounted(async () => {
    formRef.value?.clearValidate()
})
onUnmounted(() => {
    updateArTopCoa([])
})
const VNodes = (_: any, {attrs}: any) => {
    return attrs.vnodes
}
const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

defineExpose({initFormData})
</script>
<template>
    <div class="page-container-ap-invoice-form" id="scroll-box">
        <a-spin :spinning="formLoading">
            <a-form
                ref="formRef"
                :model="form"
                :layout="'vertical'"
                :rules="readonlyMode ? {} : rules"
                label-width="auto"
                label-position="top"
                class="invoice-form"
                autocomplete="off"
            >
                <div class="ap-invoice-block">aaaaaaaaaaaaaaaaaaa
                    <a-row :gutter="24">
                        <a-col :span="8">
                            <a-form-item name="reference_no" :label="$t('bkAr.referenceNo')">
                                <template v-slot:label>
                                    {{ i18n.t('bkAr.referenceNo') }}
                                    <a-tooltip>
                                        <template #title>Reference No. in the receipts.</template>
                                        <question-circle-outlined class="icon-question" />
                                    </a-tooltip>
                                </template>
                                <a-spin :spinning="referenceNoLoading" wrapperClassName="input-spin">
                                    <a-input
                                        v-model:value="form.reference_no"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        @blur="referenceNoChange"
                                        :disabled="readonlyMode"
                                    >
                                    </a-input>
                                </a-spin>
                            </a-form-item>
                        </a-col>

                        <a-col :span="8">
                            <a-form-item name="invoice_currency" :label="i18n.t('bkAp.currency')">
                                <a-select
                                    :placeholder="i18n.t('workTimeManager.msgInput')"
                                    v-model:value="form.invoice_currency"
                                    :disabled="readonlyMode"
                                    @change="updateSpot"
                                >
                                    <a-select-option
                                        v-for="item in accountCurrencyOptions"
                                        :key="item.key"
                                        :value="item.key"
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                                <div v-show="form.invoice_currency === localCurrency" style="padding: 2px 5px">
                                    &nbsp;
                                </div>
                                <div
                                    v-show="form.invoice_currency !== localCurrency"
                                    style="color: red; padding: 2px 5px"
                                >
                                    <span>
                                        {{
                                            i18n.t('ApComponents.spotCurrency', {
                                                rate: spot.rate || 'null',
                                                date: spotPostingDate,
                                            })
                                        }}
                                        {{ !spot.rate ? ', ' + i18n.t('ApComponents.contactAdmin') : '' }}
                                    </span>
                                </div>
                            </a-form-item>
                        </a-col>

                        <a-col :span="8" v-show="false">
                            <a-form-item name="invoice_create_date" :label="i18n.t('bkAp.date')">
                                <a-date-picker
                                    v-model:value="form.invoice_create_date"
                                    :disabled="readonlyMode"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    placeholder="Create Date"
                                    style="width: 100%"
                                    clearable
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>

                        <a-col :span="8" v-show="readonlyMode">
                            <a-form-item name="invoice_due_date" :label="i18n.t('bkAp.dueDate')">
                                <a-date-picker
                                    v-model:value="form.invoice_due_date"
                                    :disabled="readonlyMode"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    placeholder="Fixed Date"
                                    style="width: 100%"
                                    clearable
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>

                        <a-col :span="8">
                            <a-form-item :name="!readonlyMode ? 'postingDate' : ''" :label="i18n.t('bkAr.postingDate')">
                                <a-date-picker
                                    v-model:value="form.posting_date"
                                    :disabled="readonlyMode"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :placeholder="i18n.t('bkAr.invoiceComment')"
                                    style="width: 100%"
                                    clearable
                                    @change="updateSpot"
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>

                        <a-col :span="8">
                            <a-form-item name="payMethod" :label="i18n.t('bkAr.payMethod')">
                                <a-select
                                    :placeholder="i18n.t('workTimeManager.msgInput')"
                                    v-model:value="form.pay_method"
                                    :disabled="readonlyMode"
                                    @change="handlePayMethodChange('pay_method', $event)"
                                    default-active-first-option
                                >
                                    <a-select-option
                                        v-for="item in payMethodOptions"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.label }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
                <div class="ap-invoice-block" ref="tableWrapRef">
                    <a-table :dataSource="form.items" :pagination="false">
                        <a-table-column align="center" :title="i18n.t('bkAp.itemNo')" data-index="item_no" width="5%" />
                        <!-- <a-table-column align="left" :title="i18n.t('bkAp.modelNumber')" data-index="model" width="20%">
                            <template #default="{index, record}">
                                <a-form-item :name="['items', index, 'model']" v-if="!readonlyMode">
                                    <a-input
                                        v-model:value="record.model"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :class="index === form.items.length - 1 ? 'table-input' : 'table-input-static'"
                                    ></a-input>
                                </a-form-item>
                                <span v-else>
                                    {{ record.model }}
                                </span>
                            </template>
                        </a-table-column> -->
                        <a-table-column align="left" :title="i18n.t('bkAp.modelNumber')" data-index="model" width="20%">
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'model']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-select
                                        v-model:value="record.model"
                                        mode="combobox"
                                        :show-search="true"
                                        :filter-option="true"
                                        :allowClear="true"
                                        :showArrow="true"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        @change="FormItemModelChange($event, index, record)"
                                        :options="productServiceList.map((item: any) => ({ key: item.id, value: item.product_service, label: item.product_service }))"
                                        :getPopupContainer="getPopupContainer"
                                    >
                                        <template #dropdownRender="{menuNode: menu}">
                                            <div
                                                style="padding: 4px 8px; cursor: pointer; color: #004fc1"
                                                @click="showProductServiceModal(true)"
                                            >
                                                <plus-circle-outlined />
                                                Add New Product/Service
                                                <a-divider style="margin: 4px 0" />
                                            </div>
                                            <v-nodes :vnodes="menu" />
                                        </template>
                                    </a-select>
                                </a-form-item>
                                <span v-else>
                                    {{ record.model }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column
                            align="left"
                            :title="i18n.t('bkAp.description')"
                            data-index="description"
                            :ellipsis="true"
                            width="15%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'description']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-input
                                        v-model:value="record.description"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :class="index === form.items.length - 1 ? 'table-input' : 'table-input-static'"
                                    ></a-input>
                                </a-form-item>
                                <span v-else>
                                    {{ record.description }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column
                            align="left"
                            :title="i18n.t('bkAp.accountingCategory')"
                            data-index="debit_coa_id"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'debit_coa_id']"
                                    :rules="rules['requireGLAccount']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-select
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="record.debit_coa_id"
                                        show-search
                                        :dropdownMatchSelectWidth="400"
                                        :getPopupContainer="getPopupContainer"
                                        :filter-option="filterOption"
                                        :class="index === form.items.length - 1 ? 'table-input' : 'table-input-static'"
                                        @change="changeItemListRowExpenseAccount(record.debit_coa_id, index)"
                                    >
                                        <a-select-option
                                            v-for="item in accountDescList"
                                            :key="item.account_code + ' | ' + item.name"
                                            :value="item.id"
                                            >{{
                                                item.account_code.substring(0, 4) + ' | ' + item.name
                                            }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                                <span v-else>
                                    {{ expenseAccountAlias(record, accountDescList) }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column align="left" :title="i18n.t('bkAp.qty')" data-index="qty" width="10%">
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'qty']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-input-number
                                        v-model:value="record.qty"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :max="9999999"
                                        :min="0 || null"
                                        :class="index === form.items.length - 1 ? 'table-input' : 'table-input-static'"
                                        @change="handleQtyChange(index)"
                                    ></a-input-number>
                                </a-form-item>
                                <span v-else>
                                    {{ record.qty }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column
                            align="left"
                            :title="i18n.t('bkAp.unitPrice')"
                            data-index="unit_price"
                            width="10%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'unit_price']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-input-number
                                        v-model:value="record.unit_price"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :precision="3"
                                        :min="0 || null"
                                        :class="index === form.items.length - 1 ? 'table-input' : 'table-input-static'"
                                        @change="handleUnitPriceChange(index)"
                                    ></a-input-number>
                                </a-form-item>
                                <span v-else>
                                    {{ Number(record.unit_price).toFixed(3) }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column align="left" :title="i18n.t('bkAp.total')" data-index="total" width="12%">
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'total']"
                                    :rules="rules['requireItem']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <!-- <a-input-number
                                        v-model:value="record.total"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :precision="2"
                                        :formatter="formatNumber"
                                        :parser="parseNumber"
                                        :class="index === form.items.length - 1 ? 'table-input' : 'table-input-static'"
                                        @change="handleItemTotalChange(record, index)"
                                        @blur="blurItemTotalChange(record, index)"
                                    >
                                    </a-input-number> -->
                                    <InputNumber
                                        class="amount-input-prime item-amount"
                                        v-model="record.total"
                                        :controls="false"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        :locale="decimalFormat()"
                                        fluid
                                        @value-change="handleItemTotalChange(record, index)"
                                        @blur="blurItemTotalChange(record, index)"
                                    />
                                </a-form-item>
                                <span v-else>
                                    {{ Number(record.total).toFixed(2) }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column align="left" :title="''" v-if="!readonlyMode" key="operation" width="5%">
                            <template #default="{index}">
                                <span>
                                    <a-button
                                        :disabled="form.items.length === 1"
                                        type="link"
                                        danger
                                        @click="remove(index)"
                                    >
                                        <svg-icon name="icon_delete"></svg-icon>
                                    </a-button>
                                </span>
                            </template>
                        </a-table-column>
                    </a-table>
                    <a-button v-show="!readonlyMode" class="invoice-add" type="primary" ghost @click="addItem">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        {{ i18n.t('bkAp.addItemBtnTxt') }}
                    </a-button>
                    <div class="ap-invoice-amount-block">
                        <div class="ap-invoice-amount-block-right">
                            <div class="amount-block">
                                <!-- auto-calculate checkbox -->
                                <div v-show="!readonlyMode" class="title">
                                    <a-switch
                                        class="switch-wrap"
                                        v-model:checked="autoCalculateState"
                                        size="small"
                                        @change="changeAutoCalculateMode"
                                    />{{ i18n.t('ApComponents.auto') }}
                                </div>
                                <div v-show="!readonlyMode" class="title">
                                    <a-switch
                                        class="switch-wrap"
                                        size="small"
                                        v-model:checked="enableTaxExempt"
                                        @change="changeTaxExempt"
                                    />{{ i18n.t('ApComponents.exempt') }}
                                </div>

                                <div class="amount-item-wrap">
                                    <!-- Net Amount item-block  -->
                                    <div class="amount-item">
                                        <div class="amount-lable">{{ i18n.t('ArComponents.netAmount') }}</div>
                                        <a-input-number
                                            v-model:value="form.net_amount"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <!-- taxRatesList item-block  -->
                                    <div
                                        class="amount-item"
                                        v-for="item in form.tax_content || []"
                                        :key="item.fieldName"
                                    >
                                        <div class="amount-lable">{{ item.alias }}</div>
                                        <!-- <a-input-number
                                            v-model:value="item.value"
                                            :disabled="readonlyMode"
                                            :controls="false"
                                            :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="amount-input"
                                            @change="handleTaxChange($event, item)"
                                        >
                                        </a-input-number> -->
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="item.value"
                                            :controls="false"
                                            :disabled="readonlyMode"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="handleTaxChange($event, item)"
                                        />
                                    </div>
                                    <!-- shipping item-block  -->
                                    <div class="amount-item" v-show="false">
                                        <div class="amount-lable">{{ i18n.t('bkAp.shipping') }}</div>
                                        <a-input-number
                                            v-model:value="form.shipping"
                                            :disabled="readonlyMode"
                                            :controls="false"
                                            :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="amount-input"
                                            @change="handleShippingChange"
                                        >
                                        </a-input-number>
                                    </div>
                                    <!-- Tax Subtotal item-block  -->
                                    <div class="amount-item">
                                        <div class="amount-lable">{{ i18n.t('ApComponents.subtotal') }}</div>
                                        <a-input-number
                                            v-model:value="form.total_tax"
                                            :disabled="true && autoCalculateState"
                                            :controls="false"
                                            :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <!-- hide "Taxable Subtotal"  -->
                                    <!-- Taxable Subtotal item-block  -->
                                    <div v-if="false" class="amount-item">
                                        <div class="amount-lable">Taxable Subtotal</div>
                                        <!--                                        <a-input-number-->
                                        <!--                                            v-model:value="form.totalTaxable"-->
                                        <!--                                            :disabled="true"-->
                                        <!--                                            :controls="false"-->
                                        <!--                                            :precision="2"-->
                                        <!--                                            class="amount-input"-->
                                        <!--                                        >-->
                                        <!--                                        </a-input-number>-->
                                    </div>
                                    <!-- TOTAL item-block  -->
                                    <div class="amount-item">
                                        <div class="amount-lable bold">{{ i18n.t('ApComponents.total') }} {{ form.invoice_currency }}</div>
                                        <!-- <a-input-number
                                            v-model:value="form.total_fee"
                                            :disabled="form.items.length > 1 && autoCalculateState"
                                            :controls="false"
                                            :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="amount-input"
                                            @change="changeTotalAmount($event, false)"
                                        >
                                        </a-input-number> -->
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="form.total_fee"
                                            :controls="false"
                                            :disabled="form.items.length > 1 && autoCalculateState"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="changeTotalAmount($event, false)"
                                        />
                                    </div>
                                    <div class="amount-item" v-show="form.invoice_currency !== localCurrency">
                                        <div class="amount-lable bold">TOTAL {{ localCurrency }}</div>
                                        <!-- <a-input-number
                                            v-model:value="form.total_fee_local"
                                            :disabled="form.invoice_currency === localCurrency"
                                            :controls="false"
                                            :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="amount-input"
                                        >
                                        </a-input-number> -->
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="form.total_fee_local"
                                            :controls="false"
                                            :disabled="form.invoice_currency === localCurrency"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                        />
                                    </div>
                                    <div
                                        class="amount-item"
                                        v-if="form.invoice_currency !== localCurrency && !props.readonlyMode"
                                    >
                                        <div class="amount-lable bold">Difference</div>
                                        <a-input-number
                                            v-model:value="form_diff"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="amount-display-alert"
                                        >
                                        </a-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ap-invoice-block">
                    <a-form-item name="invoiceComments" :label="i18n.t('bkAp.invoiceComment')">
                        <a-textarea
                            class="textarea-wrap"
                            v-model:value="form.invoice_comments"
                            :placeholder="i18n.t('commonTag.msgInput')"
                            :rows="3"
                            :auto-size="{minRows: 3, maxRows: 50}"
                            :disabled="readonlyMode"
                        />
                    </a-form-item>
                </div>

                <div class="ap-invoice-footer">
                    <a-button class="cancel-button" shape="round" @click="cancel">{{
                        i18n.t('commonTag.cancel')
                    }}</a-button>
                    <a-button
                        v-show="!readonlyMode"
                        :disabled="isDisable"
                        shape="round"
                        type="primary"
                        @click="save"
                        :loading="formLoading"
                    >
                        {{ i18n.t('bkAp.create') }}
                    </a-button>
                </div>
            </a-form>
        </a-spin>

        <!--Create Product/Service Form Pop-up-->
        <a-modal
            :title="i18n.t('productService.createTitle')"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="820"
            :bodyStyle="{padding: '10px 14px 14px'}"
            :z-index="2902"
        >
            <product-service-form
                :edit-mode="false"
                :origin="'outside'"
                client-type="CUSTOMER"
                @fetchProductServiceList="fetchProductServiceList({company_code: userCompany[0].code, $limit: -1})"
                @dismiss="showProductServiceModal(false)"
            ></product-service-form>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
.ap-invoice-block {
    padding: 24px 0;
    border-bottom: 1px solid #e2e2ea;

    &:first-child {
        padding-top: 0;
    }
}

.ap-invoice-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn + .ant-btn {
        margin-left: 12px;
    }
}

.table-input {
    background-color: #f5f7f9;
    border-color: #f5f7f9;
    border-radius: 4px;
    padding-left: 4px;
    padding-right: 4px;
    &.ant-input-number {
        padding-left: 0px;
        padding-right: 0px;
        :deep(.ant-input-number-input-wrap input) {
            padding-left: 4px;
            padding-right: 4px;
        }
    }
    &.ant-select {
        padding-left: 0px;
        padding-right: 0px;
        :deep(.ant-select-selector) {
            padding-left: 4px;
            padding-right: 4px;
            .ant-select-selection-search {
                left: 4px;
            }
        }
    }
    &.ant-input:hover,
    &.ant-input-number:hover,
    &.ant-input:focus,
    &.ant-input-focused,
    &.ant-input-number-focused {
        border-color: #216fcf;
    }
    :deep(.ant-select-selector) {
        background-color: #f5f7f9 !important;
        border-color: #f5f7f9;
    }
}
.table-input-static {
    background-color: #ffffff;
    border-color: #ffffff;
    border-radius: 4px;
    padding-left: 4px;
    padding-right: 4px;
    &.ant-input-number {
        padding-left: 0px;
        padding-right: 0px;
        :deep(.ant-input-number-input-wrap input) {
            padding-left: 4px;
            padding-right: 4px;
        }
    }
    &.ant-select {
        padding-left: 0px;
        padding-right: 0px;
        :deep(.ant-select-selector) {
            padding-left: 4px;
            padding-right: 4px;
            .ant-select-selection-search {
                left: 4px;
            }
        }
    }
    &.ant-input:hover,
    &.ant-input-number:hover,
    &.ant-input:focus,
    &.ant-input-focused,
    &.ant-input-number-focused {
        border-color: #216fcf;
    }
    :deep(.ant-select-selector) {
        background-color: #ffffff !important;
        border-color: #ffffff;
    }
}

:deep(.ant-row.ant-form-item) {
    margin: 0;

    .ant-col.ant-form-item-control .table-input {
        width: 100%;
    }
}

:deep(.ap-invoice-block .ant-col.ant-form-item-label) {
    height: 31.5px;
}

:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -10px;
    font-size: 12px;
    line-height: 12px;
    min-height: 12px;
    display: none;
}

.icon-question {
    margin: 0;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #2ead2b;
    margin-left: 5px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}

:deep(.ant-form-item-label [title='Comments']) {
    font-size: 16px;
    font-weight: 700;
}

.textarea-wrap {
    min-height: 96px;
    height: 96px;
    max-height: 96px;
    margin-bottom: 15px;
}

.meta-wrap {
    .title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 700;
    }

    .meta-lable {
        margin-bottom: 8px;
    }
}

.column-item-input {
    margin-top: 12px;
    margin-bottom: 12px;
}

.ap-invoice-amount-block {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding: 24px 0 0;

    .ap-invoice-amount-block-left {
        width: 300px;
    }

    .ap-invoice-amount-block-right {
        width: 285px;
        min-width: 285px;
        .amount-block {
            width: 100%;

            .title {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                text-align: right;
            }

            .switch-wrap {
                margin-right: 8px;
            }

            .amount-item-wrap {
                padding: 20px 16px;
                width: 100%;
                background-color: #f5f7f9;
                border-radius: 8px;

                .amount-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0px;
                    }

                    .amount-lable {
                        width: calc(100% - 151px - 8px);
                        text-align: right;

                        &.bold {
                            font-weight: 700;
                        }
                    }

                    .amount-input {
                        width: 151px;

                        &.ant-input-number-disabled {
                            background-color: #fff;
                        }
                    }
                    .amount-display-alert {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: rgba(255, 0, 0, 0.2);
                            color: red;
                        }
                    }
                }
            }
        }
    }
}

.amount-input-prime {
    width: 151px;
    height: 33px;
    border-radius: 6px;
    color: #333;
    background-color: #fff;
    &.item-amount {
        max-width: 100%;
        background-color: #f5f7f9;
    }
}

:deep(.p-inputnumber input) {
    font-size: 14px;
    padding-left: 10px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border: 1px solid #4096ff;
        box-shadow: 0 0 0 2px #0591ff1a;
    }
}

:deep(.p-inputnumber.item-amount input) {
    border-color: #f5f7f9;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border-color: #216fcf;
    }
}

:deep(.p-inputnumber.item-amount input::placeholder) {
    color: #bbb;
}
</style>
