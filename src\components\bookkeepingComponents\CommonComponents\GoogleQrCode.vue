<!-- @format -->

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useStore} from 'vuex'
import {UserInfo} from '@/lib/storage'
const userInfo: any = UserInfo.get() || {}
const emits = defineEmits(['dismiss'])

const store = useStore()
const i18n: Composer = i18nInstance.global
const checked = ref(!!userInfo?.google_qrcode)
const qrCode = ref(userInfo?.google_qrcode)

const changed = async (value: any) => {
    const res = await store.dispatch('UserManagementStore/getGoogleQrCode', value)
    // console.log('res-----', res)
    qrCode.value = userInfo.google_qrcode = res.data
    UserInfo.set(userInfo)
    // console.log('userInfo-----', userInfo)
}

const cancel = () => {
    emits('dismiss')
}
</script>
<template>
    <div class="page-container-changepwd_form" v-if="checked">
        <a-qrcode v-model:value="qrCode" />
    </div>
    <a-divider class="footer-divider" />
    <footer>
        <a-switch
            v-model:checked="checked"
            :checked-children="i18n.t('commonTag.enable')"
            :un-checked-children="i18n.t('commonTag.disable')"
            @change="changed"
        />
    </footer>
</template>

<style lang="scss" scoped>
.icon-exclamation {
    margin: 0;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #aab9cb;
    margin-left: 5px;
}
.page-container-changepwd_form {
    padding: 20px 24px 0px;
    .form-box {
        // grid-column-gap: 10px;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: repeat(2, 1fr);
        grid-template-areas:
            'newpassword'
            'repassword';
        .form-box-item-newpassword {
            grid-area: newpassword;
        }
        .form-box-item-repassword {
            grid-area: repassword;
        }
    }
}
.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}
footer {
    padding: 12px 24px;
    text-align: right;
    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }
    .ant-btn {
        min-width: 65px;
    }
    .ant-btn + .ant-btn {
        margin-left: 8px;
    }
}
</style>
