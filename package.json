{"name": "infinite-nt", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:sandbox": "run-p type-check && vite build --mode sandbox", "preview": "vite preview --port 4173", "test:unit": "vitest --environment jsdom", "test:e2e": "start-server-and-test preview http://localhost:4173/ 'cypress open --e2e'", "test:e2e:ci": "start-server-and-test preview http://localhost:4173/ 'cypress run --e2e'", "build-only": "vite build", "build-only:dev": "vite build  --mode development", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint --quiet . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "fix": "prettier --write  ./src", "prepare": "husky install", "predeploy": "firebase use rakutenspeech", "deploy:firebase": "firebase deploy --only hosting:bkp-v2"}, "lint-staged": {"*.{vue,js,jsx,cjs,mjs,ts,tsx,cts,mts}": ["npm run lint", "npm run fix"], "*.css": "stylelint --fix"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@googlemaps/js-api-loader": "^1.14.3", "@vueuse/core": "^9.13.0", "ant-design-vue": "^4.1.2", "axios": "^0.27.2", "consola": "^3.2.3", "crypto-js": "^4.1.1", "currency.js": "^2.0.4", "dayjs": "^1.11.5", "decimal.js": "^10.4.3", "echarts": "^5.4.1", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "js-md5": "^0.7.3", "lodash": "^4.17.21", "moment": "^2.29.4", "nprogress": "^0.2.0", "primeicons": "^7.0.0", "primevue": "^4.2.5", "ramda": "^0.28.0", "uuid": "^8.3.2", "v-viewer": "^3.0.11", "validator": "^13.7.0", "vue": "^3.2.37", "vue-i18n": "^9.2.2", "vue-router": "^4.1.3", "vuex": "^4.0.2"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^6.0.1", "@rushstack/eslint-patch": "^1.1.4", "@types/crypto-js": "^4.1.1", "@types/file-saver": "^2.0.5", "@types/google.maps": "^3.50.1", "@types/js-md5": "^0.4.3", "@types/jsdom": "^20.0.0", "@types/lodash": "^4.14.184", "@types/node": "^16.18.101", "@types/nprogress": "^0.2.0", "@types/ramda": "^0.28.15", "@types/uuid": "^8.3.4", "@types/validator": "^13.7.8", "@vitejs/plugin-vue": "^3.0.1", "@vitejs/plugin-vue-jsx": "^2.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.0.2", "@vue/tsconfig": "^0.1.3", "cypress": "^10.4.0", "eslint": "8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.3.0", "husky": "^8.0.1", "jsdom": "^20.0.0", "less": "^4.1.3", "lint-staged": "^13.0.3", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "sass": "^1.54.8", "start-server-and-test": "^1.14.0", "stylelint": "^14.11.0", "stylelint-config-recommended": "^9.0.0", "typescript": "~4.7.4", "vite": "^3.0.4", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^0.21.0", "vue-tsc": "^0.39.5"}}