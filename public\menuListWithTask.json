{"data": [{"id": "80", "menuName": "Bookkeeping", "menuNameEn": "Bookkeeping", "menuIcon": null, "positionSort": "7", "menuLevel": "1", "menuLevelName": null, "parentId": null, "menuUrl": "/bookkeeping", "remark": "Bookkeeping", "createTime": "2021-10-28 15:29:33", "updateTime": "2021-10-28 15:29:33", "kycName": "/Bookkeeping", "childMenus": [{"id": "119", "menuName": "task", "menuNameEn": "Task", "menuIcon": null, "positionSort": "1", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "/task", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2022-05-03 13:14:08", "kycName": "/Task", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "91", "menuName": "purchase", "menuNameEn": "Purchase", "menuIcon": null, "positionSort": "2", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "accountPayable", "remark": "bookkeeping", "createTime": "2021-11-19 14:37:36", "updateTime": "2022-05-03 13:13:31", "kycName": "/Purchase", "childMenus": [{"id": "92", "menuName": "massiveProcess", "menuNameEn": "Massive Process", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "91", "menuUrl": "/bookkeeping/ap/uploadInvoice", "remark": "bookkeeping", "createTime": "2021-11-19 14:51:23", "updateTime": "2022-05-04 13:18:12", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "95", "menuName": "invoices", "menuNameEn": "Invoices", "menuIcon": null, "positionSort": "4", "menuLevel": "3", "menuLevelName": null, "parentId": "91", "menuUrl": "/bookkeeping/ap/invoiceHistory", "remark": "bookkeeping", "createTime": "2021-11-19 14:51:23", "updateTime": "2022-05-03 13:13:31", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "96", "menuName": "sales", "menuNameEn": "Sales", "menuIcon": null, "positionSort": "3", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "accountPayable", "remark": "bookkeeping", "createTime": "2021-11-19 14:54:44", "updateTime": "2022-05-03 13:13:32", "kycName": "/Sales", "childMenus": [{"id": "100", "menuName": "billing", "menuNameEn": "Billing", "menuIcon": null, "positionSort": "4", "menuLevel": "3", "menuLevelName": null, "parentId": "96", "menuUrl": "/bookkeeping/ar/invoiceHistory", "remark": "bookkeeping", "createTime": "2021-11-19 14:54:44", "updateTime": "2022-05-03 13:13:32", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "121", "menuName": "payroll", "menuNameEn": "Payroll", "menuIcon": null, "positionSort": "4", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "accountPayable", "remark": "bookkeeping", "createTime": "2021-11-19 14:37:36", "updateTime": "2022-05-03 13:13:31", "kycName": "/Payroll", "childMenus": [{"id": "123", "menuName": "payrollRecord", "menuNameEn": "Payroll Record", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "121", "menuUrl": "/bookkeeping/py/invoiceHistory", "remark": "bookkeeping", "createTime": "2021-11-19 14:51:23", "updateTime": "2022-05-04 13:18:12", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "122", "menuName": "payment", "menuNameEn": "Payment", "menuIcon": null, "positionSort": "5", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "accountPayable", "remark": "bookkeeping", "createTime": "2021-11-19 14:37:36", "updateTime": "2022-05-03 13:13:31", "kycName": "/Payment", "childMenus": [{"id": "124", "menuName": "payment", "menuNameEn": "Payment", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "122", "menuUrl": "/bookkeeping/pmt/invoiceHistory", "remark": "bookkeeping", "createTime": "2021-11-19 14:51:23", "updateTime": "2022-05-04 13:18:12", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "81", "menuName": "bankReconciliation", "menuNameEn": "Bank Reconciliation", "menuIcon": null, "positionSort": "5", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "bankReconciliation", "remark": "bookkeeping", "createTime": "2021-10-28 15:33:21", "updateTime": "2021-11-19 14:59:49", "kycName": "/BankReconciliation", "childMenus": [{"id": "84", "menuName": "reconcile", "menuNameEn": "Reconcile", "menuIcon": null, "positionSort": "3", "menuLevel": "3", "menuLevelName": null, "parentId": "81", "menuUrl": "/bookkeeping/bankReconciliation/main", "remark": "bookkeeping", "createTime": "2021-10-28 15:39:25", "updateTime": "2022-08-15 18:15:40", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "103", "menuName": "history", "menuNameEn": "History", "menuIcon": null, "positionSort": "4", "menuLevel": "3", "menuLevelName": null, "parentId": "81", "menuUrl": "/bookkeeping/bankReconciliation/history", "remark": "bookkeeping", "createTime": "2022-01-14 14:14:13", "updateTime": "2022-08-15 18:15:40", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "108", "menuName": "general<PERSON><PERSON>ger", "menuNameEn": "General <PERSON><PERSON>", "menuIcon": null, "positionSort": "6", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "gl", "remark": "bookkeeping", "createTime": "2022-05-27 17:14:42", "updateTime": "2022-07-01 13:21:47", "kycName": "/GeneralLedger", "childMenus": [{"id": "109", "menuName": "manualEntry", "menuNameEn": "Manual Entry", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "108", "menuUrl": "/bookkeeping/gl/glEntry", "remark": "bookkeeping", "createTime": "2022-05-27 17:16:42", "updateTime": "2022-06-09 18:23:56", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "110", "menuName": "journalEntries", "menuNameEn": "Journal Entries", "menuIcon": null, "positionSort": "2", "menuLevel": "3", "menuLevelName": null, "parentId": "108", "menuUrl": "/bookkeeping/gl/glListing", "remark": "bookkeeping", "createTime": "2022-05-27 17:16:42", "updateTime": "2022-10-18 17:00:53", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "82", "menuName": "setting", "menuNameEn": "Setting", "menuIcon": null, "positionSort": "8", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "common", "remark": "bookkeeping", "createTime": "2021-10-28 15:33:21", "updateTime": "2022-07-07 19:59:40", "kycName": "/Setting", "childMenus": [{"id": "85", "menuName": "contact", "menuNameEn": "Contact", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "82", "menuUrl": "/bookkeeping/common/customer", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2022-05-03 13:14:08", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "86", "menuName": "profile", "menuNameEn": "Profile", "menuIcon": null, "positionSort": "2", "menuLevel": "3", "menuLevelName": null, "parentId": "82", "menuUrl": "/bookkeeping/common/taxInformation", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2022-05-05 13:31:19", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "87", "menuName": "bankInformation", "menuNameEn": "Bank Information", "menuIcon": null, "positionSort": "3", "menuLevel": "3", "menuLevelName": null, "parentId": "82", "menuUrl": "/bookkeeping/common/bankInformation", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2021-10-28 18:04:38", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "88", "menuName": "coA", "menuNameEn": "CoA", "menuIcon": null, "positionSort": "4", "menuLevel": "3", "menuLevelName": null, "parentId": "82", "menuUrl": "/bookkeeping/common/accountDescription", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2022-05-03 13:14:08", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "113", "menuName": "coAMapping", "menuNameEn": "CoA Mapping", "menuIcon": null, "positionSort": "5", "menuLevel": "3", "menuLevelName": null, "parentId": "82", "menuUrl": "/bookkeeping/common/coaMapping", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2022-10-05 19:27:11", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "90", "menuName": "supplier", "menuNameEn": "Supplier", "menuIcon": null, "positionSort": "7", "menuLevel": "3", "menuLevelName": null, "parentId": "82", "menuUrl": "/bookkeeping/common/supplier", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2022-10-05 19:27:11", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "111", "menuName": "reporting", "menuNameEn": "Reporting", "menuIcon": null, "positionSort": "7", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "remark": "bookkeeping", "createTime": "2022-07-07 16:06:29", "updateTime": "2022-07-07 19:59:40", "kycName": "/Reporting", "childMenus": [{"id": "1112", "menuName": "exportTrialBalance", "menuNameEn": "Reports", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "111", "menuUrl": "/bookkeeping/reporting/realtimeReports", "remark": "bookkeeping", "createTime": "2021-10-28 18:04:38", "updateTime": "2022-05-03 13:14:08", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "101", "menuName": "help", "menuNameEn": "Help", "menuIcon": null, "positionSort": "9", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "help", "remark": "help", "createTime": "2022-11-16 17:48:29", "updateTime": "2022-11-16 17:48:40", "kycName": "/Help", "childMenus": [{"id": "102", "menuName": "userGuide", "menuNameEn": "User Guide", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "101", "menuUrl": "/bookkeeping/help/userGuide", "remark": "bookkeeping", "createTime": "2022-11-16 17:53:29", "updateTime": "2022-11-16 17:54:29", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "106", "menuName": "FAQ", "menuNameEn": "FAQ", "menuIcon": null, "positionSort": "2", "menuLevel": "3", "menuLevelName": null, "parentId": "101", "menuUrl": "/bookkeeping/help/faq", "remark": "bookkeeping", "createTime": "2022-11-16 17:53:29", "updateTime": "2022-11-16 17:54:48", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "115", "menuName": "account", "menuNameEn": "Account", "menuIcon": null, "positionSort": "10", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "account", "remark": "account", "createTime": "2022-12-27 11:48:29", "updateTime": "2022-12-27 11:48:40", "kycName": "/Account", "childMenus": [{"id": "116", "menuName": "user", "menuNameEn": "User", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "115", "menuUrl": "/bookkeeping/account/user", "remark": "bookkeeping", "createTime": "2022-12-27 11:53:29", "updateTime": "2022-12-27 11:54:29", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}, {"id": "117", "menuName": "subscribe", "menuNameEn": "Subscribe", "menuIcon": null, "positionSort": "10", "menuLevel": "2", "menuLevelName": null, "parentId": "80", "menuUrl": "subscribe", "remark": "subscribe", "createTime": "2022-12-27 11:48:29", "updateTime": "2022-12-27 11:48:40", "kycName": "/Subscribe", "childMenus": [{"id": "118", "menuName": "nosubscribe", "menuNameEn": "NoSubscribe", "menuIcon": null, "positionSort": "1", "menuLevel": "3", "menuLevelName": null, "parentId": "117", "menuUrl": "/bookkeeping/subscribe/nosubscribe", "remark": "bookkeeping", "createTime": "2022-12-27 11:53:29", "updateTime": "2022-12-27 11:54:29", "childMenus": [], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}], "parentMenuName": null, "parentMenuNameEn": null}], "code": 1000, "msg": "success"}