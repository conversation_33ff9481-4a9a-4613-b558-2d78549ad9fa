/** @format */

@import './base.scss';
@import 'fonts';
//@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');
//@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');

//:root {
//  --ad-font-family-dm: 'DM Sans', sans-serif;
//  --ad-font-family-lato: 'Lato', sans-serif;
//}


* {
  font-family: DM Sans, sans-serif;  //因为在 fonts.scss 中已经定义了 字体所以可以直接写字体名称不用加引号
}

.ff-lato {
  font-family: Lato, sans-serif;
}

#app {
    font-weight: normal;
}

// need iframe to display pdf
iframe {
    //display: none;
}

@media (hover: hover) {
    a:hover {
        background-color: hsla(160, 100%, 37%, 0.2);
    }
}

.input-spin .ant-spin .anticon {
    font-size: 20px !important;
    margin: -10px;
}

.ant-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.ant-btn-round.ant-btn-sm {
    padding: 4px 10px;
}

.ant-tabs-nav-wrap {
    .ant-tabs-nav-list .ant-tabs-tab .ant-tabs-tab-btn {
        font-weight: 400;
        line-height: 20px;
    }

    // adjust with prototype
    .ant-tabs-nav-list .ant-tabs-tab-active .ant-tabs-tab-btn {
        font-weight: 700;
    }
}

.ant-table-wrapper {
    .ant-table-header {
        border-radius: 8px;
    }

    .ant-table-thead {
        height: 52px;
    }

    .ant-table-thead .ant-table-cell {
        &:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        &:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }
    }

    // add thead parent for header
    :is(.ant-table-thead, .ant-table-header) .ant-table-cell {
        line-height: 20px;
        font-weight: 700;
        border: 0;
        font-size: 16px;

        &::before {
            content: none !important;
        }
    }

    .ant-table .ant-table-tbody .ant-table-cell,
    .ant-table-body .ant-table-cell {
        padding-top: 8px;
        padding-bottom: 8px;
        line-height: 20px;
    }

    .ant-table .ant-table-content {
        overflow-x: scroll;

        &::-webkit-scrollbar {
            width: 0px;
        }
    }
}

.ant-pagination {
    .ant-pagination-item-link,
    .ant-pagination-item {
        border-radius: 50%;
        a {
            border-radius: 50%;
        }
    }

    .ant-pagination-options .ant-select .ant-select-selector {
        border-radius: 32px;
        height: 32px;
        .ant-select-selection-item {
            line-height: 30px;
        }
    }
}

.ant-switch[aria-checked='false'] {
    background-color: #adbac9;
}

.modal-wrap {
    .ant-modal-content {
        border-radius: 8px;
    }

    .ant-modal-header {
        padding: 17px 24px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;

        .ant-modal-title {
            font-size: 16px;
            font-weight: 700;
        }
    }

    .ant-modal-body {
        padding: 0px;
    }
}

.drawer-wrap {
    .ant-drawer-content-wrapper,
    .ant-drawer-content,
    .ant-drawer-wrapper-body,
    .ant-drawer-header {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

    .ant-drawer-content-wrapper,
    .ant-drawer-content,
    .ant-drawer-wrapper-body {
        border-bottom-left-radius: 12px;
        border-bottom-right-radius: 12px;
    }

    .ant-drawer-header {
        height: 54px;

        .ant-drawer-title {
            font-size: 16px;
            color: #262626;
            line-height: 22px;
            font-weight: 700;
        }
    }

    .ant-drawer-body {
        padding: 0;
    }
}

.alert-wrap.ant-alert-info {
    border-radius: 2px;
    background-color: #edf4ff;
    border-color: #abcdff;
}

/* Make clicks pass-through */
#nprogress {
    pointer-events: none;
    color: red;

    .bar {
        background: #fff;

        position: fixed;
        z-index: 1031;
        top: 0;
        left: 0;

        width: 100%;
        height: 2px;
    }

    .peg {
        display: block;
        position: absolute;
        right: 0px;
        width: 100px;
        height: 100%;
        box-shadow: 0 0 10px #2c7fe4, 0 0 5px #2c7fe4;
        opacity: 1;

        -webkit-transform: rotate(3deg) translate(0px, -4px);
        -ms-transform: rotate(3deg) translate(0px, -4px);
        transform: rotate(3deg) translate(0px, -4px);
    }
}

.tutorial-main-page {
    position: absolute;
    width: 42px;
    height: 100px;
    background-color: rgb(33 33 33 / 60%);
    // inset: 0px;
    top: 0;
    left: 0;
    margin: auto;
    box-shadow: rgb(33 33 33 / 80%) 0px 0px 10px 2px, rgb(33 33 33 / 60%) 0px 0px 0px 5000px;
    opacity: 1;
    z-index: 99;
    border-radius: 5px;
    border: 2px dashed #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease-out;

    .inner-content {
        height: 5px;
        padding: 5px;
        border-radius: 5px;
        background: rgb(255 255 255 / 25%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;

        .ant-checkbox-wrapper + .ant-checkbox-wrapper {
            margin-left: 0;
        }
    }
}

.tutorial-tips {
    position: absolute;
    top: 0;
    left: -150px;
    width: 100px;
    height: 80px;
    background-color: #fff;
    z-index: 99;
    border-radius: 8px;

    .tips-content {
        display: flex;
        height: 100%;
        align-items: center;
        padding: 0 10px;
        justify-content: space-between;

        .content-wrap {
            display: flex;
            flex-direction: column;
        }

        .title {
            font-weight: bold;
        }

        .btn-select {
            color: #004fc1;
            border-color: transparent;
            background: transparent;
            box-shadow: none;
            white-space: nowrap;
        }
    }

    .arrow-up {
        width: 0;
        height: 0;
        font-size: 0;
        border-width: 15px;
        border-style: solid;
        border-color: transparent transparent #fff;
        overflow: hidden;
        position: absolute;
        right: 30px;
        top: -30px;
    }

    .arrow-up-left {
        width: 0;
        height: 0;
        font-size: 0;
        border-width: 15px;
        border-style: solid;
        border-color: transparent transparent #fff;
        overflow: hidden;
        position: absolute;
        left: 30px;
        top: -30px;
    }

    .arrow-down {
        width: 0;
        height: 0;
        font-size: 0;
        border-width: 15px;
        border-style: solid;
        border-color: #fff transparent transparent;
        overflow: hidden;
        position: absolute;
        left: 30px;
        bottom: -30px;
    }
}

.add-button {
    font-size: 16px;
    padding: 6px 16px;
}

.tag-green {
    background-color: #e1f6e2;
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #00b60a;
    line-height: 20px;
    font-weight: 500;
    margin: 0;
}

.tag-red {
    background-color: #ffe8e7;
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #ff372f;
    line-height: 20px;
    font-weight: 500;
    margin: 0;
}

.tag-gray {
    background-color: #ebeef1;
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #4c6081;
    line-height: 20px;
    font-weight: 500;
    margin: 0;
}

.tag-orange {
    background-color: #fff7e1;
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #d8a007;
    line-height: 20px;
    font-weight: 500;
    margin: 0;
}
