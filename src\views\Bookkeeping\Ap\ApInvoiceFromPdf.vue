<!-- @format -->
<script setup lang="ts">
import { useStore } from 'vuex'
import PdfViewer from '@/components/bookkeepingComponents/PdfViewer.vue'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ApInvoiceComponentIntegration from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegration.vue'
import ApInvoiceComponentIntegrationMx from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegrationMx.vue'
import ApInvoiceComponentIntegrationMxOcr from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegrationMxOcr.vue'
import ArInvoiceComponent from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponent.vue'
import PeriodicalBooking from '@/components/bookkeepingComponents/ArComponents/PeriodicalBooking.vue'
import { message, Modal } from 'ant-design-vue'
import type { ColumnsType } from 'ant-design-vue/es/table'
import { computed, nextTick, onBeforeMount, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import { useRouter } from 'vue-router'
import * as _ from 'lodash'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import ArFullInvoice from '@/views/Bookkeeping/Ar/ArFullInvoice.vue'
import { UserInfo, JushiFetchInfo, Ap_Integration, UserCompany } from '@/lib/storage'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'

// 添加 userCompany 获取
const userCompany = UserCompany.get() || []
const props = defineProps({
    pageType: String,
})

interface InvoiceCreationStatus {
    invoiceCreatedStatus: number
    id: string
}

interface FileRecord {
    id: string
    file_name?: string
    file_url?: string
    xml_url?: string
    is_identified?: string
    file_type?: string
}

const apIntegration: any = Ap_Integration.get() ?? 0
const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const userInfo: any = UserInfo.get() || {}
const jushiFetchInfo: any = JushiFetchInfo.get() || true
// init parameter
const state = reactive({
    formLoading: false,
    current: store.state.ApStore.ocrInvoiceItemByFile || {},
    currentPdfFileUrl: '',
    readonlyMode: false,
    operationMode: 'editing',
    showLeaveConfirm: false,
})
const userType = ref('')
//define element
const formCompKey = ref(0)
const pdfViewerKey = ref(0)
const formDIV = ref(null)
const pdfDIV = ref(null)
const confirmationWrap = ref<HTMLElement | null>(null)

const contentTxt = `You will lose the not-submitted data for this invoice if you cancel.`

const dataSource = ref([
    {
        key: '1',
        text: '- - -',
        quantity: 10,
        unit: '- - -',
        price: '10.00',
        amount: '100.00',
    },
    {
        key: '2',
        text: '- - -',
        quantity: 10,
        unit: '- - -',
        price: '10.00',
        amount: '100.00',
    },
    {
        key: '3',
        text: '- - -',
        quantity: 10,
        unit: '- - -',
        price: '10.00',
        amount: '100.00',
    },
])

const columns: ColumnsType = [
    {
        title: '#',
        dataIndex: 'index',
        key: 'index',
        align: 'center',
        width: '36px',
    },
    {
        title: 'PO Text',
        dataIndex: 'po_text',
        key: 'po_text',
        align: 'center',
        width: '60px',
    },
    {
        title: 'Quantity',
        dataIndex: 'quantity',
        key: 'quantity',
        align: 'center',
        width: '80px',
    },
    {
        title: 'Unit',
        dataIndex: 'unit',
        key: 'unit',
        align: 'center',
        width: '60px',
    },
    {
        title: 'Price',
        dataIndex: 'price',
        key: 'price',
        align: 'center',
        width: '60px',
    },
    {
        title: 'Amount',
        dataIndex: 'amount',
        key: 'amount',
        align: 'center',
        width: '80px',
    },
]

// define child component ref.
const invoiceForm = ref<{ resetFormField: () => void }>()
//const showFetchInfoTable = ref(false)
const enableFetchInfo = ref(false)
// vuex actions
const updateActiveKey = (key: string) => store.commit('ApStore/updateActiveTabKey', key)
const createInvoice = async (data: any) => await store.dispatch('ApStore/createInvoiceV1', data)
const saveSAPBP = async (data: any) => await store.dispatch('ApStore/saveSAPBP', data)
const createJushi = (data: any) => store.dispatch('ApStore/createInvoiceJushi', data)
const updateInvoice = async (data: any) => await store.dispatch('ApStore/updateInvoiceV1', data)
const updateInvoiceCreationStatus = async (data: InvoiceCreationStatus) =>
    await store.dispatch('ArApBrStore/updateInvoiceCreationStatusV1', data)

// getting the invoice item user just submitted.
// TODO: due new api lost necessary params, need find new way
const fetchSubmittedInvoiceItem = (refNo: string, fileId: string) => {
    const query = { reference_no: refNo, file_id: fileId, br_flag: '0' }
    return store.dispatch('ApStore/fetchInvoicesHistoryList', query)
}

const fetchInvoiceItem = (data: { file_id: string }) => {
    return store.dispatch('ApStore/fetchApOcrResultByPdfIdV1', data)
}

// vuex mutations
const updatePdfInfo = (data: FileRecord) => store.commit('ArApBrStore/updatePdfInfo', data)
const resetCurrent = (data = [{}] as any[]) => store.commit('ApStore/updateOcrInvoiceItem', data)
const initPoData = () => store.commit('ApStore/updatePoData', {})
// vue state
const currentPdfInfo = computed(() => store.state.ArApBrStore.pdfInfo)
const currentPoData = computed(() => {
    const po = store.state.ApStore.poData
    const mapped_items = po.items?.map((x: any) => {
        return {
            po_text: x.ebeln,
            quantity: x.menge,
            unit: x.meins,
            amount: x.wrbtr,
        }
    })
    return { ...po, mapped_items }
})
// common method
const invoiceFormTitle = computed(() =>
    state.readonlyMode ? i18n.t('bkApInvoice.readonly') : i18n.t('bkApInvoice.create'),
)

const isPageLoading = ref(true)

const confirmationConfirm = () => {
    state.showLeaveConfirm = false
    state.readonlyMode = false
    state.operationMode = 'creating'
    Modal.destroyAll()
    closeModalInA()
    // router.push({
    //     path: '/bookkeeping/ap/uploadInvoice',
    // })
}
const emit = defineEmits(['close-modal'])
const closeModalInA = () => {
    // 向父组件发送关闭模态框的信号
    emit('close-modal')
}
const confirmationCancel = () => {
    return
}

const dismiss = () => {
    if (!state.readonlyMode) {
        state.showLeaveConfirm = true
    }
}

// const completeInvoiceCreation = () => {
//     Modal.confirm({
//         title: i18n.t('bkCommonTag.confirmation'),
//         content: `The Invoice Creation for the current file will be completed!`,
//         async onOk() {
//             try {
//                 const payload: InvoiceCreationStatus = {
//                     id: currentFileId.value as string,
//                     invoiceCreatedStatus: '1',
//                 }
//                 const response = await updateInvoiceCreationStatus(payload)
//                 if (response.data.code === 1000) {
//                     message.success(response.data.msg)
//                     router.replace('/bookkeeping/ap/uploadInvoice').then()
//                 } else {
//                     message.error(response.data.msg)
//                 }
//             } catch (error) {
//                 console.log(error)
//             }
//         },
//         okText: i18n.t('commonTag.confirm'),
//         cancelText: i18n.t('commonTag.cancel'),
//         okType: 'danger',
//         zIndex: 3001,
//     })
// }

const reRenderForm = () => {
    formCompKey.value += 1
    pdfViewerKey.value += 1
}

const switchFetchInfo = () => {
    // showFetchInfoTable.value = !showFetchInfoTable.value
    JushiFetchInfo.set(enableFetchInfo.value)
    console.log('checking again', enableFetchInfo.value, dataSource.value, currentPoData.value)
}

const saveForm = async (form: any, operation_mode?: string) => {
    try {
        state.formLoading = true
        // if (operation_mode === 'editing') {
        //     response = await updateInvoice(form)
        // } else {
        //     response = await createInvoice(form)
        // }
        // //only create will be availiable
        let response: any = {}
        if (apIntegration === 1 && form.po != null && form.po.length > 0) {
            console.log('ap form with po ============= ', form)
            if (INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && form.purpose === 'CREDIT_MEMO') {
                response = await store.dispatch('ApStore/createIntegrationInvoiceWithCmPoV1', form)
            } else {
                response = await store.dispatch('ApStore/createIntegrationInvoiceWithPoV1', form)
            }
        } else if (apIntegration === 1) {
            response = await store.dispatch('ApStore/createIntegrationInvoiceWithoutPoV1', form)
        } else {
            response = await store.dispatch('ApStore/createInvoiceV1', form)
        }
        // const response =
        //     apIntegration === 1
        //         ? await createInvoiceIntegrationV1(form)
        //         : userType.value === 'jushi'
        //             ? await createJushi(form)
        //             : await createInvoice(form)
        if (response.data.statusCode === 200) {
            console.log('response.data========', response.data)
            // get contact if first useing
            const contactFirstUsing = await store.dispatch('ApStore/fetchContactFirstUsingV1', {
                company_code: response.data.data.company_code,
                contact_id: response.data.data.issuer_id,
            })

            if (apIntegration === 1) {
                store.dispatch('ApStore/saveSAPBP', {
                    company_code: response.data.data.company_code,
                    issuer_id: response.data.data.issuer_id,
                })
                await store.dispatch('ApStore/postSapMasterTopV1', {
                    company_code: response.data.data.company_code,
                    contact_id: response.data.data.issuer_id,
                    contact_name: response.data.data.issuer_name,
                    gl_account_code: response.data.data.items[0].sap_gl_account ?? '',
                    wbs_code: response.data.data.items[0].sap_wbs ?? '',
                    cost_center_code: response.data.data.items[0].sap_cost_center ?? '',
                    internal_order_code: response.data.data.items[0].sap_internal_order ?? '',
                    profit_center_code: response.data.data.items[0].sap_profit_center ?? '',
                    gl_account_name: '',
                    wbs_name: '',
                    cost_center_name: '',
                    internal_order_name: '',
                    profit_center_code_name: '',
                })
            }
            // create approval flow
            if (apIntegration === 1 && response.data.data.pay_method !== '2') {
                const resCreateFlow = await store.dispatch('ApStore/createApprovalFlowData', response.data.data)
                if (resCreateFlow.data.statusCode === 200) {
                    const resApproveFlow = await store.dispatch('ApStore/startApprovalFlow', response.data.data)
                    if (resApproveFlow.sap_status !== 2) {
                        message.error({content: resApproveFlow.sap_msg})
                        return
                    }
                }
            }
            // if (apIntegration === 1 && response.data.data.pay_method === '2' && (form.po == null || form.po == '')) {
            //     // send ap data to sap (credit)
            //     const resSap = await store.dispatch('ApStore/sendApDataToSap', response.data.data)
            //     if (resSap.data.sap_status !== 2) {
            //         message.error({content: resSap.data.sap_msg})
            //         return
            //     }
            // }
            if (contactFirstUsing === 0) {
                message.success(`success. ${i18n.t('ApComponents.contactFirstUsing')}`)
            } else {
                message.success(i18n.t('ApComponents.success'))
            }
            // message.success(i18n.t('ApComponents.success'))
            invoiceForm.value?.resetFormField()
            // await fetchSubmittedInvoiceItem(form.reference_no, form.file_id)
            // state.current = store.state.ApStore.ocrInvoiceItemByFile
            // state.operationMode = 'editing'
            reRenderForm()
            updateActiveKey('pending')
            // await updateInvoiceCreationStatus({invoiceCreatedStatus: 1, id: form.file_id})
            closeModalInA()
            // await router.push({
            //     name: 'UploadInvoice',
            //     query: {
            //         activeName: 'pending',
            //         pageIndex: router.currentRoute.value.query.pageIndex,
            //         pageSize: router.currentRoute.value.query.pageSize,
            //     },
            // })
        } else {
            // message.error(response.data.message)
        }
    } catch (error) {
        console.log(error)
    } finally {
        state.formLoading = false
    }
}

const getJushiFetchInfoSession = () => {
    enableFetchInfo.value = !!jushiFetchInfo
    JushiFetchInfo.set(enableFetchInfo.value)
}

onBeforeMount(async () => {
    initPoData()
    if (props.pageType && props.pageType === 'edit') {
        state.operationMode = 'editing'
    } else if (router.currentRoute.value.query.pageType === 'edit') {
        state.operationMode = 'editing'
    }
    userType.value = userInfo.roles === '9996' ? 'jushi' : 'common'
    if (userType.value === 'jushi') {
        getJushiFetchInfoSession()
    }
    const res = await fetchInvoiceItem({ file_id: currentPdfInfo.value.id })
    state.current = {
        ...res.data.data[0],
        ...currentPdfInfo.value,
    }
})

onMounted(() => {
    isPageLoading.value = false
    dataSource.value = currentPoData.value?.mapped_items
    console.log('check fetch info table', dataSource.value)
})

// const handleClose = () => {
//     if (state.readonlyMode) {
//         return
//     }
//     Modal.confirm({
//         title: i18n.t('bkCommonTag.confirmation'),
//         content: `Stop the [Invoice Creation] procedure, the modified data will be initialized!`,
//         maskClosable: true,
//         // bodyStyle: {padding: 0},
//         closable: true,
//         okText: i18n.t('commonTag.confirm'),
//         cancelText: i18n.t('commonTag.cancel'),
//         okType: 'danger',
//         zIndex: 3000,
//     })
// }

watch(
    // current change then child component is render.
    () => state.current,
    () => {
        console.log(state.current)
        if (Object.keys(state.current).length > 0) {
            isPageLoading.value = false
        }
        // if (state.current.id) {
        //     state.operationMode = 'editing'
        // }
        pdfViewerKey.value += 1
    },
    {
        deep: true,
        immediate: true,
    },
)

watch(
    () => router,
    () => {
        if (router.currentRoute.value.query.pageType === 'edit') {
            state.operationMode = 'editing'
        }
    },
    {
        deep: true,
        immediate: true,
    },
)

onUnmounted(() => {
    const resetValue: FileRecord = {
        id: '',
        file_name: '',
        file_url: '',
        xml_url: '',
    }
    updatePdfInfo(resetValue)
    resetCurrent()
})
</script>

<template>
    <div class="page-container-invoice_pdf">
        <div class="content-box">
            <div v-if="isPageLoading" class="spin-page-wrap">
                <a-spin :spinning="isPageLoading"></a-spin>
            </div>
            <template v-else>
                <div class="content-box-left">
                    <h3 class="content-box-header">{{ i18n.t('ApInvoiceFormPdf.invoice') }}</h3>
                    <a-divider class="custom-divider"></a-divider>
                    <div class="content-box-left-inner" ref="pdfDIV" id="viewer_wrap_id">
                        <pdf-viewer :key="pdfViewerKey" :url="''" />
                        <div v-if="userType === 'jushi'" class="box-left-table-wrap">
                            <div v-if="!enableFetchInfo" class="title">
                                {{ i18n.t('ApInvoiceFormPdf.fetch') }}
                                <a-switch class="switch-wrap" v-model:checked="enableFetchInfo" size="small"
                                    @change="switchFetchInfo" />
                            </div>
                            <div v-if="enableFetchInfo">
                                <a-table style="margin-top: 10px" :dataSource="currentPoData.mapped_items"
                                    :columns="columns" :pagination="false" :scroll="{ y: 126 }">
                                    <template #bodyCell="{ column, index }">
                                        <template v-if="column?.key === 'index'">
                                            {{ index + 1 }}
                                        </template>
                                    </template>
                                </a-table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-box-right">
                    <h3 class="content-box-header">{{ invoiceFormTitle }}</h3>
                    <a-divider class="custom-divider"></a-divider>
                    <a-spin :spinning="state.formLoading">
                        <div class="invoice-form-content" ref="formDIV" id="invoice_form_id">
                            <ap-invoice-component
                                :key="formCompKey"
                                v-if="
                                    !isPageLoading &&
                                    apIntegration !== 1 &&
                                    (state.current.file_type === '1' || state.current.file_type === '3')
                                "
                                ref="invoiceForm"
                                :current-invoice="{}"
                                :readonly-mode="state.readonlyMode"
                                :operation-mode="state.operationMode"
                                from="massive_process"
                                @save="saveForm"
                                @dismiss="dismiss"
                            ></ap-invoice-component>
                            <ap-invoice-component-integration
                                :key="formCompKey"
                                v-if="
                                    !isPageLoading &&
                                    apIntegration === 1 &&
                                    userCompany[0].code !== '8888' &&
                                    !INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) &&
                                    (state.current.file_type === '1' || state.current.file_type === '3')
                                "
                                ref="invoiceForm"
                                :current-invoice="{}"
                                :readonly-mode="state.readonlyMode"
                                :operation-mode="state.operationMode"
                                from="massive_process"
                                @save="saveForm"
                                @dismiss="dismiss"
                            ></ap-invoice-component-integration>
                            <ap-invoice-component-integration-mx
                                :key="formCompKey"
                                v-if="
                                    !isPageLoading &&
                                    apIntegration === 1 &&
                                    userCompany[0].code !== '8888' &&
                                    INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) &&
                                    (state.current.file_type === '1' || state.current.file_type === '3')
                                "
                                ref="invoiceForm"
                                :current-invoice="{}"
                                :readonly-mode="state.readonlyMode"
                                :operation-mode="state.operationMode"
                                from="massive_process"
                                @save="saveForm"
                                @dismiss="dismiss"
                            ></ap-invoice-component-integration-mx>
                            <ap-invoice-component-integration-mx-ocr
                                :key="formCompKey"
                                v-if="
                                    !isPageLoading &&
                                    apIntegration === 1 &&
                                    userCompany[0].code === '8888' &&
                                    (state.current.file_type === '1' || state.current.file_type === '3')
                                "
                                ref="invoiceForm"
                                :current-invoice="{}"
                                :readonly-mode="state.readonlyMode"
                                :operation-mode="state.operationMode"
                                from="massive_process"
                                @save="saveForm"
                                @dismiss="dismiss"
                            ></ap-invoice-component-integration-mx-ocr>
                            <ar-full-invoice
                                v-else-if="!isPageLoading && state.current.file_type === '0'"
                                from="massive_process"
                            ></ar-full-invoice>
                        </div>
                    </a-spin>
                </div>
            </template>
        </div>
    </div>

    <div ref="confirmationWrap">
        <a-modal v-model:visible="state.showLeaveConfirm" centered destroyOnClose :get-container="confirmationWrap"
            :width="480" :closable="false" :wrapClassName="'modal-wrap'" :ok-text="i18n.t('commonTag.confirm')"
            :ok-type="'primary'" :ok-button-props="{ shape: 'round' }" :cancel-text="i18n.t('commonTag.cancel')"
            :cancel-button-props="{ shape: 'round', ghost: true, type: 'primary' }" @ok="confirmationConfirm"
            @cancel="confirmationCancel">
            <template #title>
                <div class="confirm-title-wrap">
                    <div class="confirm-title-text">
                        <ExclamationCircleFilled class="confirm-title-icon" />
                        {{ i18n.t('bkCommonTag.confirmation') }}
                    </div>
                </div>
            </template>
            <div class="confirmation-content-text">{{ contentTxt }}</div>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.page-container-invoice_pdf {
    width: 100%;
    height: 100%;

    .content-box {
        display: flex;
        justify-content: space-between;

        .content-box-partial-base {
            background-color: #fff;

            .content-box-header {
                padding: 16px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                background-color: #fff;
                text-align: center;
                text-transform: uppercase;
                //  font-family: Calibri;
                font-size: 20px;
                color: #262626;
                line-height: 26px;
                font-weight: 700;
                margin: 0;
            }
        }

        .content-box-left {
            @extend .content-box-partial-base;
            min-width: 500px;
            max-width: 500px;
            border-radius: 12px;
            margin-bottom: 76px;

            .content-box-left-inner {
                height: calc(100% - 59px);
                display: flex;
                align-items: center;
                justify-content: space-around;
                flex-direction: column;

                .box-left-table-wrap {
                    .title {
                        font-size: 16px;
                        font-weight: 700;
                        margin-bottom: 20px;
                    }

                    .switch-wrap {
                        margin-left: 8px;
                    }

                    .select-wrap {
                        margin-bottom: 16px;
                    }

                    height: 250px;
                    min-height: 250px;
                    max-height: 250px;
                    width: 100%;
                    padding: 0px 12px 24px;
                    border-bottom-left-radius: 12px;
                    border-bottom-right-radius: 12px;

                    :deep(.ant-table) {
                        .ant-table-thead {
                            height: auto;

                            .ant-table-cell {
                                padding: 12px 2px;
                            }
                        }

                        .ant-table-cell {
                            font-size: 12px;
                            line-height: 18px;
                            padding: 11.5px 2px;
                        }
                    }
                }
            }
        }

        .content-box-right {
            border-radius: 12px;
            min-width: calc(100% - 500px - 16px);
            max-width: calc(100% - 500px - 16px);

            .content-box-header {
                background-color: #fff;
                margin: 0;
                padding: 20px 15px;
                font-size: 16px;
                font-weight: 700;
                line-height: 22px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }

            .custom-divider {
                border-color: #e2e2ea;
            }

            .btn-function-group {
                display: flex;
                justify-content: space-between;
                padding: 0 20px;
            }
        }
    }
}

.page-drawer-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.invoice-form-content {
    .ap-invoice-page-wrap {
        :deep(.ap-invoice-block) {
            padding: 24px 16px;
            background-color: #fff;
            border-bottom: 0;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                height: 1px;
                background-color: #e2e2ea;
                bottom: 0;
                left: 16px;
                right: 16px;
            }

            &:nth-last-child(2) {
                &::after {
                    content: none;
                }

                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
            }

            .ant-table-wrapper .ant-table .ant-table-container .ant-table-content {
                table colgroup {

                    col:first-child,
                    col:last-child {
                        width: 25px !important;
                    }

                    col:nth-child(2) {
                        width: 120px !important;
                    }
                }

                .ant-table-cell {
                    padding: 12px 4px;

                    .ant-btn {
                        padding: 6px 0px;
                    }
                }
            }
        }

        :deep(.ap-invoice-amount-block) {
            padding-bottom: 0;
        }

        :deep(.ap-invoice-footer) {
            background-color: #f5f7f9;
            padding: 20px 0;
            margin-top: 0px;
        }

        :deep(.textarea-wrap) {
            margin-bottom: 32px;
        }
    }
}

.custom-divider {
    margin: 0;
    padding: 0;
}

.btn-txt {
    padding-left: 0;
    padding-right: 0;
    font-size: 16px;
}

.spin-page-wrap {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgb(128, 128, 128, 0.2);
}

.confirm-title-wrap {
    padding-top: 15px;
    padding-bottom: 5px;

    .confirm-title-icon {
        color: #faad14 !important;
        font-size: 21px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .confirm-title-text {
        //  font-family: Calibri;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        font-weight: 400;
    }
}

.confirmation-content-text {
    //  font-family: Calibri;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    font-weight: 400;
    padding-left: 64px;
    padding-top: 5px;
    // height: 75px;
}

:deep(.ant-modal-body) {
    height: 50px;
}

:deep(.ant-modal-header) {
    border: none;
}

:deep(.ant-modal-footer) {
    padding-bottom: 20px;
    border: none;
}
</style>
