/** @format */

import axios from 'axios'
import router from '../router'
import {TokenPGO} from '@/lib/storage'
import {message} from 'ant-design-vue'

// create an axios instance
const servicepgo = axios.create({
    baseURL: import.meta.env.VITE_BASIC_API_PGO, // url = base url + request url
    withCredentials: true, // send cookies when cross-domain requests
    timeout: 90000, // request timeout
})

servicepgo.interceptors.request.use(
    config => {
        config.headers = {
            ...config.headers,
            token: TokenPGO.get() || '',
        }

        return config
    },
    error => {
        console.log(error) // for debug
        return Promise.reject(error)
    },
)

servicepgo.interceptors.response.use(
    response => {
        if (response.data.code !== 200) {
            message.error({
                content: `PGO - ${response.data.msg}`,
                duration: 5,
            })
        }
        return response
    },
    error => {
        if (error.code === 'ECONNABORTED' || error.message === 'Network Error' || error.message.includes('timeout')) {
            message.error({
                content: 'Time Out Error.',
                duration: 6,
            })
        } else {
            message.error({
                content: error.response.data.message || 'failed',
                duration: 5,
            })
        }
        return Promise.reject(error)
    },
)

export default servicepgo
