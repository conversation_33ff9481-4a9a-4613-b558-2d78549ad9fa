/** @format */

import axios from 'axios'
import router from '../router'
import {message} from 'ant-design-vue'

// create an axios instance
const servicent = axios.create({
    baseURL: '/payroll', // url = base url + request url
    withCredentials: true, // send cookies when cross-domain requests
    timeout: 90000, // request timeout
})

servicent.interceptors.response.use(
    response => {
        return response
    },
    error => {
        if (error.code === 'ECONNABORTED' || error.message === 'Network Error' || error.message.includes('timeout')) {
            message.error({
                content: 'Time Out Error.',
                duration: 6,
            })
        } else if (error?.response?.status === 401) {
            router.replace({name: 'Login'})
        }
        return Promise.reject(error)
    },
)

export default servicent
