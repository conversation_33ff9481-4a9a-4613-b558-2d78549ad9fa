/** @format */

import {createI18n} from 'vue-i18n'
import {Language} from '@/lib/storage'

export const languageList = [
    {
        id: 'zh',
        name: '简体中文',
        values: ['zh', 'zh-cn', 'cn'],
        dayjsLocale: 'zh-cn',
        momentLocale: 'zh-cn',
    },
    {
        id: 'en',
        name: 'English',
        values: ['en', 'en-us', 'en-GB'],
        dayjsLocale: 'en',
        momentLocale: 'en',
    },
    {
        id: 'zh-hk',
        name: '繁體中文',
        values: ['zh-hk'],
        dayjsLocale: 'zh-hk',
        momentLocale: 'zh-hk',
    },
    {
        id: 'ja',
        name: '日本語',
        values: ['ja'],
        dayjsLocale: 'ja',
        momentLocale: 'ja',
    },
    {
        id: 'fr',
        name: 'Français',
        values: ['fr'],
        dayjsLocale: 'fr',
        momentLocale: 'fr',
    },
    {
        id: 'es',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        values: ['es'],
        dayjsLocale: 'es',
        momentLocale: 'es',
    },
]

const messages: {[key: string]: any} = {}

// 获取语言字典
languageList.forEach(async ({id}) => (messages[id] = await import(`./lang/${id}.json`)))

const i18nInstance = createI18n({
    // 地区
    locale: Language.get() || 'en',
    fallbackLocale: 'en',
    messages,
    localeList: [],
    silentFallbackWarn: true,
    globalInjection: true,
    legacy: false,
})

export default i18nInstance
