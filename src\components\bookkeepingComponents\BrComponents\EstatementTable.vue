<!-- @format -->

<script setup lang="ts">
import {
    CloseOutlined,
    CloudUploadOutlined,
    DownOutlined,
    FilterOutlined,
    MailOutlined,
    QuestionCircleFilled,
    SearchOutlined,
    UpOutlined,
    FileSyncOutlined,
    ExclamationCircleOutlined,
    // UpSquareOutlined,
    // DownSquareOutlined,
    LineOutlined,
} from '@ant-design/icons-vue'
import type { BankInfo } from '@/model/bank'
import type { ColumnsType } from 'ant-design-vue/es/table'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import { computed, onBeforeMount, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'
import RcInfoTable from '@/components/bookkeepingComponents/BrComponents/RcInfoTable.vue'
import ReconciliationDetails from '@/views/Bookkeeping/BankReconciliation/ReconciliationDetails.vue'
import ReconciliationDetailsIntegration8001 from '@/views/Bookkeeping/BankReconciliation/ReconciliationDetailsIntegration8001.vue'
import UploadFileComp from '@/components/bookkeepingComponents/UploadFileComp.vue'
import dayjs from 'dayjs'
import { useStore } from 'vuex'
import { message, Modal, type FormInstance } from 'ant-design-vue'
import { UserCompany, UserInfo } from '@/lib/storage'
import { BrHisCustomizeTable, BrRecCustomizeTable, Ap_Integration, Ar_Integration } from '@/lib/storage'
import CustomColumns from '@/components/bookkeepingComponents/CommonComponents/CustomColumns.vue'
import PyInvoiceComponent from '@/components/bookkeepingComponents/PyComponents/PyInvoiceComponent.vue'
import ArInvoiceComponent from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponent.vue'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import BankChargeTypeForm from '@/components/bookkeepingComponents/BrComponents/BankChargeTypeForm.vue'
import BankChargeTypeFormIntegration from '@/components/bookkeepingComponents/BrComponents/BankChargeTypeFormIntegration.vue'
import BankChargeTypeFormIntegrationMx from '@/components/bookkeepingComponents/BrComponents/BankChargeTypeFormIntegrationMx.vue'
import BankChargeTypeFormIntegrationEeSpecial from '@/components/bookkeepingComponents/BrComponents/BankChargeTypeFormIntegrationEeSpecial.vue'
// import PeriodicalBooking from '@/components/bookkeepingComponents/ArComponents/PeriodicalBooking.vue'

import imgSrcBMO from '@/assets/image/bank/BMO.png'
import imgSrcCIBC from '@/assets/image/bank/CIBC.png'
import imgSrcRBC from '@/assets/image/bank/RBC.png'
import imgSrcDES from '@/assets/image/bank/desjardins.png'
import imgSrcTD from '@/assets/image/bank/TD.png'
import imgSrcJPCB from '@/assets/image/bank/JPCB.png'
import imgSrcFB from '@/assets/image/bank/FB.png'
import imgSrcNBC from '@/assets/image/bank/NBC.svg'
import imgSrcASPIRE from '@/assets/image/bank/ASPIRE.png'
import imgSrcNOVA from '@/assets/image/bank/NOVA.png'
import imgSrcAMEX from '@/assets/image/bank/AMEX.png'
import imgSrcCITI from '@/assets/image/bank/CITI.png'
import imgSrcBOC from '@/assets/image/bank/BOC.png'
import imgSrcCMB from '@/assets/image/bank/CMB.png'
import imgSrcICBC from '@/assets/image/bank/ICBC.jpeg'
import imgSrcBONJ from '@/assets/image/bank/BONJ.jpeg'
import imgSrcSANTANDER from '@/assets/image/bank/SANTANDER.png'
import imgSrcBASEINET from '@/assets/image/bank/BASEINET.jpeg'
import imgSrcMONEX from '@/assets/image/bank/MONEX.png'
import imgSrcBBVA from '@/assets/image/bank/BBVA.png'
import * as _ from 'lodash'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'

const userInfo: any = UserInfo.get() || {}
const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global
const emits = defineEmits(['update', 'updateHistory', 'updateNoLoading'])
const tableLength = computed(() => store.state.BrStore.totalNumber)
const pageTotalNumber = computed(() => store.state.BrStore.pageTotalNumber)
const accountDescList = computed(() => store.state.CommonDropDownStore.accountDescList)
const esReasonList = ref<any>([]) // computed(() => store.state.BrStore.esReasonList)
const apIntegration: any = Ap_Integration.get() ?? 0
const arIntegration: any = Ar_Integration.get() ?? 0
const state = reactive({
    show: false,
    uploadProcessing: false,
    showDetail: false,
    selectedItem: {} as any,
    selectedBank: [] as BankInfo[],
    invoiceDetailList: [] as any[],
    isShowFetchModal: false,
    isShowConnectModal: false,
    isShowDrawer: false,
    expandedRowKeys: [] as string[],
    bankInfo: undefined,
    bankInfoDetail: {} as any,
    currentReconciliation: {} as any,
    pgoSelectedItem: {} as any,
    fetchPGO: false,
    isShowType11Modal: false,
    pagination: { total: tableLength.value, current: 1, pageSize: 10 },
})
const pgoFormState = reactive({
    payout_id: '',
})
const pgoFormRef = ref<FormInstance>()
const tableWrapRef = ref()
const fetchModalRef = ref()
const fetchBankLoading = ref(false)
const isFetchEnabled = ref(false)
const isIntegrationBrOrReverse = ref(false)
const clicker = reactive({
    timer: 0,
    count: 0,
})
const clickTimer = ref(0)
const statementId = ref(0)
const timer = ref()
const interval = ref<number>(0)
const uploadComp = ref<{ uploadFiles: () => void }>()
const restrictFileType: string[] = ['csv']
const pageType = ref('7')
const modalUpload = ref<HTMLElement | null>(null)
//fetchEsInvoiceDetailWithId

const fetchEsInvoiceDetailWithId = (query?: any) => store.dispatch('BrStore/fetchEsInvoiceDetailWithId', query)
const fetchPlaidBankList = (query?: any) => store.dispatch('BrStore/fetchPlaidBank', query)
const fetchCompanyBankList = (query?: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', query)
const fetchAccountDescDropdown = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)
const clearBankList = () => store.commit('BrStore/updatePlaidBankList', [])
const updatePlaidBank = (payload: any) => store.dispatch('BrStore/updatePlaidBank', payload)
const fetchEsReasonList = (payload: any) => store.dispatch('BrStore/fetchEsReasonDropdown', payload)
const syncRemoteBank = (payload: any) => store.dispatch('BrStore/syncRemoteBank', payload)
const switchAutoReconcile = (payload: any) => store.dispatch('BrStore/switchAutoReconcile', payload)
const updateStatementType = (payload: any) => store.dispatch('BrStore/changeEStatementType', payload)
const updateStatementCharge = (payload: any) => store.dispatch('BrStore/uploadEsCharge', payload)
const updateEsReason = (payload: any) => store.dispatch('BrStore/updateEsReason', payload)
const pgoStartFlow = (payload: any) => store.dispatch('BrStore/pgoStartFlow', payload)
const submitReconcile = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationV1', payload)
const submitReconcileIntegration8001 = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationV1Integration8001', payload)
const submitReconcileIntegration = (payload: any) =>
    store.dispatch('ReconciliationStore/submitReconciliationV1Integration', payload)
const fetchReverse = (payload: any) => store.dispatch('BrStore/reverseStatement', payload)
const fetchReverseIntegration = (payload: any) => store.dispatch('BrStore/reverseStatementIntegration', payload)
const submitReconcileFTIntegration8001 = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationFTV1Integration8001', payload)

const showColumns = ref(false)
const customizeTable: any = ref([])
const defaultTable = () => {
    return props.isHistory
        ? apIntegration === 1
            ? [
                'brType',
                'transactionDate',
                'description',
                'reference',
                'payerPayee',
                'debit',
                'credit',
                'balance',
                'chargeFee',
                'reasonMsg',
                'currency',
                'sendSapStatus',
            ]
            : [
                'brType',
                'transactionDate',
                'description',
                'reference',
                'payerPayee',
                'debit',
                'credit',
                'balance',
                'chargeFee',
                'reasonMsg',
                'currency',
            ]
        : [
            'bankAccount',
            'brType',
            'transactionDate',
            'description',
            'reference',
            'payerPayee',
            'debit',
            'credit',
            'balance',
            'chargeFee',
            'reasonMsg',
            'currency',
        ]
}

const saveColumns = (list: any) => {
    customizeTable.value = list
    showColumns.value = false
    if (props.isHistory) {
        BrHisCustomizeTable.set(list)
    } else {
        BrRecCustomizeTable.set(list)
    }
}

const dismissColumns = () => {
    showColumns.value = false
}

const isShowInvoiceDetail = ref(false)
const invoiceDetailType = ref('')
const currentInvoiceDetail = ref({ id: '' })

const isShowBCTypeForm = ref(false)
const showChargeFeeDetail = ref(false)
const showEsReasonDetail = ref(false)
const submitLoading = ref(false)

const accountQuery = { bk_type: 2, company_code: userCompany[0].code, $limit: -1, del_flag: 0 }
const updateChargeFeeForm = ref<any>({
    id: '',
    chargeFee: '',
    chargeCoa: '',
})
const updateEsReasonForm = ref<any>({
    id: '',
    reasonCode: '',
    reasonMsg: '',
})

const bankDataInFetch = computed(() =>
    store.state.BrStore.plaidBankList.map((x: any) => {
        return {
            value: x.id,
            img: bankList.find(i => i.value === x.bank_name)?.img || '',
            account: x.bank_account,
            label: x.bank_name,
            fetch_date: x['plaid_init_fetch_date']
                ? dayjs(x['plaid_init_fetch_date'], 'YYYY-MM-DD')
                : dayjs().add(-60, 'day').format('YYYY-MM-DD'), // defaut value
            disable_fetch_date: Boolean(x['plaid_init_fetch_date']),
        }
    }),
)

const bankList = [
    {
        value: 'Bank of Montreal',
        img: imgSrcBMO,
    },
    {
        value: 'Canadian Imperial Bank of Commerce',
        img: imgSrcCIBC,
    },
    {
        value: 'Royal Bank Canada',
        img: imgSrcRBC,
    },
    {
        value: 'Desjardins Bank',
        img: imgSrcDES,
    },
    {
        value: 'Toronto-Dominion Bank',
        img: imgSrcTD,
    },
    {
        value: 'JPMorgan Chase Bank',
        img: imgSrcJPCB,
    },
    {
        value: 'Fremont Bank',
        img: imgSrcFB,
    },
    {
        value: 'National Bank of Canada',
        img: imgSrcNBC,
    },
    {
        value: 'Aspire Bank',
        img: imgSrcASPIRE,
    },
    {
        value: 'Nova Scotia',
        img: imgSrcNOVA,
    },
    {
        value: 'American Express',
        img: imgSrcAMEX,
    },
    {
        value: 'CITI Bank',
        img: imgSrcCITI,
    },
    {
        value: 'Bank Of China',
        img: imgSrcBOC,
    },
    {
        value: 'China Merchants Bank',
        img: imgSrcCMB,
    },
    {
        value: 'Industrial And Commercial Bank Of China',
        img: imgSrcICBC,
    },
    {
        value: 'Bank Of NanJing',
        img: imgSrcBONJ,
    },
    {
        value: 'Santander Bank',
        img: imgSrcSANTANDER,
    },
    {
        value: 'BASEinet Bank',
        img: imgSrcBASEINET,
    },
    {
        value: 'MONEX Bank',
        img: imgSrcMONEX,
    },
    {
        value: 'BBVA Bank',
        img: imgSrcBBVA,
    },
]

// const tableHolder = reactive({
//     tableSource: () => {
//         let res = [] as any[]
//         if (props.isHistory) {
//             console.log('this table computed sources')
//             res = store.state.BrStore.historyEsList.map((item: any) => {
//                 return {...item.entity, children: [...item.invoice_list]}
//             })
//         } else {
//             res = store.state.BrStore.esList
//         }
//         return res
//     },
// })

const tableSource = computed({
    get() {
        // history has different structure.
        let res = [] as any[]
        if (props.isHistory) {
            console.log('this table computed sources')
            res = store.state.BrStore.historyEsList.map((item: any) => {
                const { description, reference, payer_payee, deposit, withdrawal, balance, charge_fee, reason_msg, currency } =
                    item.entity
                // return {...item.entity, children: [...item.invoice_list]}
                return {
                    description,
                    reference,
                    payer_payee,
                    deposit,
                    withdrawal,
                    balance,
                    charge_fee,
                    reason_msg,
                    currency,
                    ...item,
                    children: [...item.invoice_list],
                }
            })
        } else {
            res = store.state.BrStore.esList
        }
        return res
    },
    set(data) {
        console.log('setter')
        return [...data]
    },
})

// const tableSource2 = computed(() => {
//     // history has different structure.
//     let res = [] as any[]
//     if (props.isHistory) {
//         res = store.state.BrStore.historyEsList.map((item: any) => {
//             return {...item.entity, children: [...item.invoice_list]}
//         })
//     } else {
//         res = store.state.BrStore.esList
//     }
//     return res
// })

const historyList = computed(() => store.state.BrStore.historyEsList)
const companyBankList = computed(() => store.state.BankInfoStore.bankList)
const brTypeList = computed(() => {
    const list = store.getters['ArApBrStore/brTypeList'].filter((x: any) => ![7, 8].includes(x.id))
    return list
})
const debitTypeList = computed(() => store.getters['ArApBrStore/debitTypeList'])
const creditTypeList = computed(() => store.getters['ArApBrStore/creditTypeList'])
// const selectedHistoryItem = computed(() => {})
const updateIntegrationBrSapStatus = (data: any) =>
    store.dispatch('ReconciliationStore/updateIntegrationBrSapStatusV1', data)
const sendIntergrationBrDataToSap = (data: any) => store.dispatch('ReconciliationStore/sendBrDataToSap', data)
const sendIntegrationBrReverseDataToSap = (data: any) =>
    store.dispatch('ReconciliationStore/sendBrReverseDataToSap', data)
const submitReconciliationReverseV1Integration8001 = (data: any) =>
    store.dispatch('ReconciliationStore/submitReconciliationReverseV1Integration8001', data)
const submitReconciliationReverseFTV1Integration = (data: any) =>
    store.dispatch('ReconciliationStore/submitReconciliationReverseFTV1Integration', data)
//删除Action
const deleteESAction = (payload: any) => store.dispatch('BrStore/deleteESAction', payload)

const props = defineProps({
    showViewOnly: {
        type: Boolean,
        default: false,
    },
    hideBankAccount: {
        type: Boolean,
    },
    isHistory: {
        type: Boolean,
        default: false,
    },
    isLoading: {
        type: Boolean,
        default: true,
    },
    showSizeChanger: {
        type: Boolean,
        default: false,
    },
})

const searchForm = reactive({
    searchText: '',
    startDate: '',
    endDate: '',
    minTotalFee: '',
    maxTotalFee: '',
})

// const pagination = computed(() => ({
//     total: 200,
//     current: current.value,
//     pageSize: pageSize.value,
// }))

// const pagination = reactive({
//     total: tableLength.value,
//     current: 1,
//     pageSize: 10,
// })

// history page will not show balance column
const eStatementTableColumns = computed(() =>
    // props.showViewOnly
    //     ? eStatementColumnsTemplate.filter(
    //           (x: any) =>
    //               x.dataIndex !== 'balance' && x.dataIndex !== 'operationNoData' && x.dataIndex !== 'bank_account',
    //       )
    //     : eStatementColumnsTemplate,
    eStatementColumnsTemplate.filter(
        item =>
            customizeTable.value.includes(item.key) ||
            (!props.isHistory && item.key === 'operationNoData') ||
            (apIntegration === 1 && item.key === 'operationNoData'),
    ),
)

const customCellClickEvent = (record: any, rowIndex: any, column: any) => {
    return {
        onClick: (event: any) => {
            if (column?.key === 'operationNoData' || column?.key === 'brType' || column?.key === 'reasonMsg') {
                // ignore these two column click event for expand action
                return
            } else {
                // when user not click on operation columns
                if (props.showViewOnly) {
                    viewDetails(record)
                } else {
                    // showDrawer(record)
                    event.stopPropagation()
                    expandedRow(record)
                }
            }
        },
    }
}

const eStatementColumnsTemplate: ColumnsType =
    apIntegration === 1
        ? [
            {
                title: i18n.t('esMain.bankAccount'),
                dataIndex: 'bank_account',
                key: 'bankAccount',
                align: 'center',
                width: '180px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.brType'),
                dataIndex: 'br_type',
                key: 'brType',
                align: 'center',
                width: '100px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.transactionDate'),
                dataIndex: props.isHistory ? 'posting_date' : 'date',
                key: 'transactionDate',
                align: 'center',
                width: '160px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.description'),
                dataIndex: 'description',
                key: 'description',
                align: 'center',
                width: '40%',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.reference'),
                dataIndex: 'reference',
                key: 'reference',
                align: 'center',
                width: '20%',
                ellipsis: true,
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.payerPayee'),
                dataIndex: 'payer_payee',
                key: 'payerPayee',
                align: 'center',
                width: '30%',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.debit'),
                dataIndex: 'deposit',
                key: 'debit',
                align: 'right',
                width: '140px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.credit'),
                dataIndex: 'withdrawal',
                key: 'credit',
                align: 'right',
                width: '140px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.balance'),
                dataIndex: 'balance',
                key: 'balance',
                align: 'right',
                width: '140px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.chargeFee'),
                dataIndex: 'charge_fee',
                key: 'chargeFee',
                align: 'center',
                width: '125px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.reasonMsg'),
                dataIndex: 'reason_msg',
                key: 'reasonMsg',
                align: 'center',
                width: '300px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.currency'),
                dataIndex: 'currency',
                key: 'currency',
                align: 'center',
                width: '105px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('bkApInvoice.sapStatus'),
                dataIndex: 'send_sap_status',
                key: 'sendSapStatus',
                align: 'center',
                width: '100px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('bkCustomer.operation'),
                dataIndex: 'operationNoData',
                key: 'operationNoData',
                // width: apIntegration === 1 ? '150px' : '150px',
                width: '150px',
                fixed: 'right',
                customCell: customCellClickEvent,
            },
        ]
        : [
            {
                title: i18n.t('esMain.bankAccount'),
                dataIndex: 'bank_account',
                key: 'bankAccount',
                align: 'center',
                width: '180px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.brType'),
                dataIndex: 'br_type',
                key: 'brType',
                align: 'center',
                width: '100px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.transactionDate'),
                dataIndex: props.isHistory ? 'posting_date' : 'date',
                key: 'transactionDate',
                align: 'center',
                width: '160px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.description'),
                dataIndex: 'description',
                key: 'description',
                align: 'center',
                width: '40%',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.reference'),
                dataIndex: 'reference',
                key: 'reference',
                align: 'center',
                width: '20%',
                ellipsis: true,
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.payerPayee'),
                dataIndex: 'payer_payee',
                key: 'payerPayee',
                align: 'center',
                width: '30%',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.debit'),
                dataIndex: 'deposit',
                key: 'debit',
                align: 'right',
                width: '110px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.credit'),
                dataIndex: 'withdrawal',
                key: 'credit',
                align: 'right',
                width: '120px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.balance'),
                dataIndex: 'balance',
                key: 'balance',
                align: 'right',
                width: '110px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.chargeFee'),
                dataIndex: 'charge_fee',
                key: 'chargeFee',
                align: 'center',
                width: '120px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('esMain.currency'),
                dataIndex: 'currency',
                key: 'currency',
                align: 'center',
                width: '105px',
                customCell: customCellClickEvent,
            },
            {
                title: i18n.t('bkCustomer.operation'),
                dataIndex: 'operationNoData',
                key: 'operationNoData',
                width: '150px',
                fixed: 'right',
                customCell: customCellClickEvent,
            },
        ]

const showFetchModal = async () => {
    // state.isShowFetchModal = true
    // fetchBankLoading.value = true

    // fetchPlaidBankList move to page load
    // await fetchPlaidBankList({company_code: userCompany[0].code})
    // fetchBankLoading.value = false

    // if all bank already set date, will not show fetch modal and fetch directly
    if (bankDataInFetch.value.length === 0) {
        message.warn('No Bank Found')
        return
    }
    if (bankDataInFetch.value.every((x: any) => x.disable_fetch_date)) {
        await fetchFromBank()
    } else {
        state.isShowFetchModal = true
    }
}

const typeList = (debit: number, credit: number) => {
    if (Math.abs(debit) > 0) {
        return debitTypeList.value
    } else if (Math.abs(credit) > 0) {
        return creditTypeList.value
    }
}
// const showConnectModal = (bank: any) => {
//     state.selectedBank = bank
//     state.isShowFetchModal = false
//     state.isShowConnectModal = true
// }

const updateFetchDate = async (item: any) => {
    try {
        fetchBankLoading.value = true
        const res = await updatePlaidBank({ plaid_init_fetch_date: item.fetch_date, id: item.value })

        // if (res.data.statusCode === 200) {
        //     message.success('success')
        // }
        // else {
        //     message.error('failed')
        // }
        fetchBankLoading.value = false
    } catch (e: any) {
        console.log(e)
        // message.error(e.response.data.message)
    } finally {
        fetchBankLoading.value = false
    }
    console.log('time updated: ', state.selectedBank)
}

// const selectOnlyCurrent = () => {
//     console.log(bankDataInFetch)
//     bankDataInFetch.forEach(i => (i.isChecked = !i.isChecked))
// }

// const testEvent = $event => {
//     console.log($event.path)
//     const item = $event.path.find((x: any) => x.className.includes('list-item-mark'))
//     console.log(item.id)
// }

const closeFetchModal = () => {
    state.isShowFetchModal = false
}

// const closeConnectModal = (persist: boolean) => {
//     if (persist) {
//         //TODO: get latest date from child component
//     }
//     state.isShowFetchModal = true
//     state.isShowConnectModal = false
// }

// search popover method
const search = () => {
    clearInterval(timer.value)
    emits('update', { page_index: 1, page_size: state.pagination.pageSize, ...searchForm })
}

const updateTable = (init?: number) => {
    const index = init || state.pagination.current
    state.pagination.current = index
    emits('update', { page_index: index, page_size: state.pagination.pageSize, ...searchForm })
}

const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer.value)
    timer.value = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer.value)
        }
    }, 1000)
}

const autoReconcileAll = async () => {
    if (apIntegration === 1) {
        autoReconcileAllItegration()
    } else {
        submitLoading.value = true
        const reqs = tableSource.value
            .filter(x => x.match_flag === 0 && x.match_list.length > 0)
            .map((x: any) => submitReconcileItem(x))
        try {
            const res = await Promise.all([...reqs])
            if (res.every(x => x.data.statusCode === 200)) {
                message.success(i18n.t('ApComponents.success'))
            }
            // else {
            //     message.error('failed')
            // }
        } catch (e: any) {
            console.log(e)
            // message.error(e.response.data.message)
        } finally {
            await updateTable(1)
            submitLoading.value = false
            console.log('finish')
        }
    }
}

const autoReconcileAllItegration = async () => {
    submitLoading.value = true
    isIntegrationBrOrReverse.value = true
    const reqs = tableSource.value.filter(x => x.match_flag === 0 && x.match_list.length > 0)
    try {
        let isSuccessAll = true

        for (let i = 0; i < reqs.length; i++) {
            const req = reqs[i]
            const resSap = await submitReconcileItem(req)
            if (apIntegration === 1 && (req.br_type === 1 || req.br_type === 2) && resSap.data.sap_status !== 2) {
                isSuccessAll = false
                message.error({ content: resSap.data.sap_msg })
                continue
            } else if (apIntegration === 1 && req.br_type === 4 && resSap.data.statusCode !== 200) {
                isSuccessAll = false
                message.error({ content: resSap.data.message })
                continue
            }
        }
        if (isSuccessAll) {
            message.success(i18n.t('ApComponents.success'))
        }
    } catch (e: any) {
        console.log(e)
        // message.error(e.response.data.message)
    } finally {
        await updateTable(1)
        submitLoading.value = false
        isIntegrationBrOrReverse.value = false
        console.log('finish')
    }
}

const isAutoDisable = (record: any): boolean => {
    //return ['4', '6'].includes(record.br_type.toString())
    return !(record.match_list && record.match_list.length)
}

// table operation action
// const check = (item: any) => {
//     void 0
// }

const intervalSyncTable = () => {
    if (interval.value > 0) {
        clearInterval(interval.value)
    }
    interval.value = window.setInterval(() => {
        emits('updateNoLoading', { page_index: state.pagination.current, page_size: state.pagination.pageSize })
        console.log('fetch br from bank background')
    }, 10000)
}

const fetchPGO = async () => {
    if (await pgoFormRef.value?.validateFields()) {
        try {
            state.fetchPGO = true
            await pgoStartFlow({
                ...pgoFormState,
                company_code: state.pgoSelectedItem.company_code,
                statement_id: state.pgoSelectedItem.id,
                br_type: 11,
                posting_date: state.pgoSelectedItem.date, // ？todo moment().format('yyyy-MM-DD')
                currency: state.pgoSelectedItem.currency,
                bank_account: state.pgoSelectedItem.bank_account,
                balance: state.pgoSelectedItem.balance,
                br_entity_type: state.pgoSelectedItem.br_entity_type,
                charge_coa: state.pgoSelectedItem.charge_coa,
                charge_fee: state.pgoSelectedItem.charge_fee,
                creator: userInfo?.id,
                gl_account: getGLAccount(state.pgoSelectedItem.bank_account),
            })
            // await updateStatementType({id: state.pgoSelectedItem.id, br_type: 11})
            await updateTable()
            closeSelectedId()
        } catch (error) {
            console.log(error)
        } finally {
            state.fetchPGO = false
        }
    }
}

const fetchFromBank = async () => {
    // if (state.selectedBank.length === 0) {
    //     message.warn('Please select bank')
    //     return
    // }
    state.selectedBank = [...bankDataInFetch.value]

    // const payload = state.selectedBank
    if (state.selectedBank.length === 0) {
        return
    }
    fetchBankLoading.value = true
    const payload: any = {}
    try {
        payload.company_code = userCompany[0].code
        // {
        //   "plaid_id": 110,
        //   "bank_account": "**********",
        //   "plaid_init_fetch_date": "2022-10-10"
        // }
        payload.bank_list = state.selectedBank.map((x: any) => {
            return {
                plaid_id: x.value,
                bank_account: x.account,
                plaid_init_fetch_date: dayjs(x.fetch_date, 'YYYY-MM-DD').format('YYYY-MM-DD'),
            }
        })
        const res = await syncRemoteBank(payload)
        if ([200, 201].includes(res.data.statusCode)) {
            message.success(i18n.t('ApComponents.success'))
        }
        //TODO: add Interval load
        intervalSyncTable()
        // else {
        //     message.error('failed')
        // }
        fetchBankLoading.value = false
    } catch (e: any) {
        console.log(e)
        // message.error(e.response.data.message)
    } finally {
        state.isShowFetchModal = false
        fetchBankLoading.value = false
    }
}
const selectBank = (item: any) => {
    if (item.isChecked) {
        state.selectedBank.push(item)
    } else {
        state.selectedBank = state.selectedBank.filter((x: any) => x.value !== item.value)
    }
    console.log(state.selectedBank)
}

const clearFetchBank = () => {
    state.selectedBank = []
    // clearBankList()
}

const closeSelectedId = () => {
    state.pgoSelectedItem = {}
    pgoFormState.payout_id = ''
    state.isShowType11Modal = false
}

const viewDetails = async (item: any) => {
    statementId.value = item.br_id
    // state.selectedItem = historyList.value.find((x: any) => x.br_id === item.id)
    state.selectedItem = item
    // let res
    // const query = {
    //     id: item.id,
    //     invoiceType: item.brType === '0' || item.brType === '2' ? '1' : '2',
    // }
    // try {
    //     res = await fetchEsInvoiceDetailWithId(query)
    //     if (res.data.code === 1000) {
    //         state.showDetail = true
    //         state.invoiceDetailList = res.data.data
    //     } else {
    //         message.error(res.data.msg)
    //     }
    // } catch (e) {
    //     console.error(e)
    // }
    // TODO: mock data remove
    state.showDetail = true
    state.invoiceDetailList = state.selectedItem.match_list
}
const showDrawer = (record: any) => {
    if (Number(record.br_type) === 10) {
        state.currentReconciliation = _.cloneDeep(record)
        state.currentReconciliation.gl_account = getGLAccount(state.currentReconciliation.bank_account)
        isShowBCTypeForm.value = true
    } else if (Number(record.br_type) === 11) {
        // console.log('br = ', record)
        // console.log(option.current, 'option.current')

        state.pgoSelectedItem = record //option.current
        state.isShowType11Modal = true
    } else {
        state.selectedItem = _.cloneDeep(record)
        state.selectedItem.gl_account = getGLAccount(state.selectedItem.bank_account)
        console.log('show bottom drawer', state.selectedItem)
        state.isShowDrawer = true
    }
}

const invoiceTransform = (record: any) => {
    return record
}
const showInvoiceDetail = (invoice: any) => {
    currentInvoiceDetail.value = invoiceTransform(invoice)
    invoiceDetailType.value = invoice.br_type.toString()
    isShowInvoiceDetail.value = true
    // console.log(invoice.br_type, invoice.invoice_id)
}

const closeDrawer = () => {
    state.selectedItem = {}
    state.isShowDrawer = false
}

const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const changeItemListRowExpenseAccount = (event: any) => {
    updateChargeFeeForm.value.chargeCoa = event
}
const changeItemListRowReason = (event: any) => {
    updateEsReasonForm.value.reasonCode = event
    const reasonItem = esReasonList.value.find((item: any) => item.reason_code == updateEsReasonForm.value.reasonCode)
    updateEsReasonForm.value.reasonMsg = reasonItem.desc
}
const changeColumnReason = async (id: any, code: any) => {
    const reasonItem = esReasonList.value.find((item: any) => item.reason_code == code)
    console.log('saveEsReasonChange', id, code, reasonItem?.desc)
    const res = await updateEsReason({
        id: id,
        reasonCode: code || null,
        reasonMsg: reasonItem?.desc || null,
    })
    if (res.data.statusCode === 200) {
        updateTable()
    } else {
        message.error({
            content: res.data.message,
        })
    }
}
const showEditChargeFeeModal = (record: any) => {
    console.log('showEditChargeFeeModal', record)
    updateChargeFeeForm.value.id = record.id
    updateChargeFeeForm.value.chargeFee = record.charge_fee
    updateChargeFeeForm.value.chargeCoa = record.charge_coa
    showChargeFeeDetail.value = true
}
const showEditEsReasonModal = (record: any) => {
    console.log('showEditEsReasonModal', record)
    updateEsReasonForm.value.id = record.id
    updateEsReasonForm.value.reasonCode = record.reason_code
    updateEsReasonForm.value.reasonMsg = record.reason_msg
    showEsReasonDetail.value = true
}
const sendBrDataToSap = async (record: any) => {
    if (!record.transaction_id) {
        message.error('Missing transaction id')
        return
    }
    const status = {
        id: record.id,
        status: 1, // ap data sending
        creator: userInfo?.id,
        transaction_id: record.transaction_id,
    }
    await updateIntegrationBrSapStatus(status)
    await sendIntergrationBrDataToSap(record)
    await updateTable()
}
const sendBrReverseToSap = async (record: any) => {
    if (!record.sap_document_no) {
        message.error('Missing sap document no')
        return
    }
    const status = {
        id: record.id,
        status: 4, // ap reverse data sending
        creator: userInfo?.id,
        transaction_id: record.transaction_id,
    }
    await updateIntegrationBrSapStatus(status)
    await sendIntegrationBrReverseDataToSap(record)
    await updateTable()
}
const saveChargeFeeChange = async () => {
    // validate charge fee
    if (
        updateChargeFeeForm.value.chargeFee == null ||
        updateChargeFeeForm.value.chargeFee == '' ||
        updateChargeFeeForm.value.chargeFee == 0
    ) {
        updateChargeFeeForm.value.chargeFee == 0
        updateChargeFeeForm.value.chargeCoa = ''
        // message.error({
        //     content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('esMain.chargeFee')}`,
        //     duration: 6,
        // })
        // return
    }
    // validate charge coa
    if (updateChargeFeeForm.value.chargeFee != 0 && updateChargeFeeForm.value.chargeCoa == '') {
        message.error({
            content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('esMain.chargeCoa')}`,
            duration: 6,
        })
        return
    }
    showChargeFeeDetail.value = false
    console.log('saveChargeFeeChange', updateChargeFeeForm)
    const res = await updateStatementCharge({
        id: updateChargeFeeForm.value.id,
        chargeFee: updateChargeFeeForm.value.chargeFee,
        chargeCoa: updateChargeFeeForm.value.chargeCoa,
    })
    if (res.data.statusCode === 200) {
        updateTable()
    } else {
        message.error({
            content: res.data.message,
        })
    }
    clearUpdateChargeForm()
}
const saveEsReasonChange = async () => {
    // validate reason code
    if (updateEsReasonForm.value.reasonCode == null || updateEsReasonForm.value.reasonCode == '') {
        message.error({
            content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('esMain.reasonMsg')}`,
            duration: 6,
        })
        return
    }
    showEsReasonDetail.value = false
    console.log('saveEsReasonChange', updateEsReasonForm)
    const res = await updateEsReason({
        id: updateEsReasonForm.value.id,
        reasonCode: updateEsReasonForm.value.reasonCode,
        reasonMsg: updateEsReasonForm.value.reasonMsg,
    })
    if (res.data.statusCode === 200) {
        updateTable()
    } else {
        message.error({
            content: res.data.message,
        })
    }
    clearUpdateEsReasonForm()
}
const chargeFeedismiss = () => {
    showChargeFeeDetail.value = false
    clearUpdateChargeForm()
}
const esReasondismiss = () => {
    showEsReasonDetail.value = false
    clearUpdateEsReasonForm()
}
const clearUpdateChargeForm = () => {
    updateChargeFeeForm.value.id = ''
    updateChargeFeeForm.value.chargeFee = ''
    updateChargeFeeForm.value.chargeCoa = ''
}
const clearUpdateEsReasonForm = () => {
    updateEsReasonForm.value.id = ''
    updateEsReasonForm.value.reasonCode = ''
    updateEsReasonForm.value.reasonMsg = ''
}
const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const rules = reactive({
    chargeFee: [...requireRule(i18n.t('esMain.chargeFee'))],
    chargeCoa: [...requireRule(i18n.t('esMain.chargeCoa'))],
    reasonCode: [...requireRule(i18n.t('esMain.reasonMsg'))],
})
const dismiss = () => {
    state.showDetail = false
    updateTable()
    isShowInvoiceDetail.value = false
    currentInvoiceDetail.value = { id: '' }
}
const bankChange = () => {
    console.log(state.bankInfo)
    const bankAccount = (state.bankInfo ?? '').split('|')[0]
    state.bankInfoDetail = companyBankList.value.find((item: any) => item.account_no === bankAccount)
}
const showUploadDiag = (bool = false) => {
    state.show = bool
}
const uploadInvoiceFiles = async () => {
    if (!state.bankInfo) {
        message.error({
            content: i18n.t('EstatementTable.required'), // 'Bank Account field required',
        })
    } else {
        try {
            state.uploadProcessing = true

            const response = (await uploadComp.value?.uploadFiles()) as any
            if (response !== null) {
                if (response.data.statusCode === 200) {
                    setTimeout(async () => {
                        message.success(i18n.t('ApComponents.success'))
                        showUploadDiag(false)
                        state.uploadProcessing = false
                    }, 1000)
                } else {
                    // message.error({content: response.data.message})
                    showUploadDiag(false)
                    state.uploadProcessing = false
                }
            }
            state.uploadProcessing = false
        } catch (error: any) {
            console.log(error)
            // message.error({
            //     content: error.response.data.message,
            // })
            state.uploadProcessing = false
        } finally {
            updateTable()
            // childTable.value?.fetchTableData(state.activeName)
        }
    }
}
const closeUploadModal = () => {
    state.show = false
}
const reverse = async (history: any) => {
    const param = {
        // company_code: userCompany[0].code,
        statement_id: statementId.value,
        creator: userInfo?.id,
        transaction_id: history.transaction_id,
    }
    try {
        state.showDetail = false
        submitLoading.value = true
        isIntegrationBrOrReverse.value = true
        if (apIntegration === 1 || arIntegration === 1) {
            // python API
            const payload = {
                id: history.id,
                status: 4, // ap reverse data sending
                creator: userInfo?.id,
                posting_date: history.posting_date,
                company_code: history.company_code,
                sap_document_no: history.sap_document_no,
                transaction_id: history.transaction_id,
            }
            const res =
                history.br_type === 4 || (history.br_type === 10 && history.invoice_list.length == 0)
                    ? await submitReconciliationReverseFTV1Integration(payload)
                    : await submitReconciliationReverseV1Integration8001(payload)
            if (res.data.statusCode === 200) {
                // await fetchReverseIntegration(param)
                message.success(i18n.t('ApComponents.success'))
                dismiss()
                await updateTable()
            } else {
                message.error(res.data.message)
            }
        } else {
            const res = await fetchReverse(param)
            if (res.data.statusCode === 200) {
                message.success(i18n.t('ApComponents.success'))
                dismiss()
                await updateTable()
            } else {
                // message.error(res.data.message)
            }
        }
        // const res = await fetchReverse(param)
        // if (res.data.statusCode === 200) {
        //     if (apIntegration === 1 && history.br_type === 1) {
        //         sendBrReverseToSap(history)
        //     }
        //     message.success(i18n.t('ApComponents.success'))
        //     dismiss()
        //     await updateTable()
        // } else {
        //     // message.error(res.data.message)
        // }
    } catch (e: any) {
        // message.error(e.response.data.message)
    } finally {
        submitLoading.value = false
        isIntegrationBrOrReverse.value = false
        console.log('finish')
    }
    // fetchReverse(param)
    //     .then(result => {
    //         message.success('success')
    //         updateTable()
    //     })
    //     .catch(err => {
    //         console.error(err)
    //         message.error(err.message)
    //     })
}

const getLastPage = () => {
    let page_index = state.pagination.current
    if (state.pagination.current === pageTotalNumber.value && tableSource.value.length === 1) {
        page_index = state.pagination.current === 1 ? 1 : state.pagination.current - 1
    }

    return page_index
}

const clickerEvent = (record: any, event: any) => {
    event.preventDefault()
    clicker.count++

    if (clicker.count === 1) {
        clicker.timer = window.setTimeout(() => {
            clicker.count = 0
            submitAutoReconcile(record)
        }, 500)
    } else if (clicker.count === 2) {
        clearTimeout(clicker.timer)
        clicker.count = 0
        switchAutoReconcileEvent(record)
    }
}
const submitAutoReconcile = async (record: any) => {
    if (record.match_flag === 1) {
        return
    }
    if (record.children.length === 0) {
        message.warn('No Invoice')
        return
    }
    console.log('submitted, ', record)
    submitLoading.value = true
    if (apIntegration === 1) {
        isIntegrationBrOrReverse.value = true
    }
    try {
        const res = await submitReconcileItem(record)
        if (apIntegration === 1 && (record.br_type === 1 || record.br_type === 2 || record.br_type === 12)) {
            if (res.data.statusCode !== 200) {
                message.error({content: res.data.sap_msg})
                return
            } else {
                message.success(i18n.t('ApComponents.success'))
                await updateTable(getLastPage())
            }
        } else if (apIntegration === 1 && record.br_type === 4) {
            if (res.data.statusCode === 200) {
                message.success(i18n.t('ApComponents.success'))
                await updateTable(getLastPage())
            } else {
                message.error(res.data.message)
                return
            }
        } else {
            if (res.data.statusCode === 200) {
                message.success(i18n.t('ApComponents.success'))
            } else {
                message.error(res.data.message)
            }
        }
    } catch (e: any) {
        console.log(e)
        // message.error(e.response.data.message)
    } finally {
        await updateTable(getLastPage())
        submitLoading.value = false
        isIntegrationBrOrReverse.value = false
        console.log('finish')
    }
}

const switchAutoReconcileEvent = async (record: any) => {
    if (clickTimer.value) {
        clearTimeout(clickTimer.value)
    }

    const res = await switchAutoReconcile({ id: record.id, match_flag: Number(!record.match_flag) })
    if (res.data.statusCode === 200) {
        message.success(i18n.t('ApComponents.success'))
        updateTable()
    } else {
        // message.error('failed')
    }
}

const changeType = async (selected: any, option: any) => {
    // if (Number(selected) === 11) {
    //     console.log('option.current', option)

    //     state.pgoSelectedItem = option.current
    //     state.isShowType11Modal = true
    // } else {

    const types = typeList(option.current.deposit, option.current.withdrawal)
    const report_type = types.find((type: { key: string }) => Number(type.key) === selected)
    if (!report_type) {
        message.error('report_type does not exist')
    }

    const res = await updateStatementType({
        id: option.current.id,
        br_type: Number(selected),
        report_type: report_type.value,
        charge_fee: 0,
        charge_coa: null,
    })
    if (res.data.statusCode === 200) {
        updateTable()
    } else {
        // message.error('failed')
    }
    // }
}

const BCTypeFormClosed = () => {
    state.currentReconciliation = {}
    isShowBCTypeForm.value = false
}

const submitReconcileItem = async (esItem: any) => {
    const {
        company_code,
        id,
        currency,
        bank_account,
        balance,
        date,
        br_type,
        br_entity_type,
        charge_coa,
        charge_fee,
        gl_account,
        reason_code,
        deposit,
        withdrawal,
    } = esItem as any
    const isFXFT = ['6', '4'].includes(br_type.toString())
    const glAccount = getGLAccount(bank_account)
    // const invoiceList = esItem.children.map((x: any) => ({
    //     invoice_id: x.id,
    //     balance: x.balance,
    //     bp_number: x.bp_number,
    //     reconcile_amount: Math.abs(x.balance),
    //     engine_document_id: x.engine_document_id || '',
    //     gl_account: glAccount,
    //     br_type: x.br_type,
    //     br_entity_type: x.br_entity_type,
    // }))
    const invoiceList = esItem.children.map((x: any) => {
        const item_gl_account = isFXFT ? getGLAccount(x.bank_account) : glAccount
        const listItem: any = {
            invoice_id: x.id,
            balance: x.balance,
            bp_number: x.bp_number,
            reconcile_amount: Math.abs(x.balance),
            engine_document_id: x.engine_document_id || '',
            gl_account: item_gl_account,
            br_type: x.br_type,
            br_entity_type: x.br_entity_type,
        }
        if (isFXFT) {
            listItem.bank_account = x.bank_account
        }
        return listItem
    })
    const payload = {
        company_code: company_code || userCompany[0].code,
        statement_id: id,
        br_type,
        posting_date: date,
        currency,
        bank_account,
        balance: br_type === 1 ? balance - charge_fee : balance + charge_fee,
        charge_fee,
        charge_coa,
        br_entity_type,
        creator: userInfo?.id,
        gl_account: glAccount,
        invoice_list: [...invoiceList],
    } as any
    console.log('before submit', payload)
    if (
        (apIntegration === 1 && (br_type === 1 || br_type === 2 || br_type === 12)) ||
        (arIntegration === 1 && (br_type === 0 || br_type === 3))
    ) {
        // apIntegration : 1 - RP; 2 - PR; 12 - PP;
        // arIntegration : 0 - RS; 3 - SR
        const invoiceListIntegration = esItem.children.map((x: any) => {
            const item_gl_account = glAccount
            const listItem: any = {
                invoice_id: x.id,
                item_total_fee: x.total_fee,
                balance: x.balance,
                issuer_id: x.bp_number,
                reconcile_amount: Math.abs(x.balance),
                fidoc: x.sap_document_id || '',
                gl_account: item_gl_account,
                mx_isr: x.mx_isr,
                mx_iva: x.mx_iva,
                br_type: x.br_type,
                br_entity_type: x.br_entity_type,
            }
            return listItem
        })
        const payload = {
            header: {
                company_code: company_code || userCompany[0].code,
                company_id: userCompany[0].id,
                es_id: id,
                posting_date: date,
                value_date: date,
                issuer_id: invoiceListIntegration.length > 0 ? invoiceListIntegration[0].issuer_id : '',
                invoice_currency: currency,
                bank_account,
                deposit,
                withdrawal,
                total_fee: balance,
                gl_account: glAccount,
                reason_code,
                br_type,
                br_entity_type,
                creator: userInfo?.id,
            },
            items: [...invoiceListIntegration],
        } as any
        return submitReconcileIntegration8001(payload)
    } else if ((apIntegration === 1 && payload.br_type === 4) || (arIntegration === 1 && payload.br_type === 4)) {
        // apIntegration : 4 - FT
        // arIntegration : 4 - FT
        const invoiceListFT = esItem.children.map((x: any) => {
            const item_gl_account = isFXFT ? getGLAccount(x.bank_account) : glAccount
            const listItem: any = {
                invoice_id: x.id,
                balance: x.balance,
                bp_number: x.bp_number,
                reconcile_amount: Math.abs(x.balance),
                fidoc: x.engine_document_id || '',
                gl_account: item_gl_account,
                reason_code: x.reason_code,
                br_type: x.br_type,
                issuer_id: '',
                br_entity_type: x.br_entity_type,
            }
            if (isFXFT) {
                listItem.bank_account = x.bank_account
            }
            return listItem
        })
        const payloadFT = {
            header: {
                company_code: company_code || userCompany[0].code,
                company_id: userCompany[0].id,
                es_id: id,
                posting_date: date,
                value_date: date,
                issuer_id: '',
                invoice_currency: currency,
                bank_account,
                deposit,
                withdrawal,
                total_fee: balance,
                gl_account: glAccount,
                reason_code,
                br_type,
                br_entity_type,
                creator: userInfo?.id,
            },
            items: [...invoiceListFT],
        } as any
        return await submitReconcileFTIntegration8001(payloadFT)
    } else {
        return await submitReconcile(payload)
    }
    // if (res.data.statusCode === 200) {
    //     message.success('success')
    // }
    // else {
    //     message.error('failed')
    // }
    //
    // console.log(e)
    // message.error('failed')
    //
    // //todo: add loading.
    // console.log('finally')
}
const getBankType = (bank_acocunt: string) => {
    const match = companyBankList.value.find((x: any) => x.account_no === bank_acocunt)?.bank_type
    return match || ''
}
const getGLAccount = (bank_acocunt: string) => {
    const match = companyBankList.value.find((x: any) => x.account_no === bank_acocunt)?.gl_account
    return match || ''
}

const expandedRow = (record: any) => {
    // console.log('submitted, ', record)
    // console.log(state.expandedRowKeys.indexOf(record.id))

    if (state.expandedRowKeys.indexOf(record.id) > -1) {
        state.expandedRowKeys = state.expandedRowKeys.filter(id => id !== record.id)
    } else {
        state.expandedRowKeys = [...state.expandedRowKeys, record.id]
    }
}

const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)

const tableRowClassName = (record: any, index: number) => {
    // Can be expanded return 'highlight'
    if (+record.expend_flag && state.expandedRowKeys.indexOf(record.id) > -1) {
        return 'highlight expanded'
    } else if (+record.expend_flag) {
        return 'highlight'
    } else if (record?.xml_status === 'FAILURE') {
        return 'custom-row-bg'
    } else {
        return ''
    }
}
// const tableRowClick = (record: any, rowIndex: any, column: any) => {
//     return {
//         onclick: (event: any) => {
//             console.log(column)
//             if (record.expend_flag === undefined) {
//                 return
//             }
//             if (props.showViewOnly) {
//                 viewDetails(record)
//             } else {
//                 showDrawer(record)
//             }
//         },
//     }
// }
const rowExpandable = (record: any) => {
    return record.children
}

const getBrTypeById = (record: any) => {
    // console.log(record)
    // / fx ft will not show children type? ''
    const brTypeString = ['4', '6'].includes(record.parent_br_type)
        ? ''
        : brTypeList.value.find((x: any) => x.id === Number(record.br_type)).value
    return brTypeString
}

const pageChange = (current: number, size: number) => {
    console.log(current, size)
    state.pagination.current = current
    state.pagination.pageSize = size
    emits('update', { page_index: current, page_size: size, ...searchForm })
}

const autoReconcileTitle = (record: any) => {
    return record.match_flag === 0 && !isAutoDisable(record)
        ? i18n.t('esMain.autoReconcile') // 'auto reconcile'
        : record.match_flag === 1 && isAutoDisable(record)
            ? i18n.t('esMain.autoReconcileOff') // 'auto reconcile off'
            : i18n.t('esMain.autoReconcileOn') // 'auto reconcile on'
}

const getPopupContainer = (node: any) => {
    return tableWrapRef.value
}

const getCalendarContainer = (node: any) => {
    return fetchModalRef.value
}

const remove = (record: any) => {
    if (record.id) {
        Modal.confirm({
            title: i18n.t('bkCommonTag.confirmation'),
            content: i18n.t('esMain.delWarn'), //the data
            async onOk() {
                try {
                    const response = await deleteESAction(record.id)
                    if (response.status === 200 || response.status === 201) {
                        message.success(i18n.t('ApComponents.success'))
                        updateTable()
                    }
                } catch (error) {
                    console.log(error)
                }
            },
            okText: i18n.t('commonTag.confirm'),
            cancelText: i18n.t('commonTag.cancel'),
            okType: 'danger',
            onCancel() { },
        })
    } else {
    }
}

onMounted(() => {
    customizeTable.value = props.isHistory
        ? BrHisCustomizeTable.get() || [
            'brType',
            'transactionDate',
            'description',
            'debit',
            'credit',
            'chargeFee',
            'currency',
            'sendSapStatus',
        ]
        : BrRecCustomizeTable.get() || [
            'brType',
            'transactionDate',
            'description',
            'debit',
            'credit',
            'balance',
            'chargeFee',
            'currency',
        ]
})
onBeforeMount(async () => {
    state.pagination = {
        total: tableLength.value,
        current: 1,
        pageSize: 10,
    }
    state.selectedBank = []
    isFetchEnabled.value = false
    await Promise.all([
        fetchCompanyBankList({ company_code: userCompany[0].code, $limit: 9999 }),
        fetchPlaidBankList({ company_code: userCompany[0].code, plaid_status: 1 }),
        fetchAccountDescDropdown({ ...accountQuery }),
    ])
    if (apIntegration === 1) {
        esReasonList.value = await fetchEsReasonList({ company_code: userCompany[0].code })
    }
    isFetchEnabled.value = true
})

const getPopupContainerForTable = (triggerNode: HTMLElement) => {
    return triggerNode?.parentNode?.parentNode?.parentNode
}

onBeforeUnmount(() => {
    if (interval.value > 0) {
        clearInterval(interval.value)
    }
    clearBankList()
})

watch(
    () => tableLength.value,
    (count, prevCount) => {
        if (tableLength.value >= 0) {
            state.pagination.total = tableLength.value
        }
    },
)
</script>
<template>
    <div class="br-main-page-wrap">
        <div class="br-main-page-header">
            <div class="search-group-wrap">
                <a-input v-model:value="searchForm.searchText" :placeholder="`${props.isHistory
                    ? $t('commonTag.search') + $t('esMain.description')
                    : $t('update.searchByBankAccount')
                    }`" :disabled="props.isLoading" class="search-input" @input="inputChange" @pressEnter="search">
                    <template #suffix>
                        <svg-icon name="icon_search"></svg-icon>
                    </template>
                </a-input>
                <a-popover class="popover-wrap" trigger="click" placement="bottom" @visibleChange="visibleChange">
                    <template #content>
                        <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                            <div class="search-input-group">
                                <div class="filter-date-group">
                                    <a-form-item :label="$t('bkArInvoice.date')">
                                        <a-date-picker v-model:value="searchForm.startDate" format="YYYY-MM-DD"
                                            valueFormat="YYYY-MM-DD" :placeholder="$t('gl.createStartDate')"
                                            style="width: 160px" clearable>
                                            <template #suffixIcon>
                                                <svg-icon name="icon_date"></svg-icon>
                                            </template>
                                        </a-date-picker>
                                    </a-form-item>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    <a-form-item>{{ $t('bkArInvoice.to') }}
                                        <a-date-picker v-model:value="searchForm.endDate" format="YYYY-MM-DD"
                                            valueFormat="YYYY-MM-DD" :placeholder="$t('gl.createEndDate')"
                                            style="width: 160px" clearable>
                                            <template #suffixIcon>
                                                <svg-icon name="icon_date"></svg-icon>
                                            </template>
                                        </a-date-picker>
                                    </a-form-item>
                                </div>
                                <div class="filter-amount-group">
                                    <a-form-item :label="$t('bkArInvoice.totalCol')">
                                        <a-input-number v-model:value="searchForm.minTotalFee" :controls="false"
                                            :placeholder="$t('bkArInvoice.minFee')"
                                            style="width: 160px"></a-input-number>
                                    </a-form-item>
                                    <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    <a-form-item>{{ $t('bkArInvoice.to') }}
                                        <a-input-number v-model:value="searchForm.maxTotalFee" :controls="false"
                                            :placeholder="$t('bkArInvoice.maxFee')"
                                            style="width: 160px"></a-input-number>
                                    </a-form-item>
                                </div>
                            </div>
                            <!--                            <div class="search-input-group">-->
                            <!--                                <a-form-item :label="$t('bkArInvoice.totalCol')">-->
                            <!--                                    <a-input-number-->
                            <!--                                        v-model:value="searchForm.minTotalFee"-->
                            <!--                                        :controls="false"-->
                            <!--                                        :disabled="isCustomize"-->
                            <!--                                        :min="0"-->
                            <!--                                        :placeholder="$t('bkArInvoice.minFee')"-->
                            <!--                                        style="width: 160px"-->
                            <!--                                    ></a-input-number>-->
                            <!--                                </a-form-item>-->
                            <!--                                <a-form-item :label="$t('bkArInvoice.to')" label-width="35px">-->
                            <!--                                    <a-input-number-->
                            <!--                                        v-model:value="searchForm.maxTotalFee"-->
                            <!--                                        :controls="false"-->
                            <!--                                        :disabled="isCustomize"-->
                            <!--                                        :min="0"-->
                            <!--                                        :placeholder="$t('bkArInvoice.maxFee')"-->
                            <!--                                        style="width: 160px"-->
                            <!--                                    ></a-input-number>-->
                            <!--                                </a-form-item>-->
                            <!--                            </div>-->

                            <a-button type="primary" shape="round" :disabled="props.isLoading" @click="search">
                                <template #icon>
                                    <search-outlined />
                                </template>
                                {{ i18n.t('commonTag.search') }}
                            </a-button>
                        </a-form>
                    </template>
                    <a-tooltip :title="$t('commonTag.filter')">
                        <a-button class="search-button" :disabled="props.isLoading">
                            <template #icon>
                                <svg-icon name="icon_filter"></svg-icon>
                            </template>
                            <!--                           {{ i18n.t('commonTag.filter') }}-->
                        </a-button>
                    </a-tooltip>
                </a-popover>
                <a-tooltip :title="$t('commonTag.columns')">
                    <a-button class="search-button" :disabled="props.isLoading" @click="showColumns = !showColumns">
                        <template #icon>
                            <svg-icon name="icon_columns"></svg-icon>
                        </template>
                        <!--                       {{ i18n.t('commonTag.columns') }}-->
                    </a-button>
                </a-tooltip>
            </div>
            <div v-if="!props.showViewOnly">
                <a-button class="br-main-action-btn" @click="autoReconcileAll" shape="round" type="primary">{{
                    i18n.t('EstatementTable.reconcileAll')
                }}</a-button>
                <a-button :disabled="!isFetchEnabled" class="br-main-action-btn" @click="showFetchModal" shape="round"
                    type="primary">{{ i18n.t('EstatementTable.fetch') }}</a-button>
                <a-button class="br-main-action-btn" @click="showUploadDiag(true)" shape="round" type="primary">
                    <template #icon> <cloud-upload-outlined /></template>
                    {{ i18n.t('EstatementTable.upload') }}</a-button>
            </div>
        </div>
        <div class="br-main-page-content" ref="tableWrapRef">
            <!-- <div v-if="!showViewOnly" class="accept-all-wrap">
                <div style="cursor: pointer" @click="autoReconcileAll" class="accept-all">
                    <svg-icon name="icon_auto_reconcile"></svg-icon>
                    <span>{{ i18n.t('EstatementTable.reconcileAll') }}</span>
                </div>
            </div> -->
            <!--          :customRow="tableRowClick"-->
            <a-spin :spinning="props.isLoading || submitLoading"
                :tip="apIntegration === 1 && isIntegrationBrOrReverse ? i18n.t('commonTag.sapTip') : ''"
                wrapperClassName="custom-spin">
                <a-table id="eStatement-table" :dataSource="tableSource" :pagination="false"
                    :columns="eStatementTableColumns" :rowClassName="tableRowClassName" rowKey="id"
                    :rowExpandable="rowExpandable" :indentSize="0" :expandedRowKeys="state.expandedRowKeys"
                    :expandRowByClick="false" :showExpandColumn="false" :class="showViewOnly ? 'showViewOnly' : ''"
                    :scroll="props.isHistory
                    ? tableSource.length > 5
                        ? { x: 1350, y: 'calc(100vh - 400px)' }
                        : { x: 'auto' }
                    : tableSource.length > 5
                        ? { x: 1350, y: 'calc(100vh - 400px)' }
                        : { x: 1350 }
                    ">
                    <template #headerCell="{ column }">
                        <template v-if="column?.key === 'operationNoData'">
                            <span>
                                {{ column?.title }}
                                <a-tooltip placement="bottomRight" overlayClassName="tooltip-wrap"
                                    :getPopupContainer="getPopupContainerForTable">
                                    <template #title>
                                        <div class="tooltip-content">
                                            <div>
                                                <svg-icon name="icon_auto_reconcile"></svg-icon><span>{{
                    i18n.t('EstatementTable.autoOn') }} </span>
                                            </div>
                                            <div>
                                                <svg-icon name="icon_auto_reconcile_off"></svg-icon><span>{{
                    i18n.t('EstatementTable.autoOff') }} </span>
                                            </div>
                                            <div>
                                                <svg-icon name="icon_chakan"></svg-icon><span>{{
                    i18n.t('EstatementTable.reject') }} </span>
                                            </div>
                                        </div>
                                    </template>
                                    <question-circle-filled class="icon-question" />
                                </a-tooltip>
                            </span>
                        </template>
                        <template v-if="column?.key === 'brType'">
                            <span style="display: inline-block; width: 70px; text-align: center">
                                {{ column?.title }}
                                <a-tooltip placement="bottomRight" overlayClassName="tooltip-wrap"
                                    :getPopupContainer="getPopupContainerForTable">
                                    <template #title>
                                        <div class="tooltip-content">
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.RP') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.RS') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.PR') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.SR') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.FT') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.FX') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.PY') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.BC') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.SP') }}</span>
                                            </div>
                                            <div>
                                                <span>{{ i18n.t('EstatementTable.PP') }}</span>
                                            </div>
                                        </div>
                                    </template>
                                    <question-circle-filled class="icon-question" />
                                </a-tooltip>
                            </span>
                        </template>
                    </template>
                    <template #bodyCell="{ column, record }">
                        <template v-if="column?.dataIndex === 'bank_account' && record.expend_flag === undefined">
                            <!-- <a-button  type="link" @click="showInvoiceDetail(record)">{{ record.bank_account }}</a-button> -->
                            <!--                        <span @click="showInvoiceDetail(record)"-->
                            <!--                            ><a>{{ record.bank_account }}</a></span-->
                            <!--                        >-->
                            <span>{{ record.bank_account }}</span>
                        </template>
                        <template v-if="column?.dataIndex === 'br_type'">
                            <a-select v-if="record.expend_flag !== undefined && props.isHistory === false"
                                v-bind:value="record.br_type" style="width: 70px" @change="changeType"
                                :getPopupContainer="getPopupContainer">
                                <a-select-option v-for="item in typeList(record.deposit, record.withdrawal)"
                                    :key="item.key" :current="record" :value="item.id">
                                    {{ item.label }}
                                </a-select-option>
                            </a-select>
                            <div v-else>
                                {{ getBrTypeById(record) }}
                                <a-tooltip v-if="record?.xml_status === 'FAILURE'">
                                    <template #title>
                                        {{ record.xml_message }}
                                    </template>
                                    <ExclamationCircleOutlined style="color: red; font-size: 16px; margin-left: 8px" />
                                </a-tooltip>
                            </div>
                        </template>

                        <template v-if="column.key === 'description'">
                            <span>{{ record.description }}</span>
                        </template>
                        <template v-if="column?.dataIndex === 'deposit'">
                            <!-- <span class="ff-lato">{{ (+record.deposit).toFixed(2) }}</span> -->
                            <span class="ff-lato">{{ $formatNumber(Number(+record.deposit)) }}</span>
                        </template>
                        <template v-if="column?.dataIndex === 'withdrawal'">
                            <!-- <span class="ff-lato">{{ (+record.withdrawal).toFixed(2) }}</span> -->
                            <span class="ff-lato">{{ $formatNumber(Number(+record.withdrawal)) }}</span>
                        </template>
                        <template v-if="column?.dataIndex === 'balance'">
                            <!-- <span class="ff-lato">{{ isHistory ? (+record.after_reverse_balance).toFixed(2) : (+record.balance).toFixed(2) }}</span> -->
                            <span class="ff-lato">{{ isHistory ? $formatNumber(Number(+record.after_reverse_balance)) :
                    $formatNumber(Number(+record.balance)) }}</span>
                        </template>
                        <template v-if="column?.dataIndex === 'charge_fee'">
                            <div v-if="record.expend_flag !== undefined">
                                <a-button class="charge-fee-content"
                                    :disabled="props.isLoading || (record.br_type != 0 && record.br_type != 1)"
                                    @click="showEditChargeFeeModal(record)">
                                    <span class="ff-lato"> {{ (+record.charge_fee).toFixed(2) }} </span>
                                </a-button>
                            </div>
                            <div v-else-if="typeof record.charge_fee !== 'undefined'" class="ff-lato">
                                {{ record.charge_fee.toFixed(2) }}
                            </div>
                        </template>
                        <template v-if="column?.dataIndex === 'reason_msg'">
                            <a-select
                                v-if="record.expend_flag !== undefined && getBankType(record.bank_account) !== 'Credit'"
                                v-bind:value="record.reason_code" :dropdownMatchSelectWidth="200"
                                :getPopupContainer="getPopupContainer"
                                allowClear
                                @change="(value: any) => changeColumnReason(record.id, value)" style="width: 100%">
                                <a-select-option v-for="item in esReasonList" :key="item.reason_code"
                                    :value="item.reason_code">{{
                    item.reason_code + ' | ' + item.desc }}</a-select-option>
                            </a-select>
                        </template>
                        <template v-if="column?.dataIndex === 'currency'">
                            <span>{{ record.currency }}</span>
                        </template>
                        <template v-if="column?.dataIndex === 'send_sap_status' && apIntegration === 1">
                            <a-tooltip :title="record.sap_message">
                                <span v-if="record.send_sap_status === 0">
                                    <a-tag class="tag-gray">{{ i18n.t('bkApInvoice.sapNotSent') }}</a-tag>
                                </span>
                                <span v-else-if="record.send_sap_status === 1">
                                    <a-tag class="tag-orange">{{ i18n.t('bkApInvoice.sapSending') }}</a-tag>
                                </span>
                                <span v-else-if="record.send_sap_status === 2">
                                    <a-tag color="success">{{ i18n.t('bkApInvoice.sapSentSuccess') }}</a-tag>
                                </span>
                                <span v-else-if="record.send_sap_status === 3">
                                    <a-tag class="tag-red">{{ i18n.t('bkApInvoice.sapSentFail') }}</a-tag>
                                </span>
                                <span v-else-if="record.send_sap_status === 4">
                                    <a-tag class="tag-orange">{{ i18n.t('bkApInvoice.sapReversing') }}</a-tag>
                                </span>
                                <span v-else-if="record.send_sap_status === 5">
                                    <a-tag color="success">{{ i18n.t('bkApInvoice.sapReverseSuccess') }}</a-tag>
                                </span>
                                <span v-else>
                                    <a-tag class="tag-red">{{ i18n.t('bkApInvoice.sapReverseFail') }}</a-tag>
                                </span>
                            </a-tooltip>
                        </template>

                        <template v-if="column?.dataIndex === 'operationNoData'">
                            <!-- Main页面， 除了对账按钮， cashInOut页面是edit remove按钮 -->
                            <!--  @dblclick="switchAutoReconcileEvent(record)"-->
                            <div class="btn-wrap" v-if="record.expend_flag !== undefined">
                                <span v-if="!showViewOnly">
                                    <a-button class="btn-txt" type="text"
                                        @click="clickerEvent(record, $event), $event.stopPropagation()"
                                        :title="autoReconcileTitle(record)">
                                        <template #icon>
                                            <svg-icon style="color: #004fc1"
                                                v-if="record.match_flag === 0 && !isAutoDisable(record)"
                                                name="icon_auto_reconcile"></svg-icon>
                                            <svg-icon style="color: #c100ab"
                                                v-if="record.match_flag === 0 && isAutoDisable(record)"
                                                name="icon_auto_reconcile_off"></svg-icon>
                                            <svg-icon v-else-if="record.match_flag === 1 || isAutoDisable(record)"
                                                name="icon_auto_reconcile_off"></svg-icon>
                                        </template>
                                    </a-button>
                                    <a-button class="btn-txt" @click="showDrawer(record)" type="link" :title="record.br_type == 10
                    ? i18n.t('EstatementTable.BC')
                    : i18n.t('ReconciliationDetails.manual')
                    " :style="record.br_type == 10 ? { color: 'rgb(7 166 6)' } : {}">
                                        <template #icon>
                                            <svg-icon name="icon_chakan"></svg-icon>
                                        </template>
                                    </a-button>
                                    <a-button danger class="btn-txt" @click="remove(record)" type="link" :title="record.br_type == 10
                    ? i18n.t('EstatementTable.BC')
                    : i18n.t('ReconciliationDetails.manual')
                    " :style="record.br_type == 10 ? { color: 'rgb(7 166 6)' } : {}">
                                        <template #icon>
                                            <svg-icon name="icon_delete" style="color: #ff0000"></svg-icon>
                                        </template>
                                    </a-button>
                                </span>
                                <span v-if="!showViewOnly">
                                    <!-- <span v-if="record.children && record.children.length > 0 && !showViewOnly"> -->
                                    <a-button class="expanded-btn" :title="'expend_btn'"
                                        @click="expandedRow(record), $event.stopPropagation()"
                                        v-if="record.children && record.children.length > 0">
                                        <template #icon>
                                            <up-outlined v-if="state.expandedRowKeys.includes(record.id)" />
                                            <down-outlined v-else />
                                        </template>
                                        <!--   <template #icon v-else>-->
                                        <!--       <div class="placeholder">a</div>-->
                                        <!--   </template>-->
                                    </a-button>
                                </span>
                            </div>

                            <!-- <span v-if="record.children && record.children.length > 0 && !showViewOnly">
                                <a-button
                                    class="btn-txt"
                                    type="link"
                                    :title="'Auto Reconcile'"
                                    @click="submitAutoReconcile(column); $event.stopPropagation();"
                                >
                                    <template #icon>
                                        <mail-outlined />
                                    </template>
                                </a-button>
                            </span> -->

                            <!-- history页面只能view -->
                            <span v-if="showViewOnly &&
                    apIntegration === 1 &&
                    record.send_sap_status === 3 &&
                    (record.reverse_document_no == null || record.reverse_document_no.length === 0)
                    ">
                                <a-button class="btn-txt" title="send to sap" type="link"
                                    @click="sendBrDataToSap(record)">
                                    <template #icon>
                                        <svg-icon name="icon_upload"></svg-icon>
                                    </template>
                                </a-button>
                            </span>
                            <span v-if="showViewOnly && apIntegration === 1 && record.send_sap_status === 6">
                                <a-button class="btn-txt" :title="i18n.t('commonTag.sapTip')" type="link"
                                    @click="sendBrReverseToSap(record)">
                                    <template #icon>
                                        <file-sync-outlined />
                                    </template>
                                </a-button>
                            </span>
                        </template>
                    </template>
                </a-table>
            </a-spin>
            <div class="pagination-wrap">
                <a-pagination v-model:current="state.pagination.current" v-model:page-size="state.pagination.pageSize"
                    :disabled="isLoading" :hideOnSinglePage="false" :showSizeChanger="props.showSizeChanger"
                    :total="state.pagination.total" @change="pageChange" class="paginpage table-pagination"
                    layout="total, sizes, prev, pager, next, jumper" background small></a-pagination>
                <span>{{ i18n.t('bkApInvoice.total') }} {{ state.pagination.total }} {{ state.pagination.total > 1 ?
                    i18n.t('update.items') : i18n.t('update.item') }}</span>
            </div>

            <a-modal :title="i18n.t('columns.modalTitle')" v-model:visible="showColumns" :footer="null" destroyOnClose
                :closeable="true" :width="480" :wrapClassName="'modal-wrap'">
                <custom-columns :defaultTable="defaultTable()" :customizeTable="customizeTable" prefix="esMain"
                    @dismiss="dismissColumns" @save="saveColumns"></custom-columns>
            </a-modal>
        </div>
        <!-- 点击手动对账按钮，从下方弹出的界面      -->
        <a-drawer :title="i18n.t('ReconciliationDetails.manual')" placement="bottom" :closable="false"
            :class="'drawer-wrap'" :visible="state.isShowDrawer" v-if="state.isShowDrawer && apIntegration !== 1"
            :get-container="false" :style="{ position: 'absolute' }" height="65%" @close="closeDrawer"
            :destroy-on-close="true">
            <reconciliation-details @dismiss="closeDrawer" @update="updateTable"
                :e-statement="state.selectedItem"></reconciliation-details>
            <template #extra>
                <a-button type="text" size="small" @click="closeDrawer">
                    <template #icon>
                        <close-outlined />
                    </template>
                </a-button>
            </template>
        </a-drawer>
        <!-- 点击手动对账按钮，从下方弹出的界面 integration only -->
        <a-drawer :title="i18n.t('ReconciliationDetails.manual')" placement="bottom" :closable="false"
            :class="'drawer-wrap'" :visible="state.isShowDrawer" v-if="state.isShowDrawer && (apIntegration === 1 || arIntegration === 1)"
            :get-container="false" :style="{ position: 'absolute' }" height="65%" @close="closeDrawer"
            :destroy-on-close="true">
            <reconciliation-details-integration-8001 @dismiss="closeDrawer" @update="updateTable"
                :e-statement="state.selectedItem"></reconciliation-details-integration-8001>
            <template #extra>
                <a-button type="text" size="small" @click="closeDrawer">
                    <template #icon>
                        <close-outlined />
                    </template>
                </a-button>
            </template>
        </a-drawer>
        <!-- 点击手动对账按钮，从下方弹出的界面 38LR only -->
        <!-- <a-drawer :title="i18n.t('ReconciliationDetails.manual')" placement="bottom" :closable="false"
            :class="'drawer-wrap'" :visible="state.isShowDrawer" v-if="state.isShowDrawer && apIntegration === 1 && (userCompany[0].code === '38LR')" :get-container="false"
            :style="{ position: 'absolute' }" height="65%" @close="closeDrawer" :destroy-on-close="true">
            <reconciliation-details-integration-38lr @dismiss="closeDrawer" @update="updateTable"
                :e-statement="state.selectedItem"></reconciliation-details-integration-38lr>
            <template #extra>
                <a-button type="text" size="small" @click="closeDrawer">
                    <template #icon>
                        <close-outlined />
                    </template>
                </a-button>
            </template>
        </a-drawer> -->
    </div>
    <!--      upload 弹出框-->
    <div class="upload-modal-wrap" ref="modalUpload">
        <a-modal :title="$t('EstatementTable.uploadES')" v-model:visible="state.show" :get-container="modalUpload"
            destroyOnClose :closeable="true" :width="'480px'" :min-width="'480px'"
            :bodyStyle="{ padding: '10px 14px 14px' }" :wrapClassName="'modal-wrap'">
            <div>
                <!--  grid -->
                <a-row :gutter="24">
                    <a-col :span="24" class="label_t">
                        <p>*</p>
                        <text>{{ i18n.t('EstatementTable.account') }}</text>
                    </a-col>
                    <a-col :span="24">
                        <a-select :placeholder="i18n.t('commonTag.msgSelect')" v-model:value="state.bankInfo"
                            show-search dropdownMatchSelectWidth :filter-option="false" @change="bankChange"
                            style="width: 430px; over-flow: hidden">
                            <a-select-option v-for="item in companyBankList" :key="item.id"
                                :value="item.account_no + '|' + item.currency">{{ item.name + ' | ' + item.account_no
                                }}</a-select-option>
                        </a-select>
                    </a-col>
                </a-row>
            </div>
            <div style="margin-top: 10px">
                <upload-file-comp ref="uploadComp" :upload-processing="state.uploadProcessing" :file-limit="8"
                    :pageType="'7'" :bankInfo="state.bankInfo" :bankInfoDetail="state.bankInfoDetail"
                    :fileType="restrictFileType" />
            </div>
            <template #footer>
                <a-button key="cancel" class="btn-cancel" shape="round" @click="closeUploadModal">{{
                    i18n.t('EstatementTable.cancel')
                }}</a-button>
                <a-button key="upload" shape="round" type="primary" :loading="state.uploadProcessing"
                    @click="uploadInvoiceFiles">{{ i18n.t('EstatementTable.upload') }}
                </a-button>
            </template>
        </a-modal>
    </div>
    <!-- history detail  -->
    <a-modal :title="i18n.t('ReconciliationDetails.title')" v-model:visible="state.showDetail" :footer="null" destroyOnClose
        :closeable="true" :width="980" :bodyStyle="{ padding: '0px' }" :wrapClassName="'modal-wrap'">
        <rc-info-table :br-type="state.selectedItem.br_type" :info-table-data="state.selectedItem"
            :invoices-list="state.selectedItem.invoice_list" @dismiss="dismiss" @reverse="reverse">
        </rc-info-table>
    </a-modal>
    <!--  fetch 按钮弹出框-->
    <a-modal :title="$t('EstatementTable.fetch')" v-model:visible="state.isShowFetchModal" destroyOnClose
        :closable="true" :width="'550px'" :wrapClassName="'modal-wrap'" :afterClose="closeFetchModal">
        <template #footer>
            <a-button class="modal-close-btn" @click="closeFetchModal" shape="round">{{
                    i18n.t('EstatementTable.cancel')
                }}</a-button>
            <a-button class="modal-ok-btn" type="primary" shape="round" @click="fetchFromBank">{{
                    i18n.t('EstatementTable.OK')
                }}</a-button>
        </template>
        <div class="fetch-modal-wrap" ref="fetchModalRef">
            <a-list :loading="fetchBankLoading" item-layout="horizontal" :split="false" :data-source="bankDataInFetch">
                <template #renderItem="{ item }">
                    <a-list-item v-if="!item.disable_fetch_date" class="list-item-mark">
                        <!--                        <template #actions>-->
                        <!--                            <div-->
                        <!--                                v-bind:id="item.value"-->
                        <!--                                @click="showConnectModal(item)"-->
                        <!--                                style="cursor: pointer; padding-right: 20px"-->
                        <!--                            >-->
                        <!--                                <FileSearchOutlined style="font-size: 16px; color: #0e91ff" />-->
                        <!--                            </div>-->
                        <!--                        </template>-->
                        <template #extra>
                            <div class="fetch-bank-date-wrap">
                                <a-date-picker :disabled="item.disable_fetch_date" @change="updateFetchDate(item)"
                                    v-model:value="item.fetch_date" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                    :getCalendarContainer="getCalendarContainer">
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </div>
                        </template>
                        <!--                        <a-checkbox-->
                        <!--                            style="padding-right: 10px"-->
                        <!--                            v-model:checked="item.isChecked"-->
                        <!--                            @change="selectBank(item)"-->
                        <!--                        ></a-checkbox>-->
                        <a-list-item-meta style="align-items: center" :description="item.account">
                            <template #title>
                                <div>{{ item.label }}</div>
                            </template>

                            <template #avatar>
                                <img v-if="item.img" style="height: 30px" :src="item.img" />
                            </template>
                        </a-list-item-meta>
                    </a-list-item>
                </template>
            </a-list>
        </div>
    </a-modal>

    <!--  SP手动对账弹出框-->
    <a-modal :title="i18n.t('EstatementTable.payoutIdMsgInput')" v-model:visible="state.isShowType11Modal"
        destroyOnClose :closable="true" :width="'550px'" :wrapClassName="'modal-wrap'" :bodyStyle="{ padding: '24px' }"
        :afterClose="closeSelectedId">
        <template #footer>
            <a-button class="modal-close-btn" @click="closeSelectedId" :disabled="state.fetchPGO" shape="round">{{
                    i18n.t('EstatementTable.cancel')
                }}</a-button>
            <a-button class="modal-ok-btn" type="primary" shape="round" @click="fetchPGO" :loading="state.fetchPGO">{{
                    i18n.t('EstatementTable.confirm')
                }}</a-button>
        </template>
        <div>
            <a-form :model="pgoFormState" autocomplete="off" ref="pgoFormRef">
                <a-form-item name="payout_id"
                    :rules="[{ required: true, message: i18n.t('EstatementTable.payoutIdMsgInput') }]">
                    <a-input v-model:value="pgoFormState.payout_id"
                        :placeholder="i18n.t('EstatementTable.payoutIdMsgInput')" />
                </a-form-item>
            </a-form>
        </div>
    </a-modal>
    <a-modal title="Connect to bank" v-model:visible="state.isShowConnectModal" destroyOnClose :closable="true"
        :width="'48vw'" :bodyStyle="{ paddingLeft: '20px' }" :wrapClassName="'modal-wrap'" style="top: 17vh"
        :footer="null">
        <!--        <template #footer>-->
        <!--            <a-button class="modal-ok-btn" type="primary" shape="round" @click="closeConnectModal(true)">OK</a-button>-->
        <!--        </template>-->
        <!--        <div class="connect-modal-wrap">-->
        <!--            <bank-info-form-->
        <!--                :current-bank="state.selectedBank"-->
        <!--                :edit-mode="false"-->
        <!--                :is-fetch="true"-->
        <!--                :no-plaid-cursor="false"-->
        <!--                @update-fetch="updateFetchDate"-->
        <!--            >-->
        <!--            </bank-info-form>-->
        <!--        </div>-->
    </a-modal>
    <!-- BC手动对账弹出框-->
    <a-modal v-model:visible="isShowBCTypeForm" :footer="null" :destroyOnClose="true" :closeable="true" :width="640"
        :dialogStyle="{ top: '10px' }" :bodyStyle="{ padding: '24px 24px 24px' }" :closable="false"
        :wrapClassName="'modal-wrap'" @cancel="BCTypeFormClosed">
        <bank-charge-type-form :reconciliation="state.currentReconciliation" @dismiss="BCTypeFormClosed"
            @update="updateTable" v-if="apIntegration !== 1">
        </bank-charge-type-form>
        <bank-charge-type-form-integration :reconciliation="state.currentReconciliation" @dismiss="BCTypeFormClosed"
            @update="updateTable" v-if="apIntegration === 1 && state.currentReconciliation.withdrawal !== 0 && !INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)">
        </bank-charge-type-form-integration>
        <bank-charge-type-form-integration-mx :reconciliation="state.currentReconciliation" @dismiss="BCTypeFormClosed"
            @update="updateTable" v-if="apIntegration === 1 && state.currentReconciliation.withdrawal !== 0  && INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)">
        </bank-charge-type-form-integration-mx>
        <bank-charge-type-form-integration-ee-special :reconciliation="state.currentReconciliation"
            @dismiss="BCTypeFormClosed" @update="updateTable"
            v-if="apIntegration === 1 && state.currentReconciliation.deposit !== 0">
        </bank-charge-type-form-integration-ee-special>
    </a-modal>
    <a-modal v-model:visible="isShowInvoiceDetail" :footer="null" destroyOnClose :closeable="true" :width="1000"
        :dialogStyle="{ top: '10px' }" :bodyStyle="{ padding: '10px 24px 24px' }">
        <ar-invoice-component v-if="invoiceDetailType === '0'" :invoice-id="currentInvoiceDetail.id"
            :current-invoice="currentInvoiceDetail" :readonly-mode="true" :operation-mode="''" @dismiss="dismiss">
        </ar-invoice-component>
        <ap-invoice-component v-else-if="invoiceDetailType === '1'" :invoice-id="currentInvoiceDetail.id"
            :current-invoice="currentInvoiceDetail" :readonly-mode="true" @dismiss="dismiss"></ap-invoice-component>

        <py-invoice-component v-else-if="invoiceDetailType === '5'" :invoice-id="currentInvoiceDetail.id"
            :current-invoice="currentInvoiceDetail" :readonly-mode="true" :operation-mode="''" @dismiss="dismiss">
        </py-invoice-component>
    </a-modal>
    <a-modal v-model:visible="showChargeFeeDetail" class="update-charge-fee-modal" centered destroyOnClose
        :min-width="480" :closable="false" :wrapClassName="'modal-wrap'" :ok-text="i18n.t('commonTag.confirm')"
        :ok-type="'primary'" :ok-button-props="{ shape: 'round' }" :cancel-text="i18n.t('commonTag.cancel')"
        :cancel-button-props="{ shape: 'round', ghost: true, type: 'primary' }" @ok="saveChargeFeeChange"
        @cancel="chargeFeedismiss">
        <template #title>
            <a-form class="confirm-title-wrap" :model="updateChargeFeeForm" :rules="rules" ref="formRef">
                <div class="confirm-title-text">
                    <ExclamationCircleFilled class="confirm-title-icon" />
                    {{ i18n.t('EstatementTable.updateChargeFee') }}
                </div>
                <div class="modal-body">
                    <a-row style="margin-bottom: 10px">
                        <a-col :span="24"><label><span style="color: red">*</span> {{ $t('esMain.chargeFee')
                                }}</label></a-col>
                    </a-row>
                    <a-row>
                        <a-col :span="24">
                            <a-form-item name="chargeFee" required>
                                <a-input-number class="input-area" v-model:value="updateChargeFeeForm.chargeFee"
                                    :placeholder="i18n.t('commonTag.msgInput')" :precision="2" style="width: 100%" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row style="margin-bottom: 10px">
                        <a-col :span="24"><label><span style="color: red">*</span> {{ $t('bkAr.accountingCategory')
                                }}</label></a-col>
                    </a-row>
                    <a-row style="margin-bottom: 10px">
                        <a-col :span="24">
                            <a-form-item name="chargeCoa" required>
                                <a-select :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="updateChargeFeeForm.chargeCoa" show-search
                                    :dropdownMatchSelectWidth="400" :filter-option="filterOption"
                                    :getPopupContainer="getPopupContainer" class="table-input"
                                    @change="changeItemListRowExpenseAccount" style="width: 100%">
                                    <a-select-option v-for="item in accountDescList"
                                        :key="item.account_code + ' | ' + item.name" :value="item.account_code">{{
                    item.account_code.substring(0, 4) + ' | ' + item.name }}</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
            </a-form>
        </template>
    </a-modal>
    <a-modal v-model:visible="showEsReasonDetail" class="update-charge-fee-modal" centered destroyOnClose
        :min-width="480" :closable="false" :wrapClassName="'modal-wrap'" :ok-text="i18n.t('commonTag.confirm')"
        :ok-type="'primary'" :ok-button-props="{ shape: 'round' }" :cancel-text="i18n.t('commonTag.cancel')"
        :cancel-button-props="{ shape: 'round', ghost: true, type: 'primary' }" @ok="saveEsReasonChange"
        @cancel="esReasondismiss">
        <template #title>
            <a-form class="confirm-title-wrap" :model="updateEsReasonForm" :rules="rules" ref="formRef">
                <div class="confirm-title-text">
                    <ExclamationCircleFilled class="confirm-title-icon" />
                    {{ i18n.t('esMain.reasonMsg') }}
                </div>
                <div class="modal-body">
                    <!-- <a-row style="margin-bottom: 10px">
                        <a-col :span="24"><label><span style="color: red">*</span> {{ $t('bkAr.accountingCategory')
                                }}</label></a-col>
                    </a-row> -->
                    <a-row style="margin-bottom: 10px">
                        <a-col :span="24">
                            <a-form-item name="reasonCode" required>
                                <a-select :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="updateEsReasonForm.reasonCode" show-search
                                    :dropdownMatchSelectWidth="400" :filter-option="filterOption"
                                    :getPopupContainer="getPopupContainer" class="table-input"
                                    @change="changeItemListRowReason" style="width: 100%">
                                    <a-select-option v-for="item in esReasonList" :key="item.reason_code"
                                        :value="item.reason_code">{{
                                        item.reason_code + ' | ' + item.desc }}</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
            </a-form>
        </template>
    </a-modal>
</template>
<style lang="scss" scoped>
:deep(.tooltip-wrap) {
    max-width: 385px;

    .tooltip-content {
        font-size: 12px;
        line-height: 20px;
        font-weight: 400;
        padding: 6px 4px;

        div {
            display: flex;
            align-items: flex-start;
            margin-bottom: 4px;

            span {
                margin-right: 4px;
            }

            span+span {
                margin-right: 0px;
                width: 334px;
            }
        }
    }
}

.icon-question {
    color: #004fc1;
}

.btn-txt {
    width: 20px;

    &+.btn-txt {
        margin-left: 8px;
    }
}

.expanded-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background-color: #fff;
    margin-left: 28px;

    .anticon {
        font-size: 10px;
    }
}

.btn-wrap {
    display: flex;
    align-items: center;
    width: 128px;
}

.br-main-page-wrap {
    border-radius: 12px;
    background-color: #fff;
    overflow: hidden;
    position: relative;
    height: calc(100vh - 170px);

    .br-main-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 24px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 60px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button+.search-button {
                min-width: 60px;
            }

            .popover-wrap:deep(.ant-popover-placement-bottom) {
                width: 432px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
        }
    }

    .charge-fee-content {
        min-width: 90px;
        margin-left: 8px;
        font-size: 14px;
    }

    .reason-content {
        min-width: 240px;
        margin-left: 8px;
        font-size: 14px;
    }

    .br-main-page-content {
        padding: 12px 20px;
        height: calc(100vh - 82px - 85px - 16px);
        overflow: auto;
        //:deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
        //    padding-top: 15px;
        //    padding-bottom: 15px;
        //    padding-left: 8px;
        //    padding-right: 8px;
        //}

        :deep(.ant-table-wrapper .ant-pagination) {
            margin: 0;
            margin-top: 12px;
        }

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }

        .accept-all-wrap {
            display: flex;
            justify-content: flex-end;
            padding: 10px 8px 16px;

            .accept-all {
                color: #004fc1;
                font-size: 16px;
                display: flex;
                align-items: center;

                .anticon {
                    margin-right: 5px;
                }
            }
        }

        .ant-table-wrapper.showViewOnly {
            :deep(.ant-table .ant-table-tbody .ant-table-cell) {
                padding-top: 15px;
                padding-bottom: 15px;
                padding-left: 8px;
                padding-right: 8px;
            }
        }

        :deep(.ant-table-tbody) {
            .ant-table-row {
                &.highlight .ant-table-cell {
                    background-color: #e5f8e6;
                    border-color: #a0e4a4;
                }

                &.highlight.expanded .ant-table-cell {
                    border-top: 1px solid #004fc1;

                    &:first-of-type {
                        // border-top-left-radius: 8px;
                        border-left: 1px solid #004fc1;
                    }

                    &:last-of-type {
                        // border-top-right-radius: 8px;
                        border-right: 1px solid #004fc1;
                    }
                }
            }

            .ant-table-row-level-0+.ant-table-row-level-1 {
                .ant-table-cell {
                    &:first-of-type {
                        border-left: 1px solid #004fc1;
                    }

                    &:last-of-type {
                        border-right: 1px solid #004fc1;
                    }
                }
            }

            .ant-table-row-level-1+.ant-table-row-level-1 {
                .ant-table-cell {
                    &:first-of-type {
                        border-left: 1px solid #004fc1;
                    }

                    &:last-of-type {
                        border-right: 1px solid #004fc1;
                    }
                }
            }

            .ant-table-row-level-1+.ant-table-row-level-0 {
                .ant-table-cell {
                    border-top: 1px solid #004fc1;
                }
            }
        }
    }
}

.search-input-form {
    display: flex;
    flex-direction: column;
    align-items: end;

    .search-input-group {
        display: flex;
        flex-direction: column;

        .filter-date-group {
            display: flex;
        }

        .filter-amount-group {
            display: flex;
        }

        .ant-form-item {
            margin-bottom: 12px;
        }

        :deep(.ant-form-item-label) {
            width: 45px;
        }

        .ant-form-item+.ant-form-item :deep(.ant-form-item-label) {
            width: 35px;
        }
    }
}

.br-icon {
    width: 20px;
}

.br-main-action-btn {
    margin-left: 15px;
}

.fetch-modal-wrap {
    :deep(.ant-spin-nested-loading) {
        min-height: 128px;

        .ant-empty.ant-empty-normal {
            margin: 12px 0;
        }
    }

    :deep(.ant-list-item-meta-avatar) {
        max-width: 124px;
        min-width: 124px;
        text-align: center;
    }
}

.connect-modal-wrap {
    //height: 20vh;
    //overflow-y: auto;
}

.modal-close-btn {
    min-width: 65px;
    text-align: center;
    padding: 0 8px;
    color: rgba(0, 79, 193, 1);
    border: 1px solid rgba(0, 79, 193, 1);
}

.modal-ok-btn {
    min-width: 65px;
    text-align: center;
    padding: 0 8px;
}

.list-item-mark {
    padding: 12px 20px;
}

。placeholder {
    width: 2px;
    height: 2px;
}

.fetch-bank-date-wrap {
    padding-right: 0px;
}

.label_t {
    display: flex;
    align-items: center;
}

.label_t p {
    color: #ff0000;
    display: contents;
}

.label_t text {
    margin-left: 5px;
}

.confirm-title-wrap {
    .confirm-title-text {
        margin-bottom: 18px;
        font-size: 20px;
    }

    .input-group,
    .select-group {
        height: 48px;
        display: flex;
        font-weight: normal;

        .ant-select,
        .input-area {
            width: 300px;
        }
    }
}

:deep(.ant-col.ant-form-item-label) {
    width: 120px;
}

label {
    font-weight: normal !important;
}

:deep(.custom-spin > div>.ant-spin .ant-spin-text) {
    top: calc(50% + 36px);
}

:deep(.custom-row-bg) {
    background-color: #fff4dd; // XML验证失败背景颜色
}
</style>
