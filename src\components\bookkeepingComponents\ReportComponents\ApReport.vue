<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount, watch} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {numberToCurrency} from '@/lib/utils'
import {ExportOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'

import * as _ from 'lodash'
import moment from 'moment'
import type {Dayjs} from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'

const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global

const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

const today = ref(moment().format('YYYY-MM-DD'))
const tableLoading = ref(false)
const dataSource = computed(() =>
    store.state.ReportStore.arApReportsList.map((i: any) => {
        return {
            ...i,
            detail: i.detail
                .slice()
                .sort((a: any, b: any) => {
                    const docNoComparison = a.engine_doc_no.localeCompare(b.engine_doc_no)
                    if (docNoComparison !== 0) {
                        return docNoComparison
                    } else {
                        const balanceComparison = Math.abs(a.balance - b.balance)
                        if (balanceComparison !== 0) {
                            return balanceComparison
                        } else {
                            const dateA = a.posting_date.slice(0, 10) as string
                            const dateB = b.posting_date.slice(0, 10) as string
                            return new Date(dateA).getTime() - new Date(dateB).getTime()
                        }
                    }
                })
                .map((j: any) => {
                    return {
                        ...j,
                        // bp_name: i.bp_name,
                        total_amount: numberToCurrency(j.total_amount),
                        reconcile_amount: numberToCurrency(j.reconcile_amount),
                        balance: numberToCurrency(j.balance),
                        to_0: numberToCurrency(j.to_0),
                        to_31: numberToCurrency(j.to_31),
                        to_61: numberToCurrency(j.to_61),
                        to_91: numberToCurrency(j.to_91),
                    }
                }),
            summary: i.summary.map((i: any) => {
                return {
                    ...i,
                    total_amount: numberToCurrency(i.total_amount),
                    reconcile_amount: numberToCurrency(i.reconcile_amount),
                    balance: numberToCurrency(i.balance),
                    balance_to_0: numberToCurrency(i.balance_to_0),
                    balance_to_31: numberToCurrency(i.balance_to_31),
                    balance_to_61: numberToCurrency(i.balance_to_61),
                    balance_to_91: numberToCurrency(i.balance_to_91),
                }
            }),
        }
    }),
)
const total = computed(() =>
    store.state.ReportStore.arApReportsSummary.map((i: any) => {
        return {
            ...i,
            total_amount: numberToCurrency(i.total_amount),
            reconcile_amount: numberToCurrency(i.reconcile_amount),
            balance: numberToCurrency(i.balance),
            balance_to_0: numberToCurrency(i.balance_to_0),
            balance_to_31: numberToCurrency(i.balance_to_31),
            balance_to_61: numberToCurrency(i.balance_to_61),
            balance_to_91: numberToCurrency(i.balance_to_91),
        }
    }),
)

const searchForm = reactive<{[key: string]: string | undefined}>({
    end_date: today.value,
    report_type: 'AP',
    company_code: undefined,
    company_name: undefined,
})

const fetchReportsList = (payload: any) => store.dispatch('ReportStore/getArApReportsList', payload)
const updateArApReportsList = (list: any) => store.commit('ReportStore/updateArApReportsList', list)
const updateArApReportsSummary = (list: any) => store.commit('ReportStore/updateArApReportsSummary', list)

const disabledDate = (current: Dayjs) => {
    return current && current > moment().endOf('day')
}

const tableScrollY = computed(() => {
    return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight - headerHeight * total.value.length
})

const columns: ColumnsType = [
    {
        title: i18n.t('reports.businessPartner'),
        dataIndex: 'bp_name',
        key: 'bp_name',
        align: 'left',
        width: 140,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.documentNo'),
        dataIndex: 'engine_doc_no',
        key: 'engine_doc_no',
        align: 'left',
        width: 100,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.invoiceNo'),
        dataIndex: 'invoice_no',
        key: 'invoice_no',
        align: 'left',
        width: 100,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.invoiceReference'),
        dataIndex: 'invoice_reference',
        key: 'invoice_reference',
        align: 'left',
        width: 120,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.postingDate'),
        dataIndex: 'posting_date',
        key: 'posting_date',
        align: 'center',
        width: 120,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.currency'),
        dataIndex: 'invoice_currency',
        key: 'invoice_currency',
        align: 'center',
        width: 80,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.invoiceAmount'),
        dataIndex: 'total_amount',
        key: 'total_amount',
        align: 'right',
        width: 140,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.payment'),
        dataIndex: 'reconcile_amount',
        key: 'reconcile_amount',
        align: 'right',
        width: 130,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.totalDue'),
        dataIndex: 'balance',
        key: 'balance',
        align: 'right',
        width: 130,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.current'),
        dataIndex: 'to_0',
        key: 'to_0',
        align: 'right',
        width: 130,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.31To60'),
        dataIndex: 'to_31',
        key: 'to_31',
        align: 'right',
        width: 130,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.61To90'),
        dataIndex: 'to_61',
        key: 'to_61',
        align: 'right',
        width: 130,
        ellipsis: true,
    },
    {
        title: i18n.t('reports.91+'),
        dataIndex: 'to_91',
        key: 'to_91',
        align: 'right',
        width: 130,
        ellipsis: true,
    },
]
const exportReport = async () => {
    try {
        tableLoading.value = true
        await Promise.all([
            store.dispatch('ReportStore/exportArAp', {
                company_code: userCompany[0].code,
                company_name: userCompany[0].name,
                report_type: 'AP',
                end: searchForm.end_date,
            }),
            store.dispatch('ReportStore/exportArApPdf', {
                company_code: userCompany[0].code,
                company_name: userCompany[0].name,
                report_type: 'AP',
                end: searchForm.end_date,
            }),
        ])
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}

const selectChange = async () => {
    await updateList()
}

const updateList = async () => {
    try {
        tableLoading.value = true

        await fetchReportsList(searchForm)
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

onBeforeMount(async () => {
    updateArApReportsList([])
    updateArApReportsSummary([])
    searchForm.company_name = userCompany[0].name
    searchForm.company_code = userCompany[0].code

    await updateList()
})
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="header-title-wrap">
                <div class="header-title">{{ i18n.t('reports.apReport') }}</div>
                <div class="header-subtitle">{{ i18n.t('reports.updatedOn') }}: {{ today }}</div>
            </div>
            <a-divider type="vertical" style="margin-left: 60px" />
            <div class="selects-wrap">
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.end_date"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :placeholder="i18n.t('reports.experationDate')"
                    :disabled="tableLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
            </div>

            <a-button type="primary" shape="round" :disabled="tableLoading" @click="exportReport()">
                <export-outlined />
                {{ i18n.t('workTimeManager.export') }}
            </a-button>
        </div>
        <div class="page-content" ref="tableElWrapRef">
            <a-table
                :dataSource="dataSource"
                :columns="columns"
                :pagination="false"
                :loading="tableLoading"
                :scroll="{y: tableScrollY}"
                :childrenColumnName="'detail'"
                :defaultExpandAllRows="true"
                rowKey="bp_no"
            >
                <template #bodyCell="{column, record}">
                    <template v-if="column.key === 'posting_date'">
                        <template v-if="record?.summary?.length"></template>
                        <template v-else>
                            {{ record.posting_date.slice(0, 10) }}
                        </template>
                    </template>
                    <template v-if="column.key === 'invoice_currency'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.invoice_currency }}</div>
                        </template>
                        <template v-else>
                            {{ record.invoice_currency }}
                        </template>
                    </template>
                    <template v-if="column.key === 'total_amount'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.total_amount }}</div>
                        </template>
                        <template v-else>
                            {{ record.total_amount }}
                        </template>
                    </template>
                    <template v-if="column.key === 'reconcile_amount'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.reconcile_amount }}</div>
                        </template>
                        <template v-else>
                            {{ record.reconcile_amount }}
                        </template>
                    </template>
                    <template v-if="column.key === 'balance'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.balance }}</div>
                        </template>
                        <template v-else>
                            {{ record.balance }}
                        </template>
                    </template>
                    <template v-if="column.key === 'to_0'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.balance_to_0 }}</div>
                        </template>
                        <template v-else>
                            {{ record.to_0 }}
                        </template>
                    </template>
                    <template v-if="column.key === 'to_31'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.balance_to_31 }}</div>
                        </template>
                        <template v-else>
                            {{ record.to_31 }}
                        </template>
                    </template>
                    <template v-if="column.key === 'to_61'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.balance_to_61 }}</div>
                        </template>
                        <template v-else>
                            {{ record.to_61 }}
                        </template>
                    </template>
                    <template v-if="column.key === 'to_91'">
                        <template v-if="record?.summary?.length" v-for="(item, index) in record?.summary">
                            <div>{{ item.balance_to_91 }}</div>
                        </template>
                        <template v-else>
                            {{ record.to_91 }}
                        </template>
                    </template>
                </template>
                <template #summary>
                    <a-table-summary fixed>
                        <a-table-summary-row class="table-summary-wrap" v-for="(item, index) in total" :key="index">
                            <a-table-summary-cell class="table-summary-total">
                                <a-typography-text v-if="index === 0">{{
                                    i18n.t('reports.subTotal')
                                }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text center">
                                {{ item.invoice_currency }}
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.total_amount }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.reconcile_amount }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.balance }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.balance_to_0 }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.balance_to_31 }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.balance_to_61 }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ item.balance_to_91 }}</a-typography-text>
                            </a-table-summary-cell>
                        </a-table-summary-row>
                    </a-table-summary>
                </template>
            </a-table>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.page-wrap {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: #fff;

    .page-header {
        min-height: 84px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        margin-bottom: 14px;

        .header-title-wrap {
            min-width: 148px;
            max-width: 170px;

            .header-title {
                white-space: nowrap;
                line-height: 26px;
                height: 26px;
                color: #004fc1;
                font-size: 20px;
                font-weight: 700;
                letter-spacing: 0;
            }

            .header-subtitle {
                white-space: nowrap;
                font-size: 12px;
                color: #262626;
                letter-spacing: 0;
                line-height: 18px;
                font-weight: 400;
            }
        }

        .ant-divider {
            border-left-color: #e2e2ea;
            height: 36px;
            top: 0;
            margin: 0 18px;
        }

        .selects-wrap {
            width: 100%;
            display: flex;
            align-items: center;

            .target-currency-select,
            .company-select,
            .coa-account-select {
                min-width: 175px;
                width: 175px;
                max-width: 500px;
                margin-right: 8px;
                flex: 1;
            }

            .data-select {
                min-width: 145px;
                width: 145px;
                max-width: 145px;
                margin-right: 8px;
                flex: 1;
            }

            .transaction-currency-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.2;
            }

            .coa-account-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.5;
                margin-right: 0;

                .select-all-item {
                    padding: 5px 12px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                    .ant-checkbox-wrapper {
                        width: 100%;
                    }
                }

                :deep(.ant-select-selector) {
                    overflow: hidden;

                    .ant-select-selection-overflow {
                        flex-wrap: nowrap;
                    }
                }
            }
        }

        .ant-btn-round {
            padding: 6px 10px;
            margin-left: 16px;
        }
    }

    .page-content {
        padding: 0 20px;
        overflow: hidden;
        height: calc(100% - 105px);
        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
        }
        :deep(.ant-table-summary) {
            // background: #f7f9fa;
            // border-radius: 8px;
        }

        // :deep(.ant-table-wrapper .ant-table .ant-table-thead .ant-table-cell) {
        //     text-align: center !important;
        // }
    }

    .table-summary-wrap {
        .table-summary-total {
            text-align: left;
            font-weight: 600;
        }

        .table-summary-text {
            text-align: right;
            &.center {
                text-align: center;
            }
        }
    }

    .page-footer {
        padding: 5px 8px;
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;
            width: 100%;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                min-width: 45px;
            }

            .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
                min-width: 35px;
            }
        }
    }
}
</style>
