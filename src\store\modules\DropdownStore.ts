/** @format */

import type {ActionContext} from 'vuex'
import service from '../../api/request'

const state = () => ({
    WorkType: [],
    contactType: [],
    WorkState: [],
    ForeignCountry: [],
    province: [],
    degree: [],
    eventType: [],
    employeeTitle: [],
    jobName: [],
    // bankKey: [],
    payTypeNew: [],
    eeGroup: [],
    eeSubGroup: [],
    nationality: [],
    country: [],
    Education: [],
    companies: [],
    offerSender: [],
    candidatePosition: [],
    departments: [],
    payType: [],
    candidateStates: [],
    recruitDepartments: [],
    projectType: [],
})
const mutations = {
    setCompanies(state: {companies: any[]}, companies: any) {
        state.companies = [...companies]
    },
    setOfferSender(state: {offerSender: any[]}, senders: any) {
        state.offerSender = [...senders]
    },
    setCandidatePosition(state: {candidatePosition: any[]}, candidatePosition: any) {
        state.candidatePosition = [...candidatePosition]
    },
    setCommonDropDown(state: {[x: string]: any[]}, payload: {type: any; data: any}) {
        const type = payload.type
        state[type] = [...payload.data]
    },
    setCommonDropDownWithKey(state: {[x: string]: any[]}, payload: {key: any; data: any}) {
        const key = payload.key
        state[key] = [...payload.data]
    },
    setForeignCountry(state: {ForeignCountry: any[]}, ForeignCountry: any) {
        state.ForeignCountry = [...ForeignCountry]
    },
    setDepartments(state: {departments: any[]}, departments: any) {
        state.departments = [...departments]
    },
    setPayType(state: {payType: any[]}, payType: any) {
        state.payType = [...payType]
    },
    setCandidateStates(state: {candidateStates: any[]}, candidateStates: any) {
        state.candidateStates = [...candidateStates]
    },
    setRecruitDepartments(state: {recruitDepartments: any[]}, recruitDepartments: any) {
        state.recruitDepartments = [...recruitDepartments]
    },
    resetDropdowns(state: {
        departments: never[]
        WorkType: never[]
        eventType: never[]
        contactType: never[]
        WorkState: never[]
        ForeignCountry: never[]
        province: never[]
        degree: never[]
        employeeTitle: never[]
        jobName: never[]
        payTypeNew: never[]
        eeGroup: never[]
        eeSubGroup: never[]
        nationality: never[]
        country: never[]
        Education: never[]
        companies: never[]
        offerSender: never[]
        candidatePosition: never[]
    }) {
        state.departments = []
        state.WorkType = []
        state.eventType = []
        state.contactType = []
        state.WorkState = []
        state.ForeignCountry = []
        state.province = []
        state.degree = []
        ;(state.employeeTitle = []),
            (state.jobName = []),
            // state.bankKey = [],
            (state.payTypeNew = []),
            (state.eeGroup = []),
            (state.eeSubGroup = []),
            (state.nationality = []),
            (state.country = []),
            (state.Education = [])
        state.companies = []
        state.offerSender = []
        state.candidatePosition = []
        state.departments = []
    },
}
const actions = {
    async fetchCommon(
        {state, commit}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {type: any; refresh: any},
    ) {
        const type = payload.type
        if (payload.refresh || state[type].length === 0) {
            const res = await service.post(`/common/dropDown?type=${type}`)
            commit('setCommonDropDown', {type, data: res.data.data})
            return res
        }
    },
    async fetchForeignCountry(
        {commit, state}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {refresh: any},
    ) {
        if ((payload && payload.refresh) || state.ForeignCountry.length === 0) {
            const res = await service.get('/common/manage/dropDown?key=foreign_country')
            commit('setForeignCountry', res.data.data)
            return res
        }
    },
    async fetchCommonWithKey(
        {commit, state}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {key: any; refresh: any},
    ) {
        const key = payload.key
        if ((payload && payload.refresh) || state[key].length === 0) {
            const res = await service.get(`/common/manage/dropDown?key=${key}`)
            commit('setCommonDropDownWithKey', {key, data: res.data.data})
            return res
        }
    },
    async fetchCompanies(
        {commit, state}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {refresh: any},
    ) {
        if ((payload && payload.refresh) || state.companies.length === 0) {
            service.get('/system/user/manage/company').then(res => {
                const data = res.data
                commit('setCompanies', data.data)
            })
        }
    },
    async fetchOfferSender(
        {commit, state}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {refresh: any},
    ) {
        if ((payload && payload.refresh) || state.offerSender.length === 0) {
            service.post('/recruitment/offerSenEmails', new FormData()).then(res => {
                commit('setOfferSender', res.data.data)
            })
        }
    },
    async fetchCandidatePosition(
        {commit, state}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {refresh: any},
    ) {
        if ((payload && payload.refresh) || state.candidatePosition.length === 0) {
            service.post('/recruitment/candidatePosition', new FormData()).then(res => {
                commit('setCandidatePosition', res.data.data)
            })
        }
    },
    async fetchDepartments(
        {commit, state}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: any,
    ) {
        // if ((payload && payload.refresh) || state.departments.length === 0) {
        const res = await service.get('/organization/manage/department')
        commit('setDepartments', res.data.data)
        // }
    },
    async fetchPayType(
        {commit, state, dispatch}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {refresh: any},
    ) {
        if ((payload && payload.refresh) || state.payType.length === 0) {
            const res = await dispatch('_recruitValues', 'rnj_pay_type') //await service.post('/recruitment/recruitValues', param)
            commit('setPayType', res.data.data)
        }
    },
    async fetchCandidateState(
        {commit, state, dispatch}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {refresh: any},
    ) {
        if ((payload && payload.refresh) || state.candidateStates.length === 0) {
            const res = await dispatch('_recruitValues', 'rnj_candidate_state')
            commit('setCandidateStates', res.data.data)
        }
    },
    async fetchRecruitDepartments(
        {commit, state}: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {refresh: any},
    ) {
        if ((payload && payload.refresh) || state.recruitDepartments.length === 0) {
            const res = await service.get('/recruitment/recruitDepts')
            commit('setRecruitDepartments', res.data.data)
        }
    },
    async _recruitValues(_: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: string | Blob) {
        const form = new FormData()
        form.append('paramKey', payload)
        return service.post('/recruitment/recruitValues', form)
    },
}
export default {
    state,
    actions,
    mutations,
}
