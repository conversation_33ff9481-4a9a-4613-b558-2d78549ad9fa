/** @format */

declare module 'xlsx' {
    export interface WorkSheet {
        '!cols'?: {wch: number}[]
        '!merges'?: any[]
        '!rows'?: any[]
        '!protect'?: any
    }

    export interface CellStyle {
        font?: {
            bold?: boolean
            color?: {rgb: string}
            sz?: number
        }
        fill?: {
            fgColor?: {rgb: string}
            patternType?: string
        }
        border?: {
            top?: {style: string; color?: {rgb: string}}
            bottom?: {style: string; color?: {rgb: string}}
            left?: {style: string; color?: {rgb: string}}
            right?: {style: string; color?: {rgb: string}}
        }
        alignment?: {
            horizontal?: string
            vertical?: string
            wrapText?: boolean
        }
    }

    export interface Cell {
        v: any
        t?: string
        s?: CellStyle
    }
}
