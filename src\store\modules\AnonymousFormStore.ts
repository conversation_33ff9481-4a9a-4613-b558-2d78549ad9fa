/** @format */

import type {ActionContext} from 'vuex'
import service from '../../api/request'

const actions = {
    async submitAnonymousForm(_: ActionContext<{[key: string]: any}, Record<string, unknown>>, form: any) {
        return service.post('/customerMasterData/manage', form)
    },
    async fetchEmployeeData(_: ActionContext<{[key: string]: any}, Record<string, unknown>>, name: any) {
        const result = await service.get(`/emp/manage/fetchEmployeeData?name=${name}`)
        if (result.data.code === 1000) {
            return result.data.data.filter((item: {empNo: string}) => item.empNo !== '')
        } else {
            throw new Error('server error')
        }
    },
}

export default {
    actions,
}
