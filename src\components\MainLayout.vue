<!-- @format -->

<script lang="ts" setup>
import { RouterView } from 'vue-router'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import lodash from 'lodash'
import { useStore } from 'vuex'
import { ref, computed, watch, onBeforeMount, nextTick, h, onMounted } from 'vue'
import { CaretLeftOutlined, CaretRightOutlined, CheckOutlined } from '@ant-design/icons-vue'
import {
    Language,
    FirstListMenu,
    FlatMenuListObj,
    BookkeepingMenu,
    KycMenu,
    CurrentOpenKeys,
    CurrentSidebarItem,
    Org_Id,
    UserCompany,
    LocalCurrency,
    CurrencyCountry,
    CurrencyLanguage,
    UserInfo,
    ShowTutorial,
    TokenV1,
    RemoteLogin,
    FinancialYear,
    ArIntegration,
    ApIntegration,
    FtsIntegration,
    PaymentIntegration,
    ReconciliationIntegration,
    ReceivableIntegration,
    SapPayableInternalOrder,
    SapPayableProfitCenter,
    SapPayableGl,
    SapPayableWbs,
    SapPayableCostCenter,
} from '@/lib/storage'
import { useRouter } from 'vue-router'
import { getCompanyTaxInfo, getCustomers, getCoa, getCompanyBankList, getGlList, GetKycProductList } from '@/api'
import ChangePwdForm from '@/components/bookkeepingComponents/CommonComponents/ChangePwdForm.vue'
import GoogleQrCode from '@/components/bookkeepingComponents/CommonComponents/GoogleQrCode.vue'
import { TaxData } from '@/assets/mock-data/mockData' // 临时添加 创建公司及账号页面。 删除 flag = 'company-account'
import * as _ from 'lodash'
import moment from 'moment'
import TutorialComponent from '@/components/TutorialComponent.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import { v4 as uuidv4 } from 'uuid'
import { notification, Button } from 'ant-design-vue'
import serviceKYC from '@/api/requestKyc'
import http from '@/api/requestNew'

const i18n: Composer = i18nInstance.global
const router = useRouter()
const store = useStore()
const userCompany: any = UserCompany.get() || []
const showPwd = ref(false)
const googleQr = ref(false)
const name = ref('')
const email = ref('')
const roles = ref('')
const userId = ref(0)
const collapsed = ref(false)
const isLogin = ref(true)
const value = ref('')
const openKeys: any = ref([])
const options: any = ref([])
const selectedKeys: any = ref([])
const selectWrapRef = ref()
const firstMenuList: any = computed(() => FirstListMenu.get()) //菜单列表
const show = ref(false)
const showFillBalance = ref(false)
const tutorialRef: any = ref(null)
const companyId = ref(1)
const changeCompanyList: any = ref([])
const tutorialList = ref([] as any)
const isRouterAlive = ref(true)
const dataLoaded = ref(false)
const enableTutorialPost = ref(false)
const enableTutorialNext = ref(true)
const tutorialPostLoading = ref(false)
const currentUserCompany = ref({} as any)
const userInfo: any = UserInfo.get() || {}
const currentDomain = ref(window.location.hostname.includes('netex.co.jp'))
// const rolePermissionMapping = computed(() => store.getters.rolePermissionMapping)
const fetchBankCurrent = () => store.dispatch('CommonDropDownStore/fetchBankCurrent')
const fetchCountryInfo = () => store.dispatch('CommonDropDownStore/fetchCountryInfo')
const getFirstPage = (kycMenu: any) => store.dispatch('getFirstPage', kycMenu)
const collapseIconClick = () => {
    collapsed.value = !collapsed.value
    if (collapsed.value) {
        openKeys.value = []
    } else {
        openKeys.value = CurrentOpenKeys.get() || []
        selectedKeys.value = CurrentSidebarItem.get() || []
    }
}
const changeCompany = async (val: any, option: any) => {
    // every change move the selected item to first.
    const {
        data: { data: getCompany },
    } = await getCompanyTaxInfo({ code: val })

    const currentCompany = {
        id: getCompany[0].id,
        name: getCompany[0].name,
        code: getCompany[0].code,
        email: getCompany[0].email,
        country: getCompany[0].country,
        province: getCompany[0].province,
        sap_fts_integration: getCompany[0].sap_fts_integration,
        sap_payable_cost_center: getCompany[0].sap_payable_cost_center,
        sap_payable_gl: getCompany[0].sap_payable_gl,
        sap_payable_integration: getCompany[0].sap_payable_integration,
        sap_payable_internal_order: getCompany[0].sap_payable_internal_order,
        sap_payable_profit_center: getCompany[0].sap_payable_profit_center,
        sap_payable_wbs: getCompany[0].sap_payable_wbs,
        sap_paymente_integration: getCompany[0].sap_paymente_integration,
        sap_receivable_integration: getCompany[0].sap_receivable_integration,
        sap_reconciliation_integration: getCompany[0].sap_reconciliation_integration,
    }

    const newSession = [currentCompany, ...userCompany.filter((x: any) => x.code !== val)]
    UserCompany.set(newSession)

    currentUserCompany.value = { ...newSession[0] }
    Org_Id.set(currentUserCompany.value.id)
    // const rolePermissions = rolePermissionMapping.value.find((x: any) => x.role_id === userInfo.roles)
    let kycMenus: any[] = []
    let expire = false

    const res = await GetKycProductList({ companyCode: currentUserCompany.value.code })
    if (res?.data?.body?.BK) {
        const curDate = moment().format('YYYY-MM-DD')
        if (
            res?.data?.body?.BK.expireDate >= curDate ||
            (res?.data?.body?.BK.freeTrialStart <= curDate && res?.data?.body?.BK.freeTrialEnd >= curDate)
        ) {
            expire = true
        }
    }
    if (currentUserCompany.value.code === '0001' || currentUserCompany.value.code === '0000') {
        expire = true
    }

    let bookkeepingMenu: any = KycMenu.get()
    if (bookkeepingMenu) {
        bookkeepingMenu.forEach((item: any) => {
            if (expire) {
                kycMenus.push(item.kycName)
            }
        })
    }

    console.log('ApIntegration: ', currentUserCompany.value.sap_payable_integration)
    console.log('PaymentIntegration: ', currentUserCompany.value.sap_paymente_integration)

    ArIntegration.set(currentUserCompany.value.sap_receivable_integration)
    ApIntegration.set(currentUserCompany.value.sap_payable_integration)
    FtsIntegration.set(currentUserCompany.value.sap_fts_integration)
    PaymentIntegration.set(currentUserCompany.value.sap_paymente_integration)
    ReconciliationIntegration.set(currentUserCompany.value.sap_reconciliation_integration)
    ReceivableIntegration.set(currentUserCompany.value.sap_receivable_integration)
    SapPayableInternalOrder.set(currentUserCompany.value.sap_payable_internal_order)
    SapPayableProfitCenter.set(currentUserCompany.value.sap_payable_profit_center)
    SapPayableGl.set(currentUserCompany.value.sap_payable_gl)
    SapPayableWbs.set(currentUserCompany.value.sap_payable_wbs)
    SapPayableCostCenter.set(currentUserCompany.value.sap_payable_cost_center)

    if (!expire) {
        kycMenus = ['/Subscribe']
        BookkeepingMenu.set([])
    } else {
        if (currentUserCompany.value.sap_paymente_integration === 0) {
            bookkeepingMenu = lodash.reject(bookkeepingMenu, ['menuName', 'payment'])
        }

        if (
            currentUserCompany.value.sap_paymente_integration ||
            currentUserCompany.value.sap_payable_integration ||
            currentUserCompany.value.sap_receivable_integration ||
            currentUserCompany.value.sap_reconciliation_integration ||
            currentUserCompany.value.sap_fts_integration
        ) {
            const settingMenu = lodash.find(bookkeepingMenu, ['menuName', 'setting'])
            settingMenu.childMenus = lodash.reject(settingMenu.childMenus, ['menuName', 'coA'])
            settingMenu.childMenus = lodash.reject(settingMenu.childMenus, ['menuName', 'coAMapping'])
        }
        BookkeepingMenu.set([...bookkeepingMenu])
        updateMenuList(bookkeepingMenu)
    }

    const { id, path, parentId } = await getFirstPage(kycMenus)
    if (path === router.currentRoute.value.path) {
        isRouterAlive.value = false
        selectedKeys.value = [id]
        openKeys.value = [parentId]
        CurrentSidebarItem.set([id])
        CurrentOpenKeys.set([parentId])
        nextTick(() => (isRouterAlive.value = true))
    } else {
        //router.push({path: rolePermissions.first_page, replace: true, force: true} || {name: 'Login'})
        sidebarMenuClick(id, path, parentId)
    }

    await openNotification(currentUserCompany.value.code)
    await store.dispatch('ContactStore/fetchContacts', { company_code: currentUserCompany.value.code, $limit: -1 })
    // store.dispatch('MenuList/fetchMenuList', {company_code: val})

    // ApIntegration.set(currentUserCompany.value.sap_payable_integration)
    // ChangeCompany(para).then(res => {
    //     if (res.data.code == 1000) {
    //         store.commit('resetDropdowns')
    //         FirstListMenu.del()
    //         BookkeepingMenu.del()
    //         // this.getBtnPermissions(); //获取权限按钮
    //         Org_Id.set(val)
    //         getMenuList() //获取菜单树
    //     }
    // })
}
const dismiss = () => {
    showPwd.value = false
}
const dismissGQr = () => {
    googleQr.value = false
}
const currentUserInfo = computed(() => store.state.UserInfo.userInfo)

const menuList1 = computed(() => {
    return store.state.MenuList.menuList.filter((item: any) => item.childMenus.length === 0)
})

const menuList2 = computed(() => {
    return store.state.MenuList.menuList.filter((item: any) =>
        roles.value === '9995' || roles.value === '1'
            ? item.childMenus.length > 0
            : item.childMenus.length > 0 && item.id !== '115',
    )
})

const filterOption = (input: string, option: any) => {
    return (
        (option.company_name || '').toString().toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
        (option.value || '').toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
    )
}

const getPopupContainer = (node: any) => {
    return selectWrapRef.value
}

const sidebarMenuClick = (id: string, url: string, parentId: string) => {
    if (url.includes('userGuide')) {
        show.value = true
        return
    }

    if (router.currentRoute.value.path !== url) {
        router.push(url)
        selectedKeys.value = [id]
        openKeys.value = [parentId]
        CurrentSidebarItem.set([id])
        CurrentOpenKeys.set([parentId])
    }
}

const userLogout = () => {
    // const para = new FormData()
    // Logout(para).then(res => {
    //     if (res.data.code == 1000) {
    //         router.replace({name: 'Login'})
    //         //设置i18n语言(4-5)
    //         Language.del()
    //         CurrentOpenKeys.del()
    //         CurrentSidebarItem.del()
    //         FlatMenuListObj.del()
    //     } else {
    //         const msgDiv = document.getElementById('msgDiv')
    //         if (msgDiv) {
    //             msgDiv.innerHTML = res.data.message
    //         }
    //     }
    // })
    //设置i18n语言(4-5)
    Language.del()
    CurrentOpenKeys.del()
    CurrentSidebarItem.del()
    FlatMenuListObj.del()

    ShowTutorial.del()
    UserCompany.del()
    UserInfo.del()
    BookkeepingMenu.del()
    KycMenu.del()
    FirstListMenu.del()
    TokenV1.del()
    Org_Id.del()
    LocalCurrency.del()
    CurrencyLanguage.del()
    CurrencyCountry.del()
    FinancialYear.del()

    if (RemoteLogin.get() === '1') {
        RemoteLogin.del()
        // todo RemoteLogin
        console.log("====window.location.hostname", window.location.hostname)
        if (window.location.hostname.includes('int.netex.co.jp')) {
            window.location.replace('https://es.int.netex.co.jp/#/login')
        } else {
            window.location.replace(import.meta.env.VITE_PORTAL_URL)
        }
    } else {
        router.replace({ name: 'Login' })
    }
}

const accountMenuClick = ({ key }: any) => {
    if (key === 'signOut') {
        userLogout()
    } else if (key === 'changePwd') {
        showPwd.value = true
    } else if (key === 'gQr') {
        googleQr.value = true
    } else {
        if (i18n.locale.value !== key) {
            store.dispatch('setLanguage', key)
        }
        //i18n语言切换(5-5)
    }
}
const onOpenChange = (keyList: any) => {
    openKeys.value = [keyList.pop()]
    CurrentOpenKeys.set([keyList.pop()])
}

// flatten菜单Array
const flattenArr = (arr: { menuUrl: any; id: any; parentId: any; childMenus: any }[]) => {
    let result = {}
    arr.forEach((item: { menuUrl: any; id: any; parentId: any; childMenus: any }) => {
        const { menuUrl, id, parentId, childMenus } = item
        result = {
            ...result,
            ...{
                [menuUrl]: {
                    id,
                    parentId,
                },
            },
        }

        if (childMenus.length > 0) {
            result = { ...result, ...flattenArr(childMenus) }
        }
    })
    return result
}

const getMainhomeCompany = () => {
    // const para = new FormData()
    // MainhomeCompany(para).then(res => {
    //     if (res.data.code == 1000) {
    //         options.value = res.data.data
    //         value.value = options.value[0].mainCompanyId
    //         Org_Id.set(options.value[0].mainCompanyId)
    //     }
    // })
    currentUserCompany.value = { ...userCompany[0] }
    options.value = _.cloneDeep(_.sortBy(userCompany, 'id'))
    value.value = currentUserCompany.value.code || (options.value.length ? options.value[0].code : null)

    Org_Id.set(currentUserCompany.value.id || (options.value.length ? options.value[0].id : null))
}

const fetchUserInfo = () => {
    // store.dispatch('fetchUserInfo').then(res => {
    //     if (res.data.code === 1000) {
    //         name.value = res.data.data.userName
    //         // workNo = res.data.data.workNo;
    //     } else {
    //         isLogin.value = false
    //     }
    // })
    const _user: any = UserInfo.get() || {}
    // console.log('user--->', _user)

    if (_.isEmpty(_user)) {
        isLogin.value = false
    } else {
        name.value = _user?.account
        email.value = _user?.email
        roles.value = _user?.roles
        userId.value = _user?.id
    }
}

watch(
    () => dataLoaded.value,
    newValue => {
        if (newValue) {
            show.value =
                roles.value === '3' &&
                (tutorialList.value.some((i: any) => !i.complete) ||
                    (!_.isEmpty(router.currentRoute.value.query) && router.currentRoute.value.query.showTutorial))

            dataLoaded.value = false
        }
    },
)

watch(
    () => router.currentRoute.value,
    newValue => {
        show.value =
            roles.value === '3' &&
            newValue.path === '/dashboard' &&
            !_.isEmpty(newValue.query) &&
            newValue.query.showTutorial == 'true'
    },
)

// watch(
//     () => companyId.value,
//     (newValue, oldValue) => {
//         // console.log('newValue',newValue);
//         // console.log('oldValue',oldValue);
//         const companyData: any = UserCompany.get()
//         if (newValue !== oldValue) {
//             // console.log('companyData---',companyData);
//             for (let i = 0; i < companyData?.length; i++) {
//                 const item = companyData[i]
//                 if (item.id == newValue) {
//                     changeCompanyList.value.push(item)
//                 }
//             }
//             name.value = changeCompanyList?.value[0]?.name
//             email.value = changeCompanyList?.value[0]?.email
//             // UserInfo.set({id: changeCompanyList?.value[0]?.name.id, account: changeCompanyList?.value[0]?.namer.account, email: changeCompanyList?.value[0]?.name.email})
//             // console.log('userInfo-----',UserCompany.get());
//             changeCompanyList.value = []
//         }
//     },
//     {immediate: true},
// )

const toTutorialSubPage = (item: any) => {
    show.value = false
    if (item.id === 4) {
        showFillBalance.value = true
        return
    }
    router.push({
        path: item.url,
        query: !item.param ? { showTutorial: 'true' } : { id: item.param, showTutorial: 'true' },
    })
}

const updateBankAccountTypeOptions = () => store.commit('CommonDropDownStore/updateBankAccountTypeOptions')
const accountTypeOptions = computed(() => store.state.CommonDropDownStore.bankAccountTypeOptions)

const await2js = <T, K = Error>(promise: Promise<T>) => {
    return promise
        .then<[undefined, T]>((response: any) => [undefined, response.data])
        .catch<[K, undefined]>((error: K) => [error, undefined])
}

const updateMenuList = (data: any) => store.commit('updateMenuList', data, { root: true })

onBeforeMount(async () => {
    console.log('..........into mainlayout')
    openKeys.value = CurrentOpenKeys.get() || []
    selectedKeys.value = CurrentSidebarItem.get() || []

    fetchBankCurrent()
    fetchCountryInfo()
    getMainhomeCompany()
    fetchUserInfo()

    let currentMenuList: any = BookkeepingMenu.get()
    if (currentUserCompany.value.sap_paymente_integration === 0) {
        currentMenuList = lodash.reject(currentMenuList, ['menuName', 'payment'])
    }

    if (
        currentUserCompany.value.sap_paymente_integration ||
        currentUserCompany.value.sap_payable_integration ||
        currentUserCompany.value.sap_receivable_integration ||
        currentUserCompany.value.sap_reconciliation_integration ||
        currentUserCompany.value.sap_fts_integration
    ) {
        const settingMenu = lodash.find(currentMenuList, ['menuName', 'setting'])
        if (settingMenu) {
            settingMenu.childMenus = lodash.reject(settingMenu.childMenus, ['menuName', 'coA'])
            settingMenu.childMenus = lodash.reject(settingMenu.childMenus, ['menuName', 'coAMapping'])
        }
    }
    if (currentMenuList && currentMenuList.length > 0) {
        BookkeepingMenu.set([...currentMenuList])
        updateMenuList(currentMenuList)
    }
})

onMounted(async () => {
    await openNotification(currentUserCompany.value.code)
    await store.dispatch('ContactStore/fetchContacts', { company_code: currentUserCompany.value.code, $limit: -1 })
})

const current = ref<number>(0)
const next = async () => {
    try {
        await tutorialRef.value.formValidateFields()
        current.value++
        tutorialRef.value.next(current.value)
    } catch (error) {
        console.log(error)
    }
}
const prev = () => {
    current.value--
    tutorialRef.value.prev(current.value)
}
const saveDraft = async () => {
    try {
        await tutorialRef.value.formValidateFields()
        const _res = tutorialRef.value.saveDraft()
        if (_res) {
            showFillBalance.value = false
            current.value = 0
            enableTutorialNext.value = true

            dataLoaded.value = true
        }
    } catch (error) {
        console.log(error)
    }
}
const post = () => {
    const _res = tutorialRef.value.post()
    if (_res) {
        showFillBalance.value = false
        current.value = 0
        enableTutorialNext.value = true

        dataLoaded.value = true
    }
}
const tutorialClosed = () => {
    current.value = 0
    enableTutorialNext.value = true

    dataLoaded.value = true
}

const tutorialWrapClosed = () => {
    router.replace({ query: {} })

    selectedKeys.value = CurrentSidebarItem.get()
    openKeys.value = CurrentOpenKeys.get()
}
const steps = ref([
    {
        title: 'Step 1',
        description: 'GL Balance',
        content: '1',
    },
    {
        title: 'Step 2',
        description: 'Account Payable',
        content: '2',
    },
    {
        title: 'Step 3',
        description: 'Account Receivable',
        content: '3',
    },
    {
        title: 'Step 4',
        description: 'Complete',
        content: '4',
    },
])

const getBankList = (bankList: any[]) => {
    const copied = _.cloneDeep(bankList)
    copied.forEach((x: any) => {
        const typeIndex = accountTypeOptions.value.find((y: any) => y.value === x.bank_type)
        x.accountType = typeIndex ? typeIndex.key : ''
    })
    return copied
}

const getTutorialData = async () => {
    if (roles.value === '1' || roles.value === '3') {
        const searchObj: any = {
            $limit: 10,
            $skip: 0,
        }
        const searchObjV1 = {
            company: currentUserCompany.value.code,
            page_index: 1,
            page_size: 10,
        } as any

        let companyTaxInfoComplete = false
        const [companyTaxInfoError, companyTaxInfoResponse] = await await2js(
            getCompanyTaxInfo({ code: currentUserCompany.value.code }),
        )

        LocalCurrency.set(companyTaxInfoResponse?.data[0].currency)
        CurrencyCountry.set(companyTaxInfoResponse?.data[0].country)
        CurrencyLanguage.set(companyTaxInfoResponse?.data[0].language)
        FinancialYear.set(companyTaxInfoResponse?.data[0].financial_year)

        companyTaxInfoComplete = companyTaxInfoError
            ? false
            : (companyTaxInfoResponse as any).total > 0
                ? companyTaxInfoResponse.data[0].financial_year && companyTaxInfoResponse.data[0].currency
                : false
        let customersListComplete = false
        const customerQuery: any = {}
        customerQuery['$sort[create_time]'] = 'desc'
        const [customersListError, customersListResponse] = await await2js(
            getCustomers({ ...searchObj, ...customerQuery, company_code: currentUserCompany.value.code }),
        )
        customersListComplete = customersListError ? false : customersListResponse.data.length
        let accountDescListComplete = false
        const [accountDescListError, accountDescListResponse] = await await2js(getCoa({ ...searchObjV1 }))

        accountDescListComplete = accountDescListError ? false : accountDescListResponse.data.rows.length
        updateBankAccountTypeOptions()
        let bankListComplete = false
        const [bankListError, bankListResponse] = await await2js(
            getCompanyBankList({ ...searchObj, company_code: currentUserCompany.value.code }),
        )

        bankListComplete = bankListError ? false : getBankList(bankListResponse.data).length > 0
        let glListComplete = false
        const [glListError, glkListResponse] = await await2js(
            getGlList({ company: currentUserCompany.value.code, status: '1' }),
        )

        glListComplete = glListError ? false : (glkListResponse as any).total

        tutorialList.value = [
            {
                id: 1,
                title: i18n.t('mainLayout.setFY'), //'Set Currency And Financial Year.',
                url: '/bookkeeping/common/taxInformation',
                name: 'TaxInformation',
                param: null,
                complete: companyTaxInfoComplete,
            },
            {
                id: 2,
                title: i18n.t('mainLayout.createBP'), //'Create business partners.',
                url: '/bookkeeping/common/customer',
                name: 'Customer',
                param: null,
                complete: customersListComplete,
            },
            {
                id: 3,
                title: i18n.t('mainLayout.createChart'), //'Create Chart of Accounts.',
                url: '/bookkeeping/common/accountDescription',
                name: 'AccountDescription',
                param: null,
                complete: accountDescListComplete,
            },
            {
                id: 4,
                title: i18n.t('mainLayout.fillin'), //'Fill in Opening Balance',
                url: '',
                name: null,
                param: null,
                complete: glListComplete,
            },
            {
                id: 5,
                title: i18n.t('mainLayout.careteBank'), //'Create Banks.',
                url: '/bookkeeping/common/taxInformation',
                name: 'TaxInformation',
                param: 'third',
                complete: bankListComplete,
            },
        ]
    } else {
        const [companyTaxInfoError, companyTaxInfoResponse] = await await2js(
            getCompanyTaxInfo({ code: currentUserCompany.value.code }),
        )

        LocalCurrency.set(companyTaxInfoResponse?.data[0].currency)
        CurrencyCountry.set(companyTaxInfoResponse?.data[0].country)
        CurrencyLanguage.set(companyTaxInfoResponse?.data[0].language)
        FinancialYear.set(companyTaxInfoResponse?.data[0].financial_year)
    }

    dataLoaded.value = true
}

const glPostSuccess = () => {
    tutorialList.value[3].complete = true
}

const openNotification = async (companyCode: string) => {
    const res = await getKycMessage(companyCode)
    res.forEach((item: any) => {
        if (item.sendType.includes('3')) {
            const key = `open${item.id}`
            notification.open({
                message: item.title,
                duration: 0,
                description: item.description,
                style: {
                    maxHeight: '800px',
                    overflowY: 'auto',
                },
                // btn: () =>
                //     h(
                //         Button,
                //         {
                //             type: 'primary',
                //             size: 'small',
                //             onClick: () => {
                //                 setKycMessage(item.id)
                //                 notification.close(key)
                //             },
                //         },
                //         {default: () => i18n.t('commonTag.confirm')},
                //     ),
                btn: h(
                    'div',
                    {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                        },
                    },
                    [
                        h(
                            'Button',
                            {
                                class: 'ant-btn',
                                style: {
                                    marginRight: '40px',
                                    marginBottom: '5px',
                                },
                                onClick: () => notification.close(key),
                            },
                            'Remind me later',
                        ),
                        h(
                            'Button',
                            {
                                class: 'ant-btn ant-btn-primary',
                                style: {
                                    marginLeft: '40px',
                                    marginBottom: '5px',
                                },
                                onClick: () => {
                                    setKycMessage(item.id)
                                    notification.close(key)
                                },
                            },
                            'Acknowledge',
                        ),
                    ],
                ),
                key,
                onClose: close,
            })
        }
    })
}

const getKycMessage = async (companyCode: string) => {
    const newDate = new Date()
    const { status, data } = await serviceKYC.post('/message/productMessage', {
        companyCode: companyCode,
        productCode: ['PRBKOM', 'PRBK', 'BK', 'BKOM'],
        status: ['1', '2'],
        // endDate: `${newDate.getFullYear()}-${newDate.getMonth() + 1}-${newDate.getDate()}`,
    })

    if (status === 200 && data.total > 0) {
        return data.data
    }

    return []
}

const setKycMessage = async (id: string) => {
    return await serviceKYC.patch('/message/productMessage', {
        id: id,
        status: '3',
        updaterId: userId.value,
    })
}

watch(currentUserCompany, () => {
    getTutorialData()
})
</script>
<template>
    <div class="main-layout-wrap">
        <div v-drag draggable="true" class="fixed-widgets">
            <img src="../assets/image/service.png" />
        </div>
        <a-layout id="main-layout">
            <!-- Layout Header section -->
            <a-layout-header style="padding: 0"
                :style="{ position: 'fixed', zIndex: 10, width: '100%', height: '54px' }">
                <div class="layout-header-wrapper" :style="{ background: '#004FC1' }">
                    <!-- Logo in header -->
                    <div class="logo">
                        <img id="logo-image" src="@/assets/image/logo/companyNtLogo.png" />
                    </div>
                    <div class="layout-header-menu"></div>
                    <!-- company selector in Header -->
                    <div class="field zr-menu-company" ref="selectWrapRef">
                        <a-select v-model:value="value" show-search :filter-option="filterOption"
                            :getPopupContainer="getPopupContainer" @change="changeCompany" :disabled="roles === '9995'">
                            <a-select-option v-for="item in options" :key="item.name + uuidv4()"
                                :company_name="item.name" :value="item.code">
                                {{ item.code + ' - ' + item.name }}
                            </a-select-option>
                            <template #suffixIcon> <svg-icon name="icon_down_white"></svg-icon></template>
                        </a-select>
                    </div>
                    <!-- user account box in Header -->
                    <div class="zr-menu-account-block">
                        <a-divider type="vertical" />
                        <div class="zr-menu-account-content">
                            <div class="zr-menu-account-text-arrow">
                                <div v-if="isLogin" class="zr-menu-account-text">{{ userInfo.account }}</div>
                                <div v-else class="zr-menu-account-text">{{ i18n.t('mainPage.noLogin') }}</div>
                                <div v-if="isLogin" class="zr-menu-account-sub-text">
                                    <!-- {{ userInfo.email }} -->
                                    {{ currentUserCompany.email }}
                                </div>
                            </div>
                        </div>
                        <a-dropdown :trigger="['hover']" @click="(e: any) => e.preventDefault()">
                            <a-button class="zr-menu-account-button" type="primary">
                                <template #icon>
                                    <svg-icon name="icon_ellipsis"></svg-icon>
                                </template>
                            </a-button>

                            <template #overlay>
                                <a-menu @click="accountMenuClick" class="menu-wrap">
                                    <a-menu-item key="changePwd">
                                        <svg-icon name="icon_password"></svg-icon>
                                        <span class="account-dropdown-menu-text">{{ i18n.t('login.changePwd') }}</span>
                                    </a-menu-item>
                                    <a-sub-menu key="lang">
                                        <template v-slot:title>
                                            <svg-icon name="icon_language"></svg-icon>
                                            <span class="account-dropdown-menu-text">{{
                    i18n.t('login.language')
                }}</span>
                                        </template>
                                        <a-menu-item key="en">English</a-menu-item>
                                        <a-menu-item key="zh">简体中文</a-menu-item>
                                        <a-menu-item key="zh-hk">繁體中文</a-menu-item>
                                        <a-menu-item key="ja">日本語</a-menu-item>
                                        <a-menu-item key="fr">Français</a-menu-item>
                                        <a-menu-item key="es">Español</a-menu-item>
                                    </a-sub-menu>
                                    <a-menu-item key="gQr" v-if="currentUserInfo.loginAccount === 'Jasvir Sadhra '">
                                        <svg-icon name="icon_password"></svg-icon>
                                        <span class="account-dropdown-menu-text">{{ i18n.t('login.gQr') }}</span>
                                    </a-menu-item>
                                    <a-divider style="margin: 0"></a-divider>
                                    <a-menu-item key="signOut">
                                        <svg-icon name="icon_logOut"></svg-icon>
                                        <span class="account-dropdown-menu-text">{{ i18n.t('login.logOut') }}</span>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </div>
                </div>
            </a-layout-header>
            <!-- layout-sider and layout-content section -->
            <a-layout style="width: 100%" has-sider>
                <!-- layout-sidebar section (left part) -->
                <!-- remove  height: '100vh' in a-layout-sider for menu bug-->
                <div>
                    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible :width="300"
                        :style="{ overflow: 'auto', position: 'fixed', left: 0, top: '54px', bottom: 0, zIndex: 2 }">
                        <div :class="collapsed ? 'expand-closev1' : 'expand-close'" @click="collapseIconClick">
                            <caret-left-outlined v-if="!collapsed" />
                            <caret-right-outlined v-else />
                        </div>
                        <a-menu class="sidbar-menu" mode="inline" v-model:open-keys="openKeys"
                            @openChange="onOpenChange" v-model:selectedKeys="selectedKeys">
                            <a-menu-item class="menuList1-wrap" v-for="item in menuList1" :key="item.id"
                                @click="sidebarMenuClick(item.id, item.menuUrl, item.parentId)">
                                <template #icon>
                                    <div class="menu-icon" :class="'i-' + item.id"></div>
                                </template>
                                {{ i18n.t('menu.' + item.menuName) }}
                            </a-menu-item>
                            <a-sub-menu v-for="item in menuList2" :key="item.id">
                                <template v-slot:title>
                                    <div class="menu-icon" :class="'i-' + item.id"></div>
                                    {{ i18n.t('menu.' + item.menuName) }}
                                </template>
                                <!-- hide Menu-item Supplier (id=90) && bank_Information (id=87) && Sales->upload (id=97)-->
                                <div v-for="childItem in item.childMenus" :key="childItem.id" v-show="(childItem.hidden === 'show' &&
                    childItem.id !== '99' &&
                    childItem.id !== '93' &&
                    childItem.id !== '90' &&
                    childItem.id !== '87' &&
                    childItem.id !== '109' &&
                    childItem.id !== '999' &&
                    childItem.id !== '97') ||
                    ((childItem.id === '4567' || childItem.id === '4568') &&
                        currentUserInfo.loginAccount === 'ntadmin')
                    ">
                                    <a-menu-item :key="childItem.id"
                                        @click="sidebarMenuClick(childItem.id, childItem.menuUrl, childItem.parentId)">
                                        <span>{{ i18n.t('menu.' + childItem.menuName) }}</span>
                                    </a-menu-item>
                                </div>
                            </a-sub-menu>
                        </a-menu>
                        <div v-if="currentDomain" class="reconstruction-text">
                            事業再構築2024
                        </div>
                    </a-layout-sider>
                </div>

                <a-layout :style="collapsed ? { marginLeft: '80px' } : { marginLeft: '300px' }">
                    <!-- layout-sidebar section (right part) -->
                    <a-layout-content class="layout-content-class" :style="{ padding: '0', marginTop: '54px' }">
                        <div class="main-content-class">
                            <RouterView v-if="isRouterAlive" />
                        </div>
                    </a-layout-content>
                </a-layout>
            </a-layout>
        </a-layout>
        <a-modal :title="i18n.t('login.changePwd')" v-model:visible="showPwd" :footer="null" destroyOnClose
            :closeable="true" :width="440" :wrapClassName="'modal-wrap'">
            <change-pwd-form @dismiss="dismiss"></change-pwd-form>
        </a-modal>
        <a-modal :title="i18n.t('login.gQr')" v-model:visible="googleQr" :footer="null" destroyOnClose :closeable="true"
            :width="440" :wrapClassName="'modal-wrap'">
            <google-qr-code @dismiss="dismissGQr"></google-qr-code>
        </a-modal>
    </div>
    <div>
        <a-modal :title="i18n.t('mainLayout.setup')" v-model:visible="show" :footer="null" destroyOnClose
            :closeable="true" :maskClosable="false" :centered="true" :width="700"
            :bodyStyle="{ padding: '10px 14px 14px' }" :z-index="2902" @cancel="tutorialWrapClosed">
            <a-list size="large" bordered :data-source="tutorialList">
                <template #renderItem="{ item }">
                    <a-list-item :style="{ color: item.complete ? '#566b8c' : '#004fc1' }"
                        v-if="item.id !== 4 || roles === '1'"
                        @click="; (item.id === 4 && item.complete && roles !== '1') || toTutorialSubPage(item)">
                        <div>
                            <div class="menu-icon-guide" :class="'i-' + item.id"></div>
                            {{ item.title }}
                        </div>
                        <check-outlined v-if="item.complete" />
                    </a-list-item>
                </template>
            </a-list>
        </a-modal>
        <a-modal title="Fill in Opening Balance" v-model:visible="showFillBalance" destroyOnClose :closeable="true"
            :maskClosable="false" :centered="true" :width="1000" :bodyStyle="{ padding: '10px 14px 14px' }"
            :z-index="2902" @cancel="tutorialClosed">
            <template #footer>
                <a-button class="modal-close-btn" shape="round" v-if="current > 0" @click="prev"
                    :disabled="tutorialPostLoading">Previous</a-button>
                <a-button class="modal-close-btn" type="primary" shape="round" @click="saveDraft"
                    :disabled="tutorialPostLoading">Save Draft</a-button>
                <a-button class="modal-close-btn" type="primary" shape="round" @click="post"
                    v-if="current === steps.length - 1"
                    :disabled="!enableTutorialPost || tutorialPostLoading">Post</a-button>
                <a-button class="modal-ok-btn" type="primary" shape="round" v-if="current < steps.length - 1"
                    @click="next" :disabled="tutorialPostLoading || !enableTutorialNext">Next</a-button>
            </template>
            <tutorial-component ref="tutorialRef" v-model:enablePost="enableTutorialPost"
                v-model:enableNext="enableTutorialNext" v-model:loading="tutorialPostLoading" :steps="steps"
                @success="glPostSuccess"></tutorial-component>
        </a-modal>
    </div>
</template>

<style scoped lang="scss">
.main-layout-wrap {}

#main-layout {
    .layout-header-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .logo {
            height: 36px;
            min-width: 202px;
            display: flex;
            justify-content: center;
            align-items: center;
            // margin-top:3px;

            #logo-image,
            .logo-text_short {
                width: auto;
                height: 36px;
            }

            #logo-image {
                margin: 0 30px;
                height: 36px;
            }
        }

        .trigger-wrap {
            display: flex;
            background: red;

            .trigger {
                font-size: 20px;
                color: #fff;
                padding: 0 24px;
                cursor: pointer;
                transition: background-color 0.3s ease;
            }
        }

        .layout-header-menu {
            width: 100%;
            padding-left: 60px;
            border: none;
            line-height: 54px;
            height: 54px;
            background: transparent;
            color: #d6d6d6;
            font-size: 16px;
            font-family: 'DM Sans', sans-serif;

            .ant-menu-item.ant-menu-item-selected {
                .top-menu-text {
                    color: #fff;
                }
            }

            :deep(.ant-menu-item) {
                &::after {
                    content: unset !important;
                }
            }

            :deep(.ant-menu-item.ant-menu-item-active) {
                color: #fff;
            }

            .top-menu-text {
                text-transform: uppercase;
                letter-spacing: 0.5px !important;
            }
        }
    }

    // .layout-content-class {
    //     height: calc(100vh - 82px);
    // }
}

.expand-close {
    background: #fff;
    color: #a8aec4;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 13px;
    box-shadow: 0px 0px 5px 2px #e3e3e3;
    position: fixed;
    top: 50%;
    left: 295px;
    transform: translate(-30%, -50%);
    z-index: 99;

    span {
        margin-right: 2px;
    }
}

.expand-closev1 {
    background: #fff;
    color: #a8aec4;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 13px;
    box-shadow: 0px 0px 5px 2px #e3e3e3;
    position: fixed;
    top: 50%;
    left: 75px;
    transform: translate(-30%, -50%);
    z-index: 99;

    span {
        margin-left: 2px;
    }
}

.zr-menu-account-block {
    height: 100%;
    display: flex;
    align-items: center;
    padding-right: 24px;
    min-width: 364px;
    max-width: 364px;
    flex-shrink: 0;
    transition: opacity 0.4s ease;

    .ant-divider-vertical {
        margin: 0 20px;
        height: 34px;
        background-color: rgba(#fff, 0.3);
    }

    :deep(.ant-dropdown) .ant-dropdown-menu-title-content .anticon {
        vertical-align: bottom;
    }

    .zr-menu-account-content {
        height: 100%;
        display: flex;
        align-items: center;
        color: #fff;
        padding-right: 15px;
        width: 100%;

        .zr-menu-account-user-icon {
            font-size: 16px;
            margin-right: 6px;
        }

        .zr-menu-account-text-arrow {
            .zr-menu-account-text {
                font-size: 16px;
                font-family: 'DM Sans', sans-serif;
                max-width: 250px;
                min-width: 250px;
                line-height: 22px;
                height: 22px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .zr-menu-account-sub-text {
                font-size: 14px;
                font-family: 'DM Sans', sans-serif;
                max-width: 250px;
                min-width: 250px;
                line-height: 20px;
                height: 20px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: rgba($color: #fff, $alpha: 0.6);
            }
        }

        .zr-menu-account-arrow-icon {
            font-size: 12px;
            margin-left: 5px;
        }
    }

    .zr-menu-account-button {
        min-width: 34px;
        max-width: 34px;
        width: 34px;
        height: 34px;
        border-radius: 8px;
        padding: 0;
        outline: none;
        border: none;
        transition: none;

        .anticon :deep(svg) {
            font-size: 34px;
        }
    }

    :deep(.ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-popup) {
        border-radius: 2px;

        .ant-dropdown-menu-vertical {
            padding: 1px 0;
            border-radius: 2px;

            .ant-dropdown-menu-item {
                height: 32px;
                margin: 4px;

                &:hover {
                    background-color: #f5f7f9;
                    border-radius: 2px;
                }
            }
        }
    }
}

.menu-wrap {
    padding: 1px 0;
    border-radius: 2px;

    :deep(.ant-dropdown-menu-item) {
        height: 32px;
        margin: 4px;

        &:last-of-type {
            border-bottom-right-radius: 2px;
            border-bottom-left-radius: 2px;
        }

        &:first-of-type {
            border-top-right-radius: 2px;
            border-top-left-radius: 2px;
        }

        &:hover {
            background-color: #f5f7f9;
            border-radius: 2px;
        }

        .ant-dropdown-menu-title-content {
            height: 100%;
        }
    }

    :deep(.ant-dropdown-menu-submenu) {
        height: 32px;
        margin: 4px;

        .ant-dropdown-menu-submenu-title {
            height: 100%;
        }

        &:hover {
            background-color: #f5f7f9;
            border-radius: 2px;
        }
    }
}

.account-dropdown-menu-text {
    margin-right: 8px;
    margin-left: 8px;
}

// antd selector css样式
.zr-menu-company {
    display: flex;

    :deep(.ant-select-selector) {
        height: 40px;
        color: #fff;
        border-radius: 8px;
        background-color: #1f63c7;
        border: none;
        padding: 0 8px;

        .ant-select-selection-item {
            height: 100%;
            line-height: 40px;
            z-index: 1;
        }

        .ant-select-selection-search {
            left: 8px;

            input {
                height: 100%;
            }
        }

        .anticon.anticon-down.ant-select-arrow-icon {
            color: #1a78c3;
        }
    }

    :deep(.ant-select-arrow) {
        color: #fff;
        width: 20px;
        height: 20px;
        top: calc(50% - 4px);
        right: 8px;
        z-index: 0;
        // .anticon svg{
        //     font-size: 20px;
        // }
    }

    :deep(.ant-select) {
        width: 251px;

        .ant-select-dropdown {
            border-radius: 2px;
        }

        .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
            color: #004fc1;
            background-color: #f5f7f9;
        }

        .ant-select-item {
            margin: 2px 4px;
            border-radius: 4px;
            padding-left: 8px;
            padding-right: 8px;
            height: 40px;

            .ant-select-item-option-content {
                height: 30px;
                line-height: 30px;
            }

            &:hover {
                background-color: #f5f7f9;
            }

            &:first-of-type {
                margin-top: 0;
            }

            &:last-of-type {
                margin-bottom: 0;
            }
        }

        &.ant-select-focused.ant-select-open .ant-select-arrow {
            transform: rotate(180deg);
        }

        &.ant-select-disabled .ant-select-selector {
            color: #fff;
            background: #1f63c7;
        }
    }
}

:deep(.ant-layout-sider) {
    scrollbar-width: none !important;

    &::-webkit-scrollbar {
        display: none !important;
    }
}

.sidbar-menu {
    height: 100%;

    &.ant-menu {
        :deep(.ant-tooltip.ant-menu-inline-collapsed-tooltip) {
            display: none !important;
        }
    }

    :deep(.ant-menu-submenu:hover) {
        &.ant-menu-submenu-selected {
            .ant-menu-submenu-title {

                .ant-menu-title-content,
                .ant-menu-submenu-arrow {
                    color: #004fc1;
                }
            }
        }

        .ant-menu-submenu-title {

            .ant-menu-title-content,
            .ant-menu-submenu-arrow {
                color: #262626;
            }
        }
    }

    :deep(.ant-menu-submenu-active .ant-menu-submenu-title),
    :deep(.ant-menu-item-active .ant-menu-title-content) {
        font-weight: 400;
        display: inline-block;
        vertical-align: middle;
    }

    :deep(.ant-menu-submenu-selected) {
        .ant-menu-submenu-title {

            // background-color: #f5f7f9;
            // margin-left: 8px;
            // margin-right: 8px;
            // width: calc(100% - 16px);
            .ant-menu-title-content {
                font-weight: 700;
                font-family: 'DM Sans', sans-serif;
            }

            .ant-menu-submenu-arrow {
                color: #004fc1;
            }
        }
    }

    :deep(.ant-menu-submenu-title) {
        margin: 0;
        // margin-left: -1px;
        padding-top: 0;
        padding-bottom: 0;
        height: 58px;
        line-height: 58px;
        padding-left: 22px !important;
        font-size: 14px;
        font-family: 'DM Sans', sans-serif;
        // font-weight: 400;
        margin-left: 8px;
        margin-right: 8px;
        width: calc(100% - 16px);

        .anticon {
            font-size: 18px;
            margin-right: 6px;
        }

        .ant-menu-submenu-arrow {
            color: #262626;

            &::before {
                width: 8px;
                transform: rotate(-42deg) translateX(3.5px);
            }

            &::after {
                width: 8px;
                transform: rotate(42deg) translateX(-3.5px);
            }
        }

        &:hover {
            // color: #c2121e;
            background-color: #f5f7f9;
            color: #262626;
            border-radius: 4px;

            .ant-menu-submenu-arrow {
                color: #262626;
            }
        }
    }

    :deep(.ant-menu-item) {
        margin: 4px 8px 4px 8px;
        padding-top: 4px;
        padding-bottom: 4px;
        padding-left: 64px !important;
        font-weight: 400;
        font-size: 14px;
        font-family: 'DM Sans', sans-serif;
        margin-left: 8px;
        margin-right: 8px;
        width: calc(100% - 16px);
        border-radius: 4px;

        &:hover {
            // color: #c2121e;
            background-color: #f5f7f9;
            color: #262626;
        }
    }

    // :deep(.ant-menu-submenu-title:hover),
    :deep(.ant-menu-item-selected .ant-menu-title-content) {
        font-weight: 700;
        font-family: 'DM Sans', sans-serif;
    }

    :deep(.menuList1-wrap.ant-menu-item) {
        margin: 0px 8px;
        padding-top: 0;
        padding-bottom: 0;
        height: 58px;
        line-height: 58px;
        padding-left: 22px !important;

        .anticon {
            font-size: 18px;
            margin-right: 6px;
        }

        .ant-menu-title-content {
            margin: 0;
            font-size: 14px;
            font-family: 'DM Sans', sans-serif;
        }
    }

    :deep(.ant-menu-submenu-selected),
    :deep(.ant-menu-item-selected) // :deep(.ant-menu-item-active),

    // :deep(.ant-menu-submenu-title:hover),
    // :deep(.ant-menu-submenu-active .ant-menu-submenu-title),
    // :deep(.ant-menu-submenu-open .ant-menu-submenu-title)
        {
        .menu-icon {
            &.i-112 {
                background-image: url('@/assets/image/icon/icon_Sales_blue.png');
            }

            &.i-96 {
                background-image: url('@/assets/image/icon/icon_Sales_blue.png');
            }

            &.i-81 {
                background-image: url('@/assets/image/icon/icon_Bank_Reconciliation_blue.png');
            }

            &.i-82 {
                background-image: url('@/assets/image/icon/icon_Setting_blue.png');
            }

            &.i-91 {
                background-image: url('@/assets/image/icon/icon_Purchase_blue.png');
            }

            &.i-108 {
                background-image: url('@/assets/image/icon/icon_General_Entry_blue.png');
            }

            &.i-111 {
                background-image: url('@/assets/image/icon/icon_Reporting_blue.png');
            }

            &.i-119 {
                // background-image: url('@/assets/image/icon/icon_Task_grey.png');
                background-image: url('@/assets/image/icon/icon_Bank_Reconciliation_blue.png');
            }

            //TODO:Chen
            &.i-101 {
                background-image: url('@/assets/image/icon/icon_Help_blue.png');
            }

            &.i-115 {
                background-image: url('@/assets/icons/icon_Account_blue.svg');
            }

            &.i-120 {
                background-image: url('@/assets/image/icon/icon_Massive_blue.png');
            }

            &.i-121 {
                background-image: url('@/assets/image/icon/icon_Payroll_blue.png');
            }

            &.i-122 {
                background-image: url('@/assets/image/icon/<EMAIL>');
            }
        }

        // .ant-menu-title-content,
        // .ant-menu-submenu-arrow{
        //     color: #004FC1;
        // }
    }

    :deep(.ant-menu-submenu-open .ant-menu-submenu-title .ant-menu-submenu-arrow) {
        &::before {
            transform: rotate(42deg) translateX(3.5px);
        }

        &::after {
            transform: rotate(-42deg) translateX(-3.5px);
        }
    }
}

.subMenuSty {
    left: 85px !important;
}

.menu-icon {
    width: 34px;
    height: 34px;
    margin-right: 8px;
    display: inline-block;
    vertical-align: middle;
    background-size: cover;
    background-repeat: no-repeat;

    &.i-112 {
        background-image: url('@/assets/image/icon/icon_Sales_grey.png');
    }

    &.i-96 {
        background-image: url('@/assets/image/icon/icon_Sales_grey.png');
    }

    &.i-82 {
        background-image: url('@/assets/image/icon/icon_Setting_grey.png');
    }

    &.i-81 {
        background-image: url('@/assets/image/icon/icon_Bank_Reconciliation_grey.png');
    }

    &.i-91 {
        background-image: url('@/assets/image/icon/icon_Purchase_grey.png');
    }

    &.i-108 {
        background-image: url('@/assets/image/icon/icon_General_Entry_grey.png');
    }

    &.i-111 {
        background-image: url('@/assets/image/icon/icon_Reporting_grey.png');
    }

    &.i-119 {
        // background-image: url('@/assets/image/icon/icon_Task_grey.png');
        background-image: url('@/assets/image/icon/icon_Bank_Reconciliation_grey.png');
    }

    //TODO:Chen
    &.i-101 {
        background-image: url('@/assets/image/icon/icon_Help_grey.png');
    }

    &.i-115 {
        background-image: url('@/assets/icons/icon_Account_grey.svg');
    }

    &.i-120 {
        background-image: url('@/assets/image/icon/icon_Massive_grey.png');
    }

    &.i-121 {
        background-image: url('@/assets/image/icon/icon_Payroll_grey.png');
    }

    &.i-122 {
        background-image: url('@/assets/image/icon/<EMAIL>');
    }
}

.main-content-class {
    padding: 8px;
    overflow: hidden;
    height: calc(100vh - 82px);
}

.fixed-widgets {
    position: fixed;
    right: 10px;
    bottom: 80px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    width: 65px;
    height: 65px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 4px 12px #********;

    img {
        border-radius: 50%;
        padding: 5px;
        user-select: none;
        -webkit-user-drag: none;
    }
}

.menu-icon-guide {
    width: 34px;
    height: 34px;
    margin-right: 16px;
    display: inline-block;
    vertical-align: middle;
    background-size: cover;
    background-repeat: no-repeat;

    &.i-1 {
        background-image: url('@/assets/image/icon/icon_Sales_grey.png');
    }

    &.i-2 {
        background-image: url('@/assets/image/icon/icon_Setting_grey.png');
    }

    &.i-3 {
        background-image: url('@/assets/image/icon/icon_Bank_Reconciliation_grey.png');
    }

    &.i-4 {
        background-image: url('@/assets/image/icon/icon_Purchase_grey.png');
    }

    &.i-5 {
        background-image: url('@/assets/image/icon/icon_Sales_grey.png');
    }
}

.reconstruction-text {
    position: absolute;
    bottom: 20px;
    left: 60px;
    padding: 00 10px 0;
    color: #004fc1;
    font-size: 16px;
}
</style>
