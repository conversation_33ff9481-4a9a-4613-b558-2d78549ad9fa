<!-- @format -->

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {type FormInstance, message} from 'ant-design-vue'
import {useStore} from 'vuex'
const emits = defineEmits(['dismiss', 'after-auth'])

const store = useStore()

const props = defineProps({
    initialData: {
        type: Object,
        default: () => ({}),
    },
})
console.log('props.initialData', props.initialData)
const i18n: Composer = i18nInstance.global
const authCode = ref('')
const send = async () => {
    const response = await store.dispatch('UserManagementStore/sendGoogleAuthCode', {
        google_secret: props.initialData.user.google_secret,
        google_token: authCode.value,
    })
    if (response.data.status === 'SUCCESS') {
        message.success({content: 'success'})
        emits('after-auth', true)
    } else {
        message.error({content: 'failed'})
        emits('after-auth', false)
    }
}

const cancel = () => {
    emits('dismiss')
}
</script>
<template>
    <div class="page-container-changepwd_form">
        <a-form :layout="'vertical'" class="form-box" autocomplete="off">
            <a-form-item>
                <a-input v-model:value="authCode" />
            </a-form-item>
        </a-form>
    </div>
    <a-divider class="footer-divider" />
    <footer>
        <a-button @click="cancel" size="small" class="cancel-button" shape="round">
            {{ i18n.t('commonTag.cancel') }}
        </a-button>
        <a-button size="small" type="primary" shape="round" @click="send">
            {{ i18n.t('commonTag.confirm') }}
        </a-button>
    </footer>
</template>

<style lang="scss" scoped>
.icon-exclamation {
    margin: 0;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #aab9cb;
    margin-left: 5px;
}
.page-container-changepwd_form {
    padding: 20px 24px 0px;
    .form-box {
        // grid-column-gap: 10px;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: repeat(2, 1fr);
        grid-template-areas:
            'newpassword'
            'repassword';
        .form-box-item-newpassword {
            grid-area: newpassword;
        }
        .form-box-item-repassword {
            grid-area: repassword;
        }
    }
}
.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}
footer {
    padding: 12px 24px;
    text-align: right;
    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }
    .ant-btn {
        min-width: 65px;
    }
    .ant-btn + .ant-btn {
        margin-left: 8px;
    }
}
</style>
