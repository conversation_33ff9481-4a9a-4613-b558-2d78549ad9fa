<!-- @format -->

<script setup lang="ts">
import { computed, onMounted, ref, reactive } from 'vue'
import { UserCompany, Ap_Integration, Ar_Integration } from '@/lib/storage'
import { useStore } from 'vuex'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import type { ColumnsType } from 'ant-design-vue/es/table'
import { message } from 'ant-design-vue'
import FileSaver from 'file-saver'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ApInvoiceComponentIntegration from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegration.vue'
import ApInvoiceComponentIntegrationMx from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegrationMx.vue'
import ArInvoiceComponent from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponent.vue'
import ArInvoiceComponentMX from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponentMX.vue'
import GlComponent from '@/components/bookkeepingComponents/GlComponents/GlComponent.vue'
import { ExclamationCircleFilled } from '@ant-design/icons-vue'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'
import * as _ from 'lodash'

const apIntegration: any = Ap_Integration.get() ?? 0
const arIntegration: any = Ar_Integration.get() ?? 0
const emits = defineEmits(['dismiss', 'reverse'])
const i18n: Composer = i18nInstance.global

const infoTableColumns: ColumnsType = [
    {
        title: i18n.t('RcInfoTable.account'), //'Bank Account',
        dataIndex: ['entity', 'bank_account'],
        key: 'bankAccount',
        align: 'center',
        width: '15%',
    },
    {
        title: i18n.t('RcInfoTable.date'), //Payment Date',
        dataIndex: ['entity', 'date'],
        key: 'date',
        align: 'center',
        minWidth: 40,
    },
    {
        title: i18n.t('RcInfoTable.desc'), //'Description',
        dataIndex: ['entity', 'description'],
        key: 'description',
        align: 'center',
        width: '15%',
        minWidth: 50,
        ellipsis: true,
    },
    {
        title: i18n.t('RcInfoTable.debit'), //'Debit',
        dataIndex: ['entity', 'deposit'],
        key: 'deposit',
        align: 'center',
        minWidth: 40,
        width: '10%',
    },
    {
        title: i18n.t('RcInfoTable.credit'), //'Credit',
        dataIndex: ['entity', 'withdrawal'],
        key: 'withdrawal',
        align: 'center',
        minWidth: 40,
        width: '10%',
    },
    {
        title: i18n.t('RcInfoTable.balance'), //'Balance',
        dataIndex: ['entity', 'balance'],
        key: 'balance',
        align: 'center',
        minWidth: 40,
        width: '10%',
    },
]
const invoiceTableColumns: ColumnsType = [
    {
        title: i18n.t('RcInfoTable.invoiceNo'), //'Invoice No.',
        dataIndex: 'invoice_no',
        key: 'invoice_no',
        align: 'center',
        ellipsis: true,
        minWidth: 50,
        width: '15%',
    },
    {
        title: i18n.t('RcInfoTable.comments'), //'Comments',
        dataIndex: ['entity', 'invoice_comments'],
        key: 'invoice_comments',
        align: 'center',
        minWidth: 50,
        ellipsis: true,
        width: '15%',
    },
    {
        title: i18n.t('RcInfoTable.paymentDt'), //'Payment Date',
        dataIndex: ['entity', 'invoice_due_date'],
        key: 'invoice_due_date',
        align: 'center',
        minWidth: 20,
        width: '10%',
    },
    {
        title: i18n.t('RcInfoTable.fee'), //'Total Fee',
        dataIndex: ['entity', 'total_fee'],
        key: 'total_fee',
        align: 'center',
        minWidth: 20,
        width: '10%',
    },
    {
        title: i18n.t('RcInfoTable.amount'), //'BR Amount',
        dataIndex: 'reconcile_amount', // BR Amount?
        key: 'esBrAmount',
        align: 'center',
        minWidth: 40,
        width: '10%',
    },
]
const userCompany: any = UserCompany.get() || []
const glCurrent = ref({})
const showGlDialog = ref(false)
const showReversePostingDate = ref(false)
const reverseButtonClicked = ref(false)
const reversePostingDate = ref('')
const showReverseLoading = ref(false)
const confirmationWrap = ref<HTMLElement | null>(null)
const store = useStore()
const glOperationMode = ref('apDetail')
const fetchGlList = (payload: any) => store.dispatch('GlStore/fetchGlListV1', payload)
const state = reactive({})
const props = defineProps({
    infoTableData: {
        type: Object,
    },
    invoicesList: {
        type: Array,
        default: [] as any[],
    },
    brType: {
        type: Number,
    },
})

const paging = reactive({
    index: 1,
    size: 6,
    total: props.invoicesList.length,
})
const showDetails = ref(false)
const current = ref({} as any)
const br_type = ref(0)
const invoicesTableData = computed(() => props.invoicesList)
console.log('computed', invoicesTableData)

const enableReverse = computed(() => props.infoTableData?.max_reverse_number > props.infoTableData?.reverse_number)
const pageChange = (page: any, pageSize: number) => {
    paging.index = page
    paging.size = pageSize
    console.log(invoicesTableData)
}

const getInvoiceComponent = computed(() => {
    let comp = ''
    switch (br_type.value.toString()) {
        case '0':
        case '3':
            comp = 'AR'
            break
        case '1':
        case '2':
        case '5':
        case '12':
        case '13':
            comp = 'AP'
            break
        default:
            comp = ''
    }
    return comp
})

const invioceDialog = (data: any) => {
    br_type.value = data.br_type
    if (![0, 1, 2, 3, 5, 12, 13].includes(br_type.value)) {
        return
    }
    current.value = { ...data.entity, reverse_number: data.reverse_number, max_reverse_number: data.max_reverse_number }
    // invoiceId.value =  current.value.id
    // console.log('弹窗2---',current.value);

    showDetails.value = true
}

const seeDetail = async (val: any) => {
    const query = {
        company: userCompany[0].code,
        document_no: val,
    }

    const response = await fetchGlList(query)

    glCurrent.value = _.cloneDeep(response.data)
    showGlDialog.value = true
}

const glDismiss = () => {
    showGlDialog.value = false
    glCurrent.value = {}
}

const popReversePostingPate = (detail: any) => {
    showReversePostingDate.value = true
}

const dismissReversePostingDate = () => {
    showReversePostingDate.value = false
}

const dismissDetails = (action: any) => {
    showDetails.value = false
    emits('dismiss')
    // br_type.value = 0
    // current.value = {}
}
const cancel = () => {
    emits('dismiss')
}
const reverse = (detail: any) => {
    console.log(detail)
    if (apIntegration === 1) {
        popReversePostingPate(detail)
    } else {
        emits('reverse', detail)
    }
}
const reverseIntegration = (detail: any) => {
    showReversePostingDate.value = false
    reverseButtonClicked.value = true
    detail.posting_date = reversePostingDate.value
    emits('reverse', detail)
}
// Computed properties for original file content
const originalFileData = computed(() => {
    if (props.infoTableData && props.infoTableData.original_file_content) {
        try {
            // Parse the JSON string to get the object
            const fileContent =
                typeof props.infoTableData.original_file_content === 'string'
                    ? JSON.parse(props.infoTableData.original_file_content)
                    : props.infoTableData.original_file_content

            return {
                pdfName: fileContent?.original_pdf_name || '',
                xmlName: fileContent?.original_xml_name || '',
                pdfContent: fileContent?.original_pdf_content || '',
                xmlContent: fileContent?.original_xml_content || '',
            }
        } catch (error) {
            console.error('Error parsing original_file_content:', error)
            return {pdfName: '', xmlName: '', pdfContent: '', xmlContent: ''}
        }
    }
    return {pdfName: '', xmlName: '', pdfContent: '', xmlContent: ''}
})
// Method to open PDF content in a new tab
const openPdfContent = (fileData: any) => {
    if (!fileData || !fileData.pdfContent) {
        message.error(i18n.t('update.noPdfContent')) //'No PDF content available'
        return
    }

    try {
        // Decode base64 content
        const decodedContent = atob(fileData.pdfContent)

        // Create a blob from the decoded content
        const blob = new Blob([decodedContent], {type: 'application/pdf'})

        // Create a URL for the blob
        const url = URL.createObjectURL(blob)

        // Open the URL in a new tab
        window.open(url, '_blank')
    } catch (error) {
        console.error('Error opening PDF content:', error)
        message.error(i18n.t('update.excelOpenFailed')) //'Failed to open PDF content'
    }
}

// Method to download XML content as a file
const downloadXmlContent = (fileData: any) => {
    if (!fileData || !fileData.xmlContent) {
        message.error(i18n.t('update.noXmlContent')) //'No XML content available'
        return
    }

    try {
        // Decode base64 content
        const decodedContent = atob(fileData.xmlContent)

        // Create a blob from the decoded content
        const blob = new Blob([decodedContent], {type: 'text/xml'})

        // Generate filename using the xmlName or a default name
        const fileName = fileData.xmlName || 'document.xml'

        // Use FileSaver to download the file
        FileSaver.saveAs(blob, fileName)
    } catch (error) {
        console.error('Error downloading XML content:', error)
        message.error(i18n.t('update.excelDownloadFailed')) //'Failed to download XML content'
    }
}
onMounted(() => {
    console.log('rcinfo', invoicesTableData.value, props.invoicesList, props.infoTableData)
    reversePostingDate.value = props.infoTableData?.posting_date
})
</script>
<template>
    <div>
        <div class="page-wrap">
            <!-- <div class="info-table">
            <h4>General Information</h4>
            <a-table :dataSource="props.infoTableData" :columns="infoTableColumns">
                <template #bodyCell="{column, record}">
                    <template v-if="column.dataIndex === 'deposit'">
                        <span v-if="[0, 2].includes(record.br_type)">{{ (+record.deposit).toFixed(2) }}</span>
                    </template>
<template v-else-if="column.dataIndex === 'withdrawal'">
                        <span v-if="[1, 3].includes(record.br_type)">{{ (+record.withdrawal).toFixed(2) }}</span>
                    </template>
<template v-else-if="column.dataIndex === 'balance'">
                        <span>{{ (+record.balance).toFixed(2) }}</span>
                    </template>
</template>
</a-table>
</div> -->

            <div class="invoice-list-table">
                <!-- <h4>Invoices List</h4> -->
                <a-table :dataSource="invoicesTableData" :columns="invoiceTableColumns">
                    <template #bodyCell="{ column, text, record }">
                        <template v-if="column.dataIndex === 'invoice_no'">
                            <a @click="invioceDialog(record)">{{ record.entity?.invoice_no || '' }}</a>
                        </template>
                        <template v-else-if="column.dataIndex === 'total_fee'">
                            <span>{{ (+record.total_fee).toFixed(2) }}</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'esBrAmount'">
                            <span>{{ (+record.esBrAmount).toFixed(2) }}</span>
                        </template>
                        <!-- a place holder for text-->
                        <div v-if="false">{{ text }}</div>
                    </template>
                </a-table>
                <!-- <div class="page">
                <a-pagination
                    v-model:current="paging.index"
                    show-quick-jumper
                    :total="paging.total"
                    @change="pageChange"
                />
                <span>{{ i18n.t('bkApInvoice.total') }} {{ paging.total }} {{ paging.total > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span>
            </div> -->
            </div>
            <!-- <a-divider class="footer-divider" /> -->
            <div v-if="apIntegration != 1">
                <div style="font-size: 14px; margin-left: 10px; font-weight: bold">
                    {{ i18n.t('ArComponents.JE') }}:
                    <a class="document-item-link" style="font-size: 14px; margin-left: 10px; font-weight: 100"
                        @click="seeDetail(props.infoTableData?.document_no)">{{ props.infoTableData?.document_no }}</a>
                    <a class="document-item-link" style="margin-left: 10px; font-size: 14px; font-weight: 100"
                        @click="seeDetail(props.infoTableData?.reverse_document_no)">{{
                            props.infoTableData?.reverse_document_no
                        }}</a>
                </div>
            </div>
            <div v-if="apIntegration === 1">
                <div style="font-size: 14px; margin-left: 10px; font-weight: bold">
                    {{ i18n.t('ArComponents.SAP') }}:
                    <a style="font-size: 14px; margin-left: 10px; font-weight: 100">{{
                        props.infoTableData?.sap_document_no
                    }}</a>
                    <a style="margin-left: 10px; font-size: 14px; font-weight: 100">{{
                        props.infoTableData?.sap_reverse_document_no
                    }}</a>
                </div>
            </div>
            <div v-if="originalFileData.pdfName || originalFileData.xmlName">
                <div style="font-size: 14px; margin-left: 10px; font-weight: bold">
                    {{ i18n.t('RcInfoTable.brDoc') }}:
                    <a
                        style="font-size: 14px; margin-left: 10px; font-weight: 100"
                        @click="() => openPdfContent(originalFileData)"
                        >{{ originalFileData.pdfName }}</a
                    >
                    <a
                        style="margin-left: 10px; font-size: 14px; font-weight: 100"
                        @click="() => downloadXmlContent(originalFileData)"
                        >{{ originalFileData.xmlName }}</a
                    >
                </div>
            </div>
        </div>
        <a-divider class="footer-divider" />
        <footer>
            <a-button @click="cancel" size="small" class="cancel-button" shape="round">
                {{ $t('commonTag.cancel') }}
            </a-button>
            <a-button :disabled="!enableReverse || reverseButtonClicked" @click="reverse(props.infoTableData)" size="small" type="primary"
                shape="round">
                {{ enableReverse ? $t('commonTag.reverse') : 'reversed' }}
            </a-button>
        </footer>
        <a-modal :title="$t('bkApInvoice.readonly')" v-model:visible="showDetails" :footer="null" destroyOnClose
            :closeable="true" :width="'1110px'" :bodyStyle="{ padding: '20px' }" :wrapClassName="'modal-wrap'">
            <ap-invoice-component v-if="getInvoiceComponent === 'AP' && apIntegration !== 1" operation-mode="" pageType="bkHistory"
                :current-invoice="current" :invoice-id="current.id.toString()" :readonly-mode="true"
                @dismiss="dismissDetails">
            </ap-invoice-component>
            <ap-invoice-component-integration-mx v-if="getInvoiceComponent === 'AP' && apIntegration === 1 && INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)" operation-mode="" pageType="bkHistory"
                :current-invoice="current" :invoice-id="current.id.toString()" :readonly-mode="true"
                @dismiss="dismissDetails">
            </ap-invoice-component-integration-mx>
            <ap-invoice-component-integration v-if="getInvoiceComponent === 'AP' && apIntegration === 1 && !INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)" operation-mode="" pageType="bkHistory"
                :current-invoice="current" :invoice-id="current.id.toString()" :readonly-mode="true"
                @dismiss="dismissDetails">
            </ap-invoice-component-integration>
            <ar-invoice-component v-if="getInvoiceComponent === 'AR' && arIntegration !== 1" operation-mode="" :current-invoice="current"
                :invoice-id="current.id.toString()" :readonly-mode="true" @dismiss="dismissDetails">
            </ar-invoice-component>
            <ar-invoice-component-m-x v-if="getInvoiceComponent === 'AR' && arIntegration === 1" operation-mode="" :current-invoice="current"
                :invoice-id="current.id.toString()" :readonly-mode="true" @dismiss="dismissDetails">
            </ar-invoice-component-m-x>
        </a-modal>
        <a-modal :title="i18n.t('gl.readonly')" v-model:visible="showGlDialog" :footer="null" destroyOnClose
            :closeable="true" :width="1000" style="z-index: 999" :dialogStyle="{ top: '10px' }"
            :bodyStyle="{ padding: '10px 24px 24px' }">
            <gl-component :current-invoice="glCurrent" :readonly-mode="true" :operation-mode="glOperationMode"
                @dismiss="glDismiss"></gl-component>
        </a-modal>
        <div ref="confirmationWrap">
            <a-modal v-model:visible="showReversePostingDate" centered destroyOnClose :get-container="confirmationWrap"
                :width="480" :closable="false" :confirm-loading="showReverseLoading" :wrapClassName="'modal-wrap'"
                :ok-text="i18n.t('commonTag.confirm')" :ok-type="'primary'" :ok-button-props="{ shape: 'round' }"
                :cancel-text="i18n.t('commonTag.cancel')"
                :cancel-button-props="{ shape: 'round', ghost: true, type: 'primary' }" @ok="reverseIntegration(props.infoTableData)"
                @cancel="dismissReversePostingDate">
                <template #title>
                    <div class="confirm-title-wrap">
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('bkCommonTag.confirmation') }}
                        </div>
                    </div>
                </template>
                <div class="confirmation-content-text">{{ i18n.t('ApComponents.confirm') }}</div>
                <div class="date-select">
                    <a-date-picker v-model:value="reversePostingDate" :allowClear="false" :inputReadOnly="true"
                        format="YYYY-MM-DD" valueFormat="YYYY-MM-DD">
                        <template #suffixIcon>
                            <svg-icon name="icon_date"></svg-icon>
                        </template>
                    </a-date-picker>
                </div>
            </a-modal>
        </div>
        <!-- <a-modal v-model:visible="showReversePostingDate" centered destroyOnClose
        :min-width="480" :closable="false" :wrapClassName="'modal-wrap'" :ok-text="i18n.t('commonTag.confirm')"
        :ok-type="'primary'" :ok-button-props="{ shape: 'round' }" :cancel-text="i18n.t('commonTag.cancel')"
        :cancel-button-props="{ shape: 'round', ghost: true, type: 'primary' }" @ok="reverseIntegration(props.infoTableData)"
        @cancel="chargeFeedismiss">
            <template #title>
                <a-form class="confirm-title-wrap" :model="reversePostingDate" ref="reverseformRef">
                    <a-row>
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('esMain.postingDate') }}
                        </div>
                        <div class="modal-body">
                            <div class="fetch-bank-date-wrap">
                                <a-date-picker
                                    v-model:value="reversePostingDate.posting_date"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                >
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </div>
                        </div>
                    </a-row>
                </a-form>
            </template>
        </a-modal> -->
    </div>
</template>
<style lang="scss" scoped>
.info-table {
    margin-bottom: 16px;
}

.info-table h4,
.invoice-list-table h4 {
    margin: 0 0 12px 0;
    font-size: 20px;
    line-height: 22px;
    font-weight: 700;
    color: #262626;
}

.page {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-height: 38px;
    margin-top: 10px;

    .table-pagination {
        padding: 0 20px 0 0;
        margin: 0;
    }
}

.page-wrap {
    padding: 24px 20px;
}

.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}
.confirm-title-wrap {
    padding-top: 15px;
    padding-bottom: 5px;

    .confirm-title-icon {
        color: #faad14 !important;
        font-size: 21px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .confirm-title-text {
        //  font-family: Calibri;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        font-weight: 400;
    }
}
.confirmation-content-text {
    // font-family: Calibri;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    font-weight: 400;
    padding-left: 64px;
    padding-top: 5px;
    // height: 75px;
}
.date-select {
    color: rgba(0, 0, 0, 0.65);
    min-width: 145px;
    width: 260px;
    max-width: 300px;
    padding-top: 5px;
    padding-left: 64px;
    flex: 1;
}
footer {
    padding: 12px 24px;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn {
        min-width: 65px;
    }

    .ant-btn+.ant-btn {
        margin-left: 8px;
    }
}
</style>
