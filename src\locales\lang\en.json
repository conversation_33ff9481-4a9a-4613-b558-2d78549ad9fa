{"login": {"title": "Get Start", "logIn": "Log in", "logOut": "Log out", "english": "English", "chinese": "Chinese", "language": "Languages", "username": "Username", "password": "Password", "msgInputUsername": "Please enter username", "msgInputPassword": "Please enter password", "msgInputAccount": "Please enter account", "serverError": "Server error", "changePwd": "Reset Password", "gQr": "Google Authenticator", "newpassword": "Password", "repassword": "Re-enter Password", "pwdRule": "Must include at least 1 uppercase, 1 lowercase, 1 number, 1 special character", "pwdRule1": "Please enter characters without spaces", "inconsistentPassword": "Inconsistent password", "repasswordInput": "Please re-enter password"}, "mainPage": {"noLogin": "Not logged in"}, "homepage": {"times1": "Digital payroll'", "times2": "Admin Version", "title": "Easily start to pay employee, online with us.", "info": "NT Digital Payroll application offers a fully coverage from organization to payroll, a hyper-automated process enables the core commercial operation for maximizing your human capital value.", "titleBk1": "ESSENTIAL OPERATION", "titleBk2": "", "titleBk": "Hyper Automated Bookkeeping", "infoBk": "Our financial solution is made for North America Small businesses, Whether you’re a small business owner or self-employed, We make it easy to keep your financials better organized. Therefore, you can make better business decisions, focus on growth, and take the stress out of administration and compliance time.", "link": "Learn more"}, "router": {"uploadInvoice": "Massive Process", "woBillsInvoice": "Manual Booking", "invoiceFromPdf": "Invoice From Pdf", "invoiceHistory": "Listing", "accountReceivable": "Sales", "fullInvoice": "Manual Booking", "bankReconciliation": "Bank Reconciliation", "uploadStatement": "Connect", "EStatementFromPdf": "E-Statement From CSV", "main": "Reconcile", "common": "Setting", "customer": "Contact", "taxInformation": "Profile", "bankInformation": "Bank", "accountDescription": "CoA", "taxCalculation": "Tax Chart", "spotCurrency": "Spot Currency", "exchangeRate": "Exchange Rate", "supplier": "Supplier", "payStubs": "Pay Stubs", "history": "History", "commonCompany": "Company", "commonAccount": "Account", "gl": "General <PERSON><PERSON>", "glListing": "Listing", "glEntry": "Manual Entry", "fy": "Financial Year Start", "localCurrency": "Local Currency"}, "commonTag": {"tip": "Hint", "sapTip": "Sychronizing with SAP", "confirm": "Confirm", "cancel": "Cancel", "search": "Search", "new": "Add", "save": "Save", "edit": "Edit", "delete": "Delete", "action": "Operation", "actions": "Actions", "view": "View", "remark": "Remark", "back": "Back", "close": "Close", "serial": "Serial number", "status": "Status", "submit": "Submit", "send": "Send", "resend": "Resend", "download": "Download", "sendEmail": "Send Email", "columns": "Columns", "filter": "Filter", "msgInput": "Please enter", "msgSelect": "Please select", "reverse": "Reverse", "reverseAndRedo": "Reverse & Redo", "realize": "Realize", "print": "Print", "wihtreverse": "Without reverse", "enable": "Enable", "disable": "Disable"}, "columns": {"modalTitle": "Columns", "title": "Title", "operation": "Operation"}, "bkApInvoice": {"to": " ", "readonly": "View Invoice Detail", "create": "Create Invoice", "edit": "Invoice Edit", "date": "Date ", "totalCol": "Total ", "minFee": "From", "maxFee": "To", "invoiceNo": "Invoice Number", "invoiceComment": "Comments", "creator": "Creator", "createTime": "Create Time", "createDate": "Create Date", "issuerCol": "Issuer ", "invoiceType": "Invoice Type", "issuer": "Issuer", "total": "Total", "type": "Type", "br": "BR", "status": "Status", "payMethod": "Pay Method", "sapStatus": "SAP Status", "sapNotSent": "NOT SENT", "sapPending": "PARKED", "sapSending": "SENDING", "sapSentSuccess": "SENT SUCCESS", "sapSentFail": "SENT FAIL", "sapReversing": "RESVERSING", "sapReverseSuccess": "RESVERSE SUCCESS", "sapReverseFail": "RESVERSE FAIL", "referenceNo": "Reference", "dueDate": "Due Date", "postingDate": "Invoice Date", "balance": "Balance", "operation": "Action", "checkNo": "Check No", "checkPrintTime": "Execute Time", "printStatus": "Print Status", "printStatus0": "Not Printed", "printStatus1": "Printed", "brStatus0": "Unpaid", "brStatus": "All", "brStatus2": "Open", "submitToPay": "SUBMIT TO PAY", "payInProcess": "PAY IN PROCESS", "toBeReconciled": "TO BE RECONCILED", "parked": "Parked", "posted": "Posted", "notPaid": "Not Paid", "pmntApproved": "PMNT Approved", "pmntExecuted": "PMNT Executed", "paid": "Paid", "partialPaid": "Partial Paid", "reversed": "Reversed", "captured": "Captured", "created": "Created", "abandoned": "Abandoned", "scan": "<PERSON><PERSON>", "fetch": "<PERSON>tch", "upload": "Upload", "del": "Delete", "due": "Due", "level": "Level", "emailList": "Email List", "approvalStatus": "Status", "statusPending": "Pending", "statusApproved": "Approved", "statusRejected": "Rejected", "printApCheckWarning": "Your payment has been executed, please check.", "issueNameEmpty": "Please input issuer name"}, "bkApUpload": {"fileName": "File Name", "updateTime": "Update Time", "createTime": "Create Time", "creator": "Creator", "xmlStatus": "XML Status", "payMethod": "Pay Method", "comment": "Comment", "ocrStatus": "OCR Status", "scanned": "Scanned", "nonScanned": "Not scanned", "pending": "Pending", "delFile": "Delete File", "edit": "Edit", "createFile": "Create Invoice", "analyzeFile": "Analyze File", "viewDetail": "View Detail", "downloadInvoice": "Download Invoice", "editComment": "Edit Comment", "editCommentPlaceholder": "Please enter comment"}, "bkCustomer": {"company": "Company", "tel": "Tel.", "email": "Email", "receiver": "Receiver(Office)", "receiver01": "Contact Person", "address": "Address", "address01": "Address(Office)", "street": "Street", "city": "City", "province": "Province", "country": "Country", "postalCode": "Postal Code", "expenseAccount": "Expense Account", "operation": "Action", "createReceiverTitle": "Create Customer", "createAllReceiverTitle": "Create Contact", "accountType": "Type", "editReceiverTitle": "Edit Contact Detail", "msgPhrSelect": "Please select", "officeAddress": "Office Address", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "general": "General", "sameAsOffice": "Same as office", "itemNo": "#", "category": "Category", "businessKey": "Business Key", "debitReceipt": "Debit Receipt", "creditReceipt": "Credit Receipt", "save": "Save", "caterogyRule": "Please Select Category", "coaJson": "Mapping Code"}, "bkAccountingAndBanking": {"bpNo": "Business Partner Number", "bankType": "Pay Method", "countingAndBanking": "Banking", "transitNumber": "Transit Number", "branchNumber": "Branch Number", "bankAccount": "Bank Account", "receiverName": "Account Number", "routingNumber": "Routing Number", "IBANNumber": "IBAN Number", "swiftNumber": "SWIFT Number"}, "bkAp": {"invoiceHeader": "Invoice Header", "companyName": "Issued by", "companyAddr": "Company Address", "companyTel": "Company Tel.", "companyEmail": "Company Email", "companyGst": "GST No.", "companyQst": "QST No.", "companyGstHst": "GST/HST", "companyPst": "PST (QST/--)", "invoiceNo": "Invoice No.", "referenceNo": "Invoice Reference", "purpose": "Purpose", "purposeStandard": "Standard", "purposeProforma": "<PERSON><PERSON><PERSON>", "purposeCreditMemo": "Credit Memo", "purposeDebitMemo": "Debit Memo", "purposeDepositAndReturn": "Deposit And Return", "purposeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "purposeReturn": "Return", "purposeEE": "EE", "purposeSubsequentCreditMemo": "Subsequent Credit Memo", "poPlaceholder": "Please separate multiple PO with commas", "purchaseOrder": "Purchase Order", "plannedCostInvoice": "Planned Cost Invoice", "plannedCostInvoiceDes": "Use this option if the invoice should be verified against planned costs defined in the PO.", "poInputError": "Invalid PO format. Please use comma to separate multiple PO numbers", "poChooseIssuerFirst": "Please choose issuer first", "currency": "<PERSON><PERSON><PERSON><PERSON>", "date": "Create Date", "dueDate": "Due Date", "fixedDate": "Fixed Date", "afterDays": "After Days", "customerInfo": "Customer Information", "billToCompany": "Bill To Company", "billTo": "Bill <PERSON>", "billToReceiver": "Bill To Receiver", "billingStreet": "<PERSON>", "billToCity": "<PERSON>", "billToProvince": "Bill To Province", "billToZip": "<PERSON>", "billToTel": "<PERSON>.", "billToEmail": "Bill To Email", "shipToSameAddr": "Ship To the Same Address", "shipToCompany": "Ship To Company", "shipToReceiver": "Ship To Receiver", "shipToStreet": "Ship To Street", "shipToCity": "Ship To City", "shipToProvince": "Ship To Province", "shipToZip": "Ship To Zip", "shipToTel": "Ship To Tel.", "shipToEmail": "Ship To Email", "itemsDetail": "Items Detail", "itemNo": "#", "modelNumber": "PRODUCT/SERVICE", "description": "DESCRIPTION", "payMethod": "Pay Method", "addPayMethod": "Add Pay Method", "qty": "QTY", "unitPrice": " UNIT PRICE", "total": "NET AMOUNT", "type": "Type", "bankAccount": "Bank Account", "trevenueAccount": "TRevenue account", "accountingCategory": "G/L ACCOUNT", "amountAndTax": "Amount And Tax", "amount": "NET AMOUNT", "amountRule": "Amount", "shipping": "Shipping", "discount": "Discount", "totalTaxable": "Total Taxable", "tps": "GST/HST", "tvq": "QST", "tvp": "PST", "mxDiscount": "Discount", "mxIsr": "Income Tax Withholding", "mxIva": "VAT Withholding", "colIca": "ICA Withholding", "mxIsrThreshold": "Income Tax Withholding Threshold", "mxIvaThreshold": "VAT Withholding Threshold", "colIcaThreshold": "ICA Withholding Threshold", "msgChangedIsrRate": "Income Tax withholding rate has been changed.", "msgChangedIvaRate": "VAT withholding rate has been changed.", "msgChangedIcaRate": "ICA withholding rate has been changed.", "totalTax": "Total Tax", "totalCad": "Total(CAD)", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Balance", "accountAndType": "Account And Type", "invoiceComment": "Comments", "createArInvoice": "Create Ar Invoice", "viewInvoice": "View Invoice", "preview": "Preview", "postingDate": "Invoice Date", "apIntegrationPostingDate": "Posting Date", "create": "Save", "totalFee": "Total", "msgReferenceNoExisted": "Reference number already exists!", "msgReferenceCheckFail": "Reference No. repetition check failed. Please try again!", "from": "From", "addItemBtnTxt": "Add", "addItemBtnTxtInvoice": "Add Invoice Approve", "addItemBtnTxtPayment": "Add Payment Approve", "msgCreditMemoInvoiceBeNegative": "The amount for a Credit Memo invoice must be negative.", "paymentDelayDate": "Payment Date", "paymentDelaySelect": "Select Payment Term", "originalDocument": "Original Document", "costObject": "Cost Object", "taxCode": "Tax Code", "assigmentSapCostObject": "SAP Cost Object", "assigmentGlAccount": "G/L Account", "assigmentWbs": "WBS", "assigmentCostCenter": "Cost Center", "assigmentInternalOrder": "Internal Order", "assigmentProfitCenter": "Profit Center", "paymentExportColumnOperation": "OPERATION", "paymentExportColumnIssuerName": "ALIAS / RECIPIENT", "paymentExportColumnIsserId": "DESTINATION ACCOUNT", "paymentExportColumnImport": "IMPORT", "paymentExportColumnReferenceNo": "NUMERICAL REFERENCE", "paymentExportColumnInvoiceComments": "REFERENCE / CONCEPT", "paymentExportColumnReference": "REFERENCE", "paymentExportColumnInvoiceCurrency": "DIVIDE", "paymentExportColumnIva": "IVA", "paymentExportColumnDestinationRfc": "RFC DESTINATION", "paymentExportColumnBankAccount": "ORIGIN ACCOUNT"}, "bkCommonTag": {"confirmation": "Confirmation", "msgDeleteConfirm": "the data will be deleted!", "msgRequireRule": "Please input ", "msgLengthRule": "Length should be ", "msgEmailRule": "Please input the right Email", "msgSelectRule": "Please select ", "msgDeleteSelectConfirm": "The selected pdf files will be deleted!", "msgNumberRule": "Please input a valid number"}, "bkArInvoice": {"to": " ", "readonly": "View Billing Detail", "create": "Create Billing", "date": "Date ", "totalCol": "Total ", "minFee": "From", "maxFee": "To", "billingType": "Billing Type", "billingTypeInvoice": "Invoice", "billingTypeCreditNote": "Credit Note", "billingTypeDebitMemo": "Debit Note", "invoiceNo": "Billing #", "invoiceComment": "Comments", "creator": "Creator", "createTime": "Create Time", "createDate": "Create Date", "issuerCol": "Issuer ", "issuer": "Bill <PERSON>", "total": "Total", "type": "Type", "br": "Status", "referenceNo": "Reference", "dueDate": "Due Date", "postingDate": "<PERSON>", "balance": "Balance", "operation": "Action", "payerPayee": "Payer/Payee ", "brStatus0": "Open", "brStatus1": "Partial Paid", "brStatus2": "All", "brStatus3": "All", "sapStatus": "Booking Status", "search": "Search Bill To", "billing": "Customer Billing", "bill2Customer": "Billing to Customer", "pb": "Periodical Booking", "billingNumber": "Billing No", "referenceNumber": "Reference No", "currency": "<PERSON><PERSON><PERSON><PERSON>", "paymentDue": "Payment Due", "postDate": "Post Date", "billingDate": "Billing Date", "originalBillingNumber": "Original Billing Number", "due": "Due", "uuid": "UUID"}, "workTimeManager": {"projectName": "Project Name: ", "msgInput": "Please enter", "applicant": "Employee: ", "applicationDate": "Working Date: ", "approvalDate": "Approval Date: ", "status": "Status: ", "memberNature": "Personnel Nature: ", "company": "Company: ", "export": "Export", "workingHourCode": "Work Hour Order Number", "applicantName": "Name of Applicant", "applicantPhone": "Reporter's Mobile Phone Number", "consultantType": "Consultant Type", "projectNameIntern": "Project Name", "wbs": "WBS", "whMonths": "Month of Working Hours", "totalWHPerDay": "Total Working Hours/Day", "preTotalIncome": "Estimated Total Revenue", "applicationTime": "Working Time", "approvalTime": "Processing Time", "emailApprover": "Approver", "approvalStatus": "Approval Status", "passed": "passed", "toBeSubmitted": "To submit", "toBeApproved": "Pending", "failed": "Failed to pass", "abandoned": "Voided", "canceled": "Void", "msgCancelReimburseInfo1": "After being invalidated, the time report form will become invalid, and the system will automatically send an invalid notification email to", "msgCancelReimburseInfo2": "and the person in charge of approval. This Operation is irreversible, confirm to continue?", "cancelReimburse": "Void Time report Form", "msgCancelSuccess": "Successfully scrapped!"}, "bkAr": {"invoiceHeader": "Billing <PERSON>", "companyName": "Addressed to", "companyAddr": "Address", "companyTel": "Tel.", "companyEmail": "Email", "companyGst": "GST No.", "companyQst": "QST No.", "companyGstHst": "GST/HST", "companyPst": "PST (QST/--)", "invoiceNo": "Billing No.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "date": "Create Date", "dueDate": "Due Date", "fixedDate": "Fixed Date", "afterDays": "After Days", "customerInfo": "Customer Information", "billToCompany": "Bill To Company", "billToReceiver": "Bill To Receiver", "billingStreet": "Street", "billToCity": "City", "billToProvince": "Province", "billToZip": "<PERSON>", "billToTel": "<PERSON>.", "billToEmail": "Email", "shipToSameAddr": "Ship To the Same Address", "shipToCompany": "Ship To Company", "shipToReceiver": "Ship To Receiver", "shipToStreet": "Ship To Street", "shipToCity": "Ship To City", "shipToProvince": "Ship To Province", "shipToZip": "Ship To Zip", "shipToTel": "Ship To Tel.", "shipToEmail": "Ship To Email", "itemsDetail": "Items Detail", "itemNo": "#", "modelNumber": "PRODUCT/SERVICE", "to": " ", "description": "DESCRIPTION", "accountingCategory": "ACCOUNTING CATEGORY", "payMethod": "Payment Method", "qty": "QTY", "unitPrice": "UNIT PRICE", "total": "Net Amount", "type": "Type", "bankAccount": "Bank Account", "amountAndTax": "Amount And Tax", "amount": "NET AMOUNT", "amountRule": "Amount", "shipping": "Shipping", "discount": "Discount", "totalTaxable": "Total Taxable", "tps": "GST/HST", "tvq": "QST", "tvp": "PST", "totalTax": "Total Tax", "totalCad": "Total(CAD)", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Balance", "accountAndType": "Account And Type", "invoiceComment": "Comments", "createArInvoice": "Create A<PERSON>", "viewInvoice": "View Billing", "preview": "Preview", "create": "Save", "postingDate": "<PERSON>", "totalFee": "Total", "msgReferenceNoExisted": "Reference number already exists!", "msgReferenceCheckFail": "Reference No. repetition check failed. Please try again!", "from": "From", "addItemBtnTxt": "Add Item", "billingDoc": "Billing Doc", "referenceNo": "Billing Reference"}, "esUpload": {"bankAccount": "Bank Account", "bankMonth": "Statement Period", "buttonSubmited": "Submited", "buttonEdit": "Edit"}, "bankInfo": {"bankName": "Bank", "bankAccount": "Bank Account", "accountType": "Bank Type", "currency": "<PERSON><PERSON><PERSON><PERSON>", "coa": "CoA", "operation": "Action", "createBankTitle": "Create new Bank", "editBankTitle": "Edit Bank Detail", "msgPhrSelect": "Please select", "authorizaiton": "Authorization", "accountHasTransactions": "Bank account has transactions, and cannot be changed!", "fetchFrom": "<PERSON>tch From"}, "esMain": {"date ": "Date", "description": "Description", "inOutType": "In/Out", "amount": "Amount", "bankAccount": "Bank Account", "statementPeriod": "Statement Period", "cashIn": "Cash In", "cashOut": "Cash Out", "createCashStatementTitle": "Create Cash Statement", "editCashStatementTitle": "Edit Cash Statement Detail", "cashStatement": "Cash Statement", "withdrawal": "withdrawal", "withdraw": "withdraw", "deposit": "deposit", "postingDate": "Posting Date", "reference": "Reference", "payerPayee": "Payer/Payee", "debit": "<PERSON><PERSON><PERSON><PERSON>", "debitManu": "Debit", "credit": "<PERSON><PERSON><PERSON>", "creditManu": "Credit", "sendSapStatus": "SAP Status", "viewInvoice": "View Invoice", "invoiceDetail": "Invoice Detail", "balance": "Balance", "chargeFee": "Charge/Fee", "chargeCoa": "Charge CoA", "reasonMsg": "Reason Code", "currency": "<PERSON><PERSON><PERSON><PERSON>", "brType": "Type", "transactionDate": "Transaction Date", "delWarn": "Transaction can not be recovered after deleted.", "reconcileGL": "Reconcile G/L", "autoReconcile": "Auto Reconcile", "autoReconcileOn": "Auto Reconcile On", "autoReconcileOff": "Auto Reconcile Off"}, "chartOfAccount": {"coa": "CoA", "btnLoadCoa": "Load CoA Repository", "btnReloadCoa": "Close CoA Repository", "fieldCode": "Field Code", "account": "Account Code", "accountDes": "Description", "alias": "Account Name", "operation": "Action", "fullCoa": "CoA Repository", "createCoaTitle": "Create Chart of Account", "editCoaTitle": "Edit Chart of Account", "msgCoaDelete01": "Account with Description: ", "msgCoaDelete02": " will be deleted!", "msgCoaPause01": "Account with Description: ", "msgCoaPause02": " will be deactivated!", "msgCoaActive01": "Account with Description: ", "msgCoaActive02": " will be reactivated!", "yes": "Yes", "no": "No", "groupName": "Group"}, "taxCalculation": {"msgWarning": "The tax rate calculation method automatically provided by the system is only for convenient use and does not ensure complete accuracy. Users must manually review the accuracy of the exchange rate calculation during the Invoice issuance process.", "provinceName": "Province Name", "provinceCode": "Province Code", "applicableSalesTax": "Applicable Sales Tax", "total": "Total"}, "spotCurrency": {"msgWarning": "The exchange rate published by the bank.", "date": "Date", "currency": "Currency(USD/CAD)", "operation": "Action", "createTitle": "Create Exchange Rate", "editTitle": "Edit Exchange Rate", "selectTime": "Please select date", "msgInputRate": "Please enter exchange rate"}, "connectivity": {"sourceHolder": "Please enter", "source": "From", "username": "Username", "password": "Password", "startDate": "Start Date", "endDate": "End Date", "powerAutomate": "power automate", "server": "Server", "email": "Email", "emailHost": "Host", "disk_secret": "Secret", "type": "Type", "disk_app_id": "App ID", "disk_key": "Key", "application_number": "Application Number", "instance_number": "Instance Number", "userId": "User ID", "version": "Version", "receivable_integration": "Receivable Integration", "payable_integration": "Payable Integration", "payment_integration": "Payment Execution", "reconciliation_integration": "Reconciliation Integration", "fts_integration": "FTS Integration", "sap_instance": "Instance", "sap_general": "General", "sap_setting": "Setting", "payable_approval_procedure": "Invoice Approval Flow", "payment_approval_procedure": "Payment Approval Flow"}, "gl": {"search": "Search Narration/Journal Entry", "glNo": "#", "narration": "Narration", "module": "<PERSON><PERSON><PERSON>", "journalEntry": "Journal Entry", "createDate": "Create Date", "total": "Total", "totalDebit": "Total Debit", "totalCredit": "Total Credit", "to": " ", "readonly": "Detail G/L", "edit": "Edit G/L", "create": "Create", "descriptionxxx": "Description,xxx", "post": "Post", "date": "Date ", "totalCol": "Total ", "minFee": "From", "maxFee": "To", "operation": "Action", "postingDate": "Posting Date", "draftDate": "Draft Date", "currency": "<PERSON><PERSON><PERSON><PERSON>", "itemNo": "#", "description": "DESCRIPTION", "debit": "DEBIT", "credit": "CREDIT", "saveDraft": "Save Draft", "glAccount": "G/L ACCOUNT", "createStartDate": "From", "createEndDate": "To", "status": "Status", "sapStatus": "SAP Status", "editGl": "Edit G/L", "delGl": "Delete G/L", "viewGl": "View G/L Detail", "draft": "Draft", "posted": "Posted", "failure ": "Failure", "msgTotalDebitCannotNull": "Total Debit cannot be null, please check again", "msgTotalCreditCannotNull": "Total Credit cannot be null, please check again", "msgNotMatch": "Total Credit does not match with Total Debit, please check again", "msgNumberLimited": "The input number range is limited to 0-13 digits", "msgGlDelete": "This G/L will be deleted!", "transactionCurrency": "Local Currency - ", "status0": "Draft", "status1": "Posted", "status2": "Reversed", "format": "Format is incorrect", "payerAndPayee": "payer/payee"}, "glEntry": {"totalDebit": "TOTAL DEBIT", "totalCredit": "TOTAL CREDIT", "msgAtLeastOne": "G/L must contain at least one [ Item ]", "transactionCurrency": "Local Currency - "}, "bkSupplier": {"company": "Company", "tel": "Tel", "email": "Email", "receiver": "Supplier", "receiver01": "Receiver(Office)", "supplierAddr": "Contact Person", "address": "Address", "address01": "Address(Office)", "street": "Street", "city": "City", "province": "Province", "country": "Country", "postalCode": "Postal Code", "expenseAccount": "Expense Account", "operation": "Action", "createReceiverTitle": "Create Supplier", "editReceiverTitle": "Edit Contact Detail", "msgPhrSelect": "Please select", "sameAsOffice": "Same as office"}, "taxInfo": {"companyLogo": "Logo", "companyName": "Name", "companyAddress": "Address", "companyEmail": "Email", "companyPhone": "Tel.", "gstNo": "GST / HST No.", "qstNo": "QST / PST No.", "limit": "Note: only jpg/png files can be uploaded, no more than 2M.", "editLogoBtn": "Edit <PERSON>", "rmLogoBtn": "Remove Logo"}, "account": {"userId": "Account", "password": "PassWord", "cate": "Category", "role": "Role", "add": "Add user"}, "taxRates": {"countryNotSet": "Please set the Country and/or Province of the vendor(or customer)"}, "ApComponents": {"notpaid": "NOT PAID", "bank": "Bank Debit Account", "credit": "Credit Card", "cashpaid": "Cash", "check": "Cheque", "confirm": "Confirm Reversing Date", "confirm2": "This operation will also reverse the reconciliation.", "confirm3": "Please reverse bank reconciliation first", "referenceError": "Duplicated Reference No.", "contactFirstUsing": "New contact needs to update banking info.", "atleast": "Invoice must contain at least one [ Item ]", "mustone": "Invoice must be only one [ Item ]", "lackNetAmount": "Can't create invoice without [ Net Amount ]", "notEqual": "Net amount plus tax does not equal total", "success": "success", "referenceNo": "Reference No. in the receipts.", "spotCurrency": "Spot currency is {rate} on {date}", "contactAdmin": "pls contact administrator", "NewBP": "Add New Business Partner", "subtotal": "Tax Subtotal", "taxableSubtotal": "Taxable Subtotal", "total": "TOTAL", "difference": "Difference", "drag": "Drag the file here, or Click Upload", "uploading": "Uploading", "sizeExceed": "Note: size limit 10Mb per file.", "MEAT": "META FIELDS", "oriDoc": "Original Document", "download": "Download", "auto": "Auto-Mode", "exempt": "Tax Exempt", "amount": "TOTAL AMOUNT", "netAmount": "Net Amount", "GST": "GST/HST", "QST": "QST", "PST": "PST", "search": "Search Issuer", "submitToPay": "Submit To Payment", "pay": "Pay", "repay": "<PERSON>ay", "uploadConfirm": "The original document will be replaced!", "wbs": "WBS", "costCenter": "Cost Center", "costObject": "Cost Object", "internalOrder": "Internal Order", "profitCenter": "Profit Center", "payableGL": "Payable G/L", "testGetPgoTokenSuccess": "Get Pgo Token Success", "testGetPgoTokenFail": "Get Pgo Token Failed"}, "ArComponents": {"inavailable": "Invoice file is not available.", "notExist": "Customer not exists, want to add new customer?", "postingDt": "Posting Date", "billTo": "Bill <PERSON>", "shipTo": "Ship To", "netAmount": "Net Amount", "shipping": "Shipping", "discount": "Discout", "GST": "GST/HST", "QST": "QST", "PST": "PST", "USD": "TOTAL USD", "CAD": "TOTAL CAD", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Balance", "JE": "Journal Entries", "SAP": "SAP Document", "file": "Invoice File", "download": "Download", "cancel": "Cancel", "convert": "Pay by Cash", "confirmConvert": "Original invoice will be removed and cannot be recovered."}, "PeriodicalBooking": {"notFound": "DUMMY not found!"}, "EstatementTable": {"selectBank": "Please select bank", "required": "Bank Account field required", "inovice": "Invoice", "fetch": "<PERSON>tch", "upload": "Upload", "download": "Download", "acceptAll": "Accept all", "autoOn": "Auto-Reconciliation is On. Click it to accept the reconcile.", "autoOff": "Auto-Reconciliation is Off. Click it to enable the Auto-Reconciliation.", "reject": "Click it to reject the recommended Auto-Reconciliation and navigate to the manual reconciliation page", "RP": "RP [Regular Purchase]", "RS": "RS [Regular Sales]", "PR": "PR [Purchase Refund]", "SR": "SR [Sales Refundl]", "FT": "FT [Funding Transl]", "FX": "FX [Funding Exchange]", "PY": "PY [Payroll]", "BC": "EE [Express Entry]", "SP": "SP [Stripe]", "PP": "PP [Prepayment]", "DR": "DR [Deposit And Return]", "BCMessage": "Invoice will be created and reconciled, please select vendor (or customer) and G/L account.", "GLMessage": "Please choose Type and Accounting Category.", "integrationEEbrTitle": "Express Entry", "account": "Bank Account", "cancel": "Cancel", "OK": "OK", "uploadES": "Upload e-statement File", "reconcileAll": "Reconcile all", "payoutIdMsgInput": "Please enter payoutId", "confirm": "Confirm", "confirmReconcileDate": "Confirm Reconcile Date", "updateChargeFee": "Charge/Fee"}, "RcInfoTable": {"account": "Bank Account", "date": "Payment Date", "desc": "Description", "debit": "Debit", "credit": "Credit", "balance": "Balance", "invoiceNo": "Invoice No.", "comments": "Comments", "paymentDt": "Payment Date", "fee": "Total Fee", "amount": "BR Amount", "brDoc": "Payment Document"}, "CustomerForm": {"USA": "USA postal code: 12345 or 12345-1234", "CA": "Canadian postal code: H1H 2H2"}, "PyComponents": {"total": "Total"}, "FileUploadResult": {"failed": "File upload failed. Please try again."}, "UploadFileComp": {"notPaid": "Purchase Not Paid", "cash": "Purchase Cash", "reconcile": "Reconcile", "uploadLimit": "Upload files limit ", "select": "Please Select File", "perfileLimit": "Per file size limit ", "uploadFile": "please upload {type} file ", "success": "upload success", "error_two_files": "You need to upload two files at the same time, including an XML file", "errorNoMoreThanTwoFiles": "You can only upload no more than two files at the same time", "errorPdfXmlRequired": "Two files upload must include one PDF and one XML file", "errorSinglePdfRequired": "Single file upload must be a PDF file", "retry": "upload errors [{err}]. please retry", "selectBtn": "Select", "type": "Type", "note": "Note: size limit {fileSize}Mb per file. {fileLimit} file(s) at the same time."}, "mainLayout": {"setFY": "Set Currency And Financial Year.", "createBP": "Create business partners.", "createChart": "Create Chart of Accounts.", "fillin": "Fill in Opening Balance.", "careteBank": "Create Banks.", "setup": "Please take a few steps to set up your company"}, "userGuide": {"setFY": "Set Currency And Financial Year.", "createBP": "Create business partners.", "createChart": "Create Chart of Accounts.", "fillin": "Fill in Opening Balance.", "careteBank": "Create Banks."}, "userPage": {"selectUser": "Must select a user", "success": "success", "error": "error", "companyWithDesc": "Company with Description ", "companyCd": "Company Code", "companyNm": "Company Name", "AddAll": "Add All Company", "cdnm": "Company Code/Name"}, "ApInvoiceFormPdf": {"invoice": "invoice file", "fetch": "Fetch Information", "notPaid": "NOT PAID", "partialPaid": "PARTIAL PAID", "paid": "Paid", "paidupcase": "PAID", "reversed": "Reversed"}, "ApUploadInvoice": {"upload": "Upload Invoice File", "OCR": "OCR Analyzing Procedure", "file": "File selected ", "fillError": "Invoice file is not available.", "analyzing": "Analyzing ( via {type} ) ...", "error": "Error", "confirm": "Confirm"}, "ArInvoiceHistory": {"invoice": "Invoice File is not available", "msg_error_no_date": "Please select date range", "Send Invoice": "send"}, "ReconciliationDetails": {"title": "Bank Reconciliation Detail", "invoiceNo": "Invoice #", "select": "please select invoice", "search": "Search Balance", "totals": "Totals Amt ${amt}, Selected Amt ${amt2}, Diff. ${amt3}.", "reconcile": "Reconcile", "manual": "Manual Reconcile", "manualIntegration": "Manual Reconcile For Integration", "payerAndPayee": "Search Payer/Payee"}, "AccountDescription": {"importAll": "Do you want to import all Coa from the selected group?", "add": "Do you want to add a new sub category?", "primary": "primary", "create": "Please Create Chart of Accounts.", "step1": "Step 1: Input information", "next": "Next step", "step2": "Step 2: Click button", "step3": "Step 3: Click the Select link", "got": "Got it", "importGroup": "Import Group", "pause": "Pause", "active": "Active", "copy": "Copy", "cdnm": "Account Code or Name"}, "bankInformation": {"BMO": "Bank of Montreal", "CIBC": "Canadian Imperial Bank of Commerce", "NBC": "National Bank of Canada", "RBC": "Royal Bank Canada", "Desjardins": "Desjardins Bank", "TDB": "Toronto-Dominion Bank", "JPCB": "JPMorgan Chase Bank", "FB": "Fremont Bank", "SEB": "South East Bank", "EWB": "East West Bank", "CITI": "CITI Bank", "BOC": "Bank Of China", "CMB": "China Merchants Bank", "ASPIRE": "Aspire Bank", "NOVA": "Nova Scotia", "AMEX": "American Express", "ICBC": "Industrial And Commercial Bank Of China", "BONJ": "Bank of NanJing", "SANTANDER": "Santander Bank", "BASEINET": "BASEinet Bank", "MONEX": "Monex Bank", "BBVA": "BBVA Bank", "HSBC": "HSBC Bank", "email": "Please set your email.", "create": "Please Create Banks."}, "ContactCustomer": {"create": "Please Create business partners.", "got": "Got it"}, "SettingConnectivities": {"importBilling": "Import Billing", "import": "Import", "save": "Save", "cancel": "Cancel", "pass": "Email Password", "channel": "Omnichannel", "SMTP": "SMTP", "Netdist": "Netdisk", "approvers": "Approvers", "lowCode": "Low Code", "pgoPlusSetting": "Pgo Plus Setting"}, "TaxInformation": {"removed": "success to remove the company logo", "error": "Upload Error", "recorrrect": "please input correct `{msg}`, ex: {desc}", "phoneNo": "please input correct phone number. ex: **********", "FY": "Please set currency and fiscal year."}, "reports": {"balanceSheet": "Balance Sheet", "trialBalance": "Trial Balance", "cashFlowStatement": "Cash Flow Statement", "purchaseReport": "Purchase Report", "incomeStatement": "Income Statement", "salesReport": "Sales Report", "apReport": "AP Report", "arReport": "AR Report", "mxElectronicAccountingCoaReport": "Electronic Accounting - COA", "mxElectronicAccountingTrialBalanceReport": "Electronic Accounting - Trial Balance", "mxInformativeDeclarationReport": "Informative Declaration of Operations with Third Parties (DIOT)", "mxValueAddedTaxReport": "Value Added Tax", "mxColumnSeqNo": "Seq. No.", "mxColumnGlAccountDetails": "G/L Account Details", "mxColumnGlAccount": "G/L Account", "mxColumnStartingBalanceAmount": "Starting Balance Amount", "mxColumnDebitAmount": "Debit Amount", "mxColumnCreditAmount": "Credit Amount", "mxColumnEndingBalanceAmount": "Ending Balance Amount", "mxColumnSatCode": "SAT Code", "mxColumnGlAccountDescription": "G/L Account Description", "mxColumnLevel": "Level", "mxColumnNatureOfAccount": "Nature Of Account", "mxDiotColumn1": "Third party type", "mxDiotColumn2": "Operation type", "mxDiotColumn3": "RFC", "mxDiotColumn4": "Tax identification number", "mxDiotColumn5": "Foreigner's name", "mxDiotColumn6": "Country or jurisdiction of tax residence", "mxDiotColumn7": "Specify place of tax jurisdiction", "mxDiotColumn8": "Total value of paid events or activities / Paid events or activities in the northern border region", "mxDiotColumn9": "Refunds, discounts and bonuses / Paid events or activities in the northern border region", "mxDiotColumn10": "Total value of paid events or activities / Paid events or activities in the southern border region", "mxDiotColumn11": "Refunds, discounts and bonuses / Paid events or activities in the southern border region", "mxDiotColumn12": "Total value of paid events or activities / Total events or activities paid at the 16% VAT rate", "mxDiotColumn13": "Refunds, discounts and bonuses / Total events or activities paid at the rate of 16% VAT", "mxDiotColumn14": "Total value of acts or activities paid / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn15": "Refunds, discounts and bonuses / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn16": "Total value of acts or activities paid / Acts or activities paid in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn17": "Refunds, discounts and bonuses / Acts or activities paid for the import of intangible goods and services at the rate of 16% VAT", "mxDiotColumn18": "Exclusively for taxed activities / Acts or activities paid in the northern border region", "mxDiotColumn19": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn20": "Exclusively for taxed activities / Acts or activities paid in the southern border region", "mxDiotColumn21": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn22": "Exclusively from taxable activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn23": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn24": "Exclusively for taxed activities / Acts or activities paid for in the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn25": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn26": "Exclusively for taxed activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn27": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn28": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn29": "Associated with activities that do not meet requirements / Paid events or activities in the northern border region", "mxDiotColumn30": "Associated with exempt activities / Paid acts or activities in the northern border region", "mxDiotColumn31": "Associated with non-object activities / Paid acts or activities in the northern border region", "mxDiotColumn32": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn33": "Associated with activities that do not meet requirements / Paid events or activities in the southern border region", "mxDiotColumn34": "Associated with exempt activities / Paid events or activities in the southern border region", "mxDiotColumn35": "Associated with non-object activities / Paid acts or activities in the southern border region", "mxDiotColumn36": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn37": "Associated with activities that do not meet requirements / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn38": "Associated with exempt activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn39": "Associated with non-object activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn40": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn41": "Associated with activities that do not comply with requirements / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn42": "Associated with exempt activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn43": "Associated with non-object activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn44": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn45": "Associated with activities that do not comply with requirements / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn46": "Associated with exempt activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn47": "Associated with non-object activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn48": "VAT withheld by the taxpayer", "mxDiotColumn49": "Acts or activities paid for in the importation of goods and services for which VAT is not paid (Exempt)", "mxDiotColumn50": "Paid acts or activities for which VAT will not be paid (Exempt)", "mxDiotColumn51": "Other acts or activities paid at the 0% VAT rate", "mxDiotColumn52": "Acts or activities not subject to VAT carried out in national territory", "mxDiotColumn53": "Acts or activities not subject to VAT due to not having an establishment in national territory", "mxDiotColumn54": "I declare that fiscal effects were given to the receipts that support the operations carried out with the supplier", "closingBalance": "Closing balance", "itemsDetail": "Items Detail", "startDate": "Start Date", "experationDate": "Experation Date", "company": "Company", "targetCurrency": "Target Currency", "transactionCurrency": "Transaction Currency", "COAAccount": "COA Account", "refresh": "Refresh", "category": "Category", "accountCode": "Account Code", "nameGLCategory": "Account Name", "endDebit": "Debit", "endCredit": "Credit", "all": "All", "endingAt": "Ending At", "subTotal": "Sub total", "updatedOn": "Updated On", "postingDate": "Posting Date", "documentNo": "Document No", "invoiceNo": "Invoice No", "supplier": "Supplier", "customer": "Customer", "netAmount": "Net Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "PST": "PST", "GST": "GST/HST", "QST": "QST", "VAT": "VAT", "Discount": "Discount", "IsrWithholding": "Income Tax Withholding", "VatWithholding": "VAT Withholding", "totalTaxAmount": "Total Tax Amount", "totalAmount": "Total Amount", "invoiceAmount": "Invoice Amount", "payment": "Payment", "totalDue": "Total Due", "businessPartner": "Business Partner", "invoiceReference": "Invoice Reference", "transactionType": "Transaction Type", "current": "Current", "31To60": "31 to 60", "61To90": "61 to 90", "91+": "91+", "contactName": "Contact Name", "narration": "Narration", "cashFlowName": "Cash Flow Items", "cashFlowMonth": "Current Month", "cashFlowYear": "Current Year", "vatChargeable": "VAT chargeable", "valueOfActsOrActivitiesTaxedAtTheRateOf16": "Value of acts or activities taxed at the rate of 16%", "valueOfActsOrActivitiesTaxedAtThe0ExportRate": "Value of acts or activities taxed at the 0% export rate", "valueOfActsOrActivitiesTaxedAtTheRateOf0Others": "Value of acts or activities taxed at the rate of 0% others", "sumOfTheTaxedActsOrActivities": "Sum of the taxed acts or activities", "valueOfActsOrActivitiesForWhichTaxIsNotPayable": "Value of acts or activities for which tax is not payable (exempt)", "valueOfActsOrActivitiesNotSubjectToTax": "Value of acts or activities not subject to tax", "vatChargeableAtTheRateOf16": "VAT chargeable at the rate of 16%", "vatCharged": "VAT charged", "updatedAmountToBeReimbursedDerivedFromTheAdjustment": "Updated amount to be reimbursed derived from the adjustment", "totalVatDue": "Total VAT due", "btCapture": "CAPTURE", "vatCreditable": "VAT creditable", "amountOfPaidEventsOrActivities": "Amount of paid events or activities", "totalActsPaid16Percent": "Total of acts or activities paid at the 16% VAT rate", "totalActsPaidImport16Percent": "Total of acts or activities paid for in the import of goods and services at the 16% VAT rate", "totalActsPaid0Percent": "Total of other acts or activities paid at the 0% VAT rate", "totalPaidActsExempt": "Total of paid acts or activities for which VAT will not be paid (exempt)", "determinationCreditableVAT": "Determination of the creditable Value Added Tax", "vatOnActsPaid16Percent": "VAT on acts or activities paid at the rate of 16%", "vatOnImportPaid16Percent": "VAT on acts or activities paid on the import of goods and services at the rate of 16%", "totalVATTransferred": "Total VAT transferred to the taxpayer (Effectively paid)", "updatedCreditableAmount": "Updated creditable amount to increase derived from the adjustment", "totalCreditableVAT": "Total creditable VAT", "determination": "Determination", "vatWithheld": "VAT withheld", "totalCreditableVat": "Total creditable VAT", "otherAmountsPayableByTheTaxpayer": "Other amounts payable by the taxpayer", "otherAmountsInFavorOfTheTaxpayer": "Other amounts in favor of the taxpayer", "amountDue": "Amount due", "creditingOfTheBalanceInFavorOfPreviousPeriods": "Crediting of the balance in favor of previous periods (Without exceeding the amount due)", "taxDue": "Tax due", "amountToBeDetailed1": "Amount to be detailed", "interestChargedAtRate16": "Interest charged at a rate of 16%", "royaltiesBetweenRelatedPartiesAtRate16": "Royalties between related parties at the rate of 16%", "otherActsOrActivitiesTaxedAtRate16": "Other acts or activities taxed at the rate of 16%", "amountToBeDetailed2": "Amount to be detailed", "agriculturalLivestockForestryFishingActivitiesTaxedAtRate0": "Agricultural, livestock, forestry or fishing activities taxed at a rate of 0%", "otherActsOrActivitiesTaxedAtRate0": "Other acts or activities taxed at a rate of 0%", "amountToBeDetailed3": "Amount to be detailed", "alienationOfLandAndBuildingsForResidentialHousing": "Alienation of land and buildings attached to the land, intended or used for residential housing", "saleOfBooksNewspapersMagazinesNotByTaxpayer": "Sale of books, newspapers and magazines (not published by the taxpayer)", "royaltiesChargedByAuthors": "Royalties charged by authors", "disposalOfUsedMovablePropertyExceptByCompanies": "Disposal of used movable property, except those disposed of by companies", "alienationOfLotteryTicketsAndReceipts": "Alienation of tickets and other receipts from lotteries, raffles, drawings or games with bets and contests of all kinds", "teachingServices": "Teaching services", "publicLandTransportationServiceForPeople": "Public land transportation service for people", "derivativeFinancialTransactions": "Derivative financial transactions", "ticketSalesForPublicShows": "Ticket sales for public shows", "professionalMedicalServices": "Professional medical services", "temporaryUseOfRealEstateForResidentialOrFarming": "Temporary use or enjoyment of real estate for residential purposes and for farms for agricultural or livestock purposes", "otherIncomeExemptFromVat": "Other income exempt from VAT", "amtDetail1": "Amount to be detailed", "intPaid16": "Interest paid at a rate of 16%", "royaltyRel16": "Royalties paid between related parties at the rate of 16%", "otherPaid16": "Other acts or activities paid at the rate of 16%", "amtDetail2": "Amount to be detailed", "impInt16": "Importation of intangible assets at a rate of 16%", "impIntTmp16": "Importation of intangible assets for temporary use or enjoyment at a rate of 16%", "impServ16": "Importation of services at the rate of 16%", "impOthPaid16": "Other acts or activities paid in the importation of goods and services at the rate of 16%", "amtDetail3": "Amount to be detailed", "landBuy": "Acquisition of land and buildings attached to the land, intended or used for residential housing", "bookBuy": "Acquisition of books, newspapers or magazines (not published by the taxpayer)", "royaltyAuth": "Royalties paid to authors", "usedBuy": "Acquisition of used movable property except those acquired from companies", "transSvc": "Public land transportation service for people", "medSvc": "Professional medical services", "agrInsur": "Insurance against agricultural risks", "farmUse": "Temporary use or enjoyment of farms for agricultural or livestock purposes", "impExm": "Acts or activities paid for in the importation of exempt goods and services", "othExm": "Other exempt paid acts or activities", "vatCredTot": "Total VAT creditable for activities taxed at the rate of 16%, 8% and 0%", "vatPaidTot": "Total VAT transferred to the taxpayer (effectively paid)", "vatTrf": "VAT transferred for acquisitions of goods other than investments, acquisition of services or for the temporary use or enjoyment of goods that are used exclusively to carry out taxable acts or activities", "vatTrfInv": "VAT transferred for the acquisition of investments intended exclusively for carrying out taxable acts or activities", "vatImp": "VAT paid on imports for the acquisition of goods other than investments, acquisition of services or for the temporary use or enjoyment of goods that are used exclusively to carry out taxable acts or activities", "vatImpInv": "VAT paid for the import of investments intended exclusively for carrying out taxable acts or activities", "vatTaxTot": "Total VAT corresponding to taxed acts or activities", "vatCredNo": "Total VAT creditable for imports to carry out acts or activities for which they are not required to pay the tax", "vatImpNo": "VAT transferred or paid on imports for the acquisition of goods other than investments, acquisition of services or for the temporary use or enjoyment of goods intended exclusively for carrying out acts or activities for which there is no obligation to pay the tax", "vatInvNo": "VAT transferred or paid on the import of investments intended exclusively for carrying out acts or activities for which there is no obligation to pay the tax", "vatMixed": "VAT on goods used interchangeably to carry out taxable acts or activities and acts or activities for which one is not obliged to pay the tax", "vatSel": "Select the VAT rate you will apply", "vatRatio": "VAT ratio", "vatCredMix": "VAT creditable for goods used interchangeably to carry out taxable acts or activities and acts or activities for which one is not required to pay the tax", "vatCred": "VAT creditable", "amtDetail4": "Amount to be detailed", "assetSale": "By alienation of assets", "otherOps": "For other operations"}, "menu": {"task": "Task", "purchase": "Purchase", "massiveProcess": "Massive Process", "invoices": "Invoices", "sales": "Sales", "billing": "Billing", "payroll": "Payroll", "payrollRecord": "Payroll Record", "bankReconciliation": "Bank Reconciliation", "reconcile": "Reconcile", "history": "History", "generalLedger": "General <PERSON><PERSON>", "journalEntries": "Journal Entries", "setting": "Setting", "contact": "Contact", "profile": "Profile", "coA": "CoA", "coAMapping": "CoA Mapping", "productService": "Product/Service", "connectivities": "Connectivities", "reporting": "Reporting", "reports": " Business Intelligent", "exportTrialBalance": "Reports", "help": "Help", "userGuide": "User Guide", "FAQ": "FAQ", "account": "Account", "user": "User", "dashboard": "Dashboard", "payment": "Payment"}, "fileTypeList": {"salesNotPaid": "Sales Not Paid", "notPaid": "Not Paid", "salesCash": "Sales Cash", "cashPaid": "Cash Paid", "ES": "ES", "yearEnd": "Year End Document"}, "productService": {"createTitle": "Create New Product/Service", "editTitle": "Edit Product/Service"}, "task": {"name": "Task", "companyCode": "Company Code", "companyName": "Company Name", "last": "Last", "dueDate": "Due", "estimatedHour": "Estimated (Hour)", "actualHour": "Actual (Hour)", "status": "Status", "assignedTo": "Assigned To", "email": "Email", "tag": "Tag", "priority": "Priority", "createTime": "Create Time", "updateTime": "Update Time", "action": "Action", "placeholderSearch": "Search Task Name", "placeholderStarting": "From", "placeholderEnding": "To", "placeholderMinHour": "Min Hour", "placeholderMaxHour": "Max Hour", "searchTitleEstimated": "Estimated", "statusToDo": "Todo", "statusDoing": "Doing", "statusDone": "Done", "statusDeleted": "Deleted"}, "update": {"items": "items", "item": "item", "assigment": "ASSIGMENT", "paymentDueLater": "Payment due later", "withinDay7": "Within 7 days", "withinDay15": "Within 15 days", "withinDay30": "Within 30 days", "withinDayOther": "Fixed date", "other": "Other", "searchByBankAccount": "Search by bank account", "searchByFaq": "Search", "sapInstance": "Instance", "sapApplicationNumber": "Application Number", "reverseProcessing": "Reverse Processing...", "poNumberNotFound": "PO Number Not Found", "purchaseOrderFound": "Purchase Order Found", "realizeSuccess": "Realize Success", "purchaseOrderFoundButNoOpenItem": "Purchase order found, but no open item for invoice verification.", "excelGenerated": "Excel file has been generated and downloaded", "excelGenerateFailed": "Failed to generate Excel file", "noPdfContent": "No PDF content available", "noXmlContent": "No XML content available", "excelOpenFailed": "Failed to open PDF content", "excelDownloadFailed": "Failed to download Excel file"}}