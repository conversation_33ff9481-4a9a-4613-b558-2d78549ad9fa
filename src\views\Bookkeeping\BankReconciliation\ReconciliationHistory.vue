<!-- @format -->

<script lang="ts" setup>
import EstatementTable from '@/components/bookkeepingComponents/BrComponents/EstatementTable.vue'
import {useStore} from 'vuex'
import {computed, onBeforeMount, reactive, ref, watch} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import * as _ from 'lodash'
import type {Estatement} from '@/model/reconciliation'
import {UserCompany} from '@/lib/storage'

const i18n: Composer = i18nInstance.global
const store = useStore()
const userCompany: any = UserCompany.get() || []
const tableData = ref<Estatement[]>([])

const fetchAllBankList = (query?: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', query)
const fetchEsHistoryList = async (query?: any) => {
    const payload = {...query}
    await store.dispatch('BrStore/fetchEsHistoryListV1', payload)
}
const bankList = computed(() => store.state.BankInfoStore.bankList)
// const esHistoryList = computed(() => store.state.BrStore.esList)

const esHistoryList = computed({
    get() {
        return store.state.BrStore.esList
    },
    set(data) {
        return [...data]
    },
})
const state = reactive({
    activeKey: 0,
    pageQuery: {
        page_index: 1,
        page_size: 10,
    },
    tableQuery: {
        bank_account: '',
        br_type: '',
        endDate: '',
        startDate: '',
        company_code: userCompany[0].code,
    },
    isTableLoading: true,
})

const getQueryParams = (searchForm: any) => {
    //used for transform searchForm property to api query format
    const result: any = {}
    result['company_code'] = userCompany[0].code
    // result['company_code'] = 3002

    // desc sort by create_date as default
    result['sort[rh.posting_date]'] = 'desc'
    if (searchForm.startDate && searchForm.endDate) {
        result['start_date'] = searchForm.startDate
        result['end_date'] = searchForm.endDate
    }
    if (searchForm.startDate && !searchForm.endDate) {
        result['start_date'] = searchForm.startDate
    }
    if (!searchForm.startDate && searchForm.endDate) {
        result['end_date'] = searchForm.endDate
    }
    if (searchForm.minTotalFee && searchForm.maxTotalFee) {
        // result['total_fee[$bw]'] = `[${searchForm.minTotalFee},${searchForm.maxTotalFee}]`
        result['minTotalFee'] = searchForm.minTotalFee
        result['maxTotalFee'] = searchForm.maxTotalFee
    }
    if (searchForm.minTotalFee && !searchForm.maxTotalFee) {
        // result['total_fee[$gte]'] = searchForm.minTotalFee
        result['minTotalFee'] = searchForm.minTotalFee
    }
    if (!searchForm.minTotalFee && searchForm.maxTotalFee) {
        // result['total_fee[$lte]'] = searchForm.maxTotalFee
        result['maxTotalFee'] = searchForm.maxTotalFee
    }
    if (searchForm.searchText) {
        result['desc'] = `${searchForm.searchText}`
    }
    result.page_index = searchForm.page_index || 1
    result.page_size = searchForm.page_size || 10
    return result
}

const switchTab = async () => {
    // state.isTableLoading = true
    // const bankAccount = bankList.value[state.activeKey]
    //
    // const query = getQueryParams(state.tableQuery)
    // query.bank_account = bankAccount?.account_no
    // await fetchEsHistoryList({...query, ...state.pageQuery})
    // state.isTableLoading = false
    await updateTable({...state.pageQuery})
}

const initPage = () => {
    state.isTableLoading = true
    esHistoryList.value = []
}

const updateTable = async (searchQuery: any) => {
    state.isTableLoading = true
    const bankAccount = bankList.value[state.activeKey]

    const query = getQueryParams(searchQuery)
    query.bank_account = bankAccount?.account_no
    await fetchEsHistoryList({...query})
    state.isTableLoading = false
}

onBeforeMount(async () => {
    initPage()
    await fetchAllBankList({company_code: userCompany[0].code})

    await switchTab()
})
</script>
<template>
    <div class="page-container-br-history">
        <a-tabs v-model:activeKey="state.activeKey" @change="switchTab" v-if="bankList.length">
            <a-tab-pane v-for="(item, index) in bankList" :key="index">
                <template #tab>
                    <div class="tab-panel-header">
                        {{ item.name + ' | ' + item.account_no }}
                    </div>
                </template>
                <!-- :table-data="esHistoryList" -->
                <estatement-table
                    :show-view-only="true"
                    :hide-bank-account="true"
                    :is-loading="state.isTableLoading"
                    :is-history="true"
                    :show-size-changer="true"
                    @update="updateTable"
                ></estatement-table>
            </a-tab-pane>
        </a-tabs>
        <estatement-table
            v-else
            :show-view-only="true"
            :hide-bank-account="true"
            :is-loading="state.isTableLoading"
            :is-history="true"
            :show-size-changer="true"
            @update="updateTable"
        ></estatement-table>
    </div>
</template>
<style lang="scss" scoped>
.page-container-br-history {
    height: 100%;

    .tabs-contents {
        border-radius: 12px;
        flex: 1;
    }

    :deep(.ant-tabs-content-holder) {
        .br-main-page-wrap {
            height: calc(100vh - 82px - 16px - 70px);
        }

        .br-main-page-content {
            height: calc(100vh - 82px - 85px - 16px - 70px);
        }
    }
}

.tab-panel-header {
    font-size: 16px;
    letter-spacing: 0;
    text-align: center;
    line-height: 22px;
}
</style>
