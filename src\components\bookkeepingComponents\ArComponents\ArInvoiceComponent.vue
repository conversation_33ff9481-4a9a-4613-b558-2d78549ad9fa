<!-- @format -->

<script lang="ts" setup>
// import { mapActions, mapState, mapMutations, mapGetters } from 'vuex'
import {v4 as uuidv4} from 'uuid'
import moment from 'moment'
import SupplierForm from '@/components/bookkeepingComponents/CommonComponents/SupplierForm.vue'
import GlComponent from '@/components/bookkeepingComponents/GlComponents/GlComponent.vue'
import PdfViewer from '@/components/bookkeepingComponents/PdfViewer.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import {message, Modal, type FormInstance} from 'ant-design-vue'
import {useDraggable} from '@vueuse/core'
import {
    computed,
    nextTick,
    onBeforeMount,
    onMounted,
    onUnmounted,
    reactive,
    ref,
    watch,
    type CSSProperties,
    watchEffect,
    getCurrentInstance,
} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import Decimal from 'decimal.js'

import {
    PlusOutlined,
    QuestionCircleOutlined,
    PlusCircleOutlined,
    DeleteOutlined,
    ExclamationCircleFilled,
} from '@ant-design/icons-vue'

import * as _ from 'lodash'
import {UserCompany, UserInfo, LocalCurrency, FinancialYear} from '@/lib/storage'
import FileSaver from 'file-saver'
import dayjs from 'dayjs'
import ProductServiceForm from '@/components/bookkeepingComponents/CommonComponents/ProductServiceForm.vue'
import InputNumber from 'primevue/inputnumber'

const {appContext} = getCurrentInstance()!
const formatNumber = appContext.config.globalProperties.$formatNumber
const parseNumber = appContext.config.globalProperties.$parseNumber
const decimalFormat = appContext.config.globalProperties.$decimalFormat
const modalTitleRef = ref<HTMLElement | null>(null)
const convertWrap = ref<HTMLElement | null>(null)

const {x, y, isDragging} = useDraggable(modalTitleRef)
const startedDrag = ref(false)
const startX = ref<number>(0)
const startY = ref<number>(0)
const transformX = ref(0)
const transformY = ref(0)
const preTransformX = ref(0)
const preTransformY = ref(0)
const dragRect = ref({left: 0, right: 0, top: 0, bottom: 0})

const userCompany: any = UserCompany.get() || []
const financialYear = FinancialYear.get() ? moment(FinancialYear.get()).month() : null
const userInfo: any = UserInfo.get() || {}
const localCurrency = LocalCurrency.get() || 'CAD'
const downloadFileloading = ref(false)
const i18n: Composer = i18nInstance.global
const store = useStore()
const countryCodeCheck = ref(false)
const spotRateCheck = ref(false)
const taxRateCheck = ref(false)
const isDisable = computed(() => {
    if (props.from === 'copy') {
        return false
    } else {
        return !(countryCodeCheck.value && spotRateCheck.value && taxRateCheck.value)
    }
})
const google = window.google
let googleAutocomplete: google.maps.places.Autocomplete | null = null

const props = withDefaults(
    defineProps<{
        currentInvoice: any
        readonlyMode: boolean
        operationMode: string
        invoiceId?: string
        invoiceNo?: string
        from: string
    }>(),
    {
        readonlyMode: false,
        operationMode: 'creating',
        invoiceId: '',
        inoviceNo: '',
        from: '',
    },
)
const accountQuery = {bk_type: 1, company_code: userCompany[0].code, $limit: -1, del_flag: 0}
const companyLogo = ref('')
const payMethodOptions = reactive([
    {
        value: '1',
        label: i18n.t('ApComponents.bank'), //'NOT PAID',
    },
    {
        value: '2',
        label: i18n.t('ApComponents.cashpaid'), //'CASH PAID',
    },
    // {
    //     value: '3',
    //     label: 'FUNDING TRANSFER',
    // },
    // {
    //     value: '4',
    //     label: 'INTERCOM',
    // },
])

const reverseDt = ref('')
const formRef = ref<FormInstance>()
const tableWrapRef = ref()
const form = ref<any>({
    pay_method: '1',
    print: '',
    left_shipto: '', //新加
    right_Default: '', //新加
    right_Data: '', //新加
    right_PaymentTerms: '', //新加
    right_DueDate: '', //新加
    reference_no: '',
    company_name: '',
    company_address: '',
    company_tel: '',
    company_email: '',
    company_logo: '',
    invoice_create_date: null as string | null,
    invoice_due_date: null as string | null,
    items: [] as Array<any>,
    company_gst_no: '',
    company_pst_no: '',
    invoice_currency: localCurrency,
    tax_content: [] as Array<any>,
    net_amount: 0 as number | null | undefined,
    total_tax: 0 as number | null,
    total_fee: 0 as number | null,
    total_fee_local: 0,
    shipping: undefined,
    // discount: 0 as number | null,
    // balance: null,
    // deposit: null,
    invoice_no: '',
    invoice_comments: '',
    posting_date: '',
    bill_to_receiver: '',
    bill_to_company: '',
    bill_to_street: '',
    bill_to_city: '',
    bill_to_province: '',
    bill_to_postal_code: '',
    bill_to_customer_id: '',
    bill_to_tel: '',
    bill_to_email: '',
    ship_to_receiver: '',
    ship_to_street: '',
    ship_to_city: '',
    ship_to_province: '',
    ship_to_postal_code: '',
    ship_to_email: '',
    ship_to_tel: '',
    bank_name: '',
    bank_account: '',
    bank_id: null,
    ship_to_company: '',
    // deposit: 0,
    // amountFee: 0,
    balance: 0,
    bill_to_country: '',
    ship_to_country: '',
    // officeCountry: null,
    invoice_url: null,
})
const current = reactive({
    contact_name: '',
})
const isWeekend = ref(false)
const spotPostingDate = ref(dayjs().format('YYYY-MM-DD'))

const glCurrent = ref()
const enableTaxExempt = ref(false)
const glOperationMode = ref('apDetail')
const showGlDialog = ref(false)
const showFileViewer = ref(false)
const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}
const show = ref(false)
const fixedDate = ref(0)
const formLoading = ref(false)
const referenceNoLoading = ref(false)
const shippingAsBillingCheck = ref(false)
const shipProvinceOptions = ref()
const billProvinceOptions = ref()
const autoCalTaxFlag = ref('1') // '1': change value from "Total"; '0': change value from 'item list'; '2': change value from bill province selected
const autoCalculateState = ref(true)
const spot = ref({
    rate: '',
    rate_date: '',
})
const compNameRef = ref()
const officeProvinceOptions = ref()

const contactPageSize = ref(10)
const contactLoading = ref(false)
const contactKeyword = ref('')

const newCustomerName = ref('')

const total_diff = ref(0)
const prev_total = ref(-1)

const showReverseLoading = ref(false)
const showConvertLoading = ref(false)

const showConfirm = ref(false)
const showConvert = ref(false)

const confirmationWrap = ref<HTMLElement | null>(null)
const confirmText = i18n.t('ApComponents.confirm') //'Please confirm to reverse'
const confirmConvert = i18n.t('ArComponents.confirmConvert') //'Please confirm to reverse'

const popConfirmModal = () => {
    showConfirm.value = true
}
const popConvertModal = () => {
    showConvert.value = true
}

const confirmationConfirm = async () => {
    showReverseLoading.value = true
    await reverseSales()
}
const convertConfirm = async () => {
    showConvertLoading.value = true
    await convertSales()
}
const confirmationCancel = () => {
    showConfirm.value = false
    return
}
const convertCancel = () => {
    showConvert.value = false
    return
}

const googleEvent = () => {
    if (!google) return

    const autocompleteInput: any = document.getElementById('bill_to_street')

    googleAutocomplete = new google.maps.places.Autocomplete(autocompleteInput, {
        // componentRestrictions: {country: ['ca', 'us']},
        fields: ['address_components', 'geometry'],
        types: ['address'],
    })
    googleAutocomplete.addListener('place_changed', async () => {
        const place = googleAutocomplete!.getPlace()
        const realPlace = {
            address_components: [],
            ...place,
        }
        const mapping = {
            locality: 'bill_to_city',
            administrative_area_level_1: 'bill_to_province',
        }
        const mappingStreet = {
            street_number: 'bill_to_street',
        }

        for (const item in {...mapping, ...mappingStreet}) {
            const formKey: string | undefined = mapping[item as keyof typeof mapping]
            if (formKey) form.value[formKey as keyof typeof form] = '' as any
        }

        realPlace.address_components.forEach((component: any) => {
            const type = component.types[0]
            if (Object.prototype.hasOwnProperty.call(mapping, type)) {
                form.value[mapping[type as keyof typeof mapping] as keyof typeof form] =
                    type === 'administrative_area_level_1' ? component.short_name : component.long_name || ''
            }
        })
        await fetchProvinceInfo(form.value.bill_to_country)
        const newProvinceList = _.cloneDeep(provinceOptions.value)
        if (newProvinceList.value.length !== officeProvinceOptions.value.length)
            officeProvinceOptions.value = newProvinceList
        const streetMapping = ['street_number', 'route']
        const streetWithNumber = realPlace.address_components
            .filter((component: any) => streetMapping.includes(component.types[0]))
            .map((item: any) => item.long_name)
        const fullStreetAdd = streetWithNumber.join(', ')
        form.value[mappingStreet['street_number']] = fullStreetAdd || ''
    })
}

onMounted(() => {
    googleEvent()
    clearFormValidations()
    updateLogoImg1()
})
// mapActions
const getFileBlobById = (data: any) => store.dispatch('ArApBrStore/getFileBlobByIdV1', data)
const reverseSalesRequest = (payload?: any) => store.dispatch('ArStore/reverseSalesRequestV1', payload)
const convertSalesRequest = (payload?: any) => store.dispatch('ArStore/convertSalesRequestV1', payload)

const fetchCustomerDropDown = (query?: any) => store.dispatch('CommonDropDownStore/fetchContactDropDown', query)
const fetchAccountDescDropdown = (payload: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', payload)
const fetchAllBankList = (payload: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', payload)
const fetchAccountDesc = (payload: any) => store.dispatch('AccountDescriptionStore/fetchAccountDescV1', payload)
const fetchTaxRates = (query?: any) => store.dispatch('TaxCalculationStore/fetchTaxRates2', query)
const fetchCompanyTaxInfo = (query: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
const checkReferenceNoRepetition = (payload: any) => store.dispatch('ArStore/checkReferenceNoRepetitionV1', payload)
const createClient = (payload: any) => store.dispatch('CustomerStore/createClient', payload)
const fetchCompanyLogo = (query: any) => store.dispatch('TaxInfoStore/fetchCompanyLogov1', query)
const fetchArTopCoa = (payload: any) => store.dispatch('ArStore/fetchArTopCoaV1', payload)
const fetchInvoiceDetail = (query: any) => store.dispatch('ArStore/fetchArInvoiceDetailWithIdv1', query)
const fetchGlList = (payload: any) => store.dispatch('GlStore/fetchGlListV1', payload)

const fetchProvinceInfo = (payload: any) => store.dispatch('CommonDropDownStore/fetchProvinceInfo', payload)
const provinceOptions = computed(() => store.state.CommonDropDownStore.provinceOptions)
// const fetchCompanyTaxInfo = () => store.dispatch('TaxInfoStore/fetchCompanyTaxInfo')
// cosnt uploadLogo = (payload: {raw: string | Blob}) => store.dispatch('TaxInfoStore/uploadLogo', payload)
const getSpot = (query?: any) => store.dispatch('Utils/getSpotv1', query)
const fetchProductServiceList = (payload?: any) =>
    store.dispatch('ProductServiceStore/fetchProductServiceList', payload)

// mapMutations
const updatePdfInfo = (data: any) => store.commit('ArApBrStore/updatePdfInfo', data)
const updateArTopCoa = (data: any) => store.commit('ArStore/updateArTopCoa', data)
const updateTaxRates = (data: any) => store.commit('TaxCalculationStore/updateTaxRatesList', data)
const print = () => {
    form.value.print = '!props.readonlyMode'
}

const FormItemModelChange = (value: string, index: number, record: any) => {
    const productServiceItem = productServiceList.value
        .filter((item: any) => item.product_service == record.model)
        .shift()
    form.value.items[index].description = productServiceItem?.description
    if (productServiceItem.coa) {
        form.value.items[index].debit_coa_code = productServiceItem?.coa
        const coaItem = accountDescList.value.filter((x: any) => x.account_code == productServiceItem?.coa).shift()
        form.value.items[index].debit_coa_id = coaItem?.id
        form.value.items[index].debit_coa_name = coaItem?.name
    }

    form.value.items[index].unit_price = productServiceItem?.unit_price
}
const enableReverse = ref(false)
//获取图片
const updateLogoImg1 = async () => {
    try {
        const response = await fetchCompanyLogo({id: userCompany[0].id})
        //console.log(response)
        if (response.status === 200) {
            companyLogo.value = response.data.logo
            form.value.company_logo = response.data.logo ?? ''
        }
    } catch (error) {
        console.log(error)
    }
}
const expenseAccountAlias = (row: any, accountList: any) => {
    // let alias = ''
    // accountDescList.forEach((item: any) => {
    //     if (item.id === row.debit_coa_id) {
    //         alias = item.name
    //     }
    // })
    // return row.debit_coa_code + ' | ' + alias
    const item = accountList.find((item: any) => item.account_code === row.debit_coa_code)
    return `${row.debit_coa_code.substring(0, 4)} | ${item?.name || ''}`
}
const addItem = () => {
    if (!form.value.items) {
        form.value.items = []
    }

    let debit_coa_id = null
    let debit_coa_code = ''
    let debit_coa_name = ''

    if (arTopCoaList.value.length > 0) {
        const id = arTopCoaList.value[0].debit_coa_id
        const code = arTopCoaList.value[0].debit_coa_code
        const coaItem = accountDescList.value.find((x: any) => x.account_code == code)

        debit_coa_id = id
        debit_coa_code = coaItem?.account_code
        debit_coa_name = coaItem?.name
    }

    form.value.items.push({
        item_no: form.value.items.length + 1,
        model: '',
        description: '',
        qty: null,
        unit_price: null,
        total: null,
        type: '',
        dr_cr: 'cr',
        debit_coa_code: debit_coa_code,
        debit_coa_id: debit_coa_id,
        debit_coa_name: debit_coa_name,
    })
}
const remove = (index: number) => {
    form.value.items.splice(index, 1)
    form.value.items.forEach((item: any, index: number) => {
        item.item_no = index + 1
    })
    form.value.discount = null
}
const emit = defineEmits(['save', 'dismiss', 'custom-cancel'])
const save = async () => {
    if (!form.value.items || (form.value.items && form.value.items.length === 0)) {
        message.error({
            content: 'Invoice must contain at least one [ Item ]',
            duration: 6,
        })
        return
    }
    if (!form.value.net_amount) {
        message.error({
            content: "Can't create invoice without [ Invoice Amount ]",
            duration: 6,
        })
        return
    }
    // if (!form.value.bill_to_email) {
    //     message.error({
    //         content: "Can't create invoice without [ Billing Email ]",
    //         duration: 6,
    //     })
    //     return
    // }

    if (await formRef.value?.validateFields()) {
        bankList.value.forEach((item: any) => {
            if (item.id == form.value.bank_id) {
                form.value.bank_account = item.account_no
                form.value.bank_name = item.name
            }
        })

        const queryForm = _.cloneDeep(form.value)
        // queryForm.items.forEach((item: any) => {
        //     item.credit_coa_code = accountDescList.value.filter((i: any) => i.id === item.credit_coa_id)[0].fieldCode
        // })

        // when line item total is negative, assign 'dr_cr' to opposite
        // for example ar's dr_cr default is 'cr'
        // when line item amount is negative
        // dr_cr change to 'dr'
        queryForm.items = queryForm.items.filter((i: any) => i.debit_coa_id !== null)
        queryForm.items.forEach((x: any) => {
            if (+x.total < 0) {
                x.dr_cr = 'dr'
            } else {
                if (!x.dr_cr) {
                    x.dr_cr = 'cr'
                }
            }
        })
        queryForm.fileId = currentPdfInfo.value.id || null
        queryForm.creator = userInfo?.id
        queryForm.creator_name = userInfo?.account
        // queryForm.companyName = queryForm.companyName
        //queryForm.bill_to_company=queryForm.bill_to_company.toUpperCase()
        if (fixedDate.value) {
            queryForm.invoice_due_date = moment(queryForm.invoice_due_date, 'YYYY-MM-DD')
                .add(fixedDate.value, 'days')
                .format('YYYY-MM-DD')
        }
        if (form.value.total_fee!.toFixed(2) !== (form.value.net_amount! + form.value.total_tax!).toFixed(2)) {
            message.error({
                content: 'Net amount plus tax does not equal total',
                duration: 8,
            })
            return
        }
        queryForm.br_type = queryForm.net_amount && queryForm.net_amount < 0 ? '3' : '0'
        // patch
        if (queryForm.exchange_rate === '') queryForm.exchange_rate = null
        //queryForm.value.bill_to_street = form.value.shipToStreet
        emit('save', queryForm)
    } else {
        return false
    }
}
const delayUpdate = (value: any) => {
    form.value.invoice_due_date = value
}
const exchange = () => {
    updateSpot()
}
const resetFormField = () => {
    ;(formRef.value as any).resetFields()
}
const cancel = () => {
    emit('dismiss')
}

const downloadFile = async () => {
    if (form.value.id) {
        const response = await getFileBlobById(form.value.invoice_url)
        if (response.status === 200 && response.data.size > 0) {
            const filename = form.value.invoice_url.split('/').pop()
            // const blob = new Blob([response.data], {type: 'application/pdf;charset=utf-8'})
            const blob = new Blob([response.data], {
                type: form.value.invoice_url.includes('.pdf')
                    ? 'application/pdf;charset=utf-8'
                    : 'image/jpeg;charset=utf-8',
            })
            FileSaver.saveAs(blob, filename)
        } else {
            // message.error({content: 'Invoice file is not available.'})
        }
    }
}

const reverseSales = async () => {
    if (props.readonlyMode) {
        try {
            const response = await reverseSalesRequest({
                id: form.value.id,
                creator: userInfo?.id,
                creator_name: userInfo?.account,
                posting_date: reverseDt?.value,
            })
            if (response.status === 200) {
                message.success('success')
                showConfirm.value = false

                emit('dismiss', true)
            }
        } catch (e: any) {
            console.log(e)
            // message.error(e.response.data.message)
        } finally {
            showReverseLoading.value = false
        }
    }
}

const convertSales = async () => {
    if (props.readonlyMode) {
        try {
            const response = await convertSalesRequest({
                id: form.value.id,
                creator: userInfo?.id,
                creator_name: userInfo?.account,
            })
            if (response.status === 200) {
                message.success('success')
                showConfirm.value = false

                emit('dismiss', true)
            }
        } catch (e: any) {
            console.log(e)
            // message.error(e.response.data.message)
        } finally {
            showConvertLoading.value = false
        }
    }
}

const handleQtyChange = (index: number) => {
    autoCalTaxFlag.value = '0'
    calItemTotal(index)
}
const handleUnitPriceChange = (index: number) => {
    autoCalTaxFlag.value = '0'
    calItemTotal(index)
}
const calItemTotal = (index: number) => {
    if (form.value.items[index].qty && form.value.items[index].unit_price) {
        form.value.items[index].total = new Decimal(form.value.items[index].qty || 0)
            .mul(form.value.items[index].unit_price || 0)
            .toDP(2)
            .toNumber()
    } else {
        form.value.items[index].total = 0.0
    }
}
const handleItemTotalChange = (row: any, index: number) => {
    autoCalTaxFlag.value = '0'
    if (row.total) {
        form.value.items[index].qty = undefined
        form.value.items[index].unit_price = null
    }
}
const blurItemTotalChange = (row: any, index: number) => {
    autoCalTaxFlag.value = '0'
    if (!autoCalculateState.value) {
        form.value.items[0].total = form.value.net_amount
    }
}
const selectblur = (e: any) => {
    if (e.target.value) {
        form.value.bill_to_company = e.target.value
    }
}
const selectblur1 = (e: any) => {
    if (e.target.value) {
        form.value.ship_to_company = e.target.value
    }
}
const companyNameFocus = (ref: any) => {
    // this.$refs[ref].$refs.reference.$refs.input.onblur = () => {
    //     const instantInputValue = this.$refs[ref].$refs.reference.$refs.input.value
    //     this.current.customerName = instantInputValue ? instantInputValue : ''
    // }
}
const companyNameBlur = () => {
    current.contact_name = form.value.bill_to_company ? form.value.bill_to_company : ''
}
const visibleChange = (visible: any, refName: any) => {
    if (visible) {
        const ref = refName
        let popper = ref.popper
        if (popper.$el) popper = popper.$el
        if (!Array.from(popper.children).some((v: any) => v.className === 'el-add-new-contact')) {
            //const el = document.createElement('ul')
            const el = document.createElement('div')
            el.className = 'el-add-new-contact'
            el.setAttribute(
                'style',
                'line-height: 34px; height: 34px;padding-top:7px;cursor:pointer; padding-left: 20px;color: #1A94D0; &:hover { color: lighten(#1A94D0, 10%); cursor: pointer;font-size: 14px;}',
            )
            el.innerHTML = `<div class="item-available-empty">
                    <i class="el-icon-circle-plus-outline"></i>
                    <span>Add New Business Partner</span>
                  </div>`
            //popper.appendChild(el)
            popper.insertBefore(el, popper.children[0])
            el.onclick = () => {
                addNewContact()
            }
        }
    }
}
const showContactCreationModal = (bool = false) => {
    show.value = bool
}
const addNewContact = () => {
    show.value = true
    compNameRef.value.blur()
}
const updateNewContact = async (response: any) => {
    await fetchCustomerDropDown({...currentUserCompanyQuery})
    // console.log(response)

    form.value.bill_to_street = response.data.billing_street || ''
    form.value.bill_to_city = response.data.billing_city || ''
    form.value.bill_to_province = response.data.billing_province || ''
    form.value.bill_to_postal_code = response.data.billing_postal_code || ''
    form.value.bill_to_country = response.data.billing_country || ''
    form.value.bill_to_email = response.data.email || ''
    form.value.bill_to_receiver = response.data.billing_receiver
    form.value.bill_to_tel = response.data.tel
    form.value.bill_to_company = response.data.contact_name
    form.value.bill_to_customer_id = response.data.contact_id

    form.value.ship_to_company = response.data.contact_name
    form.value.ship_to_street = response.data.shipping_street
    form.value.ship_to_city = response.data.shipping_city
    form.value.ship_to_province = response.data.shipping_province
    form.value.ship_to_postal_code = response.data.shipping_postal_code
    form.value.ship_to_country = response.data.shipping_country
    form.value.ship_to_receiver = response.data.shipping_receiver
    form.value.ship_to_email = response.data.email
    form.value.ship_to_tel = response.data.tel

    await getTaxRates({
        companyCode: companyTaxInfo.value.code,
        buyerCountryCode: response.data.office_country,
        buyerRegionCode: response.data.office_province,
        sellerCountryCode: companyTaxInfo.value.country,
        sellerRegionCode: companyTaxInfo.value.province,
    })

    await getArTopCoa()

    if (form.value.bill_to_country) {
        await fetchProvinceInfo(form.value.bill_to_country)
        billProvinceOptions.value = _.cloneDeep(provinceOptions.value)
        googleAutocomplete?.setComponentRestrictions({country: [form.value.bill_to_country.toLocaleLowerCase()]})
    }

    // calculate taxs by Province
    autoCalTaxFlag.value = '2'
    form.value.tax_content = calculateTaxRates(form.value.net_amount)
    await calculateInvoiceTotal()

    formRef.value?.clearValidate()
}

//  compNameSearch
const contactSearch = _.debounce(async (value: any) => {
    //reset volume
    contactKeyword.value = value
    contactPageSize.value = 10
    await fetchContactList()
}, 500)

const fetchContactList = async () => {
    try {
        contactLoading.value = true
        const queryObj: any = {}
        if (contactKeyword.value) {
            queryObj['contact_name[$like]'] = `%${contactKeyword.value}%`
        }
        //queryObj['$limit'] = contactPageSize.value
        queryObj['$limit'] = 10000
        queryObj['$skip'] = 0
        await fetchCustomerDropDown({...queryObj, ...currentUserCompanyQuery})
    } catch (e) {
        console.log(e)
    } finally {
        contactLoading.value = false
    }
    return void 0
}

const contactScroll = async (e: any) => {
    const {target} = e
    // when user scoll near bottom
    if (Math.abs(target.scrollTop + target.offsetHeight - target.scrollHeight) < 2 && !contactLoading.value) {
        contactPageSize.value += 10
        await fetchContactList()
    }
}

const handlePayMethodChange = async (flag: any, value: any) => {
    //console.log("flag",flag)
    //console.log("value",value)
    // if (value) {
    //     isDisable.value = false
    // } else {
    //     isDisable.value = true
    // }
    return
}

const handleCustomerNameKeyup = (event: any) => {
    newCustomerName.value = event.target.value.toUpperCase()
}

const handleCompanyChange = async (flag: any, value: any) => {
    // TODO: better to use comany id or code.
    console.log(value)
    if (!value) return
    const found = customersList.value.filter((item: any) => item.contact_id === value)
    // console.log("flag", flag)
    // console.log("customersList", this.customersList)
    console.log('found', found)
    const {
        company_id,
        office_country,
        office_province,
        contact_name,
        contact_id,
        billing_receiver,
        billing_street,
        billing_city,
        billing_province,
        billing_postal_code,
        billing_country,
        shipping_street,
        shipping_city,
        shipping_province,
        shipping_postal_code,
        shipping_receiver,
        shipping_country,
        email,
        tel,
    } = found[0]
    if (flag === 'bill') {
        form.value.bill_to_street = billing_street
        form.value.bill_to_city = billing_city
        form.value.bill_to_province = billing_province
        form.value.bill_to_postal_code = billing_postal_code
        form.value.bill_to_country = billing_country
        form.value.bill_to_email = email
        form.value.bill_to_receiver = billing_receiver
        form.value.bill_to_tel = tel
        form.value.bill_to_company = contact_name
        form.value.bill_to_customer_id = contact_id

        form.value.ship_to_company = contact_name
        form.value.ship_to_street = shipping_street
        form.value.ship_to_city = shipping_city
        form.value.ship_to_province = shipping_province
        form.value.ship_to_postal_code = shipping_postal_code
        form.value.ship_to_country = shipping_country
        form.value.ship_to_receiver = shipping_receiver
        form.value.ship_to_email = email
        form.value.ship_to_tel = tel

        await getTaxRates({
            companyCode: companyTaxInfo.value.code,
            buyerCountryCode:
                contact_name === 'DUMMY' ? office_country || companyTaxInfo.value.country : office_country,
            buyerRegionCode:
                contact_name === 'DUMMY' ? office_province || companyTaxInfo.value.province : office_province,
            sellerCountryCode: companyTaxInfo.value.country,
            sellerRegionCode: companyTaxInfo.value.province,
        })

        await getArTopCoa()

        shippingAsBillingCheck.value = true

        if (form.value.bill_to_country) {
            await fetchProvinceInfo(form.value.bill_to_country)
            billProvinceOptions.value = _.cloneDeep(provinceOptions.value)
            googleAutocomplete?.setComponentRestrictions({country: [form.value.bill_to_country.toLocaleLowerCase()]})
        }

        // calculate taxs by Province
        autoCalTaxFlag.value = '2'
        form.value.tax_content = calculateTaxRates(form.value.net_amount)
        await calculateInvoiceTotal()
    }
    if (flag === 'ship') {
        //this.form.shipToReceiver = shippingReceiver
        form.value.ship_to_street = shipping_street
        form.value.ship_to_city = shipping_city
        form.value.ship_to_province = shipping_province
        form.value.ship_to_postal_code = shipping_postal_code
        //this.form.shipToEmail = email
        //this.form.shipToTel = tel
        // const {shippingReceiver, shippingStreet, shippingCity, shippingProvince, shippingPostalCode, shippingCountry} = found[0]
        form.value.ship_to_receiver = shipping_receiver
        form.value.ship_to_email = email
        form.value.ship_to_tel = tel
        shippingAsBillingCheck.value = false
        // if (shipping_country) {
        //     shipProvinceOptions.value = getProvinceListByCountryCode.value(shipping_country)
        // }
    }
}
const getCountryFullNameByCode = (code: any, options: any) => {
    return options[code]
}
const handleShippingChange = (value: any) => {
    //if (value === null) this.form.shipping = 0.0
    if (form.value.net_amount) {
        form.value.shipping = value
        calculateInvoiceTotal()
    }
}
const handleDiscountChange = (value: any) => {
    if (value === null || value === undefined) form.value.discount = 0.0
    if (form.value.net_amount) {
        form.value.discount = value
        calculateInvoiceTotal()
    }
}
const handleDepositChange = (value: any) => {
    if (value === null || value === undefined) form.value.deposit = 0.0
    form.value.deposit = value
    calculateInvoiceTotal()
}
const changeTotalAmount = (value: any, reverse: boolean) => {
    autoCalTaxFlag.value = '1'
    if (value && form.value.items.length === 1) {
        if (form.value.invoice_currency !== localCurrency && reverse) {
            updateSpotReverse()
            value = form.value.total_fee ?? value
        }
        form.value.tax_content = calculateTaxRates(value)
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(form.value.total_fee || 0)
            .sub(new Decimal(form.value.total_tax))
            .toDP(2)
            .toNumber()
        form.value.items[0].total = form.value.net_amount
        form.value.items[0].qty = null
        form.value.items[0].unit_price = null
    } else if (!value && form.value.items.length === 1) {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0

        form.value.items[0].total = 0
        form.value.items[0].qty = 0
        form.value.items[0].unit_price = 0
    }
    if (form.value.invoice_currency !== localCurrency && !reverse) {
        updateSpot()
    }
}

const revertTaxCal = (amount: number | null = null) => {
    if (amount) {
        _.forEach(taxRatesList.value, (item, index) => {
            form.value.tax_content[index].value = new Decimal(amount).mul(item.value).toDP(2).toNumber()
        })
        form.value.total_fee = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            amount,
        )
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
    } else {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_fee = 0
        form.value.total_tax = 0
    }
}

const changeTaxExempt = () => {
    if (enableTaxExempt.value) {
        form.value.total_fee = form.value.net_amount ?? 0
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0
    } else {
        revertTaxCal(form.value.net_amount)
    }

    updateSpot()
}

const calculateTaxRates = (amount = null as number | null | undefined) => {
    if (enableTaxExempt.value) {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: 0}
        })
    }
    // change from item List ( row net amount)
    if (autoCalTaxFlag.value === '0' || autoCalTaxFlag.value === '2') {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: amount ? new Decimal(amount).mul(item.value).toDP(2).toNumber() : 0}
        })
    }

    // change from TOTAL
    const taxTotal = _.reduce(
        taxRatesList.value,
        (sum, item) => {
            return new Decimal(sum).add(new Decimal(item.value || 0)).toNumber()
        },
        0,
    )
    const netTotal = amount ? new Decimal(amount).div(new Decimal(1 + taxTotal)).toNumber() : 0
    return _.map(taxRatesList.value, (item: any) => {
        return {...item, value: netTotal ? new Decimal(netTotal).mul(item.value).toDP(2).toNumber() : 0}
    })
}
const calculateInvoiceTotal = async () => {
    if (!autoCalculateState.value) {
        console.log('this.autoCalculateState', autoCalculateState.value)
        const totalFee = form.value.total_fee ? form.value.total_fee : null
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(totalFee).sub(new Decimal(form.value.total_tax)).toDP(2).toNumber()
        form.value.items[0].total = form.value.net_amount
    } else {
        const shipFee = form.value.shipping || 0.0
        // const disCountFee = this.form.discount || 0.0
        const amountFee = form.value.net_amount ? form.value.net_amount : 0.0
        // const totalTaxableFee = amountFee + shipFee > 0 ? amountFee + shipFee : 0.0
        // const totalTaxableFee = amountFee + shipFee
        // const totalTaxableFee = amountFee + shipFee - disCountFee > 0 ? amountFee + shipFee - disCountFee : 0.0
        const totalTaxFee = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        const totalFee = new Decimal(amountFee).add(new Decimal(totalTaxFee)).toDP(2).toNumber()

        form.value.net_amount = new Decimal(amountFee).add(new Decimal(shipFee)).toDP(2).toNumber()
        // form.value.totalTaxable = totalTaxableFee
        form.value.total_tax = totalTaxFee
        form.value.total_fee = totalFee
        await updateSpot()
        // console.log(spot.value)
        // if (form.value.invoice_currency.toString() !== localCurrency) {
        //     form.value.total_fee_local = (form.value.total_fee * parseFloat(spot.value.rate ?? '')).toFixed(2)
        // } else {
        //     form.value.total_fee_local = form.value.total_fee
        // }
    }
}
const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}
const initFormData = () => {
    formRef.value?.resetFields()
    form.value.company_code = companyTaxInfo.value.code
    form.value.company_id = companyTaxInfo.value.id
    form.value.company_name = companyTaxInfo.value.name
    form.value.company_address = `${companyTaxInfo.value.address_line1} ${companyTaxInfo.value.address_line2 ?? ''}`
    form.value.company_tel = companyTaxInfo.value.phone
    form.value.company_email = companyTaxInfo.value.email
    // form.value.company_gst_no = companyTaxInfo.value.gst_hst_no
    // form.value.company_pst_no = companyTaxInfo.value.qst_pst_no // TODO
    form.value.tax_content = []
    form.value.total_tax = 0
    form.value.total_fee = 0
    form.value.total_fee_local = 0
    form.value.net_amount = 0
    form.value.items = []
    addItem()
    // this.$nextTick(() => {
    //   this.$refs.form.clearValidate()
    // })
    clearFormValidations()
}
const clearFormValidations = () => {
    nextTick(() => {
        if (formRef.value) (formRef.value as any).clearValidate()
    })
}

const handleTaxChange = (current: any, item: any) => {
    if (current === null) {
        item.value = 0
    } else {
        item.value = current
    }

    calculateInvoiceTotal()
}
const referenceNoChange = async (value: any) => {
    if (form.value.reference_no) {
        await checkRefNoRepetition(form.value.reference_no)
    }
}
const checkRefNoRepetition = async (value: any) => {
    const query = {
        company_code: userCompany[0].code,
        reference_no: value,
        page_index: 1,
        page_size: 10,
    }
    try {
        referenceNoLoading.value = true
        const response = await checkReferenceNoRepetition(query)
        if (response.data.statusCode === 200) {
            if (response.data.data.length > 0) {
                message.warning({
                    content: i18n.t('bkAp.msgReferenceNoExisted'),
                    duration: 5,
                })
            }
        } else {
            // message.error({
            //     content: i18n.t('bkAp.msgReferenceCheckFail'),
            //     duration: 5,
            // })
        }
    } catch (error) {
        console.log(error)
    } finally {
        referenceNoLoading.value = false
    }
}
const changeDate = (vale: any) => {
    console.log(moment(form.value.invoice_due_date, 'YYYY-MM-DD').add(vale, 'days').format('YYYY-MM-DD'))
}
const changeItemListRowExpenseAccount = (value: any, index: number, fieldCode: any) => {
    form.value.items[index].debit_coa_id = value
    const coaItem = accountDescList.value.find((x: any) => x.id == value)
    form.value.items[index].debit_coa_code = coaItem?.account_code
    form.value.items[index].debit_coa_name = coaItem?.name
}
const getArTopCoa = async () => {
    try {
        await fetchArTopCoa({
            company_code: form.value.company_code,
            bill_to_customer_id: form.value.bill_to_customer_id,
        })
        if (form.value.items.length === 1 && arTopCoaList.value.length > 0) {
            const code = arTopCoaList.value[0].debit_coa_code
            const coaItem = accountDescList.value.find((x: any) => x.account_code == code)

            form.value.items[0].debit_coa_id = coaItem?.id
            form.value.items[0].debit_coa_code = coaItem?.account_code
            form.value.items[0].debit_coa_name = coaItem?.name
        } else {
            form.value.items[0].debit_coa_id = null
            form.value.items[0].debit_coa_code = ''
            form.value.items[0].debit_coa_name = ''
        }
    } catch (error) {
        console.log(error)
    }
}
const changeAutoCalculateMode = (state: any) => {
    if (state) {
        const sum = form.value.items.reduce((prev: any, curr: any) => {
            return prev + curr.total
        }, 0)

        console.log(sum)
        form.value.net_amount = sum
        form.value.tax_content = calculateTaxRates(sum)
        calculateInvoiceTotal()
    }
}

const updateSpotReverse = async () => {
    if (form.value.invoice_currency.toString() === localCurrency) {
        form.value.total_fee = form.value.total_fee_local
        return
    }

    form.value.total_fee =
        form.value.total_fee_local != undefined
            ? Number((form.value.total_fee_local / parseFloat(spot.value.rate ?? '')).toFixed(2))
            : null
}

const getSpotInputDateStatus = (date: moment.MomentInput): number => {
    const weekOfDay = moment(date, 'YYYY-MM-DD').format('E')
    return 5 - +weekOfDay
}

const isEffectiveDate = (date: string) => {
    const year = moment(date).year()
    return (
        moment() <=
        moment()
            .year(year)
            .month(financialYear! + 13)
            .endOf('month')
    )
}

const postingDateChange = (date: string) => {
    if (financialYear !== null && !isEffectiveDate(date)) {
        Modal.info({
            title: 'Note',
            content: 'The deadline for input has passed.',
        })
    }
    updateSpot()
}

const updateSpot = async () => {
    if (form.value.invoice_currency.toString() === localCurrency) {
        form.value.total_fee_local = form.value.total_fee
        spotRateCheck.value = true
        return
    }

    const baseCurrency = form.value.invoice_currency
    const quoteCurrency = localCurrency

    const weekOfDayDiff = getSpotInputDateStatus(form.value.posting_date)
    isWeekend.value = weekOfDayDiff < 0
    if (!isWeekend.value) {
        spotPostingDate.value = form.value.posting_date
    } else {
        spotPostingDate.value = moment(form.value.posting_date, 'YYYY-MM-DD')
            .add(weekOfDayDiff, 'days')
            .format('YYYY-MM-DD')
    }
    try {
        spot.value = await getSpot({baseCurrency, quoteCurrency, date: spotPostingDate.value})
        form.value.exchange_rate = spot.value.rate
        spotRateCheck.value = spot.value.rate !== ''
    } catch (e) {
        console.log(e)
    }
    // spot.value = spot.value.rate_date !== form.value.posting_date ? await getSpot(form.value.posting_date) : spot.value

    // comments for test
    // postingDate.value = form.value.posting_date
    // isWeekend.value = getSpotInputDateStatus(form.value.posting_date)
    //
    // form.value.total_fee_local = form.value.total_fee != null ? form.value.total_fee * parseFloat(spot.value.rate) : null

    form.value.total_fee_local =
        form.value.total_fee != undefined
            ? Number((form.value.total_fee * parseFloat(spot.value.rate ?? '')).toFixed(2))
            : null
}

// computed
// const form_diff = computed(() => Number(form.value.total_fee_local) - Number(form.value.total_fee))
const form_diff = computed(() =>
    Math.abs(
        Number(form.value.total_fee_local) -
            Number((Number(form.value.total_fee || 0) * parseFloat(spot.value.rate || '1')).toFixed(2)),
    ),
)
const bankList = computed(() => store.state.BankInfoStore.bankList)
const customersList = computed(() => store.state.CommonDropDownStore.customerOptions)
const accountDescList = computed(() => store.state.CommonDropDownStore.accountDescList)
const accountCurrencyOptions = computed(() => store.state.CommonDropDownStore.bankCurrencyOptions)
const currentPdfInfo = computed(() => store.state.ArApBrStore.pdfInfo)
const companyTaxInfo = computed(() => store.state.TaxInfoStore.companyTaxInfo)
const arTopCoaList = computed(() => store.state.ArStore.arTopCoaList)
const invoiceDetail = computed(() => store.state.ArStore.invoiceDetail)
const taxRatesList: any = computed(() => {
    return _.cloneDeep(store.state.TaxCalculationStore.taxRatesList).map((i: any) => {
        return {...i, value: new Decimal(i.value).div(new Decimal(100)).toNumber()}
    })
})

const countryOptions = computed(() => store.getters.CommonDropDownStore.getCountriesList)
// const currentFileId = computed(() => route.query.fileId)
const discountMaxValue = computed(() => {
    const amount = form.value.net_amount || 0.0
    const shipping = form.value.shipping || 0.0
    return amount + shipping
})
const productServiceList: any = computed(() => store.state.ProductServiceStore.ProductServiceList)
const requireRule = (propName: string) => [
    {
        required: true,
        message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
        trigger: ['blur', 'change'],
    },
]

const optionalEmailRule = (rule: any, value: string) => {
    // 当值不存在时，返回 true，不触发任何验证
    if (!value) {
        return Promise.resolve()
    }

    // 当值存在时，应用邮箱验证规则
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i
    if (emailRegex.test(value)) {
        return Promise.resolve()
    }
    return Promise.reject()
}

const lengthLimitRule = (min = 1, max: number) => [
    {
        min,
        max,
        message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
        trigger: 'blur',
    },
]

const requireGLAccount = (rule: any, value: number | null) => {
    const index = Number(rule.field.match(/\.(\S*)\./)[1])
    const item = form.value.items[index]

    if (value === null && item.qty && item.total && item.unit_price) {
        return Promise.reject()
    } else {
        return Promise.resolve()
    }
}

const rules = reactive({
    // companyName: [...requireRule(i18n.t('bkAr.companyName'))],
    // companyAddress: [...this.requireRule(this.$t('bkAr.companyAddr'))],
    // companyPhone: [...this.requireRule(this.$t('bkAr.companyTel'))],
    // companyEmail: [
    //   ...this.requireRule(this.$t('bkAr.companyEmail')),
    //   {type: 'email', message: this.$t('bkCommonTag.msgEmailRule'), trigger: 'blur'},
    // ],
    companyGstNo: [...lengthLimitRule(1, 17)],
    companyPstNo: [...lengthLimitRule(1, 18)],

    // gst: [
    //   {
    //     required: true,
    //     message: this.$t('bkAr.companyGstHst'),
    //     trigger: ['blur'],
    //   },
    // ],
    // pst: [
    //   {
    //     required: true,
    //     message: this.$t('bkAr.companyPst'),
    //     trigger: ['blur'],
    //   },
    // ],
    invoice_currency: [...requireRule(i18n.t('bkAr.currency'))],
    invoice_create_date: [...requireRule(i18n.t('bkAr.date'))],
    invoice_due_date: [...requireRule(i18n.t('bkAr.dueDate'))],
    bill_to_company: [...requireRule(i18n.t('bkAr.billToCompany'))],
    billToEmail: [
        {validator: optionalEmailRule, message: i18n.t('bkCommonTag.msgEmailRule'), trigger: ['blur', 'change']},
    ],
    shipToStreet: [...requireRule(i18n.t('bkAr.shipToStreet'))],
    pay_method: [...requireRule(i18n.t('bkAr.payMethod'))],
    // billToEmail: [{type: 'email', message: this.$t('bkCommonTag.msgEmailRule'), trigger: ['blur', 'change']}],
    // shipToEmail: [{type: 'email', message: this.$t('bkCommonTag.msgEmailRule'), trigger: ['blur', 'change']}],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur', 'change'],
        },
    ],
    requireGLAccount: [
        {
            message: 'Selection required',
            validator: requireGLAccount,
            trigger: ['blur', 'change'],
        },
    ],
    // may be later. base on real procedure
    // amount: [...this.requireRule(this.$t('bkAr.amountRule'))],
    // shipping: [...this.requireRule(this.$t('bkAr.shipping'))],
    // totalTaxable: [...this.requireRule(this.$t('bkAr.totalTaxable'))],
    // totalTax: [...this.requireRule(this.$t('bkAr.totalTax'))],
    // tps: [...this.requireRule(this.$t('bkAr.tps'))],
    // tvq: [...this.requireRule(this.$t('bkAr.tvq'))],
    // tvp: [...this.requireRule(this.$t('bkAr.tvp'))],
    // totalFee: [...this.requireRule(this.$t('bkAr.totalFee'))],
    invoice_comments: [...lengthLimitRule(1, 200000)],
    posting_date: [...requireRule(i18n.t('bkAr.postingDate'))],
})
const taxsIndicator = () => {
    // TODO
    const state = {
        gstCond: 'ok',
        qstCond: 'ok',
        pstCond: 'ok',
    }
    const gstNo = form.value.company_gst_no
    const pstNo = form.value.company_pst_no
    if (gstNo && pstNo) {
        state.gstCond = 'ok'
        state.qstCond = 'ok'
        state.pstCond = 'ok'
    } else if (!gstNo && !pstNo) {
        state.gstCond = 'disable'
        state.qstCond = 'disable'
        state.pstCond = 'disable'
    } else if (gstNo) {
        state.gstCond = 'ok'
        state.qstCond = 'optional'
        state.pstCond = 'optional'
    } else {
        // with pstNo and no gstNo
        state.gstCond = 'disable'
        state.qstCond = 'disable'
        state.pstCond = 'disable'
    }
    return {...state}
}

const displayFileViewerModal = () => {
    showFileViewer.value = true
}
const closeFileViewerModal = () => {
    showFileViewer.value = false
}
const showEngineDetail = async (val: string) => {
    const query = {
        company: userCompany[0].code,
        document_no: val,
    }

    const response = await fetchGlList(query)
    glCurrent.value = _.cloneDeep(response.data)
    showGlDialog.value = true
}

const dismissEngineDetail = () => {
    glCurrent.value = {}
    showGlDialog.value = false
}

const getPopupContainer = (node: any) => {
    return tableWrapRef.value
}
const productServiceShow = ref(false)
const showProductServiceModal = (isShow = false) => {
    productServiceShow.value = isShow
}

watch(
    () => taxRatesList.value,
    list => {
        if (props.readonlyMode) return
        form.value.tax_content = []
        if (list.length) {
            list.forEach((i: any) => {
                form.value.tax_content = [...form.value.tax_content, {...i, value: 0}]
            })
            taxRateCheck.value = true
        } else {
            taxRateCheck.value = false
        }
    },
)

watch(
    () => form.value.reference_no,
    current => {
        if (current && !props.readonlyMode) {
            form.value.reference_no = current.toUpperCase()
        }
    },
)
watch(
    () => form.value.bill_to_company,
    current => {
        if (current && !props.readonlyMode) {
            form.value.bill_to_company = current.toUpperCase()
        }
    },
)
watch(
    () => form.value.bill_to_company,
    current => {
        if (current && shippingAsBillingCheck.value) {
            form.value.ship_to_company = current.toUpperCase()
        }
        if (autoCalTaxFlag.value === '0') {
            form.value.tax_content = calculateTaxRates(form.value.net_amount)
            calculateInvoiceTotal()
        }
        //  if (current && !props.readonlyMode) {
        //     this.form.shipToCompany = current.toUpperCase()
        //   }
    },
)
watch(
    () => form.value.bill_to_receiver,
    current => {
        if (current && shippingAsBillingCheck.value) {
            form.value.ship_to_receiver = current
        }
    },
)
watch(
    () => form.value.bill_to_street,
    current => {
        if (current && shippingAsBillingCheck.value) {
            form.value.ship_to_street = current
        }
    },
)
watch(
    () => form.value.bill_to_city,
    current => {
        if (current && shippingAsBillingCheck.value) {
            form.value.ship_to_city = current
        }
    },
)
watch(
    () => form.value.bill_to_province,
    current => {
        if (!props.readonlyMode) {
            form.value.tax_content = calculateTaxRates(form.value.net_amount)
            calculateInvoiceTotal()
            if (current && shippingAsBillingCheck.value) {
                form.value.ship_to_province = current
            }
        }
    },
)
watch(
    () => form.value.bill_to_postal_code,
    current => {
        if (current && shippingAsBillingCheck.value) {
            form.value.ship_to_postal_code = current
        }
    },
)
watch(
    () => form.value.bill_to_tel,
    current => {
        if (current && shippingAsBillingCheck.value) {
            form.value.ship_to_tel = current
        }
    },
)
watch(
    () => form.value.bill_to_email,
    current => {
        if (current && shippingAsBillingCheck.value) {
            form.value.ship_to_email = current
        }
    },
)
watch(
    () => form.value.items,
    newVal => {
        console.log(form.value.items)
        console.log('newVal', newVal)
        if (!autoCalculateState.value) return
        if (props.readonlyMode || !newVal) return
        const sum = newVal.reduce((prev: any, curr: any) => {
            return prev + curr.total
        }, 0)
        if (form.value.net_amount === sum) return
        //this.form.amount = sum ? sum : undefined
        form.value.net_amount = sum
        if (autoCalTaxFlag.value === '0') {
            form.value.tax_content = calculateTaxRates(form.value.net_amount)
            calculateInvoiceTotal()
        }
    },
    {deep: true},
)

watch([x, y], () => {
    if (!startedDrag.value) {
        startX.value = x.value
        startY.value = y.value
        const bodyRect = document.body.getBoundingClientRect()
        const titleRect = modalTitleRef.value?.getBoundingClientRect()

        dragRect.value.left = -(bodyRect.width + titleRect!.width)
        dragRect.value.right = bodyRect.width + titleRect!.width

        dragRect.value.bottom = bodyRect.height + titleRect!.height
        preTransformX.value = transformX.value
        preTransformY.value = transformY.value
    }
    startedDrag.value = true
})
watch(isDragging, () => {
    if (!isDragging) {
        startedDrag.value = false
    }
})

watchEffect(() => {
    if (startedDrag.value) {
        transformX.value =
            preTransformX.value + Math.min(Math.max(dragRect.value.left, x.value), dragRect.value.right) - startX.value
        transformY.value =
            preTransformY.value + Math.min(Math.max(dragRect.value.top, y.value), dragRect.value.bottom) - startY.value
    }
})

const transformStyle = computed<CSSProperties>(() => {
    return {
        transform: `translate(${transformX.value}px, ${transformY.value}px)`,
    }
})

const getTaxRates = async (query: any) => {
    const {buyerCountryCode, buyerRegionCode, sellerCountryCode, sellerRegionCode} = query

    if (!buyerCountryCode || !buyerRegionCode || !sellerCountryCode || !sellerRegionCode) {
        countryCodeCheck.value = false
        message.error({
            content: i18n.t('taxRates.countryNotSet'),
            duration: 8,
        })

        updateTaxRates([])
        return
    } else {
        countryCodeCheck.value = true
    }
    formLoading.value = true
    await fetchTaxRates({...query, invoiceDate: moment().valueOf(), action: 'Sale'})
    formLoading.value = false
}

onBeforeMount(async () => {
    updateTaxRates([])
    if (props.operationMode === 'editing' || props.readonlyMode) {
        let invoiceObj = props.currentInvoice
        if (props.invoiceId) {
            const res = await fetchInvoiceDetail({id: props.invoiceId})
            invoiceObj = res.data.data
        } else if (props.invoiceNo) {
            const res = await fetchInvoiceDetail({invoice_no: props.invoiceNo, company_code: userCompany[0].code})
            invoiceObj = res.data.data[0]
        }
        form.value = _.cloneDeep(invoiceObj)

        reverseDt.value = invoiceObj.posting_date

        const {max_reverse_number, reverse_number} = props.currentInvoice // number compare only used by BR
        if (max_reverse_number !== undefined && reverse_number !== undefined) {
            enableReverse.value = Boolean(max_reverse_number > reverse_number)
        } else {
            enableReverse.value = form.value.br_flag !== 3
        }
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee, balance
        // const {amount, totalTaxable, totalTax, totalFee, balance} = _.cloneDeep(props.currentInvoice)
        const {net_amount, total_tax, total_fee, balance} = _.cloneDeep(invoiceObj)
        await nextTick(() => {
            form.value.net_amount = net_amount ? net_amount : 0.0
            form.value.totalTaxable = net_amount ? net_amount : 0.0
            form.value.total_tax = total_tax ? total_tax : 0.0
            form.value.total_fee = total_fee ? total_fee : 0.0
            form.value.balance = balance ? balance : 0.0
        })
    }
    if (props.from === 'copy') {
        await fetchContactList()
        await fetchCompanyTaxInfo({code: userCompany[0].code})
        const invoiceObj = props.currentInvoice
        form.value = _.cloneDeep(props.currentInvoice) // 使用深克隆
        // form.value.tax_content = _.cloneDeep(props.currentInvoice.tax_content)
        // current.contact_name = props.currentInvoice.bill_to_company
        reverseDt.value = invoiceObj.posting_date
        // handleCompanyChange('bill', form.value.bill_to_customer_id)
        const {max_reverse_number, reverse_number} = props.currentInvoice // number compare only used by BR
        if (max_reverse_number !== undefined && reverse_number !== undefined) {
            enableReverse.value = Boolean(max_reverse_number > reverse_number)
        } else {
            enableReverse.value = form.value.br_flag !== 3
        }
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee, balance
        // const {amount, totalTaxable, totalTax, totalFee, balance} = _.cloneDeep(props.currentInvoice)
        const {net_amount, total_tax, total_fee, balance} = _.cloneDeep(invoiceObj)
        await nextTick(() => {
            form.value.net_amount = net_amount ? net_amount : 0.0
            form.value.totalTaxable = net_amount ? net_amount : 0.0
            form.value.total_tax = total_tax ? total_tax : 0.0
            form.value.total_fee = total_fee ? total_fee : 0.0
            form.value.balance = balance ? balance : 0.0
        })
        // console.log(
        //     '===================================props.currentInvoice.bill_to_country',
        //     props.currentInvoice.bill_to_country,
        // )
        // console.log('===================================props.currentInvoice', props.currentInvoice)
        await getTaxRates({
            companyCode: props.currentInvoice.company_code,
            buyerCountryCode: props.currentInvoice.bill_to_country,
            buyerRegionCode: props.currentInvoice.bill_to_province,
            sellerCountryCode: companyTaxInfo.value.country,
            sellerRegionCode: companyTaxInfo.value.province,
        })

        // await getArTopCoa(props.currentInvoice.company_code, props.currentInvoice.bill_to_customer_id)
        if (form.value.bill_to_country) {
            await fetchProvinceInfo(form.value.bill_to_country)
            billProvinceOptions.value = _.cloneDeep(provinceOptions.value)
            googleAutocomplete?.setComponentRestrictions({country: [form.value.bill_to_country.toLocaleLowerCase()]})
        }

        // calculate taxs by Province
        autoCalTaxFlag.value = '2'
        form.value.tax_content = calculateTaxRates(form.value.net_amount)
        await calculateInvoiceTotal()

        formRef.value?.clearValidate()
        // console.log('===================getTaxRates', form.value)
    } else {
        if (!props.readonlyMode && props.operationMode === 'creating' && form.value.items.length === 0) {
            addItem()
        }
    }
    console.log('==================props.readonlyMode', props.readonlyMode)
    if (!props.readonlyMode) {
        form.value.bill_to_company = ''
        form.value.invoice_create_date = getToday()
        form.value.invoice_due_date = getToday()
        form.value.posting_date = getToday()
    }
    try {
        formLoading.value = true
        if (!props.readonlyMode && props.from !== 'copy') {
            await Promise.all([
                fetchAllBankList({company_code: userCompany[0].code}),
                fetchCustomerDropDown({...currentUserCompanyQuery}),
                // fetchAccountDesc({company_id: userCompany[0].id}),
                fetchAccountDescDropdown(accountQuery),
                fetchCompanyTaxInfo({code: userCompany[0].code}),
                fetchProductServiceList({company_code: userCompany[0].code, $limit: -1}),
            ])
            form.value.company_code = companyTaxInfo.value.code
            form.value.company_id = companyTaxInfo.value.id
            form.value.company_name = companyTaxInfo.value.name
            form.value.company_address = `${companyTaxInfo.value.address_line1} ${
                companyTaxInfo.value.address_line2 ?? ''
            }`
            form.value.company_tel = companyTaxInfo.value.phone
            form.value.company_email = companyTaxInfo.value.email
            // form.value.company_gst_no = companyTaxInfo.value.gst_hst_no
            // form.value.company_pst_no = companyTaxInfo.value.qst_pst_no
            // TODO
        }
        if (!props.readonlyMode && props.from === 'copy') {
            await Promise.all([
                fetchAllBankList({company_code: userCompany[0].code}),
                // fetchCustomerDropDown({...currentUserCompanyQuery}),
                // fetchAccountDesc({company_id: userCompany[0].id}),
                fetchAccountDescDropdown(accountQuery),
                fetchCompanyTaxInfo({code: userCompany[0].code}),
                fetchProductServiceList({company_code: userCompany[0].code, $limit: -1}),
            ])
            form.value.company_code = companyTaxInfo.value.code
            form.value.company_id = companyTaxInfo.value.id
            form.value.company_name = companyTaxInfo.value.name
            form.value.company_address = `${companyTaxInfo.value.address_line1} ${
                companyTaxInfo.value.address_line2 ?? ''
            }`
            form.value.company_tel = companyTaxInfo.value.phone
            form.value.company_email = companyTaxInfo.value.email
            // form.value.company_gst_no = companyTaxInfo.value.gst_hst_no
            // form.value.company_pst_no = companyTaxInfo.value.qst_pst_no
            // TODO
        }
        clearFormValidations()
        // update invoice file info
        const fileObj = {
            file_name: form.value.invoice_url?.split('/').pop(),
            file_url: form.value.invoice_url,
            // id: form.value.invoice_url?.split('/').pop(),
        }
        updatePdfInfo(fileObj)
    } catch (error) {
        console.log(error)
    } finally {
        formLoading.value = false
    }
    if (form.value.reference_no && !props.readonlyMode) {
        await checkRefNoRepetition(form.value.reference_no)
    }
    const currentCustomers = customersList.value.filter(
        (item: any) => form.value.bill_to_customer_id === item.contact_id,
    )
    if (currentCustomers.length > 0) {
        form.value.bill_to_company = currentCustomers[0].contact_name
    }

    await updateSpot()
})

onUnmounted(() => {
    updateArTopCoa([])
})
const VNodes = (_: any, {attrs}: any) => {
    return attrs.vnodes
}
const filterOption = (input: string, option: any) => {
    console.log(option)

    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const viewerStyle = {
    height: '500px',
}

defineExpose({initFormData})
</script>
<template>
    <div class="page-container-ar-invoice-form">
        <div v-if="!readonlyMode">
            <a-spin :spinning="formLoading">
                <a-form
                    v-if="!readonlyMode"
                    ref="formRef"
                    :model="form"
                    :rules="readonlyMode ? {} : rules"
                    :layout="'vertical'"
                    class="form-box"
                    name="form"
                >
                    <div class="ar-invoice-block">
                        <a-row :gutter="24">
                            <a-col :span="8">
                                <!--logo图片-->
                                <div class="imagelogoName" :style="companyLogo ? {'text-align': 'left'} : {}">
                                    <img v-if="companyLogo" :src="companyLogo" class="form-form-new-left-logo" />
                                    <div v-else class="company-info-logo-img-placeholder">
                                        <img src="@/assets/image/empty.png" />
                                    </div>
                                </div>
                            </a-col>
                            <a-col :span="8"> </a-col>
                            <a-col :span="8">
                                <div class="right-title-wrap">
                                    <h1 class="rightTitle">{{ i18n.t('bkArInvoice.billing') }}</h1>
                                </div>
                            </a-col>
                            <a-col :span="8" class="form-input-box-wrap">
                                <div class="form-input-box-wrap-inner">
                                    <div class="form-input-box">
                                        <div class="form-input-box-title">To</div>
                                        <a-form-item name="bill_to_company" class="form-box-item_bill_to_company">
                                            <template v-slot:label>
                                                {{ i18n.t('bkAp.billTo') }}
                                                <a-tooltip placement="top">
                                                    <template #title>
                                                        If there is no existing item corresponding
                                                        <br />to the entered information, <br />a new item is created
                                                    </template>
                                                    <question-circle-outlined class="el-icon-question" />
                                                </a-tooltip>
                                            </template>
                                            <a-select
                                                :placeholder="i18n.t('workTimeManager.msgInput')"
                                                allow-create
                                                show-search
                                                default-active-first-option
                                                :filter-option="false"
                                                option-label-prop="label"
                                                v-model:value="form.bill_to_customer_id"
                                                @visible-change="(v: any) => visibleChange(v, 'compNameRef')"
                                                @focus="companyNameFocus('compNameRef')"
                                                @blur="companyNameBlur"
                                                ref="compNameRef"
                                                @keyup="handleCustomerNameKeyup($event)"
                                                @change="handleCompanyChange('bill', $event)"
                                                style="width: 100%"
                                                allowClear
                                                :disabled="readonlyMode"
                                                @search="contactSearch"
                                                @popupScroll="contactScroll"
                                                :loading="contactLoading"
                                                popper-class="company-name-class"
                                                :popper-append-to-body="false"
                                                :options="customersList.map((item: any) => ({ key: item.contact_id, value: item.contact_id, label: item.contact_name }))"
                                            >
                                                <template #dropdownRender="{menuNode: menu}">
                                                    <div
                                                        style="padding: 4px 8px; cursor: pointer; color: #004fc1"
                                                        @click="addNewContact"
                                                    >
                                                        <plus-circle-outlined />
                                                        Add New Business Partner
                                                        <a-divider style="margin: 4px 0" />
                                                    </div>
                                                    <v-nodes :vnodes="menu" />
                                                </template>
                                                <!-- <a-select-option style="width: 100%" v-for="item in customersList" :key="item.id"
                                                :label="item.customerName" :value="item.customerName"></a-select-option> -->
                                            </a-select>
                                        </a-form-item>
                                        <a-form-item
                                            name="bill_to_receiver"
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToReceiver')"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_receiver"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            name="bill_to_street"
                                            v-show="true"
                                            :label="i18n.t('bkAr.billingStreet')"
                                            class="form-box-item_billing_address"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_street"
                                                :placeholder="i18n.t('commonTag.msgInput')"
                                                id="bill_to_street"
                                                :disabled="readonlyMode || false"
                                            >
                                            </a-input>
                                        </a-form-item>
                                        <a-form-item
                                            name="bill_to_city"
                                            v-show="true"
                                            :label="i18n.t('bkAr.billToCity')"
                                            class="form-box-item_billing_city"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_city"
                                                :placeholder="i18n.t('commonTag.msgInput')"
                                                :disabled="readonlyMode || false"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            name="bill_to_province"
                                            v-show="true"
                                            :label="i18n.t('bkAr.billToProvince')"
                                            class="form-box-item_billing_province"
                                        >
                                            <a-select
                                                v-model:value="form.bill_to_province"
                                                :disabled="readonlyMode || false"
                                                :placeholder="' '"
                                                style="width: 100%"
                                            >
                                                <a-select-option
                                                    v-for="item in billProvinceOptions"
                                                    :value="item.key"
                                                    :key="item.value"
                                                    >{{ item.value }}
                                                </a-select-option>
                                            </a-select>
                                        </a-form-item>
                                        <a-form-item
                                            name="bill_to_postal_code"
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToZip')"
                                            class="form-box-item_billing_postalCode"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_postal_code"
                                                :placeholder="i18n.t('commonTag.msgInput')"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            name="bill_to_tel"
                                            :label="i18n.t('bkAr.billToTel')"
                                            class="form-box-item_billing_tel"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_tel"
                                                :placeholder="i18n.t('commonTag.msgInput')"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            :rules="rules.billToEmail"
                                            v-show="true"
                                            name="bill_to_email"
                                            :label="i18n.t('bkAr.billToEmail')"
                                            class="form-box-item_billing_email"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_email"
                                                :placeholder="i18n.t('commonTag.msgInput')"
                                                :disabled="readonlyMode"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item name="ship_to_company" :label="i18n.t('ArComponents.shipTo')">
                                            <a-select
                                                :placeholder="i18n.t('workTimeManager.msgInput')"
                                                show-search
                                                style="width: 100%"
                                                v-model:value="form.ship_to_company"
                                                :disabled="readonlyMode"
                                                @change="handleCompanyChange('ship', $event)"
                                            >
                                                <a-select-option
                                                    style="width: 100%"
                                                    v-for="item in customersList"
                                                    :key="item.contact_id"
                                                    :value="item.contact_name"
                                                    clearable
                                                    >{{ item.contact_name }}</a-select-option
                                                >
                                            </a-select>
                                        </a-form-item>
                                        <a-form-item
                                            name="ship_to_street"
                                            v-show="false"
                                            :label="i18n.t('bkAr.companyAddr')"
                                            class="form-box-item_shipping_address"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_street"
                                                :disabled="readonlyMode"
                                            ></a-input>
                                        </a-form-item>
                                    </div>
                                </div>
                            </a-col>
                            <a-col :span="8" class="form-input-box-wrap">
                                <div class="form-input-box-wrap-inner">
                                    <div class="form-input-box">
                                        <div class="form-input-box-title">From</div>
                                        <div label="" class="form-box-header form-box-shipping_line" v-show="false">
                                            <div class="line-item">
                                                <span style="margin-right: 10px">{{
                                                    i18n.t('bkAr.shipToSameAddr')
                                                }}</span>
                                                <a-checkbox
                                                    v-model:checked="shippingAsBillingCheck"
                                                    :disabled="readonlyMode"
                                                ></a-checkbox>
                                            </div>
                                        </div>

                                        <!-- <a-form-item
                                                                v-show="false"
                                                                :label="i18n.t('bkAr.shipToReceiver')"
                                                                class="form-box-item_shipping_receiver"
                                                            >
                                                                <a-input
                                                                    v-model:value="form.shipToReceiver"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                ></a-input>
                                                            </a-form-item>
                                                            <a-form-item
                                                                v-show="false"
                                                                :label="i18n.t('bkAr.shipToStreet')"
                                                                class="form-box-item_shipping_address"
                                                            >
                                                                <a-input
                                                                    v-model:value="form.shipToStreet"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                >
                                                                </a-input>
                                                            </a-form-item>
                                                            <a-form-item
                                                                v-show="false"
                                                                :label="i18n.t('bkAr.shipToCity')"
                                                                class="form-box-item_shipping_city"
                                                            >
                                                                <a-input
                                                                    v-model:value="form.shipToCity"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                >
                                                                </a-input>
                                                            </a-form-item>
                                                            <a-form-item
                                                                v-show="false"
                                                                :label="i18n.t('bkAr.shipToProvince')"
                                                                class="form-box-item_shipping_province"
                                                            >
                                                                <a-input
                                                                    v-model:value="form.shipToProvince"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                ></a-input>
                                                                <a-select
                                                                    v-model:value="form.shipToProvince"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                    :placeholder="' '"
                                                                >
                                                                    <a-select-option
                                                                        v-for="item in shipProvinceOptions"
                                                                        :value="item.key"
                                                                        :key="item.value"
                                                                        >{{ item.value }}
                                                                    </a-select-option>
                                                                </a-select>
                                                            </a-form-item>
                                                            <a-form-item
                                                                v-show="false"
                                                                :label="i18n.t('bkAr.shipToZip')"
                                                                class="form-box-item_shipping_postalCode"
                                                            >
                                                                <a-input
                                                                    v-model:value="form.shipToPostalCode"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                ></a-input>
                                                            </a-form-item>
                                                            <a-form-item
                                                                v-show="false"
                                                                :label="i18n.t('bkAr.shipToTel')"
                                                                class="form-box-item_shipping_tel"
                                                            >
                                                                <a-input
                                                                    v-model:value="form.shipToTel"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                >
                                                                </a-input>
                                                            </a-form-item>
                                                            <a-form-item
                                                                v-show="false"
                                                                :label="i18n.t('bkAr.shipToEmail')"
                                                                class="form-box-item_shipping_email"
                                                            >
                                                                <a-input
                                                                    v-model:value="form.shipToEmail"
                                                                    :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                                >
                                                                </a-input>
                                                            </a-form-item> -->

                                        <a-form-item style="padding-top: 2px">
                                            <template v-slot:label>
                                                {{ i18n.t('bkAr.companyName') }}
                                            </template>
                                            <a-input
                                                v-model:value="form.company_name"
                                                :disabled="readonlyMode"
                                            ></a-input>
                                        </a-form-item>

                                        <a-form-item :label="i18n.t('bkAr.companyEmail')" style="padding-top: 2px">
                                            <a-input
                                                v-model:value="form.company_email"
                                                :disabled="readonlyMode"
                                            ></a-input>
                                        </a-form-item>

                                        <a-form-item :label="i18n.t('bkAr.companyTel')" style="padding-top: 2px">
                                            <a-input
                                                v-model:value="form.company_tel"
                                                :disabled="readonlyMode"
                                            ></a-input>
                                        </a-form-item>

                                        <a-form-item
                                            :label="i18n.t('bkAr.companyAddr')"
                                            class="form-box-item_company_addr"
                                            style="padding-top: 2px"
                                        >
                                            <a-input
                                                v-model:value="form.company_address"
                                                :disabled="readonlyMode"
                                            ></a-input>
                                        </a-form-item>
                                    </div>
                                </div>
                            </a-col>
                            <a-col :span="8" class="form-input-box-wrap">
                                <div class="form-input-box-wrap-inner">
                                    <div class="form-input-box">
                                        <div class="form-input-box-title">{{ i18n.t('update.other') }}</div>
                                        <a-form-item name="reference_no" class="form-box-item_reference_no">
                                            <template v-slot:label>
                                                {{ i18n.t('bkAr.referenceNo') }}
                                                <a-tooltip placement="top">
                                                    <template #title>
                                                        <div>Reference No. in the receipts.</div>
                                                    </template>
                                                    <question-circle-outlined class="el-icon-question" />
                                                </a-tooltip>
                                            </template>
                                            <a-spin :spinning="referenceNoLoading" wrapperClassName="input-spin">
                                                <a-input
                                                    v-model:value="form.reference_no"
                                                    :placeholder="i18n.t('commonTag.msgInput')"
                                                    @blur="referenceNoChange"
                                                    :popper-append-to-body="false"
                                                    :disabled="readonlyMode"
                                                >
                                                </a-input>
                                            </a-spin>
                                        </a-form-item>

                                        <a-form-item
                                            name="invoice_currency"
                                            :label="i18n.t('bkAr.currency')"
                                            class="form-box-item_currency"
                                            style="margin-bottom: 30px"
                                        >
                                            <a-select
                                                :placeholder="i18n.t('workTimeManager.msgInput')"
                                                v-model:value="form.invoice_currency"
                                                :disabled="readonlyMode"
                                                @change="exchange"
                                                style="width: 100%"
                                            >
                                                <a-select-option
                                                    v-for="item in accountCurrencyOptions"
                                                    :key="item.value"
                                                    :value="item.key"
                                                    clearable
                                                    style="width: 100%"
                                                    >{{ item.value }}</a-select-option
                                                >
                                            </a-select>
                                            <!-- <div v-show="form.invoice_currency === localCurrency" style="padding: 2px 5px">
                                                                    &nbsp;
                                                                </div> -->
                                            <div
                                                v-show="form.invoice_currency === localCurrency"
                                                style="padding: 2px 5px"
                                            >
                                                &nbsp;
                                            </div>
                                            <div
                                                v-show="form.invoice_currency !== localCurrency"
                                                style="color: red; padding: 2px 5px"
                                            >
                                                <!--  <span v-if="!isWeekend && !spot.rate">-->
                                                <!--      Spot currency is null on {{ form.posting_date }}, pls contact-->
                                                <!--      administrator-->
                                                <!--  </span>-->
                                                <!--  <span v-else>-->
                                                <!--      Spot currency is {{ spot.rate || 'null' }} on {{ spotPostingDate-->
                                                <!--      }}{{ !spot.rate ? ', pls contact administrator' : '' }}-->
                                                <!--  </span>-->
                                                <span>
                                                    {{
                                                        i18n.t('ApComponents.spotCurrency', {
                                                            rate: spot.rate || 'null',
                                                            date: spotPostingDate,
                                                        })
                                                    }}
                                                    {{ !spot.rate ? ', ' + i18n.t('ApComponents.contactAdmin') : '' }}
                                                </span>
                                            </div>
                                        </a-form-item>

                                        <a-form-item
                                            name="invoice_due_date"
                                            :label="i18n.t('bkAr.dueDate')"
                                            class="form-box-item_due_date"
                                        >
                                            <a-date-picker
                                                v-model:value="form.invoice_due_date"
                                                type="date"
                                                format="YYYY-MM-DD"
                                                valueFormat="YYYY-MM-DD"
                                                :placeholder="i18n.t('bkAr.dueDate')"
                                                clearable
                                                :disabled="readonlyMode"
                                                style="width: 100%"
                                            >
                                            </a-date-picker>
                                        </a-form-item>

                                        <a-form-item name="pay_method" :label="i18n.t('bkAr.payMethod')" v-show="false">
                                            <template v-slot:label> {{ i18n.t('bkAr.payMethod') }} </template>
                                            <a-select
                                                :placeholder="i18n.t('workTimeManager.msgInput')"
                                                default-first-option
                                                v-model:value="form.pay_method"
                                                @change="handlePayMethodChange('pay_method', $event)"
                                                style="width: 100%"
                                                :disabled="readonlyMode"
                                                popper-class="company-name-class"
                                                :popper-append-to-body="false"
                                            >
                                                <a-select-option
                                                    style="width: 100%"
                                                    v-for="item in payMethodOptions"
                                                    :key="item.label"
                                                    :value="item.value"
                                                    >{{ item.label }}</a-select-option
                                                >
                                            </a-select>
                                        </a-form-item>

                                        <a-form-item :label="i18n.t('bkArInvoice.postingDate')" :name="!readonlyMode ? 'posting_date' : ''">
                                            <a-date-picker
                                                v-model:value="form.posting_date"
                                                type="date"
                                                format="YYYY-MM-DD"
                                                valueFormat="YYYY-MM-DD"
                                                :placeholder="i18n.t('bkAr.invoiceComment')"
                                                style="width: 100%"
                                                clearable
                                                @change="postingDateChange"
                                            >
                                            </a-date-picker>
                                        </a-form-item>
                                    </div>
                                </div>
                            </a-col>
                        </a-row>
                    </div>
                    <div class="ar-invoice-block" ref="tableWrapRef">
                        <a-table :dataSource="form.items" :pagination="false">
                            <a-table-column
                                align="center"
                                :title="i18n.t('bkAr.itemNo')"
                                data-index="item_no"
                                width="5%"
                            />
                            <!-- <a-table-column
                                align="left"
                                :title="i18n.t('bkAr.modelNumber')"
                                data-index="model"
                                width="20%"
                            >
                                <template #default="{index, record}">
                                    <a-form-item :name="['items', index, 'model']" v-if="!readonlyMode">
                                        <a-input
                                            v-model:value="record.model"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            class="table-input"
                                        ></a-input>
                                    </a-form-item>
                                    <span v-else>
                                        {{ record.model }}
                                    </span>
                                </template>
                            </a-table-column> -->
                            <a-table-column
                                align="left"
                                :title="i18n.t('bkAp.modelNumber')"
                                data-index="model"
                                width="20%"
                            >
                                <template #default="{index, record}">
                                    <a-form-item
                                        :name="['items', index, 'model']"
                                        v-if="!readonlyMode"
                                        class="column-item-input"
                                    >
                                        <a-select
                                            v-model:value="record.model"
                                            mode="combobox"
                                            :show-search="true"
                                            :filter-option="true"
                                            :allowClear="true"
                                            :showArrow="true"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            @change="FormItemModelChange($event, index, record)"
                                            :options="productServiceList.map((item: any) => ({ key: item.id, value: item.product_service, label: item.product_service }))"
                                            :getPopupContainer="getPopupContainer"
                                        >
                                            <template #dropdownRender="{menuNode: menu}">
                                                <div
                                                    style="padding: 4px 8px; cursor: pointer; color: #004fc1"
                                                    @click="showProductServiceModal(true)"
                                                >
                                                    <plus-circle-outlined />
                                                    Add New Product/Service
                                                    <a-divider style="margin: 4px 0" />
                                                </div>
                                                <v-nodes :vnodes="menu" />
                                            </template>
                                        </a-select>
                                    </a-form-item>
                                    <span v-else>
                                        {{ record.model }}
                                    </span>
                                </template>
                            </a-table-column>
                            <a-table-column
                                align="left"
                                :title="i18n.t('bkAr.description')"
                                data-index="description"
                                :ellipsis="true"
                                width="15%"
                            >
                                <template #default="{index, record}">
                                    <a-form-item
                                        :name="['items', index, 'description']"
                                        v-if="!readonlyMode"
                                        class="column-item-input"
                                    >
                                        <a-input
                                            v-model:value="record.description"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            class="table-input"
                                        ></a-input>
                                    </a-form-item>
                                    <span v-else>
                                        {{ record.description }}
                                    </span>
                                </template>
                            </a-table-column>
                            <a-table-column
                                align="left"
                                :title="i18n.t('bkAr.accountingCategory')"
                                data-index="debit_coa_id"
                                width="20%"
                            >
                                <template #default="{index, record}">
                                    <a-form-item
                                        :name="['items', index, 'debit_coa_id']"
                                        :rules="rules['requireGLAccount']"
                                        v-if="!readonlyMode"
                                        class="column-item-input"
                                    >
                                        <a-select
                                            :placeholder="i18n.t('workTimeManager.msgInput')"
                                            v-model:value="record.debit_coa_id"
                                            show-search
                                            :dropdownMatchSelectWidth="400"
                                            :filter-option="filterOption"
                                            :getPopupContainer="getPopupContainer"
                                            class="table-input"
                                            @change="
                                                changeItemListRowExpenseAccount(record.debit_coa_id, index, record)
                                            "
                                        >
                                            <a-select-option
                                                v-for="item in accountDescList"
                                                :key="item.account_code + ' | ' + item.name"
                                                :value="item.id"
                                                >{{
                                                    item.account_code.substring(0, 4) + ' | ' + item.name
                                                }}</a-select-option
                                            >
                                        </a-select>
                                    </a-form-item>
                                    <span v-else>
                                        {{ expenseAccountAlias(record, accountDescList) }}
                                    </span>
                                </template>
                            </a-table-column>
                            <a-table-column align="left" :title="i18n.t('bkAr.qty')" data-index="qty" width="10%">
                                <template #default="{index, record}">
                                    <a-form-item
                                        :name="['items', index, 'qty']"
                                        v-if="!readonlyMode"
                                        class="column-item-input"
                                    >
                                        <a-input-number
                                            v-model:value="record.qty"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            :controls="false"
                                            :max="9999999"
                                            :min="0 || null"
                                            class="table-input"
                                            @change="handleQtyChange(index)"
                                        ></a-input-number>
                                    </a-form-item>
                                    <span v-else>
                                        {{ record.qty }}
                                    </span>
                                </template>
                            </a-table-column>
                            <a-table-column
                                align="left"
                                :title="i18n.t('bkAr.unitPrice')"
                                data-index="unit_price"
                                width="10%"
                            >
                                <template #default="{index, record}">
                                    <a-form-item
                                        :name="['items', index, 'unit_price']"
                                        v-if="!readonlyMode"
                                        class="column-item-input"
                                    >
                                        <a-input-number
                                            v-model:value="record.unit_price"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            :controls="false"
                                            :precision="3"
                                            :min="0 || null"
                                            class="table-input"
                                            @change="handleUnitPriceChange(index)"
                                        ></a-input-number>
                                    </a-form-item>
                                    <span v-else>
                                        {{ Number(record.unit_price).toFixed(3) }}
                                    </span>
                                </template>
                            </a-table-column>
                            <a-table-column align="left" :title="i18n.t('bkAr.amount')" data-index="total" width="12%">
                                <template #default="{index, record}">
                                    <a-form-item
                                        :name="['items', index, 'total']"
                                        :rules="rules['requireItem']"
                                        v-if="!readonlyMode"
                                        class="column-item-input"
                                    >
                                        <!-- <a-input-number
                                            v-model:value="record.total"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            :controls="false"
                                            :precision="2"
                                            class="table-input"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            @change="handleItemTotalChange(record, index)"
                                            @blur="blurItemTotalChange(record, index)"
                                        >
                                        </a-input-number> -->
                                        <InputNumber
                                            class="amount-input-prime item-amount"
                                            v-model="record.total"
                                            :controls="false"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            :disabled="!autoCalculateState"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="handleItemTotalChange(record, index)"
                                            @blur="blurItemTotalChange(record, index)"
                                        />
                                    </a-form-item>
                                    <span v-else>
                                        {{ Number(record.total).toFixed(2) }}
                                    </span>
                                </template>
                            </a-table-column>
                            <a-table-column align="left" :title="''" v-if="!readonlyMode" key="operation" width="5%">
                                <template #default="{index}">
                                    <span>
                                        <a-button
                                            :disabled="form.items.length === 1"
                                            type="link"
                                            danger
                                            @click="remove(index)"
                                        >
                                            <delete-outlined />
                                        </a-button>
                                    </span>
                                </template>
                            </a-table-column>
                        </a-table>
                        <a-button v-show="!readonlyMode" class="invoice-add" type="primary" ghost @click="addItem">
                            <template #icon>
                                <plus-outlined />
                            </template>
                            {{ i18n.t('bkAp.addItemBtnTxt') }}
                        </a-button>
                        <div class="ar-invoice-amount-block">
                            <div class="ar-invoice-amount-block-right">
                                <div class="amount-block">
                                    <!-- auto-calculate checkbox -->
                                    <!-- may be enable checkbox later -->
                                    <div v-show="!readonlyMode" class="title">
                                        <a-switch
                                            class="switch-wrap"
                                            v-model:checked="autoCalculateState"
                                            size="small"
                                            @change="changeAutoCalculateMode"
                                        />{{ i18n.t('ApComponents.auto') }}
                                    </div>
                                    <div v-show="!readonlyMode" class="title">
                                        <a-switch
                                            class="switch-wrap"
                                            size="small"
                                            v-model:checked="enableTaxExempt"
                                            @change="changeTaxExempt"
                                        />{{ i18n.t('ApComponents.exempt') }}
                                    </div>

                                    <div class="amount-item-wrap">
                                        <!-- Net Amount item-block  -->
                                        <div class="amount-item">
                                            <div class="amount-lable">{{ i18n.t('ArComponents.netAmount') }}</div>
                                            <a-input-number
                                                v-model:value="form.net_amount"
                                                :disabled="true"
                                                :controls="false"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input"
                                            >
                                            </a-input-number>
                                        </div>
                                        <!-- taxRatesList item-block  -->
                                        <div
                                            class="amount-item"
                                            v-for="item in form.tax_content || []"
                                            :key="item.fieldName"
                                        >
                                            <div class="amount-lable">{{ item.alias }}</div>
                                            <!-- <a-input-number
                                                v-model:value="item.value"
                                                :disabled="readonlyMode"
                                                :controls="false"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input"
                                                @change="handleTaxChange($event, item)"
                                            >
                                            </a-input-number> -->
                                            <InputNumber
                                                class="amount-input-prime"
                                                v-model="item.value"
                                                :controls="false"
                                                :disabled="props.readonlyMode"
                                                :minFractionDigits="2"
                                                :maxFractionDigits="2"
                                                :locale="decimalFormat()"
                                                fluid
                                                @value-change="handleTaxChange($event, item)"
                                            />
                                        </div>
                                        <!-- Shipping item-block  -->
                                        <div class="amount-item" v-if="false">
                                            <div class="amount-lable">{{ i18n.t('bkAr.shipping') }}</div>
                                            <a-input-number
                                                v-model:value="form.shipping"
                                                :disabled="readonlyMode"
                                                :controls="false"
                                                :min="0 || null"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input"
                                                @change="handleShippingChange"
                                            >
                                            </a-input-number>
                                        </div>
                                        <!-- Tax Subtotal item-block  -->
                                        <div class="amount-item">
                                            <div class="amount-lable">{{ i18n.t('ApComponents.subtotal') }}</div>
                                            <a-input-number
                                                v-model:value="form.total_tax"
                                                :disabled="true"
                                                :controls="false"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input"
                                            >
                                            </a-input-number>
                                        </div>
                                        <!-- hide "Taxable Subtotal"  -->
                                        <!-- Taxable Subtotal item-block  -->
                                        <div v-show="false" class="amount-item">
                                            <div class="amount-lable">Taxable Subtotal</div>
                                            <a-input-number
                                                v-model:value="form.net_amount"
                                                :disabled="true"
                                                :controls="false"
                                                :min="0 || null"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input"
                                            >
                                            </a-input-number>
                                        </div>
                                        <!-- TOTAL item-block  -->
                                        <div class="amount-item">
                                            <div class="amount-lable bold">
                                                {{ i18n.t('ApComponents.total') }} {{ form.invoice_currency }}
                                            </div>
                                            <!-- <a-input-number
                                                v-model:value="form.total_fee"
                                                :disabled="form.items.length > 1 && autoCalculateState"
                                                :controls="false"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input"
                                                @change="changeTotalAmount($event, false)"
                                            >
                                            </a-input-number> -->
                                            <InputNumber
                                                class="amount-input-prime"
                                                v-model="form.total_fee"
                                                :controls="false"
                                                :disabled="form.items.length > 1 && autoCalculateState"
                                                :minFractionDigits="2"
                                                :maxFractionDigits="2"
                                                :locale="decimalFormat()"
                                                fluid
                                                @value-change="changeTotalAmount($event, false)"
                                            />
                                        </div>
                                        <div class="amount-item" v-show="form.invoice_currency !== localCurrency">
                                            <div class="amount-lable bold">
                                                {{ i18n.t('ApComponents.total') }} {{ localCurrency }}
                                            </div>
                                            <!-- <a-input-number
                                                v-model:value="form.total_fee_local"
                                                :disabled="form.invoice_currency === localCurrency"
                                                :controls="false"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input"
                                            >
                                            </a-input-number> -->
                                            <InputNumber
                                                class="amount-input-prime"
                                                v-model="form.total_fee_local"
                                                :controls="false"
                                                :disabled="form.invoice_currency === localCurrency"
                                                :minFractionDigits="2"
                                                :maxFractionDigits="2"
                                                :locale="decimalFormat()"
                                                fluid
                                            />
                                        </div>
                                        <div
                                            class="amount-item"
                                            v-if="form.invoice_currency !== localCurrency && !props.readonlyMode"
                                        >
                                            <div class="amount-lable bold">Difference</div>
                                            <a-input-number
                                                v-model:value="form_diff"
                                                :disabled="true"
                                                :controls="false"
                                                :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-display-alert"
                                            >
                                            </a-input-number>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ar-invoice-block">
                        <a-form-item name="invoice_comments" :label="i18n.t('bkAr.invoiceComment')">
                            <a-textarea
                                class="textarea-wrap"
                                v-model:value="form.invoice_comments"
                                :placeholder="i18n.t('commonTag.msgInput')"
                                :rows="3"
                                :auto-size="{minRows: 3, maxRows: 50}"
                                :disabled="readonlyMode"
                            />
                        </a-form-item>

                        <a-form-item
                            name="bank_id"
                            :label="i18n.t('esUpload.bankAccount')"
                            class="form-box-bankAccount"
                        >
                            <a-select
                                v-model:value="form.bank_id"
                                :placeholder="i18n.t('bkCustomer.msgPhrSelect')"
                                clearable
                                style="width: 50%"
                                filterable
                            >
                                <!-- :key="(item.name + ' | ' + item.account_no)"-->
                                <a-select-option
                                    v-for="item in bankList"
                                    :key="uuidv4() + item.id"
                                    :value="item.id"
                                    clearable
                                    >{{ item.name + ' | ' + item.account_no }}</a-select-option
                                >
                            </a-select>
                        </a-form-item>
                    </div>

                    <div class="ar-invoice-footer">
                        <a-button class="cancel-button" shape="round" @click="cancel">{{
                            i18n.t('commonTag.cancel')
                        }}</a-button>
                        <a-button
                            v-show="!readonlyMode"
                            shape="round"
                            type="primary"
                            @click="save"
                            :loading="formLoading"
                            :disabled="isDisable"
                        >
                            {{ i18n.t('bkAp.create') }}
                        </a-button>
                    </div>
                </a-form>
            </a-spin>
        </div>

        <!-- 打印静态页view PDF�-->
        <div v-else id="printform">
            <div class="inputDeep">
                <a-spin :spinning="formLoading">
                    <a-form
                        ref="formRef"
                        :model="form"
                        :rules="readonlyMode ? {} : rules"
                        :inline="true"
                        class="form-box1"
                    >
                        <div class="ar-invoice-block-readonly">
                            <a-row :gutter="24">
                                <!-- 左侧 -->
                                <a-col :span="8">
                                    <!-- logo -->
                                    <a-form-item style="width: 100%" class="imageName">
                                        <div class="imagelogoName" :style="companyLogo ? {'text-align': 'left'} : {}">
                                            <img
                                                v-if="companyLogo"
                                                :src="companyLogo"
                                                class="form-form-new-left-logo"
                                            />
                                            <div v-else class="company-info-logo-img-placeholder">
                                                <img src="@/assets/image/empty.png" />
                                            </div>
                                        </div>
                                    </a-form-item>
                                    <div>{{ form.company_name }}</div>
                                    <div>{{ form.company_address }}</div>
                                    <div>{{ form.company_email }}</div>
                                    <div>{{ form.company_tel }}</div>
                                </a-col>
                                <a-col :span="8"></a-col>
                                <!-- 右侧 -->
                                <a-col :span="8" class="custom-billing-block">
                                    <a-form-item style="width: 100%">
                                        <h1 class="rightTitle1">{{ i18n.t('bkArInvoice.billing') }}</h1>
                                    </a-form-item>
                                    <div>{{ i18n.t('bkArInvoice.billingNumber') }} : {{ form.invoice_no }}</div>
                                    <div>{{ i18n.t('bkArInvoice.referenceNumber') }} : {{ form.reference_no }}</div>
                                    <div>
                                        <span v-for="value in accountCurrencyOptions" :key="value.key">
                                            {{
                                                value.key === form.invoice_currency
                                                    ? i18n.t('bkArInvoice.currency') + ' : ' + value.value
                                                    : ''
                                            }}
                                        </span>
                                    </div>
                                    <!-- <div v-show="false">Create Date : {{ form.invoice_create_date }}</div> -->
                                    <div>{{ i18n.t('bkArInvoice.paymentDue') }} : {{ form.invoice_due_date }}</div>
                                    <!-- <div v-show="false">After Days : {{fixedData}}</div> -->
                                    <div>{{ i18n.t('bkArInvoice.postDate') }} : {{ form.posting_date }}</div>
                                </a-col>
                            </a-row>
                        </div>
                        <div class="billship" style="padding: 15px 35px">
                            <a-row :gutter="10" type="flex" justify="space-between">
                                <a-col :span="12">
                                    <div label="" class="form-box-header form-box-shipping_line" v-show="false">
                                        <div class="line-item">
                                            <span style="margin-right: 10px">{{ i18n.t('bkAr.shipToSameAddr') }}</span>
                                            <a-checkbox
                                                v-model:checked="shippingAsBillingCheck"
                                                :disabled="readonlyMode"
                                            ></a-checkbox>
                                        </div>
                                    </div>
                                    <div>
                                        <div>
                                            {{ i18n.t('ArComponents.billTo') }}:
                                            <br />
                                            {{ form?.bill_to_company }}
                                            <br />
                                            {{ form?.bill_to_street }}, {{ form?.bill_to_city }},
                                            {{ form?.bill_to_province }}
                                            {{ form.bill_to_postal_code }}
                                            <br />
                                            {{ form?.bill_to_tel }}
                                            <br />
                                            {{ form?.bill_to_email }}
                                        </div>
                                        <a-form-item
                                            :label="i18n.t('ArComponents.billTo')"
                                            style="width: 80%"
                                            v-show="false"
                                        >
                                            <a-select
                                                :placeholder="i18n.t('workTimeManager.msgInput')"
                                                allow-create
                                                filterable
                                                clearable
                                                default-first-option
                                                v-model:value="form.bill_to_company"
                                                style="width: 100%"
                                                :disabled="readonlyMode"
                                            >
                                                <a-select-option
                                                    style="width: 100%"
                                                    v-for="item in customersList"
                                                    :key="item.contact_id"
                                                    :value="item.contact_id"
                                                    >{{ item.contact_name }}</a-select-option
                                                >
                                            </a-select>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToReceiver')"
                                            class="form-box-item_billing_receiver"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_receiver"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.billingStreet')"
                                            class="form-box-item_billing_address"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_street"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToCity')"
                                            class="form-box-item_billing_city"
                                        >
                                            <a-input
                                                v-model="form.bill_to_city"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToProvince')"
                                            class="form-box-item_billing_province"
                                        >
                                            <a-select
                                                v-model:value="form.bill_to_province"
                                                :disabled="readonlyMode || true"
                                                :placeholder="' '"
                                            >
                                                <a-select-option
                                                    v-for="item in billProvinceOptions"
                                                    :value="item.key"
                                                    :key="item.value"
                                                    >{{ item.value }}
                                                </a-select-option>
                                            </a-select>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToZip')"
                                            class="form-box-item_billing_postalCode"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_postal_code"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToTel')"
                                            class="form-box-item_billing_tel"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_tel"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.billToEmail')"
                                            class="form-box-item_billing_email"
                                        >
                                            <a-input
                                                v-model:value="form.bill_to_email"
                                                :disabled="readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                    </div>
                                </a-col>
                                <a-col :span="12">
                                    <div>
                                        <div>
                                            {{ i18n.t('ArComponents.shipTo') }}:
                                            <br />
                                            {{ form?.ship_to_company }}
                                            <br />
                                            {{ form?.ship_to_street }}, {{ form?.ship_to_city }},
                                            {{ form?.ship_to_province }}
                                            {{ form?.ship_to_postal_code }}
                                            <br />
                                            {{ form?.ship_to_tel }}
                                            <br />
                                            {{ form?.ship_to_email }}
                                        </div>
                                        <a-form-item
                                            :label="i18n.t('ArComponents.shipTo')"
                                            style="width: 80%"
                                            v-show="false"
                                        >
                                            <a-select
                                                :placeholder="i18n.t('workTimeManager.msgInput')"
                                                allow-create
                                                filterable
                                                clearable
                                                default-first-option
                                                style="width: 100%"
                                                v-model:value="form.ship_to_company"
                                                :disabled="readonlyMode"
                                                @change="handleCompanyChange('ship', $event)"
                                            >
                                                <a-select-option
                                                    style="width: 100%"
                                                    v-for="item in customersList"
                                                    :key="item.contact_id"
                                                    :value="item.contact_id"
                                                    clearable
                                                    >{{ item.contact_name }}</a-select-option
                                                >
                                            </a-select>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.shipToReceiver')"
                                            class="form-box-item_shipping_receiver"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_receiver"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                            >
                                            </a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.shipToStreet')"
                                            class="form-box-item_shipping_address"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_street"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                            >
                                            </a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.shipToCity')"
                                            class="form-box-item_shipping_city"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_city"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                            >
                                            </a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.shipToProvince')"
                                            class="form-box-item_shipping_province"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_province"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                            >
                                            </a-input>
                                            <a-select
                                                v-model:value="form.ship_to_province"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                                :placeholder="' '"
                                            >
                                                <a-select-option
                                                    v-for="item in shipProvinceOptions"
                                                    :value="item.key"
                                                    :key="item.value"
                                                    >{{ item.value }}
                                                </a-select-option>
                                            </a-select>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.shipToZip')"
                                            class="form-box-item_shipping_postalCode"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_postal_code"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                            ></a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.shipToTel')"
                                            class="form-box-item_shipping_tel"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_tel"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                            >
                                            </a-input>
                                        </a-form-item>
                                        <a-form-item
                                            v-show="false"
                                            :label="i18n.t('bkAr.shipToEmail')"
                                            class="form-box-item_shipping_email"
                                        >
                                            <a-input
                                                v-model:value="form.ship_to_email"
                                                :disabled="shippingAsBillingCheck || readonlyMode || true"
                                            >
                                            </a-input>
                                        </a-form-item>
                                    </div>
                                </a-col>
                            </a-row>
                        </div>
                        <div class="item-table-block">
                            <div class="table">
                                <a-table :dataSource="form.items" style="width: 100%" size="small" :pagination="false">
                                    <a-table-column
                                        :title="i18n.t('bkAr.itemNo')"
                                        data-index="item_no"
                                        :ellipsis="true"
                                        align="center"
                                        width="8%"
                                    >
                                    </a-table-column>
                                    <a-table-column
                                        :title="i18n.t('bkAr.modelNumber')"
                                        data-index="model"
                                        align="center"
                                        width="15%"
                                    >
                                        <template #default="{index, record}">
                                            <a-form-item :name="['items', index, 'model']" v-if="!readonlyMode">
                                                <a-input v-model:value="record.model"> </a-input>
                                            </a-form-item>
                                            <template v-else>
                                                {{ record.model }}
                                            </template>
                                        </template>
                                    </a-table-column>
                                    <a-table-column
                                        :title="i18n.t('bkAr.description')"
                                        data-index="description"
                                        align="center"
                                        :ellipsis="true"
                                        width="15%"
                                    >
                                        <template #default="{index, record}">
                                            <a-form-item :name="['items', index, 'description']" v-if="!readonlyMode">
                                                <a-input v-model:value="record.description"></a-input>
                                            </a-form-item>
                                            <template v-else>
                                                {{ record.description }}
                                            </template>
                                        </template>
                                    </a-table-column>
                                    <a-table-column
                                        :title="i18n.t('bkAr.accountingCategory')"
                                        data-index="debit_coa_id"
                                        align="center"
                                        width="20%"
                                    >
                                        <template #default="{index, record}">
                                            <a-form-item
                                                :name="['items', index, 'debit_coa_id']"
                                                :rules="rules['requireItemTypeSelect']"
                                                v-if="!readonlyMode"
                                            >
                                                <a-select
                                                    v-show="false"
                                                    :placeholder="i18n.t('workTimeManager.msgInput')"
                                                    filterable
                                                    v-model:value="record.debit_coa_id"
                                                    style="width: 100%"
                                                >
                                                    <a-select-option
                                                        v-for="item in accountDescList"
                                                        :key="item.field_code + ' | ' + item.alias"
                                                        :value="item.id"
                                                        >{{ item.field_code + ' | ' + item.alias }}</a-select-option
                                                    >
                                                </a-select>
                                            </a-form-item>
                                            <template v-else>
                                                {{ expenseAccountAlias(record, accountDescList) }}
                                            </template>
                                        </template>
                                    </a-table-column>
                                    <a-table-column
                                        :title="i18n.t('bkAr.qty')"
                                        data-index="qty"
                                        align="center"
                                        width="10%"
                                    >
                                        <template #default="{index, record}">
                                            <a-form-item :name="['items', index, 'qty']" v-if="!readonlyMode">
                                                <a-input-number
                                                    v-model:value="record.qty"
                                                    :controls="false"
                                                    style="width: 100%"
                                                    :max="9999999"
                                                    :min="0 || null"
                                                    @change="handleQtyChange(index)"
                                                ></a-input-number>
                                            </a-form-item>
                                            <template v-else>
                                                {{ record.qty }}
                                            </template>
                                        </template>
                                    </a-table-column>
                                    <a-table-column
                                        :title="i18n.t('bkAr.unitPrice')"
                                        data-index="unit_price"
                                        align="center"
                                        width="10%"
                                    >
                                        <template #default="{index, record}">
                                            <a-form-item :name="['items', index, 'unit_price']" v-if="!readonlyMode">
                                                <a-input-number
                                                    v-model:value="record.unit_price"
                                                    :placeholder="i18n.t('commonTag.msgInput')"
                                                    :controls="false"
                                                    :precision="1"
                                                    :min="0 || null"
                                                    style="width: 150%"
                                                    @change="handleUnitPriceChange(index)"
                                                ></a-input-number>
                                            </a-form-item>
                                            <template v-else>
                                                {{ Number(record.unit_price).toFixed(3) }}
                                            </template>
                                        </template>
                                    </a-table-column>
                                    <a-table-column
                                        :title="i18n.t('bkAr.amount')"
                                        data-index="total"
                                        align="center"
                                        width="15%"
                                    >
                                        <template #default="{index, record}">
                                            <a-form-item
                                                :name="['items', index, 'total']"
                                                :rules="rules['requireItem']"
                                                v-if="!readonlyMode"
                                            >
                                                <a-input-number
                                                    v-model:value="record.total"
                                                    :placeholder="i18n.t('commonTag.msgInput')"
                                                    :controls="false"
                                                    :precision="2"
                                                    style="width: 100%"
                                                    @change="handleItemTotalChange(record, index)"
                                                >
                                                </a-input-number>
                                            </a-form-item>
                                            <template v-else>
                                                <!-- {{ Number(record.total).toFixed(2) }} -->
                                                {{ $formatNumber(Number(record.total)) }}
                                            </template>
                                        </template>
                                    </a-table-column>
                                    <a-table-column
                                        v-if="!readonlyMode"
                                        :title="''"
                                        data-index="operation"
                                        align="center"
                                        width="5%"
                                    >
                                        <template #default="{index}">
                                            <span>
                                                <a-button
                                                    class="btn-txt btn-del"
                                                    type="link"
                                                    :class="{'margin-class': !readonlyMode}"
                                                    @click="remove(index)"
                                                    :disabled="form.items.length === 1"
                                                >
                                                    <delete-outlined />
                                                </a-button>
                                            </span>
                                        </template>
                                    </a-table-column>
                                </a-table>
                            </div>
                        </div>
                        <div class="ar-invoice-block-readonly">
                            <div class="line-four1">
                                <div style="min-width: 70%; max-width: 70%">
                                    <div class="bottom_comment">
                                        <div>{{ i18n.t('bankInfo.bankName') }}:</div>
                                        <div class="bottom_comment_text">{{ form.bank_name }}</div>
                                    </div>
                                    <div class="bottom_comment">
                                        <div>{{ i18n.t('bankInfo.bankAccount') }}:</div>
                                        <div class="bottom_comment_text">{{ form.bank_account }}</div>
                                    </div>
                                    <div class="bottom_comment">
                                        <div>{{ i18n.t('bkAr.invoiceComment') }}:</div>
                                        <div class="bottom_comment_text" style="white-space: pre-line">
                                            {{ form.invoice_comments }}
                                        </div>
                                    </div>
                                </div>
                                <div class="amount-block2">
                                    <div>
                                        {{ i18n.t('ArComponents.netAmount') }} :
                                        {{ $formatNumber(Number(form.net_amount)) }}
                                    </div>
                                    <div v-if="false">{{ i18n.t('ArComponents.shipping') }} : {{ form.shipping }}</div>
                                    <div v-if="false">{{ i18n.t('ArComponents.discount') }} : {{ form.discount }}</div>
                                    <!-- <div><span>GST/HST : </span>{{ Number(form.gst).toFixed(2) }}</div>
                                    <span class="ygst-pst-label-box">
                                        <span class="ygst-pst-label-txt"> </span>
                                        <span class="1gst-pst-label-icons">
                                            <span v-if="taxsIndicator().gstCond === 'ok'"> </span>
                                            <span v-else-if="taxsIndicator().gstCond === 'optional'">
                                                <a-tooltip
                                                    class="item"
                                                    effect="light"
                                                    content="Optional"
                                                    placement="top"
                                                ></a-tooltip>
                                            </span>
                                            <span v-else> </span>
                                        </span>
                                    </span>
                                    <div><span>QST : </span>{{ Number(form.qst).toFixed(2) }}</div>
                                    <div class="gst-pst-label-box">
                                        <span class="gst-pst-label-txt"> </span>
                                        <span class="gst-pst-label-icons">
                                            <div v-if="taxsIndicator().qstCond === 'ok'"></div>
                                            <div v-else-if="taxsIndicator().qstCond === 'optional'">
                                                <a-tooltip
                                                    class="item"
                                                    effect="light"
                                                    content="Optional"
                                                    placement="top"
                                                ></a-tooltip>
                                            </div>
                                            <div v-else></div>
                                        </span>
                                    </div>
                                    <div><span>PST : </span>{{ Number(form.pst).toFixed(2) }}</div>
                                    <div class="gst-pst-label-box">
                                        <span class="gst-pst-label-txt"> </span>
                                        <span class="gst-pst-label-icons">
                                            <div v-if="taxsIndicator().pstCond === 'ok'"></div>
                                            <div v-else-if="taxsIndicator().pstCond === 'optional'">
                                                <a-tooltip
                                                    class="item"
                                                    effect="light"
                                                    content="Optional"
                                                    placement="top"
                                                ></a-tooltip>
                                            </div>
                                            <div v-else></div>
                                        </span>
                                    </div> -->
                                    <div v-for="item in form.tax_content || []" :key="item.fieldName">
                                        <span>{{ item.alias }} : </span>{{ $formatNumber(Number(item.value)) }}
                                    </div>
                                    <div>
                                        Tax Subtotal :
                                        <!-- {{ Number(form.total_tax).toFixed(2) }} -->
                                        {{ $formatNumber(Number(form.total_tax)) }}
                                    </div>
                                    <div v-show="false">
                                        <!-- Taxable Subtotal : {{ Number(form.net_amount).toFixed(2) }} -->
                                        Taxable Subtotal : {{ $formatNumber(Number(form.net_amount)) }}
                                    </div>
                                    <div>
                                        {{ i18n.t('ApComponents.total') }} {{ form.invoice_currency }}:
                                        <!-- {{ Number(form.total_fee).toFixed(2) }} -->
                                        {{ $formatNumber(Number(form.total_fee)) }}
                                    </div>
                                    <div v-show="form.invoice_currency !== localCurrency">
                                        {{ i18n.t('ApComponents.total') }} {{ localCurrency }}:
                                        <!-- {{ form.total_fee_local }} -->
                                        {{ $formatNumber(Number(form.total_fee_local)) }}
                                    </div>
                                    <!--                                    <div v-show="false">Deposit : {{ form.deposit }}</div>-->
                                    <!--                                    <div v-show="false">Balance : {{ form.balance }}</div>                                    -->
                                    <div v-show="false">{{ i18n.t('ArComponents.deposit') }} :</div>
                                    <div v-show="false">{{ i18n.t('ArComponents.balance') }} :</div>
                                </div>
                            </div>
                        </div>
                        <div class="line-four"></div>
                    </a-form>
                </a-spin>
            </div>
            <a-divider class="footer-divider" />
            <div id="arDisplayBlock" style="margin-bottom: 10px">
                <div>
                    <div class="display-doc-title">
                        {{ i18n.t('ArComponents.JE') }}:<a
                            style="padding-left: 10px"
                            class="document-item-link"
                            @click="showEngineDetail(form.engine_document_id)"
                            >{{ form.engine_document_id }}</a
                        >
                        <a class="document-item-link" @click="showEngineDetail(form.cash_engine_payment_no)">{{
                            form.cash_engine_payment_no || ''
                        }}</a>
                        <a
                            class="document-item-link"
                            v-for="id in form.engine_reverse_document_id?.split(',')"
                            :key="id"
                            @click="showEngineDetail(id)"
                            >{{ id }}</a
                        >
                    </div>
                </div>
                <div>
                    <div class="display-doc-title">
                        Billing Doc:<a style="padding-left: 10px" @click="displayFileViewerModal">{{
                            form.invoice_url?.split('/').pop()
                        }}</a>
                    </div>
                </div>
            </div>
            <a-divider class="footer-divider" />
            <div class="ar-invoice-footer">
                <a-button class="cancel-button" shape="round" @click="downloadFile">{{
                    i18n.t('commonTag.download')
                }}</a-button>

                <a-button
                    v-if="form.pay_method === '1' && form.br_flag === 0"
                    class="cancel-button"
                    shape="round"
                    @click="popConvertModal()"
                    >{{ i18n.t('ArComponents.convert') }}</a-button
                >

                <a-button type="primary" :disabled="!enableReverse" shape="round" @click="popConfirmModal">{{
                    i18n.t('commonTag.reverse')
                }}</a-button>
            </div>
        </div>

        <!-- engine details pop-up -->
        <a-modal
            :title="i18n.t('gl.readonly')"
            v-model:visible="showGlDialog"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="1000"
            style="z-index: 999"
            :dialogStyle="{top: '10px'}"
            :bodyStyle="{padding: '10px 24px 24px'}"
        >
            <gl-component
                :current-invoice="glCurrent"
                :readonly-mode="true"
                :operation-mode="glOperationMode"
                @dismiss="dismissEngineDetail"
            ></gl-component>
        </a-modal>

        <!-- file viewer-->
        <a-modal
            :body-style="viewerStyle"
            v-model:visible="showFileViewer"
            destroyOnClose
            :closeable="true"
            :mask="false"
            :maskClosable="false"
            :width="820"
            :wrapClassName="'modal-wrap'"
            ><pdf-viewer :url="''" />
            <template #title>
                <div ref="modalTitleRef" class="ant-modal-title-detail">
                    <span>View File</span>
                </div>
            </template>
            <template #modalRender="{originVNode}">
                <div :style="transformStyle">
                    <component :is="originVNode" />
                </div>
            </template>
            <template #footer>
                <a-button key="back" @click="closeFileViewerModal" shape="round" class="cancel-button">
                    {{ i18n.t('cancel') }}
                </a-button>
                <a-button
                    key="submit"
                    type="primary"
                    shape="round"
                    :loading="downloadFileloading"
                    @click="downloadFile"
                >
                    Download
                </a-button>
            </template></a-modal
        >

        <!--Create Customer Form Pop-up-->
        <a-modal
            title="Create New Customer"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="820"
            :bodyStyle="{padding: '10px 14px 14px'}"
            :z-index="2902"
        >
            <supplier-form
                :current-customer="current"
                :edit-mode="false"
                :origin="'outside'"
                client-type="CUSTOMER"
                :contact-name="newCustomerName"
                @updateNewContact="updateNewContact"
                @dismiss="showContactCreationModal(false)"
            ></supplier-form>
        </a-modal>

        <div ref="confirmationWrap">
            <a-modal
                v-model:visible="showConfirm"
                centered
                destroyOnClose
                :get-container="confirmationWrap"
                :width="480"
                :closable="false"
                :confirm-loading="showReverseLoading"
                :wrapClassName="'modal-wrap'"
                :ok-text="i18n.t('commonTag.confirm')"
                :ok-type="'primary'"
                :ok-button-props="{shape: 'round'}"
                :cancel-text="i18n.t('commonTag.cancel')"
                :cancel-button-props="{shape: 'round', ghost: true, type: 'primary'}"
                @ok="confirmationConfirm"
                @cancel="confirmationCancel"
            >
                <template #title>
                    <div class="confirm-title-wrap">
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('bkCommonTag.confirmation') }}
                        </div>
                    </div>
                </template>
                <div class="confirmation-content-text">{{ confirmText }}</div>
                <div class="date-select">
                    <a-date-picker
                        v-model:value="reverseDt"
                        :allowClear="false"
                        :inputReadOnly="true"
                        format="YYYY-MM-DD"
                        valueFormat="YYYY-MM-DD"
                    >
                        <template #suffixIcon>
                            <svg-icon name="icon_date"></svg-icon>
                        </template>
                    </a-date-picker>
                </div>
            </a-modal>
        </div>

        <div ref="convertWrap">
            <a-modal
                v-model:visible="showConvert"
                centered
                destroyOnClose
                :get-container="convertWrap"
                :width="480"
                :closable="false"
                :confirm-loading="showConvertLoading"
                :wrapClassName="'modal-wrap'"
                :ok-text="i18n.t('commonTag.confirm')"
                :ok-type="'primary'"
                :ok-button-props="{shape: 'round'}"
                :cancel-text="i18n.t('commonTag.cancel')"
                :cancel-button-props="{shape: 'round', ghost: true, type: 'primary'}"
                @ok="convertConfirm"
                @cancel="convertCancel"
            >
                <template #title>
                    <div class="confirm-title-wrap">
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('bkCommonTag.confirmation') }}
                        </div>
                    </div>
                </template>
                <div class="confirmation-content-text">{{ confirmConvert }}</div>
            </a-modal>
        </div>

        <!--Create Product/Service Form Pop-up-->
        <a-modal
            :title="i18n.t('productService.createTitle')"
            v-model:visible="productServiceShow"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="820"
            :bodyStyle="{padding: '10px 14px 14px'}"
            :z-index="2902"
        >
            <product-service-form
                :edit-mode="false"
                :origin="'outside'"
                client-type="CUSTOMER"
                @fetchProductServiceList="fetchProductServiceList({company_code: userCompany[0].code, $limit: -1})"
                @dismiss="showProductServiceModal(false)"
            ></product-service-form>
        </a-modal>
    </div>

    <!-- todo  列表中  除mail之外的button 加到此处 -->
</template>
<style lang="scss" scoped>
.imagelogoName {
    max-height: 105px;
}

.form-form-new-left-logo {
    // width: 220px;
    margin: 20px 0;
    height: 65px;

    img {
        height: 65px;
        max-height: 105px;
        width: auto;
    }
}

.right-title-wrap {
    display: flex;
    align-items: flex-end;
    height: 100%;
}

.rightTitle {
    margin: 0;
    font-size: 44px;
    font-weight: 200;
    color: #232e38;
    height: 65px;
    width: 100%;
}

.form-input-box-wrap {
    margin-top: 20px;

    .form-input-box-wrap-inner {
        background-color: #f5f7f9;
        border-radius: 8px;

        .form-input-box {
            width: 90%;
            margin: 0 auto;
            padding: 20px 0;

            .ant-row + .ant-row {
                margin-top: 26px;
            }

            .form-input-box-title {
                font-weight: bold;
                margin-bottom: 20px;
            }
        }
    }
}

.el-icon-question {
    margin: auto;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #2ead2b;
    margin-left: 5px;
}

.ar-invoice-block {
    padding: 24px 0;
    border-bottom: 1px solid #e2e2ea;

    &:first-child {
        padding-top: 0;
    }

    .company-info-logo-img-placeholder {
        img {
            max-height: 105px;
        }
    }
}

.ar-invoice-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn + .ant-btn {
        margin-left: 12px;
    }
}

.table-input {
    background-color: #f5f7f9;
    border-color: #f5f7f9;
    border-radius: 4px;
    padding-left: 4px;
    padding-right: 4px;

    &.ant-input-number {
        padding-left: 0px;
        padding-right: 0px;

        :deep(.ant-input-number-input-wrap input) {
            padding-left: 4px;
            padding-right: 4px;
        }
    }

    &.ant-select {
        padding-left: 0px;
        padding-right: 0px;

        :deep(.ant-select-selector) {
            padding-left: 4px;
            padding-right: 4px;

            .ant-select-selection-search {
                left: 4px;
            }
        }
    }

    &.ant-input:hover,
    &.ant-input-number:hover,
    &.ant-input:focus,
    &.ant-input-focused,
    &.ant-input-number-focused {
        border-color: #216fcf;
    }

    :deep(.ant-select-selector) {
        background-color: #f5f7f9 !important;
        border-color: #f5f7f9;
    }
}

.ar-invoice-block {
    :deep(.ant-row.ant-form-item) {
        margin: 0;

        .ant-col.ant-form-item-control .table-input {
            width: 100%;
        }
    }
}

:deep(.ar-invoice-block .ant-col.ant-form-item-label) {
    height: 31.5px;
    overflow: visible;
}

:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -10px;
    font-size: 12px;
    line-height: 12px;
    min-height: 12px;
    display: none;
}

.icon-question {
    margin: 0;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #2ead2b;
    margin-left: 5px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}

:deep(.ant-form-item-label [title='Comments']) {
    font-size: 16px;
    font-weight: 700;
}

.textarea-wrap {
    min-height: 96px;
    height: 96px;
    max-height: 96px;
    margin-bottom: 15px;
}

.meta-wrap {
    .title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 700;
    }

    .meta-lable {
        margin-bottom: 8px;
    }
}

.ar-invoice-amount-block {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding: 24px 0 0;

    .ar-invoice-amount-block-left {
        width: 300px;
    }

    .ar-invoice-amount-block-right {
        width: 281px;

        .amount-block {
            width: 100%;

            .title {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                text-align: right;
            }

            .switch-wrap {
                margin-right: 8px;
            }

            .amount-item-wrap {
                padding: 20px 16px;
                width: 100%;
                background-color: #f5f7f9;
                border-radius: 8px;

                .amount-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0px;
                    }

                    .amount-lable {
                        width: calc(100% - 151px - 8px);
                        text-align: right;

                        &.bold {
                            font-weight: 700;
                        }
                    }

                    .amount-input {
                        width: 151px;

                        &.ant-input-number-disabled {
                            background-color: #fff;
                        }
                    }

                    .amount-display-alert {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: rgba(255, 0, 0, 0.2);
                            color: red;
                        }
                    }
                }
            }
        }
    }
}

.ar-invoice-block-readonly {
    padding: 15px 35px;

    .company-info-logo-img-placeholder {
        img {
            max-height: 105px;
        }
    }

    .rightTitle1 {
        margin: 20px 0;
        font-size: 30px;
        font-weight: 200;
        color: #232e38;
        height: 65px;
    }

    .line-four1 {
        display: flex;
        justify-content: space-between;
        padding-top: 340px;
        align-items: flex-end;
    }

    .custom-billing-block {
        background-color: #f4f4f4;
        border: 1px solid #cecece;
        border-radius: 20px;
        padding-bottom: 10px;
    }
}

.footer-divider {
    margin-top: 0px;
    margin-bottom: 10px;
}

.ar-invoice-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn + .ant-btn {
        margin-left: 12px;
    }
}

.confirm-title-wrap {
    padding-top: 15px;
    padding-bottom: 5px;

    .confirm-title-icon {
        color: #faad14 !important;
        font-size: 21px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .confirm-title-text {
        // font-family: Calibri;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        font-weight: 400;
    }
}

.confirmation-content-text {
    // font-family: Calibri;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    font-weight: 400;
    padding-left: 64px;
    padding-top: 5px;
    // height: 75px;
}

.date-select {
    color: rgba(0, 0, 0, 0.65);
    min-width: 145px;
    width: 260px;
    max-width: 300px;
    padding-top: 5px;
    padding-left: 64px;
    flex: 1;
}

.display-doc-title {
    font-size: 16px;
    font-weight: bold;
    padding-right: 10px;
}

.document-item-link {
    margin-right: 5px;
}

:deep(.ant-modal-body) {
    height: 50px;
}

:deep(.ant-modal-header) {
    padding: 0;
    .ant-modal-title {
        .ant-modal-title-detail {
            padding: 17px 24px;
            width: 100%;
            cursor: move;
            user-select: none;
        }
    }
}
:deep(.ant-modal-footer) {
    border-top: none;
}
.column-item-input {
    margin-top: 12px;
    margin-bottom: 12px;
}

.amount-input-prime {
    width: 151px;
    height: 33px;
    border-radius: 6px;
    color: #333;
    background-color: #fff;
    &.item-amount {
        max-width: 100%;
        background-color: #f5f7f9;
    }
}

:deep(.p-inputnumber input) {
    font-size: 14px;
    padding-left: 10px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border: 1px solid #4096ff;
        box-shadow: 0 0 0 2px #0591ff1a;
    }
}

:deep(.p-inputnumber.item-amount input) {
    border-color: #f5f7f9;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border-color: #216fcf;
    }
}

:deep(.p-inputnumber.item-amount input::placeholder) {
    color: #bbb;
}
</style>
