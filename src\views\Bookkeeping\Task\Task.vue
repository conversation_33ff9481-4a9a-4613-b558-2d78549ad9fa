<!-- @format -->

<script lang="ts" setup>
import {reactive, ref, onBeforeMount, computed, watch} from 'vue'
import type {Composer} from 'vue-i18n'
import {useStore} from 'vuex'
import i18nInstance from '@/locales/i18n'
import {TaskCustomizeTable, UserCompany, UserInfo} from '@/lib/storage'
import {useRouter} from 'vue-router'
import TaskComponent from '../../../components/bookkeepingComponents/TaskComponents/TaskComponent.vue'
import CustomColumns from '@/components/bookkeepingComponents/CommonComponents/CustomColumns.vue'
import PyInvoiceComponent from '@/components/bookkeepingComponents/PyComponents/PyInvoiceComponent.vue'
import {MailOutlined, DeleteOutlined, PlusOutlined} from '@ant-design/icons-vue'
import {T} from 'ramda'
import * as _ from 'lodash'
import {v4 as uuidv4} from 'uuid'
import SvgIcon from '@/components/SvgIcon.vue'
import {message} from 'ant-design-vue'

const i18n: Composer = i18nInstance.global
const router = useRouter()
const store = useStore()
const actualHourDom = ref(null)
const estimatedHourDom = ref(null)
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || []
const searchForm = reactive({
    taskName: '',
    dueStartDate: '',
    dueEndDate: '',
    estimatedMinHour: null,
    estimatedMaxHour: null,
    taskStatus: [],
    company: [],
    assignTo: '',
})
const tableLoading = ref(false)
const showColumns = ref(false)
const showDetails = ref(false)
const showAddView = ref(false)
const activeTabName = ref('todo')
const companyOptions: any = ref([])
companyOptions.value = _.cloneDeep(_.sortBy(userCompany, 'id'))
const current = ref({})
const pageQuery = reactive({
    page_index: 1,
    page_size: 10,
    sortField: null,
    sortDirection: 0,
    currentPageNumber: 1,
})
const defaultTable = [
    'name',
    'companyCode',
    'companyName',
    'last',
    'dueDate',
    'estimatedHour',
    'actualHour',
    'status',
    'assignedTo',
    'email',
    'tag',
    'priority',
    'createTime',
    'updateTime',
]
let timer: any = undefined
const customizeTable: any = ref(
    TaskCustomizeTable.get() || [
        'name',
        'companyCode',
        'last',
        'dueDate',
        'estimatedHour',
        'actualHour',
        'status',
        'assignedTo',
        'email',
        'tag',
        'priority',
    ],
)
const adminList = ['kycadmin', '<EMAIL>', '<EMAIL>', '<EMAIL>']
const adminOptions = reactive([
    {
        value: 'kycadmin',
        label: 'kycadmin',
    },
    {
        value: '<EMAIL>',
        label: '<EMAIL>',
    },
    {
        value: '<EMAIL>',
        label: '<EMAIL>',
    },
    {
        value: '<EMAIL>',
        label: '<EMAIL>',
    },
])

const taskList = computed(() => store.state.TaskStore.taskList)
const localTaskList = ref([])

// 监听 taskList 的变化
watch(
    taskList,
    newTaskList => {
        localTaskList.value = newTaskList.map((task: any) => ({...task}))
    },
    {immediate: true},
)

const totalNumber = computed(() => store.state.TaskStore.totalNumber)

const dismissColumns = () => {
    showColumns.value = false
}
const customSorter = async (pagination: any, filters: any, sorter: any) => {
    if (sorter.field && sorter.order) {
        const order = sorter.order === 'descend' ? -1 : 1
        pageQuery.sortField = sorter.field
        pageQuery.sortDirection = order
        pageQuery.currentPageNumber = 1
    } else {
        pageQuery.sortField = null
        pageQuery.sortDirection = 0
        pageQuery.currentPageNumber = 1
    }
    await updateTable()
}

const edit = (row: any) => {
    current.value = {...row}
    showDetails.value = true
}

const dismissDetails = async (action: any) => {
    showDetails.value = false
    current.value = {}
    if (action) {
        await updateTable()
    }
}

const search = async () => {
    clearInterval(timer)
    if (timer !== undefined) {
        clearInterval(timer.value)
    }
    pageQuery.page_index = 1
    await updateTable()
}
const statusOptions = reactive([
    {
        value: 'todo',
        label: i18n.t('task.statusToDo'),
    },
    {
        value: 'doing',
        label: i18n.t('task.statusDoing'),
    },
    {
        value: 'done',
        label: i18n.t('task.statusDone'),
    },
])
const saveColumns = (list: any) => {
    customizeTable.value = list
    showColumns.value = false
    TaskCustomizeTable.set(list)
}
const uploadSuccess = () => {
    message.success({
        content: i18n.t('UploadFileComp.success'), //'success',
    })
}
const visible = ref(false)
const switchTab = (value: any) => {
    activeTabName.value = value
    pageQuery.page_index = 1
    updateTable()
}
const getQueryParams = (searchForm: any) => {
    //used for transform searchForm property to api query format
    const result: any = {}
    // desc sort by create_date as default
    // result['sort[create_time]'] = 'desc'
    if (pageQuery.sortDirection === 0) {
        result['sort[update_time]'] = 'desc'
    }
    if (searchForm.company !== null && searchForm.company.length > 0) {
        result['company_code[$in]'] = JSON.stringify(searchForm.company).replace(/"/g, '')
    }
    if (searchForm.dueStartDate !== '' && searchForm.dueEndDate !== '') {
        result['due_date[$bw]'] = `[${searchForm.dueStartDate},${searchForm.dueEndDate}]`
    }
    if (searchForm.dueStartDate !== '' && searchForm.dueEndDate == '') {
        result['due_date[$gte]'] = searchForm.dueStartDate
    }
    if (searchForm.dueStartDate == '' && searchForm.dueEndDate !== '') {
        result['due_date[$lte]'] = searchForm.dueEndDate
    }
    if (searchForm.estimatedMinHour !== null && searchForm.estimatedMaxHour !== null) {
        result['estimated_hour[$bw]'] = `[${searchForm.estimatedMinHour},${searchForm.estimatedMaxHour}]`
    }
    if (searchForm.estimatedMinHour !== null && searchForm.estimatedMaxHour == null) {
        result['estimated_hour[$gte]'] = searchForm.estimatedMinHour
    }
    if (searchForm.estimatedMinHour !== null && searchForm.estimatedMaxHour == null) {
        result['estimated_hour[$lte]'] = searchForm.estimatedMaxHour
    }
    if (searchForm.taskName) {
        result['name[$like]'] = `${searchForm.taskName}`
    }
    if (searchForm.taskStatus !== null && searchForm.taskStatus.length > 0) {
        result['status[$in]'] = JSON.stringify(searchForm.taskStatus).replace(/"/g, '')
    } else if (activeTabName.value === 'deleted') {
        result['status[$in]'] = `[todo,doing,done]`
    } else {
        result['status[$in]'] = `[${activeTabName.value}]`
    }
    if (activeTabName.value === 'deleted') {
        result['del_flag'] = '1'
    } else {
        result['del_flag'] = '0'
    }
    if (!adminList.includes(userInfo?.account)) {
        result['assign_to'] = userInfo?.account
    } else if (adminList.includes(userInfo?.account) && searchForm.assignTo !== null) {
        result['assign_to[$like]'] = searchForm.assignTo
    }
    // if (searchForm.assignTo !== null) {
    //     result['assign_to[$like]'] = searchForm.assignTo
    // }
    // console.log('result ----> ', result)
    return result
}
const updateTable = async () => {
    try {
        tableLoading.value = true
        const query = {
            ...getQueryParams(searchForm),
            // ...pageQuery,
            ...{page_index: pageQuery.page_index, page_size: pageQuery.page_size},
            // company_code: userCompany[0].code,
            // assign_to: userInfo.account,
            // ...(activeTabName.value != 'null' ? {br_flag: activeTabName.value} : null),
        }
        if (pageQuery.sortField) {
            query[`sort[${pageQuery.sortField}]`] = pageQuery.sortDirection === 1 ? 'asc' : 'desc'
        }
        await fetchTaskList(query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}
const fetchTaskList = (query: any) => {
    return store.dispatch('TaskStore/fetchTaskListV1', query)
}

const changePage = () => {
    updateTable()
}

const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer)
    timer = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer)
        }
    }, 1000)
}

const sendEmail = async (record: any) => {
    const res = await store.dispatch('TaskStore/sendTaskEmail', {
        data: record,
    })
    if (res.data.code === 200) {
        message.success({
            content: res.data.message, //'success',
        })
        await updateTable()
    } else {
        message.error({
            content: res.data.message, //'faiure',
        })
    }
}

const rowClick = (record: any, rowIndex: any, column: any) => {
    return {
        onClick: (event: any) => {
            if (column.key !== 'operation') {
                edit(record)
            }
        },
    }
}

const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)

onBeforeMount(async () => {
    await updateTable()
})
const cancel = () => (visible.value = false)

const handleInput = async (record: any, key: string) => {
    const task = taskList.value.find((item: any) => item.id === record.id)
    if (record[key] !== task[key]) {
        await store.dispatch('TaskStore/updateTableData', {
            id: record.id,
            filed: key,
            data: record[key],
        })
        await updateTable()
    }
}
const deleteTask = async (record: any) => {
    await store.dispatch('TaskStore/updateTableData', {
        id: record.id,
        filed: 'del_flag',
        data: '1',
    })
    await updateTable()
}
const handleEnterEvent = (event: any) => {
    event.target.blur()
}
const selectInputValues = (e: FocusEvent) => {
    const target = e.target as HTMLInputElement // 使用类型断言告诉TypeScript不为空
    if (target) {
        target.select()
    }
}
const toAddTask = () => {
    showAddView.value = true
}
const dismiss = () => {
    showAddView.value = false
    // readonlyMode.value = false
}
const postTask = (payload: any) =>
    store.dispatch('TaskStore/createTaskData', {
        name: payload.value.name,
        company_code: payload.value.company_code,
        company_name: payload.value.company_name,
        assign_to: payload.value.assign_to,
        due_date: payload.value.due_date,
        period_start: payload.value.period_start ?? null,
        period_end: payload.value.period_end ?? null,
        status: payload.value.status,
        estimated_hour: payload.value.estimated_hour ?? null,
        actual_hour: payload.value.actual_hour ?? null,
        tag: payload.value.tag ?? null,
        email: payload.value.email ?? null,
        priority: payload.value.priority ?? null,
        creator: userInfo?.account,
    })
const post = async (form: any) => {
    let response = {} as any
    try {
        tableLoading.value = true
        response = await postTask(form)
        if (response.data.statusCode === 200) {
            const res = await store.dispatch('TaskStore/sendNewTaskEmail', {
                data: {
                    assign_to: form.value.assign_to,
                    name: form.value.name,
                },
            })
            if (res.data.code === 200) {
                message.success(i18n.t('ApComponents.success'))
                updateTable()
            }
        }
    } catch (error: any) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
</script>
<template>
    <div class="history-page-wrap">
        <div class="history-page-content">
            <div class="gl-list-tabs" style="display: flex; justify-content: space-between">
                <a-tabs class="header-tabs" v-model:activeKey="activeTabName" @change="switchTab">
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('task.statusToDo')" key="todo"></a-tab-pane>
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('task.statusDoing')" key="doing"></a-tab-pane>
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('task.statusDone')" key="done"></a-tab-pane>
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('task.statusDeleted')" key="deleted"></a-tab-pane>
                </a-tabs>
                <div class="history-page-header">
                    <div class="search-group-wrap">
                        <a-input
                            v-model:value="searchForm.taskName"
                            :placeholder="$t('task.placeholderSearch')"
                            :disabled="tableLoading"
                            class="search-input"
                            @input="inputChange"
                            @pressEnter="search"
                        >
                            <template #suffix>
                                <svg-icon name="icon_search"></svg-icon>
                            </template>
                        </a-input>
                        <a-popover
                            class="popover-wrap"
                            trigger="click"
                            placement="bottom"
                            @visibleChange="visibleChange"
                        >
                            <template #content>
                                <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                                    <div class="search-input-group">
                                        <a-form-item :label="$t('task.dueDate')">
                                            <a-date-picker
                                                v-model:value="searchForm.dueStartDate"
                                                :disabled="tableLoading"
                                                format="YYYY-MM-DD"
                                                valueFormat="YYYY-MM-DD"
                                                :placeholder="$t('task.placeholderStarting')"
                                                style="width: 160px"
                                                clearable
                                            >
                                                <template #suffixIcon>
                                                    <svg-icon name="icon_date"></svg-icon>
                                                </template>
                                            </a-date-picker>
                                        </a-form-item>
                                        <a-form-item :label="$t('bkApInvoice.to')">
                                            <a-date-picker
                                                v-model:value="searchForm.dueEndDate"
                                                :disabled="tableLoading"
                                                format="YYYY-MM-DD"
                                                valueFormat="YYYY-MM-DD"
                                                :placeholder="$t('task.placeholderEnding')"
                                                style="width: 160px"
                                                clearable
                                            >
                                                <template #suffixIcon>
                                                    <svg-icon name="icon_date"></svg-icon>
                                                </template>
                                            </a-date-picker>
                                        </a-form-item>
                                    </div>
                                    <div class="search-input-group">
                                        <a-form-item :label="$t('task.searchTitleEstimated')">
                                            <a-input-number
                                                v-model:value="searchForm.estimatedMinHour"
                                                :controls="false"
                                                :disabled="tableLoading"
                                                :min="0"
                                                :placeholder="$t('task.placeholderMinHour')"
                                                style="width: 160px"
                                            ></a-input-number>
                                        </a-form-item>
                                        <a-form-item :label="$t('bkApInvoice.to')" label-width="35px">
                                            <a-input-number
                                                v-model:value="searchForm.estimatedMaxHour"
                                                :controls="false"
                                                :disabled="tableLoading"
                                                :min="0"
                                                :placeholder="$t('task.placeholderMaxHour')"
                                                style="width: 160px"
                                            ></a-input-number>
                                        </a-form-item>
                                    </div>
                                    <div class="search-input-group">
                                        <a-form-item :label="$t('router.commonCompany')">
                                            <a-select
                                                mode="multiple"
                                                v-model:value="searchForm.company"
                                                show-search
                                                style="width: 355px"
                                                :controls="false"
                                            >
                                                <a-select-option
                                                    v-for="item in companyOptions"
                                                    :key="item.name + uuidv4()"
                                                    :company_name="item.name"
                                                    :value="item.code"
                                                >
                                                    {{ item.code + ' - ' + item.name }}
                                                </a-select-option>
                                            </a-select>
                                        </a-form-item>
                                    </div>
                                    <div class="search-input-group">
                                        <a-form-item
                                            :label="$t('task.assignedTo')"
                                            v-show="adminList.includes(userInfo?.account)"
                                        >
                                            <a-input
                                                v-model:value="searchForm.assignTo"
                                                :disabled="tableLoading"
                                                style="width: 355px"
                                            />
                                        </a-form-item>
                                    </div>
                                    <a-button type="primary" shape="round" :disabled="tableLoading" @click="search">
                                        <template #icon>
                                            <svg-icon name="icon_search"></svg-icon>
                                        </template>
                                        {{ $t('commonTag.search') }}
                                    </a-button>
                                </a-form>
                            </template>
                            <a-button class="search-button" :disabled="tableLoading">
                                <template #icon>
                                    <svg-icon name="icon_filter"></svg-icon>
                                </template>
                                <!-- {{ i18n.t('commonTag.filter') }} -->
                            </a-button>
                        </a-popover>

                        <a-button class="search-button" :disabled="tableLoading" @click="showColumns = !showColumns">
                            <template #icon>
                                <svg-icon name="icon_columns"></svg-icon>
                            </template>
                            <!-- {{ i18n.t('commonTag.columns') }} -->
                        </a-button>
                        <a-button type="primary" shape="round" class="add-button" @click="toAddTask()">
                            <template #icon>
                                <plus-outlined />
                            </template>
                            {{ i18n.t('commonTag.new') }}
                        </a-button>
                    </div>
                </div>
            </div>
            <a-table
                :dataSource="localTaskList"
                :loading="tableLoading"
                :pagination="false"
                :scroll="customizeTable.length > 5 ? {x: 1350, y: 'calc(100vh - 300px)'} : {x: 'auto'}"
                @change="customSorter"
            >
                <!-- name -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.name')"
                    data-index="name"
                    :ellipsis="false"
                    width="250px"
                    v-if="customizeTable.includes('name')"
                />
                <!-- company code -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.companyCode')"
                    data-index="company_code"
                    sorter="true"
                    :ellipsis="false"
                    width="150px"
                    v-if="customizeTable.includes('companyCode')"
                />
                <!-- company name -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.companyName')"
                    data-index="company_name"
                    v-if="customizeTable.includes('companyName')"
                    width="250px"
                    :ellipsis="false"
                />
                <!-- last -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.last')"
                    data-index="last"
                    v-if="customizeTable.includes('last')"
                    sorter="true"
                    width="240px"
                    :ellipsis="false"
                >
                    <template #default="{record}">
                        <span> {{ record.period_start }} ~ {{ record.period_end }} </span>
                    </template>
                </a-table-column>
                <!-- due date -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.dueDate')"
                    data-index="due_date"
                    width="180px"
                    sorter="true"
                    :ellipsis="false"
                    v-if="customizeTable.includes('dueDate')"
                />
                <!-- estimated hour -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.estimatedHour')"
                    data-index="estimated_hour"
                    v-if="customizeTable.includes('estimatedHour')"
                    width="150px"
                    :ellipsis="false"
                >
                    <template #customRender="{record}">
                        <a-input-number
                            v-model:value="record.estimated_hour"
                            @blur="handleInput(record, 'estimated_hour')"
                            @keypress.enter="handleEnterEvent"
                            ref="estimatedDom"
                            @focus="selectInputValues"
                        ></a-input-number>
                    </template>
                </a-table-column>
                <!-- actual hour -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.actualHour')"
                    data-index="actual_hour"
                    width="150px"
                    :ellipsis="false"
                    v-if="customizeTable.includes('actualHour')"
                    class="actual-hour-column"
                >
                    <template #customRender="{record}">
                        <a-input-number
                            v-model:value="record.actual_hour"
                            @blur="handleInput(record, 'actual_hour')"
                            @keypress.enter="handleEnterEvent"
                            ref="actualDom"
                            @focus="selectInputValues"
                        ></a-input-number>
                    </template>
                </a-table-column>
                <!-- status -->
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.status')"
                    data-index="status"
                    width="150px"
                    :ellipsis="false"
                    v-if="customizeTable.includes('status')"
                >
                    <template #customRender="{record}">
                        <a-select
                            v-model:value="record.status"
                            style="width: 100px"
                            @change="handleInput(record, 'status')"
                        >
                            <a-select-option
                                style="width: 100px"
                                v-for="item in statusOptions"
                                :key="item.label"
                                :value="item.value"
                                >{{ item.label }}</a-select-option
                            >
                        </a-select>
                    </template>
                </a-table-column>
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.assignedTo')"
                    data-index="assign_to"
                    width="260px"
                    :ellipsis="false"
                    v-if="customizeTable.includes('assignedTo')"
                >
                    <template #customRender="{record}">
                        <a-select
                            v-model:value="record.assign_to"
                            style="width: 240px"
                            @change="handleInput(record, 'assign_to')"
                        >
                            <a-select-option
                                style="width: 240px"
                                v-for="item in adminOptions"
                                :key="item.label"
                                :value="item.value"
                                >{{ item.label }}</a-select-option
                            >
                        </a-select>
                    </template>
                </a-table-column>
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.email')"
                    data-index="email"
                    width="260px"
                    :ellipsis="false"
                    v-if="customizeTable.includes('email')"
                />
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.tag')"
                    data-index="tag"
                    width="100px"
                    :ellipsis="false"
                    sorter="true"
                    v-if="customizeTable.includes('tag')"
                >
                    <template #customRender="{record}">
                        <a-input
                            v-model:value="record.tag"
                            @blur="handleInput(record, 'tag')"
                            @keypress.enter="handleEnterEvent"
                            ref="tagDom"
                            @focus="selectInputValues"
                        ></a-input>
                    </template>
                </a-table-column>
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.priority')"
                    data-index="priority"
                    width="100px"
                    sorter="true"
                    :ellipsis="false"
                    v-if="customizeTable.includes('priority')"
                >
                    <template #customRender="{record}">
                        <a-input
                            v-model:value="record.priority"
                            @blur="handleInput(record, 'priority')"
                            @keypress.enter="handleEnterEvent"
                            ref="priorityDom"
                            @focus="selectInputValues"
                        ></a-input>
                    </template>
                </a-table-column>
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.createTime')"
                    data-index="create_time"
                    v-if="customizeTable.includes('createTime')"
                    width="120px"
                    :ellipsis="false"
                >
                    <template #default="{record}">
                        <span> {{ record.create_time.substring(0, 10) }}</span>
                    </template>
                </a-table-column>
                <a-table-column
                    :custom-cell="rowClick"
                    align="center"
                    :title="i18n.t('task.updateTime')"
                    data-index="update_time"
                    v-if="customizeTable.includes('updateTime')"
                    width="120px"
                    :ellipsis="false"
                >
                    <template #default="{record}">
                        <span> {{ record.update_time.substring(0, 10) }}</span>
                    </template>
                </a-table-column>

                <a-table-column
                    v-if="true"
                    align="center"
                    :title="i18n.t('bkApInvoice.operation')"
                    key="operation"
                    fixed="right"
                    width="150px"
                    :custom-cell="rowClick"
                >
                    <template #default="{record}">
                        <span>
                            <a-button
                                class="btn-txt"
                                type="link"
                                @click="sendEmail(record)"
                                :disabled="record.email === null || record.email === '' || record.del_flag === '1'"
                            >
                                <mail-outlined />
                            </a-button>
                            <a-button
                                class="btn-txt"
                                type="link"
                                :disabled="record.del_flag === '1'"
                                @click="deleteTask(record)"
                            >
                                <delete-outlined style="color: red" />
                            </a-button>
                        </span>
                    </template>
                </a-table-column>
            </a-table>
            <div class="pagination-wrap">
                <a-pagination
                    v-model:current="pageQuery.page_index"
                    v-model:page-size="pageQuery.page_size"
                    :disabled="tableLoading"
                    :hideOnSinglePage="false"
                    :showSizeChanger="true"
                    :total="totalNumber"
                    @change="changePage"
                />
                <span
                    >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                    {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                >
            </div>
        </div>
    </div>
    <a-modal
        :title="i18n.t('gl.create')"
        v-model:visible="showAddView"
        :footer="null"
        destroyOnClose
        :closeable="true"
        :width="1000"
        style="z-index: 999"
        :dialogStyle="{top: '10px'}"
        :bodyStyle="{padding: '10px 24px 24px'}"
    >
        <task-component :current-invoice="current" @dismiss="dismiss" @post="post"></task-component>
    </a-modal>
    <a-modal
        :title="i18n.t('columns.modalTitle')"
        v-model:visible="showColumns"
        :footer="null"
        destroyOnClose
        :closeable="true"
        :width="480"
        :wrapClassName="'modal-wrap'"
    >
        <custom-columns
            :defaultTable="defaultTable"
            :customizeTable="customizeTable"
            prefix="task"
            @dismiss="dismissColumns"
            @save="saveColumns"
        ></custom-columns>
    </a-modal>
</template>
<style lang="scss" scoped>
.no-scroll {
    overflow-y: hidden;
}
.history-page-wrap {
    border-radius: 10px;
    background-color: #fff;

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 8px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 60px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button + .search-button {
                min-width: 113px;
            }

            .popover-wrap:deep(.ant-popover-placement-bottom) {
                width: 432px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
            margin-left: 24px;
        }
    }

    .history-page-content {
        padding: 12px 20px;
        .main-head {
            display: flex;
            justify-content: space-between;
        }
        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
}

.search-input-form {
    display: flex;
    flex-direction: column;
    align-items: end;

    .search-input-group {
        display: flex;

        .ant-form-item {
            margin-bottom: 12px;
        }

        :deep(.ant-form-item-label) {
            width: 80px;
        }

        .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
            width: 35px;
        }
    }
}

.br-icon {
    width: 20px;
}
</style>
