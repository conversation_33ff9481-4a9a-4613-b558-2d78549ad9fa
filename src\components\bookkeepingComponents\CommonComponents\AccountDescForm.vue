<!-- @format -->

<script lang="ts" setup>
import {onMounted, reactive, ref} from 'vue'
import {useStore} from 'vuex'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {message, type FormInstance} from 'ant-design-vue'
import {UserCompany} from '@/lib/storage'
import dayjs from 'dayjs'

const i18n: Composer = i18nInstance.global
const store = useStore()
const userCompany: any = UserCompany.get() || []
const props = withDefaults(
    defineProps<{
        currentAccountDesc: any
        editMode: boolean
    }>(),
    {
        editMode: false,
    },
)

const emits = defineEmits(['updateData', 'clean', 'dismiss'])

const formRef = ref<FormInstance>()

const form = ref({} as any)

const account = ref('')
const formLoading = ref(false)

const createChartOfAccount = (query: any) => store.dispatch('AccountDescriptionStore/createChartOfAccountV1', query)
const updateChartOfAccount = (query: any) => store.dispatch('AccountDescriptionStore/updateChartOfAccountV1', query)

// const field_mapping = (source: any) => {
//     const payload = {} as any
//     payload['company'] = source.company_code || userCompany[0].code
//     payload['account_code'] = source.account_code || ''
//     payload['name'] = source.name || ''
//     return payload
// }

const save = async () => {
    if (await formRef.value?.validateFields()) {
        let response
        try {
            // account,  company_id.
            // form.value.account_code = account.value
            // form.value.company_id = userCompany[0].id
            form.value.company = userCompany[0].code
            // form.value.create_time = form.value.create_time || dayjs().toISOString()
            // form.value.update_time = dayjs().toISOString()
            formLoading.value = true
            if (!props.editMode) {
                const {id, ...payload} = form.value
                response = await createChartOfAccount({...payload})
            } else {
                response = await updateChartOfAccount({...form.value})
            }
            if (response.status === 200 || response.status === 201) {
                message.success(i18n.t('ApComponents.success'))
                emits('updateData')
                if (!props.editMode) {
                    emits('clean')
                }
                cancel()
            } else {
                // message.error({
                //     content: 'failed',
                //     duration: 5,
                // })
            }
        } catch (error: any) {
            console.log(error)
            // message.error({
            //     content: error.response.data.errors || 'failed',
            //     duration: 5,
            // })
        } finally {
            formLoading.value = false
        }
    }
}
const cancel = () => {
    emits('dismiss')
}

const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: 'blur',
        },
    ]
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}

const rules = reactive({
    // field_code: requireRule(i18n.t('chartOfAccount.fieldCode')),
    account_des: requireRule(i18n.t('chartOfAccount.accountDes')),
    name: [...requireRule(i18n.t('chartOfAccount.alias')), ...lengthLimitRule(1, 500)],
})

onMounted(() => {
    form.value = props.currentAccountDesc
    if (props.editMode) {
        account.value = form.value.account_code || form.value.field_code + '-xxxx-XX'
    } else {
        const currentUser = store.state.UserInfo.userInfo.loginAccount || 'xxxx'
        account.value = form.value.field_code + `-${currentUser}` + '-XX'
    }
})
</script>

<template>
    <div class="account-desc-form-wrap">
        <a-form
            :layout="'vertical'"
            :model="form"
            :rules="rules"
            ref="formRef"
            class="account-desc-form"
            :loading="formLoading"
        >
            <a-form-item :label="i18n.t('chartOfAccount.account')" name="account" class="">
                <a-input v-model:value="form.account_code" :disabled="true" />
            </a-form-item>
            <a-form-item
                v-if="false"
                :label="i18n.t('chartOfAccount.fieldCode')"
                name="field_code"
                class="form-box-field_code"
            >
                <a-input v-model:value="form.field_code" :disabled="true" />
            </a-form-item>
            <a-form-item :label="i18n.t('chartOfAccount.alias')" name="name" class="">
                <a-input v-model:value="form.name" />
            </a-form-item>
            <!--            <a-form-item v-if="false" :label="i18n.t('chartOfAccount.accountDes')" name="" class="">-->
            <!--                <a-input v-model:value="form.name" />-->
            <!--            </a-form-item>-->
        </a-form>
        <a-divider class="footer-divider"></a-divider>

        <footer>
            <a-button shape="round" @click="cancel" size="small"> {{ i18n.t('commonTag.cancel') }} </a-button>
            <a-button type="primary" :loading="formLoading" size="small" shape="round" @click="save">
                {{ editMode ? i18n.t('commonTag.confirm') : i18n.t('commonTag.new') }}
            </a-button>
        </footer>
    </div>
</template>

<style lang="scss" scoped>
.account-desc-form-wrap {
    .account-desc-form {
        padding: 20px 24px 0px;
    }
}
.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}
footer {
    padding: 12px 24px;
    text-align: right;
    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }
    .ant-btn {
        min-width: 65px;
    }
    .ant-btn + .ant-btn {
        margin-left: 8px;
    }
}
</style>
