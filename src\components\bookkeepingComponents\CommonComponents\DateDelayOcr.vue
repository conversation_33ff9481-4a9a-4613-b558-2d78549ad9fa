<!-- @format -->

<template>
    <div class="date-delay-wrap">
        <div class="title">
            {{ i18n.t('update.paymentDueLater') }}<a-switch class="switch-wrap" v-model:checked="delayed" size="small"
                @change="delayedStatus" />
        </div>
        <div v-show="delayed">
            <div class="select-wrap">
                <a-select :placeholder="$t('bkAp.paymentDelaySelect')" style="width: 100%" v-model:value="delayValue"
                    @change="updateDelay">
                    <a-select-option v-for="item in delayOptions" :key="item.value" :value="item.value">{{
                        item.label
                    }}</a-select-option>
                </a-select>
            </div>
            <div class="picker-wrap">
                <a-date-picker v-model:value="delayDate" :disabled="delayValue !== 'other'" format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD" :placeholder="$t('bkAp.paymentDelayDate')" style="width: 100%"
                    :clearable="false" @change="otherDelayDate">
                </a-date-picker>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, defineEmits, defineProps, onMounted } from 'vue'
import moment from 'moment'
import { inject } from 'vue'
import type { Composer } from 'vue-i18n'

const props = defineProps({
    invoice_due_date: {
        type: String,
        default: '',
    }
})

const invoiceDateRef = inject<any>('invoiceDate', '')
const invoiceDate = ref<string>(invoiceDateRef._value) // 使用 ref 包装 invoiceDateRef
const i18n: Composer = i18nInstance.global
import i18nInstance from '@/locales/i18n'

const delayed = ref(props.invoice_due_date !== '' && props.invoice_due_date !== null)
const delayOptions = ref([
    {
        value: '7',
        label: i18n.t('update.withinDay7'),
    },
    {
        value: '15',
        label: i18n.t('update.withinDay15'),
    },
    {
        value: '30',
        label: i18n.t('update.withinDay30'),
    },
    {
        value: 'other',
        label: i18n.t('update.withinDayOther'),
    },
])
// Set delayValue to 'other' and delayDate to invoice_due_date if invoice_due_date is not null or empty
const delayValue = ref<string | null>(props.invoice_due_date !== '' && props.invoice_due_date !== null ? 'other' : null) // 指定 delayValue 的类型为 string 或 null
const delayDate = ref<string | null>(props.invoice_due_date !== '' && props.invoice_due_date !== null ? props.invoice_due_date : null) // 指定 delayDate 的类型为 string 或 null
const emits = defineEmits(['update'])

const getToday = () => {
    const today = moment().format('YYYY-MM-DD') // 修改这里的格式化方式
    return today
}

const otherDelayDate = () => {
    if (!delayDate.value) delayDate.value = getToday()
    delayValue.value = 'other'
    emits('update', delayDate.value)
}

const updateDelay = () => {
    // console.log('invoiceDateRef', invoiceDateRef)
    // console.log('invoiceDate.value', invoiceDate.value)
    if (invoiceDate.value && delayValue.value) {
        delayDate.value = moment(invoiceDate.value, 'YYYY-MM-DD').add(Number(delayValue.value), 'days').format('YYYY-MM-DD') // 添加对 invoiceDate 是否存在的判断
        emits('update', delayDate.value)
    }
}
const delayedStatus = () => {
    if (!delayed.value) emits('update', getToday())
}
watch(invoiceDateRef, () => {
    // console.log("dafasdfad")
    invoiceDate.value = invoiceDateRef._value; // 当 invoiceDateRef 改变时更新 invoiceDate
    updateDelay()
})

// Watch for changes in the invoice_due_date prop
watch(() => props.invoice_due_date, (newValue, oldValue) => {
    if (newValue !== '' && newValue !== null) {
        // If invoice_due_date changes to a non-empty value
        delayed.value = true
        delayValue.value = 'other'
        delayDate.value = newValue
        emits('update', delayDate.value)
    } else if ((oldValue !== '' && oldValue !== null) && (newValue === '' || newValue === null)) {
        // If invoice_due_date changes from a value to empty or null
        // Reset the component state but keep the switch on if it was on
        if (delayed.value) {
            // Only reset the values if the switch is still on
            delayValue.value = '7' // Default to 7 days
            updateDelay() // This will calculate the new date based on delayValue
        }
    }
}, { immediate: true })

// Emit the update event when the component is mounted if invoice_due_date is not null or empty
onMounted(() => {
    if (props.invoice_due_date !== '' && props.invoice_due_date !== null) {
        emits('update', props.invoice_due_date)
    }
})
</script>



<style lang="scss" scoped>
.date-delay-wrap {
    .title {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 20px;
    }

    .switch-wrap {
        margin-left: 8px;
    }

    .select-wrap {
        margin-bottom: 16px;
    }
}
</style>
