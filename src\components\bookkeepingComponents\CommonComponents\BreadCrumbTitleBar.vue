<!-- @format -->

<script setup lang="ts">
import {ref, reactive, onMounted} from 'vue'
const name = ref('BreadCrumbTitleBar')
const props = defineProps<{
    itemList: {
        type: Array<any>
        required: true
    }
}>()
const data = () => {
    return {}
}
</script>

<template>
    <div class="page-container-breadcrumb">
        <a-icon type="home" class="home-icon" />
        <a-breadcrumb>
            <a-breadcrumb-item v-for="(item, index) in itemList" :key="index">
                {{ $t('router.' + item) }}
            </a-breadcrumb-item>
        </a-breadcrumb>
    </div>
</template>

<style lang="scss" scoped>
.page-container-breadcrumb {
    display: flex;
    align-items: center;
    height: 35px;
    min-height: 35px;
    padding-left: 20px;
    background: #1a94d0;

    .home-icon {
        font-size: 16px;
        margin-right: 10px;
        color: #ffffff;
    }

    /deep/ .a-breadcrumb__inner {
        color: #ffffff;
    }

    /deep/ .a-breadcrumb__item {
        .a-breadcrumb__inner,
        .a-breadcrumb__separator {
            color: #fff !important;
            pointer-events: none;
        }
    }
}
</style>
