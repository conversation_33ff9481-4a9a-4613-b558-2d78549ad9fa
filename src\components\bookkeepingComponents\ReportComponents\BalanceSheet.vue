<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount, watch} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {numberToCurrency} from '@/lib/utils'
import {ExportOutlined, DownOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'
import {v4 as uuidv4} from 'uuid'

import * as _ from 'lodash'
import moment from 'moment'
import type {Dayjs} from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'
import GlComponent from '@/components/bookkeepingComponents/GlComponents/GlComponent.vue'

const current = ref({})
const showGL = ref(false)
const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global

const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

const today = ref(moment().format('YYYY-MM-DD'))
const tableLoading = ref(false)
const treeLoading = ref(false)

const selectedKeys = ref<string[]>([])

const treeSource = computed(() =>
    generateTreeData(store.state.ReportStore.balanceSheet, []).map((item: any) => {
        return {
            ...item,
            children: _.sortBy(item.children, ['sort']),
        }
    }),
)

const generateTreeData = (data: any, list: any, parentKey?: string, showTotal?: boolean) => {
    _.forIn(data, (value, key) => {
        if (key !== 'total') {
            const item = {
                title: key,
                key: uuidv4(),
                value: _.isPlainObject(value)
                    ? _.isNumber(value.total) && parentKey
                        ? numberToCurrency(value.total)
                        : null
                    : numberToCurrency(value),
                disabled: false,
                children: _.isPlainObject(value) ? [] : null,
                sort: 1,
            }
            if (item.children) {
                generateTreeData(value, item.children, key, !parentKey)
            }

            list.push(item)
        } else if (showTotal) {
            const item = {
                title: `Total ${parentKey}`,
                key: uuidv4(),
                value: numberToCurrency(value),
                disabled: true,
                sort: 999,
            }
            list.push(item)
        }
    })

    return list
}

const total = reactive({
    net_amount: '0.00',
    gst: '0.00',
    qst: '0.00',
    pst: '0.00',
    total_tax: '0.00',
    total_fee: '0.00',
})

const searchForm = reactive<{[key: string]: string | undefined}>({
    end: today.value,
    company: undefined,
})

const fetchReportsList = (payload: any) => store.dispatch('ReportStore/getBalanceSheetData', payload)
const updateBalanceSheet = (list: any) => store.commit('ReportStore/updateBalanceSheet', list)

const disabledDate = (current: Dayjs) => {
    return current && current > moment().endOf('day')
}

const tableScrollY = computed(() => {
    return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight - 56
})

const columns: ColumnsType = [
    {
        title: i18n.t('reports.accountCode'),
        dataIndex: 'gl_account',
        key: 'gl_account',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.documentNo'),
        dataIndex: 'document_no',
        key: 'document_no',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.postingDate'),
        dataIndex: 'posting_date',
        key: 'posting_date',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.narration'),
        dataIndex: 'narration',
        key: 'narration',
        align: 'center',
        ellipsis: true,
    },
    {
        title: 'BP',
        dataIndex: 'bp_name',
        key: 'bp_name',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.endDebit'),
        dataIndex: 'debit',
        key: 'debit',
        width: 80,
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.endCredit'),
        dataIndex: 'credit',
        key: 'credit',
        width: 80,
        align: 'center',
        ellipsis: true,
    },
]

const state = reactive({
    tableData: [] as any,
})

const drail = async (title: string) => {
    if (!title || title.length < 4) return

    try {
        const code = title.substring(0, 4)
        if (!+code) return

        treeLoading.value = true
        state.tableData = await store.dispatch('ReportStore/glExportJson', {
            company: userCompany[0].code,
            // start: '2023-08-01',
            end: searchForm.end,
            coa: code,
        })
    } catch (err) {
        console.log(err)
    } finally {
        treeLoading.value = false
    }
}

const exportReport = async () => {
    try {
        treeLoading.value = true
        await Promise.all([
            store.dispatch('ReportStore/exportBS', {
                company: userCompany[0].code,
                company_name: userCompany[0].name,
                end: searchForm.end,
            }),
            store.dispatch('ReportStore/exportBSPdf', {
                company: userCompany[0].code,
                company_name: userCompany[0].name,
                end: searchForm.end,
            }),
        ])
    } catch (err) {
        console.log(err)
    } finally {
        treeLoading.value = false
    }
}

const selectChange = async () => {
    await updateList()
}

const updateList = async () => {
    try {
        treeLoading.value = true
        updateBalanceSheet({})
        await fetchReportsList(searchForm)
        treeLoading.value = false
    } catch (error) {
        console.log(error)
        treeLoading.value = false
    }
}

onBeforeMount(async () => {
    updateBalanceSheet({})

    searchForm.company = userCompany[0].code

    await updateList()
})

const fetchGlList = (payload: any) => store.dispatch('GlStore/fetchGlListV1', payload)

const onClickRow = (record: any, company: any) => {
    // debugger;
    return {
        onclick: async (event: any) => {
            // current.value = _.cloneDeep(record)
            const response = await fetchGlList({
                company: company,
                document_no: record.document_no,
            })
            current.value = _.cloneDeep(response.data)

            // readonlyMode.value = true
            // operationMode.value = 'detail'
            showDialog(true)
        },
    }
}

const showDialog = (bool: boolean) => {
    showGL.value = bool
    if (!bool) {
        current.value = {}
    }
}

const dismiss = () => {
    showDialog(false)
    // readonlyMode.value = false
}

// const popConfirmModal = () => {
//     const { posting_date } = current.value as any
//     reverseDt.value = posting_date
//     showConfirm.value = true
// }

// const confirmationConfirm = async () => {
//     showReverseLoading.value = true
//     const alist = JSON.parse(JSON.stringify(current.value))
//     // reverseDt = alist['posting_date']
//     const queryParam = {
//         document_no: alist['document_no'],
//         posting_date: reverseDt.value, //alist['posting_date'],
//         creator: userInfo?.id || '',
//     }
//     await reverse(queryParam)
//     showReverseLoading.value = false
//     showConfirm.value = false
// }

// const reverse = async (obj: any) => {
//     let response = {} as any
//     try {
//         tableLoading.value = true
//         response = await fetchReverse(obj)
//         if (response.data.data.success) {
//             message.success(i18n.t('ApComponents.success'))

//             showDialog(false)
//             updateTable()
//         }
//     } catch (error: any) {
//         // showConfirm.value = false
//         console.error(error)
//     } finally {
//         showReverseLoading.value = false
//         tableLoading.value = false
//     }
// }

// watch(
//     () => dataSource.value,
//     val => {
//         total.net_amount = numberToCurrency(
//             val.reduce((prev: any, curr: any) => {
//                 return prev + +curr.net_amount
//             }, 0) || 0,
//         )
//         total.gst = numberToCurrency(
//             val.reduce((prev: any, curr: any) => {
//                 return +prev + +curr.gst
//             }, 0) || 0,
//         )
//         total.pst = numberToCurrency(
//             val.reduce((prev: any, curr: any) => {
//                 return +prev + +curr.pst
//             }, 0) || 0,
//         )
//         total.qst = numberToCurrency(
//             val.reduce((prev: any, curr: any) => {
//                 return +prev + +curr.qst
//             }, 0) || 0,
//         )
//         total.total_tax = numberToCurrency(
//             val.reduce((prev: any, curr: any) => {
//                 return +prev + +curr.total_tax
//             }, 0) || 0,
//         )
//         total.total_fee = numberToCurrency(
//             val.reduce((prev: any, curr: any) => {
//                 return +prev + +curr.total_fee
//             }, 0) || 0,
//         )
//     },
// )
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="header-title-wrap">
                <div class="header-title">{{ i18n.t('reports.balanceSheet') }}</div>
                <div class="header-subtitle">{{ i18n.t('reports.updatedOn') }}: {{ today }}</div>
            </div>
            <a-divider type="vertical" style="margin-left: 60px" />
            <div class="selects-wrap">
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.end"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :placeholder="i18n.t('reports.experationDate')"
                    :disabled="treeLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
            </div>

            <a-button type="primary" shape="round" :disabled="treeLoading" @click="exportReport()">
                <export-outlined />
                {{ i18n.t('workTimeManager.export') }}
            </a-button>
        </div>
        <div class="page-content">
            <div class="page-content-left">
                <div class="title">{{ i18n.t('reports.closingBalance') }}</div>
                <a-spin :spinning="treeLoading">
                    <a-tree
                        v-if="treeSource.length"
                        v-model:selectedKeys="selectedKeys"
                        :tree-data="treeSource"
                        default-expand-all="true"
                    >
                        <template #switcherIcon="{switcherCls}"><down-outlined :class="switcherCls" /></template>
                        <template #title="{title, value}">
                            <div class="tree-details-wrap">
                                <span @click="drail(title)">{{ title }}</span>
                                <span>{{ value }}</span>
                            </div>
                        </template>
                    </a-tree>
                </a-spin>
            </div>
            <div class="page-content-right" ref="tableElWrapRef">
                <div class="title">{{ i18n.t('reports.itemsDetail') }}</div>
                <a-table
                    :dataSource="state.tableData"
                    :columns="columns"
                    :pagination="false"
                    :loading="tableLoading"
                    :scroll="{y: tableScrollY}"
                    :customRow="(row: any) => onClickRow(row, userCompany[0].code)"
                >
                    <!-- <template #summary>
                    <a-table-summary fixed>
                        <a-table-summary-row class="table-summary-wrap">
                            <a-table-summary-cell class="table-summary-total">
                                <a-typography-text>{{ i18n.t('reports.subTotal') }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>
                            <a-table-summary-cell></a-table-summary-cell>

                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.net_amount }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.gst }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.pst }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.qst }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.total_tax }}</a-typography-text>
                            </a-table-summary-cell>
                            <a-table-summary-cell class="table-summary-text">
                                <a-typography-text>{{ total.total_fee }}</a-typography-text>
                            </a-table-summary-cell>
                        </a-table-summary-row>
                    </a-table-summary>
                </template> -->
                </a-table>
            </div>
            <a-modal
                :title="i18n.t('gl.readonly')"
                v-model:visible="showGL"
                :footer="null"
                destroyOnClose
                :closeable="true"
                :width="1000"
                style="z-index: 999"
                :dialogStyle="{top: '10px'}"
                :bodyStyle="{padding: '10px 24px 24px'}"
            >
                <gl-component
                    :current-invoice="current"
                    :readonly-mode="true"
                    :operation-mode="'detail'"
                    @dismiss="dismiss"
                ></gl-component>
            </a-modal>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.page-wrap {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: #fff;

    .page-header {
        min-height: 84px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        margin-bottom: 14px;

        .header-title-wrap {
            min-width: 148px;
            max-width: 148px;

            .header-title {
                line-height: 26px;
                height: 26px;
                color: #004fc1;
                font-size: 20px;
                font-weight: 700;
                letter-spacing: 0;
            }

            .header-subtitle {
                font-size: 12px;
                color: #262626;
                letter-spacing: 0;
                line-height: 18px;
                font-weight: 400;
            }
        }

        .ant-divider {
            border-left-color: #e2e2ea;
            height: 36px;
            top: 0;
            margin: 0 18px;
        }

        .selects-wrap {
            width: 100%;
            display: flex;
            align-items: center;

            .target-currency-select,
            .company-select,
            .coa-account-select {
                min-width: 175px;
                width: 175px;
                max-width: 500px;
                margin-right: 8px;
                flex: 1;
            }

            .data-select {
                min-width: 145px;
                width: 145px;
                max-width: 145px;
                margin-right: 8px;
                flex: 1;
            }

            .transaction-currency-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.2;
            }

            .coa-account-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.5;
                margin-right: 0;

                .select-all-item {
                    padding: 5px 12px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                    .ant-checkbox-wrapper {
                        width: 100%;
                    }
                }

                :deep(.ant-select-selector) {
                    overflow: hidden;

                    .ant-select-selection-overflow {
                        flex-wrap: nowrap;
                    }
                }
            }
        }

        .ant-btn-round {
            padding: 6px 10px;
            margin-left: 16px;
        }
    }

    .page-content {
        padding: 0 20px;
        overflow: hidden;
        height: calc(100% - 105px);
        display: flex;
        justify-content: space-between;
        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding: 15px 8px;
        }
        :deep(.ant-table-wrapper .ant-table .ant-table-header .ant-table-cell) {
            padding: 12px 8px;
        }
        .page-content-left {
            width: 52%;
            border-radius: 8px;
            border: 1px solid #c3c7d4;
            padding: 8px;
            overflow: auto;
            height: 100%;
            .title {
                color: #262626;
                text-align: right;
                line-height: 20px;
                font-weight: 700;
                margin-bottom: 10px;
            }
            .ant-spin-spinning {
                height: calc(100% - 30px);
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            :deep(.ant-spin-nested-loading) {
                height: calc(100% - 30px);
                width: 100%;
                .ant-spin-container {
                    height: 100%;
                }
            }
            :deep(.ant-tree) {
                .ant-tree-treenode {
                    width: 100%;
                    border-bottom: 1px solid #f0f0f0;
                    padding: 2px 0;
                    .ant-tree-switcher-noop {
                        display: none;
                    }
                    .ant-tree-switcher-noop + .ant-tree-node-content-wrapper {
                        .tree-details-wrap {
                            font-weight: 400;
                        }
                    }
                    .ant-tree-node-content-wrapper {
                        width: 100%;
                        background-color: transparent !important;
                        display: block;
                        .ant-tree-title {
                            display: block;
                        }
                        &.ant-tree-node-selected {
                            color: #004fc1;
                        }
                    }
                    &.ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
                        color: #262626;
                        cursor: default;

                        .tree-details-wrap {
                            font-weight: 600;
                        }
                    }
                }
            }

            .tree-details-wrap {
                display: flex;
                justify-content: space-between;
                font-weight: 600;
            }
        }
        .page-content-right {
            width: calc(48% - 8px);
            border-radius: 8px;
            border: 1px solid #c3c7d4;
            padding: 8px;
            .title {
                color: #262626;
                text-align: center;
                line-height: 20px;
                font-weight: 700;
                margin-bottom: 10px;
            }
        }
    }

    .table-summary-wrap {
        .table-summary-total {
            text-align: left;
            font-weight: 600;
        }

        .table-summary-text {
            text-align: right;
        }
    }

    .page-footer {
        padding: 5px 8px;
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;
            width: 100%;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                min-width: 45px;
            }

            .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
                min-width: 35px;
            }
        }
    }
}
</style>
