<!-- @format -->

<template>
    <div style="height: 100%">
        <a-form :model="discountForm" :layout="'vertical'" autocomplete="off" @submit="submit">
            <div class="import-form-block">
                <a-row type="flex" :gutter="[24, 32]">
                    <a-col :span="12">
                        <a-form-item name="payment_terms_day_1" :label="'Payment terms-Day1'">
                            <a-input v-model:value="discountForm.payment_terms_day_1"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="payment_terms_discount_1" :label="'Discount%1'">
                            <a-input v-model:value="discountForm.payment_terms_discount_1"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
                <div>&nbsp;</div>
                <a-row :gutter="[24, 32]">
                    <a-col :span="12">
                        <a-form-item name="payment_terms_day_2" :label="'Payment terms-Day2'">
                            <a-input v-model:value="discountForm.payment_terms_day_2"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="payment_terms_discount_2" :label="'Discount%2'">
                            <a-input v-model:value="discountForm.payment_terms_discount_2"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
                <div>&nbsp;</div>
                <a-row :gutter="[24, 32]">
                    <a-col :span="12">
                        <a-form-item name="payment_terms_day_3" :label="'Payment terms-Day3'">
                            <a-input v-model:value="discountForm.payment_terms_day_3"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item name="payment_terms_discount_3" :label="'Discount%3'">
                            <a-input v-model:value="discountForm.payment_terms_discount_3"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
                <div>&nbsp;</div>
            </div>
            <div style="text-align: right">
                <a-button shape="round" class="cancel-button" @click="cancel">Cancel</a-button>
                <a-button shape="round" type="primary" html-type="submit">Confirm</a-button>
            </div>
        </a-form>
    </div>
</template>

<script lang="ts" setup>
import {onBeforeMount, ref} from 'vue'
const props = withDefaults(
    defineProps<{
        discounts: any
    }>(),
    {
        discounts: {} as any,
    },
)

const discountForm = ref({} as any)
const emits = defineEmits(['confirm', 'dismiss'])

const submit = () => {
    console.log('check terms: ', discountForm.value)
    emits('confirm', discountForm.value)
}

const cancel = () => {
    emits('dismiss')
}

const initForm = () => {
    const {
        payment_terms_day_1,
        payment_terms_day_2,
        payment_terms_day_3,
        payment_terms_discount_1,
        payment_terms_discount_2,
        payment_terms_discount_3,
    } = props.discounts
    discountForm.value = {
        payment_terms_day_1: payment_terms_day_1 || '',
        payment_terms_day_2: payment_terms_day_2 || '',
        payment_terms_day_3: payment_terms_day_3 || '',
        payment_terms_discount_1: payment_terms_discount_1 || '',
        payment_terms_discount_2: payment_terms_discount_2 || '',
        payment_terms_discount_3: payment_terms_discount_3 || '',
    }
}
onBeforeMount(() => {
    initForm()
})
</script>

<style lang="scss" scoped>
.import-form-block {
    padding: 0px 0px;
}

.cancel-button {
    border-color: #004fc1;
    color: #004fc1;
    margin-right: 8px;
}
</style>
