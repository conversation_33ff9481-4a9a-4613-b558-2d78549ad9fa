/** @format */

import service from '../../../../api/request'
import servicev1 from '@/api/requestNew'
import servicevPgo from '@/api/requestPgo'
import {v4 as uuidv4} from 'uuid'
import type {AxiosRequestConfig} from 'axios'
import type {ActionContext} from 'vuex'
import * as _ from 'lodash'
import moment from 'moment'

const http = service
const httpv1 = servicev1
const httpPgo = servicevPgo

const BrStore = {
    namespaced: true,
    state: {
        esPdfList: [],
        ocrEsListWithPdf: [],
        totalNumber: 0,
        pageTotalNumber: 0,
        esList: [],
        statementDescInfo: {},
        esInvoiceDetailList: [],
        plaidBankList: [],
        historyEsList: [],
        manualEStatement: {} as any,
        esReasonList: [],
    },
    getters: {
        getStatementDescInfo: (
            state: {
                statementDescInfo: {
                    accountType: any
                    accountTypeId: string
                    fileCurrency: any
                }
            },
            getters: any,
            rootState: any,
            rootGetters: {[x: string]: any},
        ) => {
            const accountTypeList = rootGetters['CommonDropDownStore/getAccountTypeList']
            const currencyTypeList = rootGetters['CommonDropDownStore/getCurrencyTypeList']
            const showInfo = {...state.statementDescInfo}
            const aTFound = accountTypeList.find((x: {key: any}) => x.key === state.statementDescInfo.accountType)
            const cTFound = currencyTypeList.find((x: {key: any}) => x.key === state.statementDescInfo.fileCurrency)
            showInfo.accountType = aTFound ? aTFound.value : state.statementDescInfo.accountType
            showInfo.fileCurrency = cTFound ? cTFound.value : state.statementDescInfo.fileCurrency
            showInfo.accountTypeId = state.statementDescInfo.accountType
            return showInfo
        },
    },
    mutations: {
        updateEsPdfList(state: {esPdfList: any[]}, list: any) {
            state.esPdfList = [...list]
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updatePageTotalFoundNumber(state: {pageTotalNumber: any}, num: any) {
            state.pageTotalNumber = num
        },
        updateOcrEsListByPdf(state: {ocrEsListWithPdf: any[]}, list: any) {
            state.ocrEsListWithPdf = [...list]
        },
        updateEsList(state: {esList: any[]}, list: any) {
            state.esList = [...list]
            // console.log('1',state.esList)
        },
        updateStatementDescInfo(state: {statementDescInfo: any}, desc: any) {
            state.statementDescInfo = {...desc}
        },
        updateEsInvoiceDetailList(state: {esInvoiceDetailList: any[]}, list: any) {
            state.esInvoiceDetailList = [...list]
        },
        updatePlaidBankList(state: {plaidBankList: any[]}, list: any) {
            state.plaidBankList = [...list]
        },
        updateHistoryEsList(state: {historyEsList: any[]}, list: any) {
            state.historyEsList = [...list]
        },
        updateManualInvoice(state: {manualEStatement: any[]}, eStatement: any) {
            state.manualEStatement = {...eStatement}
        },
        updateEsReasonList(state: {esReasonList: any[]}, list: any) {
            state.esReasonList = {...list}
        },
    },
    actions: {
        async fetchEsPdfList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {flg: string},
        ) {
            let response: any = []
            if (payload.flg === 'cwb') {
                response = await http.post('/bk/file/esFetchlist', payload)
            } else {
                response = await http.post('/bk/file/eslist', payload)
            }

            store.commit('updateEsPdfList', response.data.data.list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchEsFromPlaid(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: AxiosRequestConfig<any> | undefined,
        ) {
            const response = await http.get('/bk/bank/transactions/sync', payload)
            //store.commit('updateEsPdfList', response.data.data.list)
            //store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async uploadEsPdf(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const formData = payload
            const response = await http.post('/bk/file/upload', formData)
            return response
        },
        async uploadEsCharge(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            console.log('uploadEsCharge', payload)
            const response = await httpv1.patch(`invoice-statement/api/v1/es/${payload?.id}`, {
                charge_fee: payload.chargeFee,
                charge_coa: payload.chargeCoa,
            })
            return response
        },
        async fetchEsReasonDropdown(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get(`/prefectClient/reason_code/${payload?.company_code}`)
            store.commit('updateEsReasonList', response.data)
            return response.data
        },
        async fetchEsByBankAccount(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get(`/prefectClient/bank_account_transactions?company_code=${payload?.company_code}&bank_account=${payload?.bank_account}`)
            return response.data
        },
        async updateEsReason(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            console.log('uploadEsCharge', payload)
            const response = await httpv1.patch(`invoice-statement/api/v1/es/${payload?.id}`, {
                reason_code: payload.reasonCode,
                reason_msg: payload.reasonMsg,
            })
            return response
        },
        async deletePdfWithId(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/file/${payload}`)
        },
        async fetchOcrEsListWithPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {fileId: any},
        ) {
            const response = await http.post(`/bk/ocr/pdf/${payload.fileId}/ocrestatement/list`)
            // const list = response.data.data.reduce((pre, cur) => {
            //   const {date, description, withdrawal, deposit} = cur
            //   pre.push({date, description, withdrawal, deposit})
            //   return pre
            // }, [])
            // store.commit('updateOcrEsListByPdf', list)
            store.commit('updateOcrEsListByPdf', response.data.data)
            return response
        },
        async fetchOcrEsListWithPdf1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {fileId: any},
        ) {
            const response = await http.post(`/bk/br/pdf/${payload.fileId}/estatement/list`)
            console.log('response111', response)
            store.commit('updateOcrEsListByPdf', response.data.data)
            return response
        },
        async fetchEsList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {
                pageIndex: any
                pageSize: any
                createStartDate: any
                createEndDate: any
                bankAccount: any
            },
        ) {
            //const response = await http.post('/bk/br/reconciliation/estatement/list', payload)
            //上面是原来的API请求，下面是为了展示树形数据，改的新API。
            const response = await http.post('/bk/br/reconciliation/list', {
                pageIndex: payload.pageIndex,
                pageSize: payload.pageSize ? payload.pageSize : 5,
                createStartDate: payload.createStartDate || '',
                createEndDate: payload.createEndDate || '',
                bankAccount: payload.bankAccount || '',
            })
            for (let i = 0; i < response.data.data.list.length; i++) {
                if (response.data.data.list[i].deposit === 0) {
                    response.data.data.list[i].brType = '1'
                }
            }
            const treeList = response.data.data.list.map((item: {id?: any; matchList?: any}) => {
                const {matchList} = item
                // const eStatement = matchList.eStatement.map(e=>{
                //   return {
                //     id: e.id,
                //     bankAccount: e.invoiceNo,
                //     date:e.invoiceCreateDate,
                //     reference:e.referenceNo,
                //     deposit:e.totalFee,
                //   }
                // })
                const invoiceList = matchList.invoice?.reduce((acc: any[], current: any) => {
                    return (acc = [...acc, ...current])
                }, [])
                const invoice =
                    invoiceList?.map(
                        (
                            m: {
                                id: any
                                invoiceNo: any
                                invoiceCreateDate: any
                                referenceNo: any
                                companyCode: any
                                invoiceType: string
                                totalFee: any
                                balance: any
                            },
                            index: any,
                        ) => {
                            return {
                                id: m.id,
                                key: uuidv4(),
                                bankAccount: m.invoiceNo,
                                date: m.invoiceCreateDate,
                                reference: m.referenceNo,
                                payerPayee: m.companyCode,
                                deposit: m.invoiceType === '1' ? m.totalFee : null,
                                withdrawal: m.invoiceType === '2' ? m.totalFee : null,
                                balance: m.balance,
                            }
                        },
                    ) || []
                return {
                    key: item.id,
                    ...item,
                    children: [
                        // ...eStatement,
                        ...invoice,
                    ],
                }
            })
            console.log('treeList', treeList)
            // store.commit('updateEsList', response.data.data.list)
            store.commit('updateEsList', treeList)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchEsListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get('/invoice-statement/api/v1/es-reconcile/list', {params: {...payload}})

            const treeList = response.data.data.map((item: any) => {
                return transformStatementInvoice(item, payload.ap_integration)
                // let {match_list} = item
                //
                // if (!match_list) {
                //     match_list = []
                // }
                // const invoiceList = [...match_list]
                //
                // // children for table view, the object of children is static
                // // match_list for source from API, no modification on it
                // const invoice =
                //     invoiceList?.map((m: any) => {
                //         return {
                //             id: m.id,
                //             key: m.id,
                //             bp_number: m.bp_number,
                //             bank_account: m.invoice_no,
                //             date: m.posting_date,
                //             reference: m.reference_no,
                //             payer_payee: m.company_code,
                //             deposit: m.deposit,
                //             withdrawal: m.withdrawal,
                //             balance: m.balance,
                //             invoice_due_date: m.invoice_due_date,
                //             posting_date: m.posting_date,
                //             br_type: m.br_type,
                //             br_entity_type: m.br_entity_type,
                //             engine_document_id: m.engine_document_id,
                //         }
                //     }) || []
                // const obj = {
                //     key: item.id,
                //     // match_flag: invoice.length > 0 ? 1 : 0,
                //     ...item,
                //     children: [...invoice],
                //     match_list: [...invoiceList],
                // }
                // obj.expend_flag = invoice.length > 0 ? 1 : 0
                // return obj
            })

            console.log('treelist: ', treeList)

            store.commit('updateEsList', treeList)
            store.commit('updateTotalFoundNumber', response.data.paginated.total)
            store.commit('updatePageTotalFoundNumber', response.data.paginated.page_total)
            return treeList
        },
        async fetchEsHistoryList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/br/history/estatement/list', payload)
            const treeList = response.data.data.map((item: any) => {
                return {key: item.id, ...item, children: [...item.invoice_list]}
            })
            // store.commit('updateEsList', treeList)
            store.commit('updateHistoryEsList', response.data.data)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchEsHistoryListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get('/invoice-statement/api/v1/es-reconcile-reverse/e-statements', {
                params: payload,
            })
            store.commit('updateHistoryEsList', response.data.data)
            store.commit('updateTotalFoundNumber', response.data.paginated.total)
            return response
        },
        // async fetchEsHistoryListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        //     const response = await httpv1.get('/invoice-statement/api/v1/es-reconcile/list', {params: {...payload}})
        //     store.commit('updateEsList', response.data.data.list)
        //     store.commit('updateTotalFoundNumber', response.data.data.totalCount)
        //     return response
        // },?statement_id=${payload.statement_id}&creator=${payload.creator}
        async reverseStatement(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post(`/invoice-statement/api/v1/es-reconcile-reverse/transaction`, null, {
                params: payload,
            })
            return response
        },
        async reverseStatementIntegration(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post(
                `/invoice-statement/api/v1/es-reconcile-reverse/transaction/integration`,
                null,
                {
                    params: payload,
                },
            )
            return response
        },
        async fetchEsInvoiceDetailWithId(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any; invoiceType: any},
        ) {
            const response = await http.post(`/bk/br/history/estatement/${payload.id}`, {
                invoiceType: payload.invoiceType,
            })
            // store.commit('updateEsInvoiceDetailList', response.data.data)
            return response
        },
        async fetchPlaidBank(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            //?company_id=201&plaid_status=1
            // const response = await httpv1.get('/invoice-statement/api/v1/plaid-bank-info/company-bank', {params: payload})
            const response = await httpv1.get('/invoice-statement/api/v1/plaid-bank-info', {params: payload})
            store.commit('updatePlaidBankList', response.data.data)
            return response
        },

        async fetchManualInvoice(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get('/invoice-statement/api/v1/es-reconcile/manual', {params: payload})
            const eStatement = transformStatementInvoice(response.data.data, 1)
            store.commit('updateManualInvoice', eStatement)
            return response
        },

        async updatePlaidBank(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.patch(`invoice-statement/api/v1/plaid-bank-info/${payload?.id}`, payload)
            return response
        },
        async syncRemoteBank(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post('invoice-statement/api/v1/es-connect/sync', payload)
            return response
        },
        async switchAutoReconcile(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.patch(`invoice-statement/api/v1/es/${payload.id}`, {
                match_flag: payload.match_flag,
            })
            return response
        },
        async changeEStatementType(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.patch(`invoice-statement/api/v1/es/${payload.id}`, {
                br_type: payload.br_type,
                report_type: payload.report_type,
                charge_fee: payload.charge_fee,
                charge_coa: payload.charge_coa,
            })
            return response
        },
        async pgoStartFlow(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpPgo.post(
                `pgo/startFlow/${
                    process.env.NODE_ENV === 'development'
                        ? '40288189899d396901899d3adae10002'
                        : '8ab8819789d6a78b0189da9be3ef0003'
                }`,
                payload,
            )
            return response
        },
        async changeReconcileReportType(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            console.log('the pay load is ', payload)
            const response = await httpv1.patch(`invoice-statement/api/v1/es/${payload.id}`, {
                report_type: payload.report_type,
                deleted_time: moment().format('YYYY-MM-DD'),
            })
            return response
        },

        async createCashStatement(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/br/estatement/create', payload)
            return response
        },
        async updateCashStatement(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.put('/bk/br/estatement', payload)
        },
        async deleteCashStatement(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/br/estatement/${payload}`)
        },
        async saveOcrEsListWithPdf(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            // console.log(payload)
            const response = await http.post('/bk/br/estatement/batchcreate', payload)
            return response
        },

        async deleteESAction(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.delete(`/invoice-statement/api/v1/es/${payload}`)
            return response
        },
    },
}

const transformStatementInvoice = (record: any, apIntegration?: number): any => {
    const statement = _.cloneDeep(record)
    const isFXFT = ['6', '4'].includes(statement.br_type?.toString())

    let {match_list} = statement

    if (!match_list) {
        statement.match_list = []
        match_list = []
    }
    statement.expend_flag = statement.match_list.length > 0 ? 1 : 0
    statement.key = statement.id
    // children for table view, the key of children is static
    // match_list for source from API, no modification on it
    const invoice = match_list.map((m: any) => {
        let invoiceItem: any = {}
        
        invoiceItem = {
            // table view key start
            id: m.id,
            key: m.id,
            invoice_no: m.invoice_no || '',
            date: isFXFT ? m.date : m.posting_date,
            description: apIntegration === 1 ? m.invoice_comments : m.reference_no,
            reference: isFXFT ? '' : m.reference_no,
            payer_payee: m.payer_payee,
            balance: m.balance,
            total_fee: m.total_fee,
            mx_isr: m.mx_isr,
            mx_iva: m.mx_iva,
            reference_no: m.reference_no,
            invoice_comments: m.invoice_comments,
            // table view key end
            // other useful key
            br_type: m.br_type,
            reason_code: m.reason_code,
            br_entity_type: m.br_entity_type,
            currency: isFXFT ? m.currency_type : m.invoice_currency,
            invoice_id: m.invoice_id,
            bp_number: isFXFT ? '' : m.bp_number,
            engine_document_id: isFXFT ? '' : m.engine_document_id,
            sap_document_id: m.sap_document_id,
            parent_br_type: statement.br_type?.toString(),
        }

        // assign debit/credit with total_fee according br_type for invoices
        switch (m.br_type.toString()) {
            case '0': //regular sales
                invoiceItem.deposit = +m.total_fee
                invoiceItem.withdrawal = 0
                break
            case '3': // sales refund, negative value
                invoiceItem.deposit = -m.total_fee
                invoiceItem.withdrawal = 0
                break
            case '1': //regular purchase
            case '5': //payroll
                invoiceItem.deposit = 0
                invoiceItem.withdrawal = +m.total_fee
                break
            case '2': //purchase refund, negative value
                invoiceItem.deposit = 0
                invoiceItem.withdrawal = -m.total_fee
                break
            case '4': //funding trans
            case '6': //Es of different currency
                invoiceItem.deposit = m.deposit
                invoiceItem.withdrawal = m.withdrawal
                break
            default:
                invoiceItem.deposit = 0
                invoiceItem.withdrawal = 0
        }
        if (isFXFT) {
            invoiceItem.bank_account = m.bank_account
        }
        return invoiceItem
    })
    statement.children = [...invoice]
    return statement
}
export default BrStore
