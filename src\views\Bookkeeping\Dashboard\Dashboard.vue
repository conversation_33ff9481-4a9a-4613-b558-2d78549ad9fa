<!-- @format -->

<script lang="ts" setup>
import * as echarts from 'echarts'
// import { throttle } from "@/utils/index";
import {h, onMounted, ref, reactive, watch} from 'vue'
import {useStore} from 'vuex'
import {UserCompany} from '@/lib/storage'
import moment from 'moment'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {Progress} from 'ant-design-vue'

const i18n: Composer = i18nInstance.global
const todayTitle = ref(moment().format('YYYY-MM-DD'))
const store = useStore()
const userCompany: any = UserCompany.get() || []
const loading = ref(true) // 添加这行
const state = reactive({
    initialSales: 0.0,
    initialCostOfSales: 0.0,
    initialGrossProfit: 0.0,
    initialOperatingExpenses: 0.0,
    initialNetIncome: 0.0,
})

onMounted(() => {
    items.value = []
    init()
})

const throttle = (fn: any, delay: any) => {
    let isRunning = true
    return function () {
        if (!isRunning) {
            return
        }
        isRunning = false
        setTimeout(() => {
            fn.apply()
            isRunning = true
        }, delay)
    }
}

// const items = ref([
//     {date: '2022-04', gross: -910.39, netIncome: 910.39},
//     {date: '2022-05', gross: -14.89, netIncome: 1151.73},
//     {date: '2022-06', gross: -155.63, netIncome: 374.56},
//     {date: '2022-07', gross: 11959.51, netIncome: 11956.51},
//     {date: '2022-08', gross: 2206.61, netIncome: 2206.61},
//     {date: '2022-09', gross: 2696.97, netIncome: 2425.53},
//     {date: '2022-10', gross: -4825.0, netIncome: -4979.5},
//     {date: '2022-11', gross: 4270.84, netIncome: 4053.76},
//     {date: '2022-12', gross: -3315.33, netIncome: -9344.3},
//     {date: '2023-01', gross: 5866.49, netIncome: 2605.62},
//     {date: '2023-02', gross: 6031.9, netIncome: 4376.97},
//     {date: '2023-03', gross: 6031.9, netIncome: 4376.97},
// ])
// const items = ref([
//     {
//         date: '2022-04',
//         sales: 0,
//         costOfSales: 0,
//         grossProfit: 0,
//         operatingExpenses: 0,
//         netIncome: 910.39,
//     },
//     {
//         date: '2022-05',
//         sales: 0,
//         costOfSales: 0,
//         grossProfit: 0,
//         operatingExpenses: 0,
//         netIncome: 1151.73,
//     },
// ])
const items = ref([
    {
        date: '',
        sales: 0,
        costOfSales: 0,
        grossProfit: 0,
        operatingExpenses: 0,
        netIncome: 0,
    },
])

const columns = ref([
    {
        title: 'Year Month',
        key: 'date',
        align: 'center',
    },
    {
        title: 'Gross Profit',
        key: 'gross',
        align: 'center',
        render(row: any) {
            return h('div', {style: row.gross > 0 ? 'color:red' : 'color:green'}, row.gross)
        },
    },
    {
        title: 'Net InCome',
        key: 'netIncome',
        align: 'center',
        render(row: any) {
            return h('div', {style: row.netIncome > 0 ? 'color:red' : 'color:green'}, row.gross)
        },
    },
])
const today = new Date() // 获取当前日期
const currentYear = today.getFullYear() // 获取当前年份

const searchForm = reactive({
    start_date: new Date(currentYear, 0, 1).toISOString().split('T')[0],
    end_date: new Date(currentYear, 11, 31).toISOString().split('T')[0],
    company_code: userCompany[0].code,
})
const fetchDashBoardData = (payload: any) => store.dispatch('ReportStore/getDashBoardData', payload)
const fetchDashBoardData1 = (payload: any) => store.dispatch('ReportStore/getDashBoard1Data', payload)
const fetchDashBoardData2= (payload: any) => store.dispatch('ReportStore/getDashBoard2Data', payload)
const fetchDashBoardData3 = (payload: any) => store.dispatch('ReportStore/getDashBoard3Data', payload)
const fetchDashBoardData4 = (payload: any) => store.dispatch('ReportStore/getDashBoard4Data', payload)
const fetchDashBoardData5 = (payload: any) => store.dispatch('ReportStore/getDashBoard5Data', payload)
const fetchDashBoardData6 = (payload: any) => store.dispatch('ReportStore/getDashBoard6Data', payload)
const fetchDashBoardData7 = (payload: any) => store.dispatch('ReportStore/getDashBoard7Data', payload)
const fetchDashBoardData8 = (payload: any) => store.dispatch('ReportStore/getDashBoard8Data', payload)
const fetchDashBoardData9 = (payload: any) => store.dispatch('ReportStore/getDashBoard9Data', payload)
const fetchDashBoardData10 = (payload: any) => store.dispatch('ReportStore/getDashBoard10Data', payload)
const fetchDashBoardData11 = (payload: any) => store.dispatch('ReportStore/getDashBoard11Data', payload)
const fetchDashBoardData12 = (payload: any) => store.dispatch('ReportStore/getDashBoard12Data', payload)
const months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
// const months = ['02']

const init = async () => {
    // 获取 x 轴的月份
    const xAxisData = months.map(month => `${currentYear}-${month}`)
    const doc: any = document.getElementById('container')
    doc.removeAttribute('_echarts_instance_')
    const myChart = echarts.init(doc)

    const option = {
        title: {
            text: '',
        },
        tooltip: {
            trigger: 'axis',
        },
        legend: {
            data: ['Sales', 'Cost of Sales', 'Gross Profit', 'Operating Expenses', 'Net Income'],
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        toolbox: {
            feature: {
                // saveAsImage: {},
            },
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData,
        },
        yAxis: {
            type: 'value',
        },
        series: [
            {
                name: 'Sales',
                type: 'line',
                stack: 'Total',
                data: [] as number[],
            },
            {
                name: 'Cost of Sales',
                type: 'line',
                stack: 'Total',
                data: [] as number[],
            },
            {
                name: 'Gross Profit',
                type: 'line',
                stack: 'Total',
                data: [] as number[],
            },
            {
                name: 'Operating Expenses',
                type: 'line',
                stack: 'Total',
                data: [] as number[],
            },
            {
                name: 'Net Income',
                type: 'line',
                stack: 'Total',
                data: [] as number[],
            },
        ],
    }

    myChart.setOption(option)

    await fetchDashBoardData(searchForm)
    // store.dispatch('ReportStore/getDashBoardData', searchForm)
    const initialData = {...store.state.ReportStore.dashBoard}
    console.log('initialData', initialData)
    // 获取初始值
    state.initialSales = Number(parseFloat(initialData['Sales']).toFixed(2))
    state.initialCostOfSales = Number(parseFloat(initialData['Cost of Sales']).toFixed(2))
    state.initialGrossProfit = Number(parseFloat(initialData['Gross Profit']).toFixed(2))
    state.initialOperatingExpenses = Number(parseFloat(initialData['Operating Expenses']).toFixed(2))
    state.initialNetIncome = Number(parseFloat(initialData['Net Income']).toFixed(2))
    // 清空原始数据
    items.value = []

    for (const month of months) {
        loading.value = true
        // 设置搜索表单的日期范围
        searchForm.start_date = `${currentYear}-${month}-01`
        searchForm.end_date = `${currentYear}-${month}-${new Date(currentYear, parseInt(month), 0).getDate()}`

        // 获取数据

        await fetchDashBoardData(searchForm)

        // 获取该月数据
        const monthData = {...store.state.ReportStore.dashBoard}
        console.log('monthData', monthData)
        // 保存数据，并保留两位小数
        items.value.push({
            date: `${currentYear}-${month}`,
            sales: Number(parseFloat(monthData['Sales']).toFixed(2)),
            costOfSales: Number(parseFloat(monthData['Cost of Sales']).toFixed(2)),
            grossProfit: Number(parseFloat(monthData['Gross Profit']).toFixed(2)),
            operatingExpenses: Number(parseFloat(monthData['Operating Expenses']).toFixed(2)),
            netIncome: Number(parseFloat(monthData['Net Income']).toFixed(2)),
        })
        // 获取当前月的数据
        const currentMonthData: {
            sales: number
            costOfSales: number
            grossProfit: number
            operatingExpenses: number
            netIncome: number
        } = {
            sales: items.value[items.value.length - 1].sales,
            costOfSales: items.value[items.value.length - 1].costOfSales,
            grossProfit: items.value[items.value.length - 1].grossProfit,
            operatingExpenses: items.value[items.value.length - 1].operatingExpenses,
            netIncome: items.value[items.value.length - 1].netIncome,
        }
        // 更新图表数据
        option.series[0].data.push(currentMonthData.sales)
        option.series[1].data.push(currentMonthData.costOfSales)
        option.series[2].data.push(currentMonthData.grossProfit)
        option.series[3].data.push(currentMonthData.operatingExpenses)
        option.series[4].data.push(currentMonthData.netIncome)
        // 更新图表数据
        myChart.setOption(option)
        loading.value = false
    }

    // // 获取 series 的数据
    // const salesData = items.value.map(item => item.sales)
    // const costOfSalesData = items.value.map(item => item.costOfSales)
    // const grossProfitData = items.value.map(item => item.grossProfit)
    // const operatingExpensesData = items.value.map(item => item.operatingExpenses)
    // const netIncomeData = items.value.map(item => item.netIncome)

    window.addEventListener(
        'resize',
        throttle(() => {
            myChart.resize()
        }, 500),
    )
}
// const init = async () => {
//     // 获取 x 轴的月份
//     const xAxisData = months.map(month => `${currentYear}-${month}`)
//     const doc: any = document.getElementById('container')
//     doc.removeAttribute('_echarts_instance_')
//     const myChart = echarts.init(doc)

//     const option = {
//         title: {
//             text: '',
//         },
//         tooltip: {
//             trigger: 'axis',
//         },
//         legend: {
//             data: ['Sales', 'Cost of Sales', 'Gross Profit', 'Operating Expenses', 'Net Income'],
//         },
//         grid: {
//             left: '3%',
//             right: '4%',
//             bottom: '3%',
//             containLabel: true,
//         },
//         toolbox: {
//             feature: {
//                 // saveAsImage: {},
//             },
//         },
//         xAxis: {
//             type: 'category',
//             boundaryGap: false,
//             data: xAxisData,
//         },
//         yAxis: {
//             type: 'value',
//         },
//         series: [
//             {
//                 name: 'Sales',
//                 type: 'line',
//                 stack: 'Total',
//                 data: [] as number[],
//             },
//             {
//                 name: 'Cost of Sales',
//                 type: 'line',
//                 stack: 'Total',
//                 data: [] as number[],
//             },
//             {
//                 name: 'Gross Profit',
//                 type: 'line',
//                 stack: 'Total',
//                 data: [] as number[],
//             },
//             {
//                 name: 'Operating Expenses',
//                 type: 'line',
//                 stack: 'Total',
//                 data: [] as number[],
//             },
//             {
//                 name: 'Net Income',
//                 type: 'line',
//                 stack: 'Total',
//                 data: [] as number[],
//             },
//         ],
//     }

//     myChart.setOption(option)

//     await fetchDashBoardData(searchForm)
//     // store.dispatch('ReportStore/getDashBoardData', searchForm)
//     const initialData = {...store.state.ReportStore.dashBoard}
//     console.log('initialData', initialData)
//     // 获取初始值
//     state.initialSales = Number(parseFloat(initialData['Sales']).toFixed(2))
//     state.initialCostOfSales = Number(parseFloat(initialData['Cost of Sales']).toFixed(2))
//     state.initialGrossProfit = Number(parseFloat(initialData['Gross Profit']).toFixed(2))
//     state.initialOperatingExpenses = Number(parseFloat(initialData['Operating Expenses']).toFixed(2))
//     state.initialNetIncome = Number(parseFloat(initialData['Net Income']).toFixed(2))
//     // 清空原始数据
//     items.value = []
//     const searchForms = [];
//     for (const month of months) {
//         loading.value = true
//         // 设置搜索表单的日期范围
//         const newSearchForm = {
//             ...searchForm,
//             start_date: `${currentYear}-${month}-01`,
//             end_date: `${currentYear}-${month}-${new Date(currentYear, parseInt(month), 0).getDate()}`,
//             company_code: userCompany[0].code,
//         };
//         searchForms.push(newSearchForm);
//     }
//     console.log('=========================searchForms',searchForms)

//     const promises = [
//         fetchDashBoardData1(searchForms[0]),
//         fetchDashBoardData2(searchForms[1]),
//         fetchDashBoardData3(searchForms[2]),
//         fetchDashBoardData4(searchForms[3]),
//         fetchDashBoardData5(searchForms[4]),
//         fetchDashBoardData6(searchForms[5]),
//         fetchDashBoardData7(searchForms[6]),
//         fetchDashBoardData8(searchForms[7]),
//         fetchDashBoardData9(searchForms[8]),
//         fetchDashBoardData10(searchForms[9]),
//         fetchDashBoardData11(searchForms[10]),
//         fetchDashBoardData12(searchForms[11]),
//     ];

//     await Promise.all(promises);
//         // await fetchDashBoardData(searchForm)
//     for (const month of months) {
//         console.log('============month',month)
//         // 获取该月数据
//         // const monthData = {...store.state.ReportStore.dashBoard01}
//         const monthData = { ...store.state.ReportStore[`dashBoard${month}`] };
//         console.log('monthData', monthData)
//         // 保存数据，并保留两位小数
//         items.value.push({
//             date: `${currentYear}-${month}`,
//             sales: Number(parseFloat(monthData['Sales']).toFixed(2)),
//             costOfSales: Number(parseFloat(monthData['Cost of Sales']).toFixed(2)),
//             grossProfit: Number(parseFloat(monthData['Gross Profit']).toFixed(2)),
//             operatingExpenses: Number(parseFloat(monthData['Operating Expenses']).toFixed(2)),
//             netIncome: Number(parseFloat(monthData['Net Income']).toFixed(2)),
//         })
//         // 获取当前月的数据
//         const currentMonthData: {
//             sales: number
//             costOfSales: number
//             grossProfit: number
//             operatingExpenses: number
//             netIncome: number
//         } = {
//             sales: items.value[items.value.length - 1].sales,
//             costOfSales: items.value[items.value.length - 1].costOfSales,
//             grossProfit: items.value[items.value.length - 1].grossProfit,
//             operatingExpenses: items.value[items.value.length - 1].operatingExpenses,
//             netIncome: items.value[items.value.length - 1].netIncome,
//         }
//         // 更新图表数据
//         option.series[0].data.push(currentMonthData.sales)
//         option.series[1].data.push(currentMonthData.costOfSales)
//         option.series[2].data.push(currentMonthData.grossProfit)
//         option.series[3].data.push(currentMonthData.operatingExpenses)
//         option.series[4].data.push(currentMonthData.netIncome)
//         // 更新图表数据
//         myChart.setOption(option)
//         loading.value = false
    
//     }
//     // // 获取 series 的数据
//     // const salesData = items.value.map(item => item.sales)
//     // const costOfSalesData = items.value.map(item => item.costOfSales)
//     // const grossProfitData = items.value.map(item => item.grossProfit)
//     // const operatingExpensesData = items.value.map(item => item.operatingExpenses)
//     // const netIncomeData = items.value.map(item => item.netIncome)

//     window.addEventListener(
//         'resize',
//         throttle(() => {
//             myChart.resize()
//         }, 500),
//     )
// }
const getLoadingPercent = () => {
    const totalMonths = months.length
    const completedMonths = items.value.length
    return Math.floor((completedMonths / totalMonths) * 100)
}
</script>
<template>
    <div class="flex main-contain">
        <div class="flex top-box">
            <div class="flex top-left">
                <div class="logo-div">
                    <div class="header-title">Dashboard</div>
                    <div class="header-subtitle">{{ i18n.t('reports.updatedOn') }}: {{ todayTitle }}</div>
                </div>
                <div class="vertical-line"></div>
                <!-- <div class="date-interval">
                    <div>Date Interval</div>
                    <div>2020/8/31 2023/2/28</div>
                </div> -->
            </div>
            <div class="top-right">
                Date Interval {{ new Date().getFullYear() }}/1/1 - {{ new Date().getFullYear() }}/12/31
            </div>
        </div>
        <div class="middle-line">
            <hr />
        </div>
        <a-progress :percent="getLoadingPercent()" v-if="loading" showInfo="{false}"></a-progress>
        <div class="flex middle-box">
            <div class="flex middle-box-item">
                <div class="title">Sales</div>
                <div class="flex num">
                    <span class="green">{{ state.initialSales }}</span>
                </div>
            </div>
            <div class="flex middle-box-item">
                <div class="title">Cost of Sales</div>
                <div class="flex num">
                    <span class="red">{{ state.initialCostOfSales }}</span>
                </div>
            </div>
            <div class="flex middle-box-item">
                <div class="title">Gross Profit</div>
                <div class="flex num">
                    <span class="green">{{ state.initialGrossProfit }}</span>
                </div>
            </div>
            <div class="flex middle-box-item">
                <div class="title">Operating Expenses</div>
                <div class="flex num">
                    <span class="red">{{ state.initialOperatingExpenses }}</span>
                </div>
            </div>
            <div class="flex middle-box-item">
                <div class="title">Net Income</div>
                <div class="flex num">
                    <span class="green">{{ state.initialNetIncome }}</span>
                </div>
            </div>
        </div>
        <div class="flex bottom-box">
            <div class="flex bottom-box-left">
                <div class="title">Sales and Cost</div>
                <div class="echars-content">
                    <div id="container" style="height: 100%"></div>
                </div>
            </div>
            <div class="flex bottom-box-right">
                <div class="title">Profit and Net Incoome</div>
                <div class="flex table-contain">
                    <!-- <n-data-table :columns="columns" :data="items" :bordered="false" /> -->
                    <a-table
                        :dataSource="items"
                        :pagination="false"
                        :bordered="false"
                        style="align: center; width: 100%"
                    >
                        <a-table-column align="center" title="Year Month" data-index="date" />
                        <a-table-column align="center" title="Gross Profit" data-index="grossProfit" />
                        <a-table-column align="center" title="Net InCome" data-index="netIncome" />
                    </a-table>
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.header-title {
    line-height: 26px;
    height: 26px;
    color: #004fc1;
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0;
    text-align: left; /* 左对齐 */
}
.header-subtitle {
    font-size: 12px;
    color: #262626;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: 400;
    text-align: left; /* 左对齐 */
}
div {
    border: 0px solid;
}

.flex {
    display: flex;
}

:deep(.ant-table-thead > tr > th) {
    background: #fff !important;
}

.main-contain {
    background-color: #fff;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding-left: 10px;
    padding-right: 40px;

    .title {
        padding-top: 5px;
        align-items: center;
        font-weight: 700;
        font-size: 18px;
    }

    .top-box {
        justify-content: space-between;
        height: 120px;

        .top-left {
            .logo-div {
                text-align: center;
                margin: 30px 30px 0 30px;

                .Dashboard {
                    font-family: 'DM Sans', sans-serif;
                    font-size: 35px;
                    color: rgb(43, 44, 44);
                }
            }

            .vertical-line {
                margin: 25px 0 0 0;
                border-left: 1px solid #aaa;
                height: 60px;
            }

            .date-interval {
                font-size: 13px;
                margin-left: 35px;
                margin-top: 45px;

                .border {
                    border: 1px solid #999;
                }
            }
        }

        .top-right {
            margin-top: 70px;
            margin-right: 10px;
            font-size: 12px;
            color: #262626;
        }
    }

    .middle-line {
        padding: 0 10px 5px 10px;
        color: #999;
        width: 100%;
    }

    .middle-box {
        margin: 5px;
        height: 30%;

        .middle-box-item {
            flex-direction: column;
            align-items: center;
            box-shadow: 2px 2px 10px #999;
            background-color: #fff;
            margin: 5px;
            width: 20%;
            border-radius: 12px;
            background-image: url(../../../assets/image/dashboard/bg.png);

            .num {
                padding-bottom: 20px;
                height: 100%;
                justify-content: center;
                align-items: center;

                .green {
                    font-size: 30px;
                    color: rgb(102, 160, 136);
                }

                .red {
                    font-size: 30px;
                    color: rgb(205, 74, 63);
                }
            }
        }
    }

    .bottom-box {
        height: 100%;
        padding: 0 5px 0 5px;

        .bottom-box-left {
            flex-direction: column;
            align-items: center;
            box-shadow: 2px 2px 10px #999;
            background-color: #fff;
            border-radius: 12px;
            margin: 5px;
            width: 70%;

            .echars-content {
                height: 100%;
                width: 100%;
            }
        }

        .bottom-box-right {
            flex-direction: column;
            align-items: center;
            box-shadow: 2px 2px 10px #999;
            background-color: #fff;
            border-radius: 12px;
            margin: 5px;
            width: 35%;

            .table-contain {
                width: 100%;
            }
        }
    }
}
</style>
