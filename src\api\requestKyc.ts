/** @format */

import axios from 'axios'
import router from '../router'
import {message} from 'ant-design-vue'

// create an axios instance
const servicekyc = axios.create({
    baseURL: import.meta.env.VITE_BASIC_API_KYC, // url = base url + request url
    withCredentials: true, // send cookies when cross-domain requests
    timeout: 90000, // request timeout
})

servicekyc.interceptors.response.use(
    response => {
        if (String(response.data.code) !== '0') {
            if (response?.config?.url?.includes('productMessage')) {
                console.error(`KYC - ${response.data.msg}`)
            } else {
                message.error({
                    content: `KYC - ${response.data.msg}`,
                    duration: 5,
                })
            }
        }
        return response
    },
    error => {
        if (error.code === 'ECONNABORTED' || error.message === 'Network Error' || error.message.includes('timeout')) {
            message.error({
                content: 'Time Out Error.',
                duration: 6,
            })
        } else if (error?.response?.status === 401) {
            router.replace({name: 'Login'})
        } else {
            message.error({
                content: error.response.data.message || 'failed',
                duration: 5,
            })
        }
        return Promise.reject(error)
    },
)

export default servicekyc
