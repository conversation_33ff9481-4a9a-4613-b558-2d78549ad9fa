<!-- @format -->

<script lang="ts" setup>
import {RouterView} from 'vue-router'
import {computed, onMounted, watch} from 'vue'
import {useStore} from 'vuex'
import {BookkeepingMenu} from '@/lib/storage'
import {TaxData} from '@/assets/mock-data/mockData' // 临时添加 创建公司及账号页面。 删除 flag = 'company-account'
import {useRouter} from 'vue-router'

const router = useRouter()
const store = useStore()
const updateMenuList = (data: any) => store.commit('updateMenuList', data, {root: true})

const disableScroll = computed(() => store.state.disableScroll)

onMounted(() => {
    const currentMenuList: any = BookkeepingMenu.get()
    if (currentMenuList && currentMenuList.length > 0) {
        // 临时添加 创建公司及账号页面。 删除 flag = 'company-account'
        // currentMenuList[currentMenuList.length - 1].childMenus.push(TaxData[0])
        // currentMenuList[currentMenuList.length - 1].childMenus.push(TaxData[1])

        updateMenuList(currentMenuList)
    }
})

watch(
    () => router.currentRoute,
    currentRoute => {
        const menus = BookkeepingMenu.get()
        if (menus) {
            updateMenuList(menus)
        }
    },
    {immediate: true, deep: true},
)
</script>

<template>
    <div class="common-content" :class="disableScroll ? 'disabled' : ''">
        <RouterView />
    </div>
</template>

<style lang="scss" scoped>
.common-content {
    height: 100%;
    min-width: 1100px;
    overflow: auto;
    &.disabled {
        overflow: hidden;
    }
}
</style>
