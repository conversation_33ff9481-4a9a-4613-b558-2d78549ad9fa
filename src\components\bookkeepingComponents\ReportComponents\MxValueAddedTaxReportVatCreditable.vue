<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount, watch} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {numberToCurrency} from '@/lib/utils'
import {ExportOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'

import * as _ from 'lodash'
import moment from 'moment'
import type {Dayjs} from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'

// 定义props，接收从父组件传递的data和loading状态
const props = defineProps<{
    data: any
    loading?: boolean
}>()

const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global

const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

const today = ref(moment().format('YYYY-MM-DD'))
const tableLoading = ref(false)

const searchForm = reactive<{[key: string]: string | undefined}>({
    end_date: today.value,
    report_type: 'AR',
    company_code: undefined,
    company_name: undefined,
})

const fetchMxAddedTaxList = (payload: any) => store.dispatch('ReportStore/getMxValueAddedTaxReport', payload)

const disabledDate = (current: Dayjs) => {
    return current && current > moment().endOf('day')
}

// const tableScrollY = computed(() => {
//     return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight - headerHeight * total.value.length
// })

const selectChange = async () => {
    await updateList()
}

const updateList = async () => {
    // try {
    //     tableLoading.value = true
    //     await fetchMxAddedTaxList(searchForm)
    //     tableLoading.value = false
    // } catch (error) {
    //     console.log(error)
    //     tableLoading.value = false
    // }
}

// const i18n = {
//     t: (key: string) => key, // Mock translation function
// }

// 使用传入的props.data填充字段值
const fields = computed(() => {
    // 默认字段结构
    const defaultFields = [
        {
            label: i18n.t('reports.amountOfPaidEventsOrActivities'),
            value: 0,
            required: false,
            inputRequired: false,
            disabled: true,
            labelClass: 'bold-label', // 默认样式
            key: 'amountOfPaidEventsOrActivities', // 对应data中的键
        },
        {
            label: i18n.t('reports.totalActsPaid16Percent'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 默认样式
            key: 'totalActsPaid16Percent', // 对应data中的键
        },
        {
            label: i18n.t('reports.totalActsPaidImport16Percent'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 默认样式
            key: 'totalActsPaidImport16Percent', // 对应data中的键
        },
        {
            label: i18n.t('reports.totalActsPaid0Percent'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 加粗样式
            key: 'totalActsPaid0Percent', // 对应data中的键
        },
        {
            label: i18n.t('reports.totalPaidActsExempt'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 加粗样式
            key: 'totalPaidActsExempt', // 对应data中的键
        },
        {
            label: i18n.t('reports.determinationCreditableVAT'),
            value: 0,
            required: false,
            inputRequired: false,
            disabled: true,
            labelClass: 'bold-label', // 默认样式
            key: 'determinationCreditableVAT', // 对应data中的键
        },
        {
            label: i18n.t('reports.vatOnActsPaid16Percent'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 默认样式
            key: 'vatOnActsPaid16Percent', // 对应data中的键
        },
        {
            label: i18n.t('reports.vatOnImportPaid16Percent'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 默认样式
            key: 'vatOnImportPaid16Percent', // 对应data中的键
        },
        {
            label: i18n.t('reports.totalVATTransferred'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'bold-label', // 加粗样式
            key: 'totalVATTransferred', // 对应data中的键
        },
        {
            label: i18n.t('reports.vatCreditable'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 加粗样式
            key: 'vatCreditable', // 对应data中的键
        },
        {
            label: i18n.t('reports.updatedCreditableAmount'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'form-label', // 加粗样式
            key: 'updatedCreditableAmount', // 对应data中的键
        },
        {
            label: i18n.t('reports.totalCreditableVAT'),
            value: 0,
            required: true,
            inputRequired: true,
            disabled: true,
            labelClass: 'bold-label', // 加粗样式
            key: 'totalCreditableVAT', // 对应data中的键
        },
    ]

    // 如果有传入数据，则更新字段值
    if (props.data && typeof props.data === 'object') {
        // 遍历字段，查找对应的数据
        defaultFields.forEach(field => {
            if (field.key && props.data[field.key] !== undefined) {
                field.value = props.data[field.key]
            }
        })

        // 计算总和字段
        // defaultFields[0].value = defaultFields.slice(1, 5).reduce((sum, field) => sum + (field.value || 0), 0)
        // defaultFields[8].value = defaultFields.slice(6, 8).reduce((sum, field) => sum + (field.value || 0), 0)
        // defaultFields[11].value = defaultFields.slice(9, 11).reduce((sum, field) => sum + (field.value || 0), 0)
    }

    return defaultFields
})

// 这些计算属性已经在fields计算属性中处理，不再需要单独定义

const currencyFormatter = (value: number) =>
    new Intl.NumberFormat('en-US', {
        style: 'decimal',
        minimumFractionDigits: 2, // 保留两位小数
        maximumFractionDigits: 2, // 保留两位小数
    }).format(value)

const currencyParser = (value: string) => parseFloat(value.replace(/[^0-9.-]+/g, ''))

// 移除未使用的函数

// 模态框的显示状态
const isModalVisible = ref(false)
const modalTitle = ref('') // 定义模态框标题
// 当前模态框显示的数据
const currentModalData = ref<Array<{label: string; value: number; required: boolean}>>([])

// 模态框表单数据
const modalForm = reactive({
    montoPorDetallar: 0,
    interesesCobrados: 0,
    regalias: 0,
    otrosActos: 500000,
})
const modalData = computed(() => [
    {
        key: 1,
        value: [
            {
                label: i18n.t('reports.amtDetail1'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.intPaid16'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.royaltyRel16'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.otherPaid16'),
                value: props.data?.totalActsPaid16Percent || 0,
                required: true,
            },
        ],
    },
    {
        key: 2,
        value: [
            {
                label: i18n.t('reports.amtDetail2'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.impInt16'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.impIntTmp16'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.impServ16'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.impOthPaid16'),
                value: props.data?.totalActsPaidImport16Percent || 0,
                required: true,
            },
        ],
    },
    {
        key: 4,
        value: [
            {
                label: i18n.t('reports.amtDetail3'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.landBuy'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.bookBuy'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.royaltyAuth'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.usedBuy'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.transSvc'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.medSvc'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.agrInsur'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.farmUse'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.impExm'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.othExm'),
                value: props.data?.totalPaidActsExempt || 0,
                required: true,
            },
        ],
    },
    {
        key: 8,
        value: [
            {
                label: i18n.t('reports.vatCredTot'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatPaidTot'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatTrf'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatTrfInv'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatImp'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatImpInv'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.teachingServices'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatTaxTot'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatCredNo'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatImpNo'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatInvNo'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatMixed'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatSel'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatRatio'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatCredMix'),
                value: 0,
                required: true,
            },
            {
                label: i18n.t('reports.vatCred'),
                value: props.data?.totalVATTransferred || 0,
                required: true,
            },
        ],
    },
])

// 点击按钮的处理函数
const handleButtonClick = (index: number) => {
    const matchedData = modalData.value.find(item => item.key === index)
    if (matchedData) {
        modalTitle.value = fields.value[index].label // 设置模态框标题
        currentModalData.value = JSON.parse(JSON.stringify(matchedData.value)) // 深拷贝数据
        isModalVisible.value = true // 显示模态框
    }
}

// 模态框的确认按钮处理函数
const handleModalOk = () => {
    console.log('Modal data:', modalForm)
    isModalVisible.value = false // 关闭模态框
}

onBeforeMount(async () => {
    // searchForm.company_name = userCompany[0].name
    // searchForm.company_code = userCompany[0].code

    // await updateList()
})
</script>
<template>
    <div class="tab1-container">
        <!-- 添加加载状态的显示 -->
        <a-spin :spinning="props.loading">
            <a-form layout="vertical">
                <div class="form-row" v-for="(field, index) in fields" :key="index">
                    <label :class="field.labelClass">
                        <span
                            v-if="field.required"
                            class="required-star"
                            :style="{color: field.labelClass === 'bold-label' ? '#000' : '#8c8c8c'}"
                        >
                            *
                        </span>
                        {{ field.label }}
                    </label>
                    <!-- 包裹符号和输入框，确保对齐 -->
                    <div class="input-wrapper" v-if="field.inputRequired">
                        <span v-if="index === 10" class="additional-label">(+)</span>
                        <span v-if="index === 11" class="additional-label">(=)</span>
                        <a-input-number
                            v-model="field.value"
                            :disabled="field.disabled"
                            :formatter="currencyFormatter"
                            :parser="currencyParser"
                            :value="field.value"
                            class="form-input"
                        />
                    </div>
                    <a-button
                        v-if="index === 1 || index === 2 || index === 4 || index === 8"
                        type="primary"
                        shape="round"
                        @click="handleButtonClick(index)"
                        class="capture-button"
                        :disabled="props.loading"
                    >
                        {{ i18n.t('reports.btCapture') }}
                    </a-button>
                </div>
            </a-form>
        </a-spin>

        <a-modal v-model:visible="isModalVisible" width="50%" class="modal-capture">
            <div class="modal-header-background">
                <div class="modal-title">
                    {{ modalTitle }}
                </div>
            </div>
            <a-form layout="vertical" class="modal-body">
                <div class="form-row popup" v-for="(item, idx) in currentModalData" :key="idx" :label="item.label">
                    <label class="form-label">
                        <span v-if="item.required" class="required-star"> * </span>
                        {{ item.label }}
                    </label>
                    <div class="input-wrapper">
                        <a-input-number
                            v-model="item.value"
                            disabled="true"
                            :formatter="currencyFormatter"
                            :parser="currencyParser"
                            :value="item.value"
                            class="form-input"
                        />
                    </div>
                </div>
            </a-form>
            <template #footer>
                <a-button type="primary" @click="handleModalOk">OK</a-button>
            </template>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.tab1-container {
    padding: 20px;
}

.form-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px; /* 将行间距从 16px 缩小到 8px */
}

.form-label {
    flex: 0 0 550px; /* Label 的宽度 */
    margin-right: 16px; /* Label 和输入框之间的间隔 */
    text-align: left; /* 确保左对齐 */
    color: #8c8c8c; /* 默认灰色字体 */
    font-weight: normal; /* 默认不加粗 */
}

.bold-label {
    flex: 0 0 550px; /* Label 的宽度 */
    margin-right: 16px; /* Label 和输入框之间的间隔 */
    text-align: left; /* 确保左对齐 */
    color: #000; /* 黑色字体 */
    font-weight: bold; /* 加粗 */
}

.required-star {
    margin-right: 4px; /* 星号和文字之间的间隔 */
    font-weight: bold;
}

.input-wrapper {
    display: flex;
    align-items: center;
    flex: 0 1 auto; /* 限制宽度为内容宽度 */
    min-width: 150px; /* 确保输入框左对齐 */
    position: relative; /* 确保符号和输入框在同一行 */
    margin-right: 40px; /* 缩小输入框与按钮之间的间隔 */
}

.additional-label {
    color: #000; /* 黑色字体 */
    font-weight: bold;
    margin-right: 0px; /* 符号和输入框之间的间隔 */
    flex-shrink: 0; /* 确保符号不会挤压输入框 */
    position: absolute;
    left: 0; /* 符号始终对齐到左侧 */
    transform: translateX(-30px); /* 调整符号的位置 */
}

.form-input {
    width: 200px; /* 固定输入框宽度 */
    text-align: right; /* 确保输入框内容右对齐 */
    flex-shrink: 0; /* 确保输入框不会被压缩 */
}

::v-deep(.ant-input-number-input) {
    width: 200px; /* 固定输入框宽度 */
    text-align: right; /* 确保输入框内容右对齐 */
}

.capture-button {
    display: flex;
    min-width: 150px;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5; /* 按钮背景色 */
    border: 1px solid #d9d9d9; /* 按钮边框颜色 */
    color: #595959; /* 按钮文字颜色 */
    padding: 4px 12px; /* 按钮内边距 */
    font-size: 14px; /* 按钮字体大小 */
    height: 32px; /* 按钮高度 */
    line-height: 1; /* 按钮行高 */
    margin-left: 4px; /* 缩小按钮与输入框的间隔 */
    border-radius: 5px; /* 按钮圆角 */
    transition: all 0.3s; /* 添加过渡效果 */
}

.capture-button:hover {
    background-color: #e6f7ff; /* 鼠标悬停时的背景色 */
    border-color: #91d5ff; /* 鼠标悬停时的边框颜色 */
    color: #1890ff; /* 鼠标悬停时的文字颜色 */
}

// Modal Style start
.modal-header-background {
    position: absolute; /* 绝对定位，覆盖模态框顶部 */
    top: 0;
    left: 0;
    width: 100%; /* 覆盖模态框的宽度 */
    height: 50px;
    background-color: #a41e34; /* 深红色背景 */
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 24px;
    z-index: 1; /* 确保在内容区域之上 */
}

.modal-title {
    color: #fff; /* 白色文字 */
    font-size: 16px; /* 字体大小 */
    font-weight: bold; /* 加粗 */
    text-align: center; /* 居中对齐 */
    margin: 0; /* 去除外边距 */
}

.modal-body {
    margin-top: 50px; /* 增加顶部内边距，确保内容从顶部区域之后开始 */
    .form-row.popup {
        width: 100%;
        justify-content: space-between;
        > label {
            max-width: calc(100% - 218px);
        }
        > .input-wrapper {
            margin-right: 0; /* 缩小输入框与按钮之间的间隔 */
        }
    }
}
// Modal Style EndEnd
</style>
