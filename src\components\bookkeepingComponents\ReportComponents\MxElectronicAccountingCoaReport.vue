<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {DeleteOutlined, SyncOutlined, DownloadOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'
import {message, Modal} from 'ant-design-vue'

// import * as _ from 'lodash' // 未使用，已注释
import moment from 'moment'
import SvgIcon from '@/components/SvgIcon.vue'
import * as FileSaver from 'file-saver'

const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global

const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

// const today = ref(moment().format('YYYY-MM-DD')) // 未使用，已注释
const tableLoading = ref(false)
// 添加fetch按钮的禁用状态变量
const fetchButtonDisabled = ref(false)
// 添加下载按钮的禁用状态变量
const downloadButtonDisabled = computed(() => {
    return !reportFile.value || !reportFile.value.file_name || !reportFile.value.file_content
})
const dataSource = computed(() => {
    // 获取原始数据
    const originalData = store.state.ReportStore.mxElectronicAccountingCoaReport || []

    // 按照gl_account字段从小到大排序
    const sortedData = [...originalData].sort((a, b) => {
        // 确保gl_account是字符串，并进行比较
        const accountA = String(a.gl_account || '')
        const accountB = String(b.gl_account || '')
        return accountA.localeCompare(accountB)
    })

    // 重新为seq_no字段赋值，从1开始递增
    return sortedData.map((item, index) => {
        // 获取gl_account字段的值，确保它是字符串类型
        const glAccount = String(item.gl_account || '')

        // 设置nature_of_account字段的值
        let natureOfAccount = ''

        // 如果gl_account字段是2，4，60，61，62，63开头的，设置为A
        if (
            glAccount.startsWith('2') ||
            glAccount.startsWith('4') ||
            glAccount.startsWith('60') ||
            glAccount.startsWith('61') ||
            glAccount.startsWith('62') ||
            glAccount.startsWith('63')
        ) {
            natureOfAccount = 'A'
        }
        // 如果gl_account字段是1，5，7，64，或者大于64开头的，设置为D
        else if (
            glAccount.startsWith('1') ||
            glAccount.startsWith('5') ||
            glAccount.startsWith('7') ||
            glAccount.startsWith('64') ||
            parseInt(glAccount.substring(0, 2)) > 64
        ) {
            natureOfAccount = 'D'
        }

        return {
            ...item,
            seq_no: index + 1,
            level: 3,
            nature_of_account: natureOfAccount,
        }
    })
})
// We don't need a total for this report
const total = computed(() => [])

const searchForm = reactive<{[key: string]: string | undefined}>({
    report_period: moment().format('YYYY-MM'),
    company_code: undefined,
})

// 定义payload类型
interface ReportPayload {
    params: {
        company_code?: string
        report_period?: string
    }
}

// 定义报表文件请求类型
interface ReportFilePayload {
    params: {
        company_code?: string
        period?: string
        file_type?: string
    }
}

// 定义报表文件类型
interface ReportFileData {
    file_name?: string
    file_content?: string
    [key: string]: any
}

const reportFile = ref<ReportFileData>({})

const fetchElectronicAccountingCoaList = (payload: ReportPayload) =>
    store.dispatch('ReportStore/getMxElectronicAccountingCoaReport', payload)
const deleteElectronicAccountingCoaList = (payload: ReportPayload) =>
    store.dispatch('ReportStore/deleteMxElectronicAccountingCoaReport', payload)
const triggerElectronicAccountingCoaList = (payload: ReportPayload) =>
    store.dispatch('ReportStore/triggerMxElectronicAccountingCoaReport', payload)
const fetchMxReportFile = (payload: ReportFilePayload) => store.dispatch('ReportStore/getMxReportFile', payload)

const tableScrollY = computed(() => {
    // Add extra padding (50px) at the bottom to ensure the last row is fully visible
    return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight - headerHeight * total.value.length - 50
})

const totalNumber = computed(() => dataSource.value.length)
const tableDataSource = computed(() => {
    const start = (pageQuery.page_index - 1) * pageQuery.page_size
    const end = start + pageQuery.page_size
    return dataSource.value.slice(start, end)
})
const pageQuery = reactive({
    page_index: 1,
    page_size: 10,
    sortField: null,
    sortDirection: 0,
})
// This function is called when the pagination changes
const changePage = (page: number, pageSize: number) => {
    pageQuery.page_index = page
    pageQuery.page_size = pageSize
    // No need to reload data as we're using client-side pagination with tableDataSource
}

const columns: ColumnsType = [
    {
        title: i18n.t('reports.mxColumnSeqNo'),
        dataIndex: 'seq_no',
        key: 'seq_no',
        align: 'center',
        width: 80,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxColumnGlAccountDetails'),
        dataIndex: 'gl_account_details',
        key: 'gl_account_details',
        align: 'center',
        width: 100,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxColumnSatCode'),
        dataIndex: 'sat_code',
        key: 'sat_code',
        align: 'center',
        width: 100,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxColumnGlAccount'),
        dataIndex: 'gl_account',
        key: 'gl_account',
        align: 'center',
        width: 100,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxColumnGlAccountDescription'),
        dataIndex: 'gl_account_description',
        key: 'gl_account_description',
        align: 'center',
        width: 120,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxColumnLevel'),
        dataIndex: 'level',
        key: 'level',
        align: 'center',
        width: 80,
        ellipsis: false,
    },
    {
        title: i18n.t('reports.mxColumnNatureOfAccount'),
        dataIndex: 'nature_of_account',
        key: 'nature_of_account',
        align: 'center',
        width: 80,
        ellipsis: false,
    },
]
// Export report function - uncomment the button in the template to use this
// 注释掉未使用的函数，避免IDE警告
// const exportReport = async () => {
//     try {
//         tableLoading.value = true
//         await Promise.all([
//             store.dispatch('ReportStore/getMxElectronicAccountingCoaReport', {
//                 params: {
//                     company_code: userCompany[0].code,
//                     report_period: searchForm.report_period, // Already in YYYY-MM format
//                 },
//             }),
//         ])
//     } catch (err) {
//         console.log(err)
//     } finally {
//         tableLoading.value = false
//     }
// }

const selectChange = async (date: string) => {
    if (date) {
        searchForm.report_period = date
    }
    // 调用更新列表函数，并根据返回的数据更新fetch按钮状态
    await updateList()
}

const updateList = async () => {
    try {
        tableLoading.value = true

        await fetchElectronicAccountingCoaList({
            params: {
                company_code: searchForm.company_code,
                report_period: searchForm.report_period,
            },
        })

        // 根据返回的数据更新fetch按钮状态
        // 如果有数据，则禁用fetch按钮；如果没有数据，则启用fetch按钮
        const hasData = store.state.ReportStore.mxElectronicAccountingCoaReport?.length > 0
        fetchButtonDisabled.value = hasData
        // fetch report file
        const reportFileResponse = await fetchMxReportFile({
            params: {
                company_code: searchForm.company_code,
                period: searchForm.report_period,
                file_type: 'COA',
            },
        })
        if (reportFileResponse && reportFileResponse.code === 200) {
            reportFile.value = reportFileResponse.data
        }

        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false

        // 如果出错，也要更新fetch按钮状态
        // 默认启用fetch按钮，允许用户重试
        fetchButtonDisabled.value = false
    }
}

// 删除按钮的处理函数
const handleDelete = () => {
    // 使用Modal.confirm代替message.confirm
    const modal = Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: i18n.t('bkCommonTag.msgDeleteConfirm'),
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
        onOk: () => {
            // 立即关闭对话框
            modal.destroy()

            // 异步执行删除操作
            ;(async () => {
                try {
                    tableLoading.value = true
                    // 调用删除数据的API
                    const response = await deleteElectronicAccountingCoaList({
                        params: {
                            company_code: searchForm.company_code,
                            report_period: searchForm.report_period,
                        },
                    })

                    // 检查删除是否成功
                    if (response && response.data && response.data.code === 200) {
                        message.success(i18n.t('ApComponents.success'))
                        updateList()
                    } else {
                        message.error(response?.data?.message || 'Failed to delete')
                    }
                } catch (error) {
                    console.error(error)
                    message.error('Failed to delete')
                } finally {
                    tableLoading.value = false
                }
            })()
        },
    })
}

// 获取按钮的处理函数
const handleFetch = async () => {
    try {
        // 立即禁用fetch按钮，不管数据是否返回或是否有数据
        fetchButtonDisabled.value = true

        // 设置加载状态
        tableLoading.value = true
        // message.loading(i18n.t('common.fetching') || 'Fetching...', 0)

        // 调用触发PGO的API
        const response = await triggerElectronicAccountingCoaList({
            params: {
                company_code: searchForm.company_code,
                report_period: searchForm.report_period,
            },
        })

        // message.destroy() // 关闭加载提示

        // 检查响应
        if (response && response.data) {
            if (response.data.code === 200) {
                // 显示成功消息，使用API返回的message
                message.success(i18n.t('ApComponents.success'))

                // 刷新数据列表以显示最新数据
                // await updateList()
            } else {
                // 显示错误消息，使用API返回的message
                message.error(response.data.message)

                // 如果触发失败，重新启用fetch按钮
                fetchButtonDisabled.value = false
            }
        }
    } catch (err: unknown) {
        console.error(err)
        message.destroy() // 关闭加载提示

        // 显示错误消息
        let errorMessage = i18n.t('common.fetchFailed') || 'Failed to fetch data'

        // 尝试从错误对象中提取消息
        if (err && typeof err === 'object') {
            // 定义一个接口来描述可能的错误对象结构
            interface ErrorWithResponse {
                response?: {
                    data?: {
                        message?: string
                    }
                }
            }

            // 类型断言为我们定义的接口
            const error = err as ErrorWithResponse
            if (error.response?.data?.message) {
                errorMessage = error.response.data.message
            }
        }

        message.error(errorMessage)

        // 如果出错，重新启用fetch按钮
        fetchButtonDisabled.value = false
    } finally {
        tableLoading.value = false
    }
}

// 下载按钮的处理函数
const handleDownload = async () => {
    try {
        if (!reportFile.value || !reportFile.value.file_name || !reportFile.value.file_content) {
            message.error(i18n.t('common.noFileAvailable') || 'No file available for download')
            return
        }

        tableLoading.value = true

        // 解码base64内容
        const decodedContent = atob(reportFile.value.file_content)

        // 创建blob
        const blob = new Blob([decodedContent], {
            type: reportFile.value.file_name.toLowerCase().endsWith('.pdf')
                ? 'application/pdf'
                : 'application/octet-stream'
        })

        // 下载文件
        FileSaver.saveAs(blob, reportFile.value.file_name)

        message.success(i18n.t('common.downloadSuccess') || 'File downloaded successfully')
    } catch (error) {
        console.error('Error downloading file:', error)
        message.error(i18n.t('common.downloadFailed') || 'Failed to download file')
    } finally {
        tableLoading.value = false
    }
}

onBeforeMount(async () => {
    searchForm.company_name = userCompany[0].name
    searchForm.company_code = userCompany[0].code

    // 获取数据
    await updateList()

    // 根据是否有数据来初始化fetch按钮的状态
    // 如果已经有数据，则禁用fetch按钮
    fetchButtonDisabled.value = store.state.ReportStore.mxElectronicAccountingCoaReport?.length > 0
})
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="header-title-wrap">
                <div class="header-title">{{ i18n.t('reports.mxElectronicAccountingCoaReport') }}</div>
                <!-- <div class="header-subtitle">{{ i18n.t('reports.updatedOn') }}: {{ today }}</div> -->
            </div>
            <a-divider type="vertical" style="margin-left: 260px" />
            <div class="selects-wrap">
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.report_period"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    picker="month"
                    format="YYYY-MM"
                    valueFormat="YYYY-MM"
                    :placeholder="i18n.t('reports.experationDate')"
                    :disabled="tableLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>

                <!-- 空白占位元素，将按钮推到最右边 -->
                <div class="flex-spacer"></div>

                <!-- 添加删除按钮 - 只有在有数据时才可用 -->
                <a-button
                    type="primary"
                    :disabled="tableLoading || totalNumber === 0"
                    @click="handleDelete"
                    class="action-button"
                >
                    <delete-outlined />
                    {{ i18n.t('commonTag.delete') || 'Delete' }}
                </a-button>

                <!-- 添加获取按钮 - 点击后立即变为不可用 -->
                <a-button
                    type="primary"
                    :disabled="tableLoading || fetchButtonDisabled"
                    @click="handleFetch"
                    class="action-button"
                >
                    <sync-outlined />
                    {{ i18n.t('bkApInvoice.fetch') || 'Fetch' }}
                </a-button>

                <!-- 添加下载按钮 - 只有在有文件时才可用 -->
                <a-button
                    type="primary"
                    :disabled="tableLoading || downloadButtonDisabled"
                    @click="handleDownload"
                    class="action-button"
                >
                    <download-outlined />
                    {{ i18n.t('commonTag.download') || 'Download' }}
                </a-button>
            </div>

            <!-- <a-button type="primary" shape="round" :disabled="tableLoading" @click="exportReport()">
                <export-outlined />
                {{ i18n.t('workTimeManager.export') }}
            </a-button> -->
        </div>
        <div class="page-content" ref="tableElWrapRef">
            <a-table
                :dataSource="tableDataSource"
                :columns="columns"
                :pagination="false"
                :loading="tableLoading"
                :scroll="{y: tableScrollY}"
                class="table-with-bottom-space"
            >
            </a-table>
            <!-- 只有当有数据时才显示分页视图 -->
            <div class="pagination-wrap" v-if="totalNumber > 0">
                <a-pagination
                    v-model:current="pageQuery.page_index"
                    v-model:page-size="pageQuery.page_size"
                    :disabled="false"
                    :hideOnSinglePage="false"
                    :showSizeChanger="true"
                    :total="totalNumber"
                    @change="changePage"
                />
                <span>
                    {{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                    {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}
                </span>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.page-wrap {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: #fff;

    .page-header {
        min-height: 84px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        margin-bottom: 14px;

        .header-title-wrap {
            min-width: 148px;
            max-width: 148px;

            .header-title {
                white-space: nowrap;
                line-height: 26px;
                height: 26px;
                color: #004fc1;
                font-size: 20px;
                font-weight: 700;
                letter-spacing: 0;
            }

            .header-subtitle {
                white-space: nowrap;
                font-size: 12px;
                color: #262626;
                letter-spacing: 0;
                line-height: 18px;
                font-weight: 400;
            }
        }

        .ant-divider {
            border-left-color: #e2e2ea;
            height: 36px;
            top: 0;
            margin: 0 18px;
        }

        .selects-wrap {
            width: 100%;
            display: flex;
            align-items: center;

            .target-currency-select,
            .company-select,
            .coa-account-select {
                min-width: 175px;
                width: 175px;
                max-width: 500px;
                margin-right: 8px;
                flex: 1;
            }

            .data-select {
                min-width: 145px;
                width: 145px;
                max-width: 145px;
                margin-right: 8px;
                flex: 1;
            }

            /* 空白占位元素，将按钮推到最右边 */
            .flex-spacer {
                flex: 1;
            }

            /* 添加按钮样式 */
            .action-button {
                margin-left: 8px;
                height: 32px; /* 与日期选择框高度一致 */
                padding: 0 15px; /* 增加水平内边距 */

                /* 图标和文字的间距 */
                :deep(.anticon) {
                    margin-right: 6px;
                }
            }

            .transaction-currency-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.2;
            }

            .coa-account-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.5;
                margin-right: 0;

                .select-all-item {
                    padding: 5px 12px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                    .ant-checkbox-wrapper {
                        width: 100%;
                    }
                }

                :deep(.ant-select-selector) {
                    overflow: hidden;

                    .ant-select-selection-overflow {
                        flex-wrap: nowrap;
                    }
                }
            }
        }

        .ant-btn-round {
            padding: 6px 10px;
            margin-left: 16px;
        }
    }

    .page-content {
        padding: 0 20px 60px; /* Increased bottom padding to make room for fixed pagination */
        overflow: hidden;
        height: calc(100% - 105px);
        position: relative; /* Added position relative for absolute positioning of pagination */

        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
        }
        :deep(.ant-table-wrapper) {
            padding-bottom: 20px; /* Added padding to the table wrapper */
            /* 动态调整表格高度，当有分页时留出空间，没有分页时占满 */
            height: v-bind('totalNumber > 0 ? "calc(100% - 60px)" : "100%"');
        }

        :deep(.ant-table-body) {
            /* Ensure the table body has enough space at the bottom */
            padding-bottom: 20px;
            /* Add a min-height to ensure there's always space for scrolling */
            min-height: 300px;
        }

        .table-with-bottom-space {
            /* 根据是否有数据动态调整底部边距 */
            margin-bottom: v-bind('totalNumber > 0 ? "50px" : "0"'); /* 有数据时增加边距为分页留出空间 */

            :deep(.ant-table-body) {
                /* 只有在有数据时才添加额外的底部空间 */
                &:after {
                    content: '';
                    display: block;
                    height: v-bind('totalNumber > 0 ? "30px" : "0"'); /* 根据是否有数据调整额外空间的高度 */
                }
            }
        }

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;
            position: absolute; /* Fixed position at the bottom */
            bottom: 20px;
            right: 20px;
            background-color: #fff; /* Ensure it's visible on top of other content */
            padding: 5px 0;
            z-index: 10; /* Ensure it's above other elements */
            width: calc(100% - 40px); /* Full width minus padding */

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }

        // :deep(.ant-table-wrapper .ant-table .ant-table-thead .ant-table-cell) {
        //     text-align: center !important;
        // }
    }

    .table-summary-wrap {
        .table-summary-total {
            text-align: left;
            font-weight: 600;
        }

        .table-summary-text {
            text-align: right;
            &.center {
                text-align: center;
            }
        }
    }

    .page-footer {
        padding: 5px 8px;
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;
            width: 100%;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                min-width: 45px;
            }

            .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
                min-width: 35px;
            }
        }
    }
}
</style>
