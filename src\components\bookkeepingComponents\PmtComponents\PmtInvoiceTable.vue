<!-- @format -->

<template>
    <div class="page-container_ap_table">
        <div :class="props.activeName === 'pending' ? 'table-pending' : 'table-created'">
            <a-table
                :row-selection="rowSelection"
                :dataSource="state.tableData"
                height="100%"
                :loading="isLoading"
                :pagination="false"
            >
                <a-table-column
                    key="file_name"
                    dataIndex="file_name"
                    :title="i18n.t('bkApUpload.fileName')"
                    align="center"
                    header-align="center"
                    width="30%"
                >
                </a-table-column>
                <a-table-column
                    key="update_time"
                    dataIndex="update_time"
                    :title="i18n.t('bkApUpload.updateTime')"
                    align="center"
                    header-align="center"
                    min-width="25%"
                >
                </a-table-column>
                <a-table-column
                    key="is_identified"
                    v-if="activeName === 'pending'"
                    dataIndex="is_identified"
                    :title="i18n.t('bkApUpload.ocrStatus')"
                    align="center"
                    header-align="center"
                    min-width="10%"
                >
                    <template #default="{record}">
                        <span v-if="record.is_identified === '1'">
                            <a-tag class="tag-green" type="success" color="green">{{
                                i18n.t('bkApUpload.scanned')
                            }}</a-tag>
                        </span>
                        <span v-else>
                            <a-tag type="danger" class="tag-red" color="red">{{ i18n.t('bkApUpload.pending') }}</a-tag>
                        </span>
                    </template>
                </a-table-column>
                <a-table-column
                    key="operation"
                    dataIndex="operation"
                    :title="i18n.t('bkCustomer.operation')"
                    align="center"
                    header-align="center"
                    min-width="40%"
                >
                    <template #default="{record}">
                        <div v-if="record.invoice_created_status === 0">
                            <a-button
                                class="btn-txt"
                                type="link"
                                :title="i18n.t('bkApUpload.createFile')"
                                @click="edit(record)"
                            >
                                <edit-outlined />
                            </a-button>

                            <a-divider type="vertical" />
                            <a-button
                                v-show="record.invoice_created_status === 0"
                                class="btn-txt"
                                :title="i18n.t('bkApUpload.analyzeFile')"
                                type="link"
                                disabled
                                @click="ocrFileAbbyy(record)"
                            >
                                <scan-outlined />
                            </a-button>
                            <a-divider type="vertical" />
                            <a-button
                                v-show="record.invoice_created_status === 0"
                                class="btn-txt"
                                :title="i18n.t('bkApUpload.delFile')"
                                type="link"
                                @click="remove(record)"
                                :disabled="record.is_identified !== 0"
                            >
                                <!-- <delete-outlined
                                    :style="record.is_identified !== 0 ? {color: '#b8b9bc'} : {color: '#f5222d'}"
                                /> -->
                                <svg-icon
                                    name="icon_delete"
                                    :style="record.is_identified !== 0 ? {color: '#b8b9bc'} : {color: '#f5222d'}"
                                ></svg-icon>
                            </a-button>
                        </div>
                        <div v-else>
                            <a-button
                                class="btn-txt"
                                :title="i18n.t('bkApUpload.viewDetail')"
                                type="link"
                                @click="view(record)"
                            >
                                <file-search-outlined />
                            </a-button>
                            <a-divider type="vertical" />
                            <a-button
                                :title="i18n.t('bkApUpload.downloadInvoice')"
                                class="btn-txt"
                                type="link"
                                @click="download(record)"
                            >
                                <download-outlined />
                            </a-button>
                        </div>
                    </template>
                </a-table-column>
            </a-table>
        </div>

        <div class="pagination-wrap">
            <a-pagination
                v-model:current="state.pageQuery.page_index"
                v-model:page-size="state.pageQuery.page_size"
                :disabled="isLoading"
                :hideOnSinglePage="false"
                :showSizeChanger="true"
                :total="totalNumber"
                @change="changePage"
                class="paginpage table-pagination"
                layout="total, sizes, prev, pager, next, jumper"
                background
                small
                @current-change="changeCurrentPageNumber"
                @size-change="changePageSize"
            ></a-pagination>
            <span
                >{{ i18n.t('PyComponents.total') }} {{ totalNumber }}
                {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
            >
        </div>
    </div>
</template>

<script setup lang="ts">
import {useStore} from 'vuex'
import {reactive, ref, computed, onBeforeMount} from 'vue'
import {FileSearchOutlined, ScanOutlined, DownloadOutlined, EditOutlined} from '@ant-design/icons-vue'
import * as _ from 'lodash'
// import dayjs from 'dayjs'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import SvgIcon from '@/components/SvgIcon.vue'
import moment from 'moment'

const i18n: Composer = i18nInstance.global
const store = useStore()
const userCompany: any = UserCompany.get() || []
const props = defineProps({
    tableList: {
        type: Array,
        default: [] as any[],
    },
    activeName: {
        type: String,
        default: 'pending',
    },
})
const isLoading = ref(true)

//parent method
const emit = defineEmits(['edit', 'delete', 'ocrFiles', 'view', 'download', 'pageChange', 'updateSelected'])
const edit = (record: any) => {
    console.log('before emit item', record)
    emit('edit', record)
}
const remove = (record: any) => emit('delete', record)
const ocrFileAbbyy = (record: any) => emit('ocrFiles', [record], 'ABBYY')
const view = (record: any) => emit('view', record)
const download = (record: any) => emit('download', record)
const changePagination = () => emit('pageChange', state.pageQuery)
const updateSelectedItem = (records: any[]) => emit('updateSelected', records)

const state = reactive({
    tableData: [] as any,
    pageQuery: {
        page_index: 1,
        page_size: 10,
    },
    selectedRowKeys: [] as any[],
    sortOrder: 'descend',
})

const rowSelection = computed(() => {
    return props.activeName === 'pending'
        ? {
              selectedRowKeys: state.selectedRowKeys,
              onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
                  console.log(selectedRowKeys, selectedRows)
                  state.selectedRowKeys = selectedRowKeys
                  updateSelectedItem(selectedRows)
              },
          }
        : null
})

const sortDatetime = (a: any, b: any) => new Date(b.create_time).getTime() - new Date(a.create_time).getTime()
const sortString = (a: any, b: any) => (a.fileName as string).localeCompare(b.fileName)
const sortingTable = (pagination: any, filters: any, sorter: any, extra: any) => {
    state.sortOrder = state.sortOrder === 'descend' ? 'ascend' : 'descend'
}

const changeCurrentPageNumber = (pageNumber: number) => {
    state.pageQuery.page_index = pageNumber
    changePagination()
}

const changePage = () => {
    fetchTableData(props.activeName, state.pageQuery)
}

const changePageSize = (pageSize: number) => {
    state.pageQuery.page_size = pageSize
    state.pageQuery.page_index = 1
    changePagination()
}

const totalNumber = computed(() => store.state.ApStore.totalNumber)
const fetchTableData = async (index: string, pageQuery?: any) => {
    try {
        isLoading.value = true
        const invoiceQuery = {
            file_type: '1',
            invoice_created_status: '0',
        }
        pageQuery = pageQuery || {
            page_index: 1,
            page_size: 10,
        }
        const sortObj = {} as any
        sortObj['sort[update_time]'] = 'desc'
        invoiceQuery.invoice_created_status = ['finished', 'failure'].includes(index) ? '1' : '0'
        const query = {
            ...sortObj,
            ...invoiceQuery,
            ...pageQuery,
            company_code: userCompany[0].code,
        }
        const res = await store.dispatch('ApStore/fetchInvoicesListV1', query)

        // state.tableData = _.cloneDeep(store.state.ApStore.invoicesList)
        state.tableData = _.cloneDeep(res.data.data)
        state.tableData.forEach((x: any) => {
            x.key = x.id
            x.update_time = moment(x.update_time).format('YYYY-MM-DD HH:mm:SS')
        })
        console.log('the state.tableDate', state.tableData)
        // isLoading.value = false
    } catch (e) {
        console.log('fetch invoice list error: ', e)
    } finally {
        isLoading.value = false
    }
}
defineExpose({
    fetchTableData,
})
onBeforeMount(async () => {
    await fetchTableData(props.activeName)
    state.tableData = _.cloneDeep(store.state.ApStore.invoicesList)
    state.tableData.forEach((x: any) => {
        x.key = x.id
        x.update_time = moment(x.update_time).format('YYYY-MM-DD HH:mm:SS')
    })
})
</script>
<style lang="scss" scoped>
.page-container_ap_table {
    padding: 0px 20px 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    border-radius: 12px;

    .table-pending {
        position: relative;
    }

    .table-created {
        position: relative;
        margin-top: 30px;
    }

    .pagination-wrap {
        display: flex;
        margin-top: 12px;
        justify-content: flex-end;
        align-items: center;

        span {
            font-size: 12px;
            margin-left: 8px;
            line-height: 16px;
            color: #8c8c8c;
        }
    }
}

.btn-txt {
    padding-left: 0px;
    padding-right: 0px;
    font-size: 16px;
}

.tag-green {
    background-color: rgb(0, 182, 10, 0.1);
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #00b60a;
    line-height: 20px;
    font-weight: 400;
}

.tag-red {
    background-color: rgba(255, 55, 47, 0.1);
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #ff372f;
    line-height: 20px;
    font-weight: 400;
}

//:deep(.ant-checkbox-inner) {
//    background: #ffffff;
//    border: 1px solid rgba(103, 109, 124, 1);
//    border-radius: 2px;
//}
</style>
