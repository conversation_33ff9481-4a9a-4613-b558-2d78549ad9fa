<!-- @format -->

<script lang="ts" setup>
import {computed} from 'vue'

const props = withDefaults(
    defineProps<{
        name: string
        prefix?: string
        color?: string
    }>(),
    {
        prefix: 'icon',
        name: '',
    },
)
const symbolId = computed(() => `#${props.prefix}-${props.name}`)
</script>
<template>
    <span role="img" class="anticon">
        <svg focusable="false" aria-hidden="true">
            <use :xlink:href="symbolId" :fill="color" />
        </svg>
    </span>
</template>
<style lang="scss" scoped>
.anticon {
    vertical-align: middle;
}
svg {
    width: 1em;
    height: 1em;
    fill: currentcolor;
    font-size: 16px;
}
</style>
