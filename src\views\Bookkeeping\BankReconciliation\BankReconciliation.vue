<!-- @format -->

<script lang="ts" setup>
import EstatementTable from '@/components/bookkeepingComponents/BrComponents/EstatementTable.vue'
import { computed, onBeforeMount, reactive, ref } from 'vue'

import * as _ from 'lodash'
import { useStore } from 'vuex'

// import {type Composer} from 'vue-i18n'
// import i18nInstance from '@/locales/i18n'
import { Ar_Integration, Ap_Integration, UserCompany } from '@/lib/storage'
import { getCompanyBankList } from '@/api'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
const store = useStore()
// const i18n: Composer = i18nInstance.global
const userCompany: any = UserCompany.get() || []
const apIntegration: any = Ap_Integration.get() ?? 0
const arIntegration: any = Ar_Integration.get() ?? 0
const i18n: Composer = i18nInstance.global
const state = reactive({
    activeKey: 0,
})

const fetchESList = (query: any) => store.dispatch('BrStore/fetchEsListV1', query)
const fetchSapMasterData = (query?: any) => store.dispatch('ApStore/fetchApSapMasterData', query)
const fetchAccountDescDropdown1 = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)

// const tableData = ref<Estatement[]>([])

const sapMasterData = computed(() => store.state.ApStore.sapMasterData)
const accountDescOptions = computed(() =>
    store.state.CommonDropDownStore.accountDescList.map((x: any) => {
        return {
            key: x.id,
            label: `${x.account_code.substring(0, 4)} | ${x.name}`,
            value: x.account_code,
        }
    }),
)

const tableLoading = ref(false)

const getQueryParams = (searchForm: any) => {
    const result: any = {}
    result['sort[date]'] = 'desc'
    if (searchForm.searchText) {
        result['bank_account'] = searchForm.searchText
    }
    if (searchForm.startDate && searchForm.endDate) {
        result['start_date'] = searchForm.startDate
        result['end_date'] = searchForm.endDate
    }
    if (searchForm.startDate && !searchForm.endDate) {
        result['start_date'] = searchForm.startDate
    }
    if (!searchForm.startDate && searchForm.endDate) {
        result['end_date'] = searchForm.endDate
    }
    result['balance_min'] = searchForm.minTotalFee
    result['balance_max'] = searchForm.maxTotalFee

    // if (searchForm.minTotalFee !== null && searchForm.maxTotalFee !== null) {
    //     result['balance[$bw]'] = `[${searchForm.minTotalFee},${searchForm.maxTotalFee}]`
    // }
    // if (searchForm.minTotalFee !== null && searchForm.maxTotalFee == null) {
    //     result['balance[$gte]'] = searchForm.minTotalFee
    // }
    // if (searchForm.minTotalFee == null && searchForm.maxTotalFee !== null) {
    //     result['balance[$lte]'] = searchForm.maxTotalFee
    // }
    result['bank_account'] = bankList.value[state.activeKey].account_no
    if (apIntegration === 1 && bankList.value[state.activeKey].bank_type === 'Credit') {
        result['bank_type'] = bankList.value[state.activeKey].bank_type
    }
    if (apIntegration === 1 && bankList.value[state.activeKey].bank_type !== 'Credit') {
        result['bank_type'] = 'NoCredit'
    }
    result.page_index = searchForm.page_index || 1
    result.page_size = searchForm.page_size || 10
    result.company_code = userCompany[0].code
    result.ar_integration = arIntegration
    result.ap_integration = apIntegration
    return result
}

const updateTable = async (query: any) => {
    tableLoading.value = true
    // query['company_id'] = query['company_id'] ?? 201
    await fetchESList(getQueryParams(query))
    // tableData.value = resData
    tableLoading.value = false
}
const fetchAllBankList = (query?: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', query)
const bankList = computed(() => store.state.BankInfoStore.bankList)

const updateTableNoLoading = async (query: any) => {
    await fetchESList(getQueryParams(query))
}
const switchTab = async () => {
    await updateTable({})
}

// 获取tooltip的容器，确保tooltip不被tab容器裁剪
const getTooltipContainer = () => {
    return document.body
}

// 根据apIntegration参数获取CoA描述信息
const getCoaDescription = (glAccount: string) => {
    if (!glAccount) return '-'

    if (apIntegration === 1) {
        // 从SAP主数据中查找描述
        const sapAccount = sapMasterData.value?.ET_GL_ACCOUNT?.find((item: any) => item.GL_ACCOUNT === glAccount)
        return sapAccount ? `${glAccount} | ${sapAccount.DESCRIPTION}` : glAccount
    } else {
        // 从accountDescOptions中查找描述
        const accountDesc = accountDescOptions.value.find((item: any) => item.value === glAccount)
        return accountDesc ? accountDesc.label : glAccount
    }
}

onBeforeMount(async () => {
    // TODO: mock data remove
    const initQuery: any = { page_index: 1, page_size: 10 }
    tableLoading.value = true
    try {
        await fetchAllBankList({ company_code: userCompany[0].code })
        await updateTable(getQueryParams({ ...initQuery }))
        await fetchAccountDescDropdown1({ company_code: userCompany[0].code, category: 'Bank', $limit: -1, del_flag: 0 })
        if (apIntegration === 1) {
            await fetchSapMasterData({ company_code: userCompany[0].code })
        }
    } catch (e) {
        console.log(e)
    }

    tableLoading.value = false
    // tableData.value = _.fill(Array(12), '').map((_, i) => {
    //     const matchFlag = Math.floor(Math.random() * 2)
    //     return {
    //         id: `${i}`,
    //         orgId: 'string',
    //         date: 'string',
    //         description: 'string',
    //         reference: 'string',
    //         payerPayee: 'string',
    //         withdrawal: 0,
    //         deposit: 0,
    //         balance: 0,
    //         bankAccount: 'string',
    //         statementPeriod: 'string',
    //         statementType: 'string',
    //         brType: 'string',
    //         brFlag: 'string',
    //         delFlag: 'string',
    //         fileId: 'string',
    //         creator: 'string',
    //         creatorName: 'string',
    //         createTime: 'string',
    //         updateTime: 'string',
    //         filePageIndex: 'string',
    //         currencyType: 'string',
    //         plaidTransactionId: 'string',
    //         matchFlag: `${matchFlag}`,
    //         expenseAccount: 'string',
    //         matchList: {},
    //         children: matchFlag
    //             ? [
    //                   {
    //                       id: `${i * 10}`,
    //                       orgId: 'string',
    //                       date: 'string',
    //                       description: 'string',
    //                       reference: 'string',
    //                       payerPayee: 'string',
    //                       withdrawal: 0,
    //                       deposit: 0,
    //                       balance: 0,
    //                       bankAccount: 'string',
    //                       statementPeriod: 'string',
    //                       statementType: 'string',
    //                       brType: 'string',
    //                       brFlag: 'string',
    //                       delFlag: 'string',
    //                       fileId: 'string',
    //                       creator: 'string',
    //                       creatorName: 'string',
    //                       createTime: 'string',
    //                       updateTime: 'string',
    //                       filePageIndex: 'string',
    //                       currencyType: 'string',
    //                       plaidTransactionId: 'string',
    //                       matchFlag: `0`,
    //                       expenseAccount: 'string',
    //                       matchList: {},
    //                   },
    //                   {
    //                       id: `${i * 10 + 1}`,
    //                       orgId: 'string',
    //                       date: 'string',
    //                       description: 'string',
    //                       reference: 'string',
    //                       payerPayee: 'string',
    //                       withdrawal: 0,
    //                       deposit: 0,
    //                       balance: 0,
    //                       bankAccount: 'string',
    //                       statementPeriod: 'string',
    //                       statementType: 'string',
    //                       brType: 'string',
    //                       brFlag: 'string',
    //                       delFlag: 'string',
    //                       fileId: 'string',
    //                       creator: 'string',
    //                       creatorName: 'string',
    //                       createTime: 'string',
    //                       updateTime: 'string',
    //                       filePageIndex: 'string',
    //                       currencyType: 'string',
    //                       plaidTransactionId: 'string',
    //                       matchFlag: `0`,
    //                       expenseAccount: 'string',
    //                       matchList: {},
    //                   },
    //               ]
    //             : null,
    //     }
    // })
})
</script>
<template>
    <div class="page-container-br-reconcile">
        <a-tabs v-model:activeKey="state.activeKey" @change="switchTab">
            <a-tab-pane v-for="(item, key) in bankList" :key="key">
                <template #tab>
                    <a-tooltip
                        placement="bottom"
                        overlayClassName="bank-info-tooltip"
                        :getPopupContainer="getTooltipContainer"
                        :mouseEnterDelay="0.3"
                        :mouseLeaveDelay="0.1">
                        <template #title>
                            <div class="bank-tooltip-content">
                                <div class="bank-info-item">
                                    <div class="bank-info-title">{{ i18n.t('bankInfo.bankName') }}</div>
                                    <div class="bank-info-value">{{ i18n.t(`bankInformation.${item.code}`) }}</div>
                                </div>
                                <div class="bank-info-item">
                                    <div class="bank-info-title">{{ i18n.t('bankInfo.accountType') }}</div>
                                    <div class="bank-info-value">{{ item.bank_type }}</div>
                                </div>
                                <div class="bank-info-item">
                                    <div class="bank-info-title">{{ i18n.t('bankInfo.currency') }}</div>
                                    <div class="bank-info-value">{{ item.currency }}</div>
                                </div>
                                <div class="bank-info-item">
                                    <div class="bank-info-title">{{ i18n.t('bankInfo.bankAccount') }}</div>
                                    <div class="bank-info-value">{{ item.account_no }}</div>
                                </div>
                                <div class="bank-info-item">
                                    <div class="bank-info-title">{{ i18n.t('bankInfo.coa') }}</div>
                                    <div class="bank-info-value">{{ getCoaDescription(item.gl_account) }}</div>
                                </div>
                            </div>
                        </template>
                        <div class="tab-panel-header">
                            {{ i18n.t(`bankInformation.${item.code}`) + ' | ' + item.account_no }}
                        </div>
                    </a-tooltip>
                </template>
                <estatement-table :is-loading="tableLoading" :hide-bank-account="false" :show-view-only="false"
                    :show-size-changer="false" @update="updateTable"
                    :current-bank-account="`${bankList[state.activeKey].account_no}|${bankList[state.activeKey].currency}`"
                    @updateNoLoading="updateTableNoLoading"></estatement-table>
            </a-tab-pane>
        </a-tabs>
    </div>
    <!-- <estatement-table
        :is-loading="tableLoading"
        :hide-bank-account="false"
        :show-view-only="false"
        :show-size-changer="false"
        @update="updateTable"
        @updateNoLoading="updateTableNoLoading"
    ></estatement-table> -->
</template>
<style lang="scss" scoped>
.tab-panel-header {
    font-size: 16px;
    letter-spacing: 0;
    text-align: center;
    line-height: 22px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: rgba(0, 79, 193, 0.05);
    }
}

:deep(.ant-avatar > img) {
    object-fit: contain;
}

// 银行信息tooltip的专用样式 - 全局样式确保在body中正确显示

.bank-tooltip-content {
    .bank-info-item {
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }

        .bank-info-title {
            font-weight: 600;
            color: #262626;
            font-size: 13px;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .bank-info-value {
            font-weight: 400;
            color: #595959;
            font-size: 14px;
            line-height: 1.4;
            word-break: break-all;
            padding-left: 8px;
        }
    }
}
</style>

<!-- 全局样式，确保tooltip在body中正确显示 -->
<style lang="scss">
.bank-info-tooltip {
    z-index: 9999 !important;

    .ant-tooltip-inner {
        min-width: 320px !important;
        max-width: 450px !important;
        padding: 12px 16px !important;
        background-color: #fff !important;
        border: 1px solid #e8e8e8 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        color: #262626 !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
        white-space: normal !important;
        word-wrap: break-word !important;
    }

    .ant-tooltip-arrow {
        &::before {
            background-color: #fff !important;
            border: 1px solid #e8e8e8 !important;
        }
    }

    .bank-tooltip-content {
        .bank-info-item {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }

            .bank-info-title {
                font-weight: 600 !important;
                color: #262626 !important;
                font-size: 13px !important;
                margin-bottom: 4px !important;
                line-height: 1.3 !important;
            }

            .bank-info-value {
                font-weight: 400 !important;
                color: #595959 !important;
                font-size: 14px !important;
                line-height: 1.4 !important;
                word-break: break-all !important;
                padding-left: 8px !important;
            }
        }
    }
}
</style>
