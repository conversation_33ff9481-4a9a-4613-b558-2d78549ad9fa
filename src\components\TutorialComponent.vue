<!-- @format -->

<script lang="ts" setup>
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useStore} from 'vuex'
import {ref, onMounted, computed, watch, reactive, nextTick} from 'vue'
import {PlusOutlined} from '@ant-design/icons-vue'
import {UserCompany} from '@/lib/storage'
import {getCompanyTaxInfo, getCustomers, getCoa} from '@/api'
import SvgIcon from '@/components/SvgIcon.vue'
import {message, Modal, type FormInstance} from 'ant-design-vue'

import * as _ from 'lodash'
import moment from 'moment'
import Decimal from 'decimal.js'

const i18n: Composer = i18nInstance.global
const store = useStore()

const props = defineProps({
    steps: {
        type: Object,
    },
    enablePost: {
        type: Boolean,
        default: false,
    },
    enableNext: {
        type: Boolean,
        default: true,
    },
    loading: {
        type: Boolean,
        default: false,
    },
})
const emits = defineEmits(['update:enablePost', 'update:enableNext', 'update:loading', 'success'])
const userCompany: any = UserCompany.get() || []
const companyTaxInfo = ref<any>({})
const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}
const companyCurrency = computed(() => companyTaxInfo.value.currency)
const formLoading = ref(false)
const current = ref<number>(0)
const tableWrapRef = ref()
const glDraft = ref<any>({})
const posting_date = ref<string>(getToday())
const form1 = ref<any>({})
const form2 = ref<any>({})
const form3 = ref<any>({})
const form4 = ref<any>({})
const formRef = ref<FormInstance>()
const form = ref<any>({
    company_code: userCompany[0].code,
    opening: 1,
    document_no: `${userCompany[0].code}-Draft-Opening`,
    draft: 1,
    header_text: 'Opening Balance 0142592518fe45569fa78a8537478c1c',
    currency: companyCurrency.value,
    posting_date: getToday(),
    createDate: getToday(),
    line_items: [],
    totalCredit: 0.0,
    totalDebit: 0.0,
    totalCreditCAD: 0.0,
    totalDebitCAD: 0.0,
})
const disableAll = ref<boolean>(false)
const totalAP = ref<number>(0)
const totalAR = ref<number>(0)
const restFormData = (_targetForm: any) => {
    _targetForm.value = _.cloneDeep(form.value)
    formRef.value?.resetFields()
    form.value.description = null
    form.value.posting_date = posting_date.value
    form.value.totalCredit = 0.0
    form.value.totalDebit = 0.0
    form.value.totalCreditCAD = 0.0
    form.value.totalDebitCAD = 0.0
    form.value.createDate = getToday()
    form.value.currency = companyCurrency.value
    form.value.id = null
    form.value.opening = 1
    form.value.draft = 1
    form.value.line_items = []
}
const prepareFinalList = (type: any) => {
    let finalList: any[] = []
    const tmpForm1 = _.cloneDeep(current.value === 0 ? form : form1)
    if (tmpForm1 && !_.isEmpty(tmpForm1.value))
        finalList =
            type === 'post'
                ? [...tmpForm1.value.line_items.filter((item: any) => item.gl_account)]
                : [...tmpForm1.value.line_items.filter((item: any) => item.gl_account)]
    const tmpForm2 = _.cloneDeep(current.value === 1 ? form : form2)
    if (tmpForm2 && !_.isEmpty(tmpForm2.value))
        finalList = [
            ...(finalList as any),
            ...tmpForm2.value.line_items.filter((item: any) => item.gl_account && item.business_partner),
        ]
    const tmpForm3 = _.cloneDeep(current.value === 2 ? form : form3)
    if (tmpForm3 && !_.isEmpty(tmpForm3.value))
        finalList = [
            ...(finalList as any),
            ...tmpForm3.value.line_items.filter((item: any) => item.gl_account && item.business_partner),
        ]

    return _.cloneDeep(finalList)
}
const mapField = (item: any) => {
    if (item.dr_cr === 'dr') {
        item.amount_tc_1 = item.amount_tc
        item.amount_tc_2 = 0
    } else {
        item.amount_tc_1 = 0
        item.amount_tc_2 = item.amount_tc
    }

    item.name =
        item.business_partner && item.business_partner !== 'ACCOUNT-ALL'
            ? customersList.value.find((i: any) => i.contact_id === item.business_partner)?.contact_name || ''
            : item.coaCategory || item.coaName
}
const prepareForm = () => {
    if (current.value === 0) {
        if (!_.isEmpty(glDraft.value) || !_.isEmpty(form1.value)) {
            if (!_.isEmpty(form1.value)) {
                form.value.line_items = _.cloneDeep(form1.value.line_items)
            } else {
                form.value.line_items = _.sortBy(
                    _.cloneDeep(
                        glDraft.value.ledger_entry_line.filter(
                            (i: any) => i.business_partner === 'ACCOUNT-ALL' || !i.business_partner,
                        ),
                    ),
                    ['line_no'],
                )
                if (form.value.line_items.length) {
                    form.value.line_items.forEach((item: any) => {
                        mapField(item)
                    })
                } else {
                    addItem(2)
                }
            }
        } else {
            addItem(2)
        }
    } else if (current.value === 1) {
        if (!_.isEmpty(glDraft.value) || !_.isEmpty(form2.value)) {
            if (!_.isEmpty(form2.value)) {
                form.value.line_items = _.cloneDeep(form2.value.line_items)
            } else {
                form.value.line_items = _.sortBy(
                    _.cloneDeep(
                        glDraft.value.ledger_entry_line.filter(
                            (i: any) =>
                                i.gl_account === accountDescAPList.value[0].account_code &&
                                i.business_partner != 'ACCOUNT-ALL',
                        ),
                    ),
                    ['line_no'],
                )
                if (form.value.line_items.length) {
                    form.value.line_items.forEach((item: any) => {
                        mapField(item)
                    })
                } else {
                    addItem(1, false, 'ap')
                }
            }
        } else {
            addItem(1, false, 'ap')
        }
    } else if (current.value === 2) {
        if (!_.isEmpty(glDraft.value) || !_.isEmpty(form3.value)) {
            if (!_.isEmpty(form3.value)) {
                form.value.line_items = _.cloneDeep(form3.value.line_items)
            } else {
                form.value.line_items = _.sortBy(
                    _.cloneDeep(
                        glDraft.value.ledger_entry_line.filter(
                            (i: any) =>
                                i.gl_account === accountDescARList.value[0].account_code &&
                                i.business_partner != 'ACCOUNT-ALL',
                        ),
                    ),
                    ['line_no'],
                )
                if (form.value.line_items.length) {
                    form.value.line_items.forEach((item: any) => {
                        mapField(item)
                    })
                } else {
                    addItem(1, false, 'ar')
                }
            }
        } else {
            addItem(1, false, 'ar')
        }
    }
}
const next = (index: number) => {
    current.value = index
    if (current.value === 1) {
        const _ap = form.value.line_items.find((item: any) => item.name === 'Account Payable')
        const _ar = form.value.line_items.find((item: any) => item.name === 'Account Receivable')

        totalAP.value = new Decimal(_ap.amount_tc_2 || 0).toDP(2).toNumber()
        totalAR.value = new Decimal(_ar.amount_tc_1 || 0).toDP(2).toNumber()

        restFormData(form1)
        prepareForm()
    }
    if (current.value === 2) {
        restFormData(form2)
        prepareForm()
        // form.value = _.cloneDeep(form3.value)
    }
    if (current.value === 3) {
        restFormData(form3)
        form.value.createDate = form1.value.createDate
        // const finalList = [...form1.value.line_items.filter((item: any) => !(item.coaCategory == 'Account Payable' || item.coaCategory == 'Account Receivable') && item.gl_account), ...form2.value.line_items.filter((item: any) => item.gl_account), ...form3.value.line_items.filter((item: any) => item.gl_account)]
        form.value.line_items = prepareFinalList('post').filter(i => i.business_partner !== 'ACCOUNT-ALL')
        // addItem(2)
        // form.value = _.cloneDeep(form4.value)
    }
}
const prev = (index: number) => {
    current.value = index
    if (current.value === 0) {
        restFormData(form2)
        form.value = _.cloneDeep({...form1.value, posting_date: posting_date.value})
    }
    if (current.value === 1) {
        restFormData(form3)
        form.value = _.cloneDeep({...form2.value, posting_date: posting_date.value})
    }
    if (current.value === 2) {
        restFormData(form4)
        form.value = _.cloneDeep({...form3.value, posting_date: posting_date.value})
    }
}

const dateChange = () => {
    posting_date.value = form.value.posting_date
}

const formValidateFields = () => {
    return formRef.value?.validateFields()
}
// get CoA list
const accountDescList = ref<any>({})
const _accountDescList = computed(() =>
    accountDescList.value.filter((i: any) => !form.value.line_items.some((j: any) => j.gl_account === i.account_code)),
)
const accountDescAPList = computed(() => accountDescList.value.filter((i: any) => i.category === 'Account Payable'))
const accountDescARList = computed(() => accountDescList.value.filter((i: any) => i.category === 'Account Receivable'))
// get contact list
const customersList = ref<any>({})
const getGlDraftOpening = (payload: any) => store.dispatch('GlStore/getGlDraftOpening', payload)
// save gl
const editNewGl = (payload: any) => store.dispatch('GlStore/editGl', payload)
const saveGl = (payload: any) => store.dispatch('GlStore/saveGlAction', payload)
const saveToServer = async (_form: any, type: any) => {
    let response: any = {}
    if (type === 'post') {
        delete _form.document_no
        delete _form.draft
    }
    try {
        formLoading.value = true
        response =
            type === 'post'
                ? await saveGl({..._form, createDate: getToday()})
                : !_.isEmpty(glDraft.value)
                ? await editNewGl({..._form, createDate: getToday()})
                : await saveGl({..._form, createDate: getToday()})
        if (response.data.ok || response.data.data.success) {
            message.success({
                content: 'success',
                duration: 5,
            })
            if (type === 'post') emits('success')
        }
        // else {
        // message.error({
        //     content: 'failed',
        //     duration: 5,
        // })
        // }
    } catch (error: any) {
        console.log(error)
        // message.error({
        //     content: error.response.data.errors ? error.response.data.errors : 'failed',
        //     duration: 5,
        // })
    } finally {
        formLoading.value = false
    }
}
const saveDraft = async () => {
    console.log('save draft')
    const queryForm = {..._.cloneDeep(form.value)}
    queryForm.line_items = prepareFinalList('save')

    if (!queryForm.line_items || (queryForm.line_items && queryForm.line_items.length === 0)) {
        message.error({
            content: i18n.t('glEntry.msgAtLeastOne'),
            duration: 5,
        })
        return false
    }

    queryForm.line_items.forEach((item: any) => {
        item.amount_tc = item.dr_cr === 'dr' ? item.amount_tc_1 : item.amount_tc_2
        delete item.amount_tc_1
        delete item.amount_tc_2
    })

    saveToServer(queryForm, 'save')
    return true
}
const post = () => {
    console.log('save draft')
    const queryForm = {..._.cloneDeep(form.value)}
    queryForm.line_items = prepareFinalList('post')

    if (!queryForm.line_items || (queryForm.line_items && queryForm.line_items.length === 0)) {
        message.error({
            content: i18n.t('glEntry.msgAtLeastOne'),
            duration: 6,
        })
        return false
    }
    if (+queryForm.totalCredit !== +queryForm.totalDebit) {
        message.error({
            content: i18n.t('gl.msgNotMatch'),
            duration: 6,
        })
        return false
    }

    queryForm.line_items.forEach((item: any) => {
        item.amount_tc = item.dr_cr === 'dr' ? item.amount_tc_1 : item.amount_tc_2
        delete item.amount_tc_1
        delete item.amount_tc_2
    })

    saveToServer(queryForm, 'post')
    return true
}
defineExpose({
    next,
    prev,
    saveDraft,
    post,
    formValidateFields,
})
const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const changeItemListRowExpenseAccount = (value: any, index: number) => {
    form.value.line_items[index].gl_account = String(value)
    const _target = accountDescList.value.find((ele: any) => ele.account_code == value)
    form.value.line_items[index].name = _target.category ? _target.category : _target.name
    form.value.line_items[index].coaName = _target.name || ''
    form.value.line_items[index].coaCategory = _target.category || ''
}
const creditChange = (index: number) => {
    if (!form.value.line_items[index].amount_tc_2) return
    form.value.line_items[index].amount_tc_1 = 0
    form.value.line_items[index].dr_cr = 'cr'
}
const debitChange = (index: number) => {
    if (!form.value.line_items[index].amount_tc_1) return
    form.value.line_items[index].amount_tc_2 = 0
    form.value.line_items[index].dr_cr = 'dr'
}

const getPopupContainer = (node: any) => {
    return tableWrapRef.value
}

const changeContact = (id: string, index: number) => {
    const contact_name = customersList.value.find((i: any) => i.contact_id === id).contact_name
    form.value.line_items[index].name = contact_name
}

const getForms = () => {
    if (_.isEmpty(glDraft.value)) return
    let line_items1: any[] = []
    let line_items2: any[] = []
    let line_items3: any[] = []

    posting_date.value = form.value.posting_date = moment(glDraft.value.posting_date).format('yyyy-MM-DD')

    line_items1 = _.sortBy(
        _.cloneDeep(
            glDraft.value.ledger_entry_line.filter(
                (i: any) => i.business_partner === 'ACCOUNT-ALL' || !i.business_partner,
            ),
        ),
        ['line_no'],
    )
    if (line_items1.length) {
        line_items1.forEach((item: any) => {
            mapField(item)
        })
    }
    line_items2 = _.sortBy(
        _.cloneDeep(
            glDraft.value.ledger_entry_line.filter(
                (i: any) =>
                    i.gl_account === accountDescAPList.value[0].account_code && i.business_partner != 'ACCOUNT-ALL',
            ),
        ),
        ['line_no'],
    )
    if (line_items2.length) {
        line_items2.forEach((item: any) => {
            mapField(item)
        })
    }
    line_items3 = _.sortBy(
        _.cloneDeep(
            glDraft.value.ledger_entry_line.filter(
                (i: any) =>
                    i.gl_account === accountDescARList.value[0].account_code && i.business_partner != 'ACCOUNT-ALL',
            ),
        ),
        ['line_no'],
    )
    if (line_items3.length) {
        line_items3.forEach((item: any) => {
            mapField(item)
        })
    }
    if (line_items1.length) form1.value.line_items = line_items1
    if (line_items2.length) form2.value.line_items = line_items2
    if (line_items3.length) form3.value.line_items = line_items3
}

onMounted(async () => {
    try {
        formLoading.value = true
        const companyTaxInfoRes = await getCompanyTaxInfo({code: userCompany[0].code})
        companyTaxInfo.value = companyTaxInfoRes.data.data[0] || {}

        const accountDescListRes = await getCoa({company: userCompany[0].code})
        accountDescList.value = accountDescListRes.data.data.rows

        const customersRes = await getCustomers({company_code: userCompany[0].code, $limit: 1000, $skip: 0})
        customersList.value = customersRes.data.data

        if (accountDescAPList.value.length === 0 || accountDescARList.value.length === 0) {
            disableAll.value = true
            nextTick(() => {
                Modal.error({
                    title: 'Error',
                    content: 'Missing AP/AR CoA, please add first',
                    zIndex: 9999,
                })
                emits('update:loading', true)
            })

            return
        }

        glDraft.value = await getGlDraftOpening({
            company: userCompany[0].code,
            document_no: `${userCompany[0].code}-Draft-Opening`,
        })

        getForms()
        prepareForm()
    } catch (error) {
        console.log(error)
    } finally {
        formLoading.value = false
    }
})
const remove = (index: number) => {
    form.value.line_items.splice(index, 1)
}
const addItem = (times = 1, manual = false, type?: 'ap' | 'ar') => {
    if (!form.value.line_items) {
        form.value.line_items = []
    }
    // 添加item
    for (let i = 0; i < times; i++) {
        let _coa = [accountDescAPList.value[0], accountDescARList.value[0]]

        if (type === 'ap' || current.value === 1) _coa = [accountDescAPList.value[0]]
        else if (type === 'ar' || current.value === 2) _coa = [accountDescARList.value[0]]

        form.value.line_items.push({
            itemNo: form.value.line_items.length + 1,
            description: '', //暂时先用这个
            gl_account: manual
                ? current.value === 1 || current.value === 2
                    ? _coa[i % 2].account_code
                    : ''
                : _coa[i % 2].account_code,
            name: manual ? '' : _coa[i % 2].category,
            neg_posting: false,
            amount_tc: 0,
            amount_tc_1: 0,
            amount_tc_2: 0,
            dr_cr: 'dr', // 'cr'
            business_partner: current.value === 0 && form.value.line_items.length < 2 ? 'ACCOUNT-ALL' : '',
            due_date: getToday(),
        })
    }
}

const rules = reactive({
    contact: [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgSelectRule') + `Contact`,
            trigger: ['blur', 'change'],
        },
    ],
})

watch(
    () => form.value.line_items,
    newVal => {
        if (!newVal) return
        form.value.totalDebit = new Decimal(
            newVal.reduce((prev: any, curr: any, index: any) => {
                return parseFloat(prev) + parseFloat(curr.amount_tc_1)
            }, 0) || 0,
        )
            .toDP(2)
            .toNumber()

        form.value.totalCredit = new Decimal(
            newVal.reduce((prev: any, curr: any, index: any) => {
                return parseFloat(prev) + parseFloat(curr.amount_tc_2)
            }, 0) || 0,
        )
            .toDP(2)
            .toNumber()

        emits('update:enablePost', Boolean(form.value.totalDebit === form.value.totalCredit))
    },
    {deep: true},
)

watch(
    formLoading,
    newVal => {
        emits('update:loading', newVal)
    },
    {deep: true},
)

watch(
    current,
    () => {
        if (current.value === 0 || current.value === 3) emits('update:enableNext', true)
    },
    {deep: true},
)

const differenceComputed = (actual: any, predict: any) => {
    const difference = new Decimal(actual || 0)
        .sub(new Decimal(predict || 0))
        .abs()
        .toDP(2)
        .toNumber()

    emits('update:enableNext', Boolean(difference === 0 || current.value === 0 || current.value === 3))
    return difference
}
</script>
<template>
    <a-spin :spinning="formLoading">
        <a-form ref="formRef" :model="form" label-width="auto" label-position="top" class="form-box">
            <div ref="tableWrapRef">
                <a-steps :current="current">
                    <a-step
                        v-for="item in props.steps"
                        :key="item.title"
                        :title="item.title"
                        :description="item.description"
                    />
                </a-steps>
                <div class="steps-content">
                    <div v-if="current === 0 || current === 3" class="table-header-title">
                        <span>Cut-off Date: </span>
                        <a-date-picker
                            v-model:value="form.posting_date"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                            @change="dateChange"
                            :allowClear="false"
                            :disabled="disableAll"
                        >
                        </a-date-picker>
                    </div>
                    <div v-else class="table-header-title">
                        <span>{{ current === 1 ? 'Total AP: ' : 'Total AR: ' }}</span>
                        <a-input-number
                            :value="current === 1 ? totalAP : totalAR"
                            :precision="2"
                            class="total-num"
                            :disabled="true"
                        ></a-input-number>
                    </div>
                    <a-table :dataSource="form.line_items" style="width: 100%" size="small" :pagination="false">
                        <!-- gl_account枚举值 -->
                        <a-table-column
                            data-index="['line_items', 'gl_account']"
                            :title="i18n.t('gl.glAccount')"
                            align="center"
                            header-align="center"
                            width="23%"
                        >
                            <template #default="{index, record}">
                                <a-form-item style="margin-bottom: 0" :name="['line_items', index, 'gl_account']">
                                    <a-select
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="record.gl_account"
                                        show-search
                                        :dropdownMatchSelectWidth="400"
                                        :filter-option="filterOption"
                                        :getPopupContainer="getPopupContainer"
                                        class="table-input"
                                        @change="changeItemListRowExpenseAccount(record.gl_account, index)"
                                        :disabled="!!record.gl_account || current === 3"
                                    >
                                        <!-- tab 2: only category = ap, tab 3: only category = ar, tab 1&4: all -->
                                        <a-select-option
                                            v-for="item in current === 1
                                                ? accountDescAPList
                                                : current === 2
                                                ? accountDescARList
                                                : _accountDescList"
                                            :key="
                                                item.account_code +
                                                ' | ' +
                                                `${item.category ? item.category : item.name}`
                                            "
                                            :value="item.account_code"
                                            >{{
                                                item.account_code +
                                                ' | ' +
                                                `${item.category ? item.category : item.name}`
                                            }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column
                            data-index="['line_items', 'name']"
                            :title="'Name'"
                            align="center"
                            header-align="center"
                            width="23%"
                            :ellipsis="true"
                            v-if="current === 0 || current === 3"
                        >
                            <template #default="{index, record}">
                                <a-form-item :name="['line_items', index, 'name']">
                                    <!--  class="table-input-disabled"-->
                                    <a-input
                                        v-model:value="record.name"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :disabled="index === 0 || index === 1 || current === 3"
                                        class="table-input"
                                    ></a-input>
                                </a-form-item>
                            </template>
                        </a-table-column>
                        <!-- contact -->
                        <a-table-column
                            data-index="['line_items', 'business_partner']"
                            :title="'Contact'"
                            align="center"
                            header-align="center"
                            width="23%"
                            :ellipsis="true"
                            v-else
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    required
                                    :rules="rules.contact"
                                    style="margin-bottom: 0"
                                    :name="['line_items', index, 'business_partner']"
                                >
                                    <a-select
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="record.business_partner"
                                        show-search
                                        :dropdownMatchSelectWidth="400"
                                        :filter-option="filterOption"
                                        :getPopupContainer="getPopupContainer"
                                        @change="changeContact($event, index)"
                                        class="table-input"
                                    >
                                        <a-select-option
                                            v-for="item in customersList"
                                            :key="item.contact_id + ' | ' + item.contact_name"
                                            :value="item.contact_id"
                                            >{{ item.contact_id + ' | ' + item.contact_name }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column
                            data-index="['line_items', 'amount_tc_1']"
                            :title="i18n.t('gl.debit')"
                            align="center"
                            header-align="center"
                            width="23%"
                        >
                            <template #default="{index, record}">
                                <a-form-item :name="['line_items', index, 'amount_tc']">
                                    <a-input-number
                                        v-model:value="record.amount_tc_1"
                                        :disabled="
                                            current === 1 ||
                                            current === 3 ||
                                            (current === 0 && record.name === 'Account Payable')
                                        "
                                        :precision="2"
                                        :controls="false"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                        style="width: 100%"
                                        @change="debitChange(index)"
                                    ></a-input-number>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column
                            data-index="['line_items', 'amount_tc_2']"
                            :title="i18n.t('gl.credit')"
                            align="center"
                            header-align="center"
                            width="23%"
                        >
                            <template #default="{index, record}">
                                <a-form-item :name="['line_items', index, 'amount_tc']">
                                    <a-input-number
                                        v-model:value="record.amount_tc_2"
                                        :precision="2"
                                        :disabled="
                                            current === 2 ||
                                            current === 3 ||
                                            (current === 0 && record.name === 'Account Receivable')
                                        "
                                        :controls="false"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                        style="width: 100%"
                                        @change="creditChange(index)"
                                    ></a-input-number>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column key="operation" title="" align="center" width="5%">
                            <template #default="{index}">
                                <span>
                                    <a-button
                                        :disabled="
                                            (current === 0 && (index === 0 || index === 1)) ||
                                            ((current === 1 || current === 2) && index === 0) ||
                                            current === 3
                                        "
                                        type="link"
                                        danger
                                        @click="remove(index)"
                                    >
                                        <svg-icon name="icon_delete"></svg-icon>
                                    </a-button>
                                </span>
                            </template>
                        </a-table-column>
                    </a-table>
                    <a-button
                        v-if="current !== 3"
                        :disabled="disableAll"
                        class="invoice-add"
                        type="primary"
                        ghost
                        @click="addItem(1, true)"
                    >
                        <template #icon>
                            <plus-outlined />
                        </template>
                        {{ i18n.t('bkAp.addItemBtnTxt') }}
                    </a-button>
                    <a-table
                        :dataSource="[form]"
                        style="width: 100%"
                        size="small"
                        :pagination="false"
                        :showHeader="false"
                        class="total-diff-wrapper"
                        v-if="!formLoading"
                    >
                        <a-table-column
                            :title="'GL Account'"
                            align="center"
                            header-align="center"
                            width="23%"
                            :ellipsis="true"
                        >
                        </a-table-column>

                        <a-table-column
                            :title="'Name'"
                            align="center"
                            header-align="center"
                            width="23%"
                            :ellipsis="true"
                        >
                            <a-input value="Total" :disabled="true" class="table-input-disabled total"></a-input>
                        </a-table-column>

                        <a-table-column :title="i18n.t('gl.debit')" align="center" header-align="center" width="23%">
                            <template #default="{record}">
                                <a-form-item>
                                    <a-input-number
                                        v-model:value="record.totalDebit"
                                        :precision="2"
                                        :controls="false"
                                        class="total-background"
                                        :disabled="true"
                                    ></a-input-number>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column :title="i18n.t('gl.credit')" align="center" header-align="center" width="23%">
                            <template #default="{record}">
                                <a-form-item>
                                    <a-input-number
                                        v-model:value="record.totalCredit"
                                        :precision="2"
                                        :controls="false"
                                        class="total-background"
                                        :disabled="true"
                                    ></a-input-number>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column key="operation" title="" align="center" width="5%"> </a-table-column>
                    </a-table>
                    <a-table
                        :dataSource="[form]"
                        style="width: 100%"
                        size="small"
                        :pagination="false"
                        :showHeader="false"
                        class="total-diff-wrapper"
                        v-if="!formLoading && (current === 1 || current === 2)"
                    >
                        <a-table-column
                            :title="'GL Account'"
                            align="center"
                            header-align="center"
                            width="23%"
                            :ellipsis="true"
                        >
                        </a-table-column>

                        <a-table-column
                            :title="'Name'"
                            align="center"
                            header-align="center"
                            width="23%"
                            :ellipsis="true"
                        >
                            <a-input value="Difference" :disabled="true" class="table-input-disabled total"></a-input>
                        </a-table-column>

                        <a-table-column :title="i18n.t('gl.debit')" align="center" header-align="center" width="23%">
                            <template v-if="current != 1">
                                <a-form-item>
                                    <a-input-number
                                        :value="differenceComputed(form.totalDebit, totalAR)"
                                        :precision="2"
                                        :controls="false"
                                        class="diff-background"
                                        :disabled="true"
                                    ></a-input-number>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column :title="i18n.t('gl.credit')" align="center" header-align="center" width="23%">
                            <template v-if="current != 2">
                                <a-form-item>
                                    <a-input-number
                                        :value="differenceComputed(form.totalCredit, totalAP)"
                                        :precision="2"
                                        :controls="false"
                                        class="diff-background"
                                        :disabled="true"
                                    ></a-input-number>
                                </a-form-item>
                            </template>
                        </a-table-column>

                        <a-table-column key="operation" title="" align="center" width="5%"> </a-table-column>
                    </a-table>
                </div>
            </div>
        </a-form>
    </a-spin>
</template>
<style scoped lang="scss">
.steps-content {
    padding: 24px 0;

    &:first-child {
        padding-top: 0;
    }

    :deep(.ant-col.ant-form-item-label) {
        height: 31.5px;
    }

    :deep(.ant-row.ant-form-item) {
        margin: 0;
        .ant-col.ant-form-item-control .table-input {
            width: 100%;
        }
    }

    .ant-picker {
        border-radius: 15px;
    }
}
.table-header-title {
    margin-bottom: 20px;

    .total-num {
        border-radius: 15px;
        color: #004fc1;
        background-color: #e4ecf8;
        border: 1px solid #adc6eb;
    }
}
.total-wrap {
    height: 30px;

    :deep(.ant-col) {
        text-align: center;
    }
}
.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}
.table-input {
    background-color: #f5f7f9;
    border-color: #f5f7f9;
    border-radius: 4px;
    padding-left: 4px;
    padding-right: 4px;
    &.ant-input-number {
        padding-left: 0px;
        padding-right: 0px;
        :deep(.ant-input-number-input-wrap input) {
            padding-left: 4px;
            padding-right: 4px;
        }
    }
    &.ant-select {
        padding-left: 0px;
        padding-right: 0px;
        :deep(.ant-select-selector) {
            padding-left: 4px;
            padding-right: 4px;
            .ant-select-selection-search {
                left: 4px;
            }
        }
    }
    &.ant-input:hover,
    &.ant-input-number:hover,
    &.ant-input:focus,
    &.ant-input-focused,
    &.ant-input-number-focused {
        border-color: #216fcf;
    }
    :deep(.ant-select-selector) {
        background-color: #f5f7f9 !important;
        border-color: #f5f7f9;
    }
}
.table-input-disabled {
    color: #000;
    background-color: #fff;
    border-color: #fff;
    box-shadow: none;
    cursor: not-allowed;
    text-align: center;
    &.ant-input.ant-input-disabled {
        color: #000;
        background-color: #fff;
        border-color: #fff;
        box-shadow: none;
        cursor: not-allowed;
        text-align: center;
    }
}
.total-diff-wrapper {
    .total {
        font-weight: bold;
        font-size: 16px;
    }
    .total-background {
        width: 100%;
        text-align: center;
        border-radius: 5px;
        color: #004fc1;
        background-color: #e4ecf8;
        border: 1px solid #adc6eb;
        margin-top: 5px;
    }
    .diff-background {
        width: 100%;
        text-align: center;
        border-radius: 5px;
        color: #ff372e;
        background-color: #ffefee;
        border: 1px solid #ffc1c2;
        margin-top: 5px;
    }
    :deep(.ant-input-number-input) {
        text-align: center;
    }
    :deep(.ant-table-tbody > tr > td) {
        border-bottom: 0;
    }
}

:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -10px;
    font-size: 12px;
    line-height: 12px;
    min-height: 12px;
    display: none;
}
</style>
