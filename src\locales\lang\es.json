{"login": {"title": "titulo", "logIn": "In<PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON>", "english": "Inglés", "chinese": "chino", "language": "Idiomas", "username": "Nombre de usuario", "password": "Contraseña", "msgInputUsername": "Por favor, introduzca el nombre de usuario", "msgInputPassword": "Por favor, introduzca la contraseña", "msgInputAccount": "Por favor, introduzca la cuenta", "serverError": "Error del servidor", "changePwd": "Restablecer contraseña", "gQr": "Autenticador de Google", "newpassword": "Contraseña", "repassword": "Vuelva a ingresar la contraseña", "pwdRule": "Debe incluir al menos 1 mayúscula, 1 minúscula, 1 number, 1 carácter especial", "pwdRule1": "Por favor, introduzca los caracteres sin espacios", "inconsistentPassword": "Contraseña incoherente", "repasswordInput": "Por favor, vuelva a introducir la contraseña"}, "mainPage": {"noLogin": "No ha iniciado sesión"}, "homepage": {"times1": "Nómina digital'", "times2": "Versión de administración", "title": "Comience a pagar fácilmente a los empleados, en línea con nosotros.", "info": "La aplicación NT Digital Payroll ofrece una cobertura completa desde la organización hasta la nómina, un proceso hiperautomatizado permite la operación comercial central para maximizar el valor de su capital humano.", "titleBk1": "FUNCIONAMIENTO ESENCIAL", "titleBk2": "titulo bk2", "titleBk": "Contabilidad hiperautomatizada", "infoBk": "Nuestra solución financiera está hecha para las pequeñas empresas de América del Norte, ya sea que sea propietario de una pequeña empresa o trabajador por cuenta propia, hacemos que sea fácil mantener sus finanzas mejor organizadas. Por lo tanto, puede tomar mejores decisiones empresariales, centrarse en el crecimiento y eliminar el estrés del tiempo de administración y cumplimiento.", "link": "Learn more"}, "router": {"uploadInvoice": "Proceso masivo", "woBillsInvoice": "Reserva manual", "invoiceFromPdf": "Factura desde pdf", "invoiceHistory": "Listado", "accountReceivable": "Ventas", "fullInvoice": "Reserva manual", "bankReconciliation": "Conciliación Bancaria", "uploadStatement": "Conéctate", "EStatementFromPdf": "Declaración electrónica de CSV", "main": "Reconciliar", "common": "Ambientación", "customer": "Contacto", "taxInformation": "Perfil", "bankInformation": "Banco", "accountDescription": "CoA", "taxCalculation": "Tabla de impuestos", "spotCurrency": "Moneda al contado", "exchangeRate": "Tipo de cambio", "supplier": "<PERSON><PERSON><PERSON><PERSON>", "payStubs": "Recibos de pago", "history": "Historia", "commonCompany": "Empresa", "commonAccount": "C<PERSON><PERSON>", "gl": "Libro Mayor", "glListing": "Listado", "glEntry": "Entrada manual", "fy": "Inicio del ejercicio", "localCurrency": "Moneda local"}, "commonTag": {"tip": "<PERSON><PERSON>", "sapTip": "Sincronización con SAP", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "search": "Buscar", "new": "<PERSON><PERSON><PERSON>", "save": "Guardar", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "action": "Operación", "actions": "Acciones", "view": "Vista", "remark": "Observación", "back": "Atrás", "close": "<PERSON><PERSON><PERSON>", "serial": "Número de serie", "status": "Estado", "submit": "Enviar", "send": "Enviar", "resend": "Reenviar", "download": "<PERSON><PERSON><PERSON>", "sendEmail": "Enviar correo electrónico", "columns": "Columnas", "filter": "Filtro", "msgInput": "Por favor, ingrese", "msgSelect": "Por favor, seleccione", "reverse": "Al revés", "reverseAndRedo": "Reverse & Redo", "realize": "Realizar", "print": "imprimir", "wihtreverse": "Sin reversa", "enable": "Habilitar", "disable": "Desactivar"}, "columns": {"modalTitle": "Columnas", "title": "<PERSON><PERSON><PERSON><PERSON>", "operation": "Operación"}, "bkApInvoice": {"to": "para", "readonly": "Ver detalle de la factura", "create": "<PERSON><PERSON><PERSON> factura", "edit": "<PERSON><PERSON>", "date": "<PERSON><PERSON> ", "totalCol": "Total ", "minFee": "De", "maxFee": "Para", "invoiceNo": "Número de factura", "invoiceComment": "Comentarios", "creator": "<PERSON><PERSON><PERSON>", "createTime": "<PERSON><PERSON><PERSON> tiempo", "createDate": "Fecha de creación", "issuerCol": "<PERSON><PERSON><PERSON> ", "invoiceType": "Tipo de factura", "issuer": "<PERSON><PERSON><PERSON>", "total": "Total de", "type": "Tipo", "br": "BR", "status": "Estado", "payMethod": "Método de pago", "sapStatus": "SAP Estado", "sapNotSent": "NO ENVIADO", "sapPending": "APARCADO", "sapSending": "ENVÍO", "sapSentSuccess": "ENVIADO EXITOSO", "sapSentFail": "ERROR DE ENVÍO", "sapReversing": "REVIRTIENDO", "sapReverseSuccess": "RESVERSE SUCCESS", "sapReverseFail": "FALLO DE REVERSO", "referenceNo": "Referencia", "dueDate": "<PERSON><PERSON>nc<PERSON>o", "postingDate": "<PERSON><PERSON> la factura", "balance": "Equilibrio", "operation": "Acción", "checkNo": "Marque No", "checkPrintTime": "Tiempo de ejecución", "printStatus": "Estado de impresión", "printStatus0": "No impreso", "printStatus1": "Impreso", "brStatus0": "impago", "brStatus": "Todos", "brStatus2": "Abrir", "submitToPay": "ENVIAR A PAGAR", "payInProcess": "PAGO EN PROCESO", "toBeReconciled": "PARA CONCILIAR", "parked": "aparcado", "posted": "Publicada", "notPaid": "No se paga", "pmntApproved": "PMNT Aprobado", "pmntExecuted": "PMNT Ejecutado", "paid": "pagado", "partialPaid": "Pago parcial", "reversed": "Invertida", "captured": "<PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "scan": "Escanear", "fetch": "Buscar", "upload": "subir", "del": "Eliminar", "due": "pendiente", "level": "<PERSON><PERSON>", "emailList": "Lista de correo electrónico", "approvalStatus": "Estado", "statusPending": "Pendiente", "statusApproved": "Aprobado", "statusRejected": "<PERSON><PERSON><PERSON><PERSON>", "printApCheckWarning": "Su pago ha sido ejecutado, por favor compruébelo.", "issueNameEmpty": "Por favor, introduzca el nombre del emisor"}, "bkApUpload": {"fileName": "Nombre del archivo", "updateTime": "Tiempo de actualización", "createTime": "<PERSON><PERSON><PERSON>", "creator": "<PERSON><PERSON><PERSON>", "xmlStatus": "XML Estado", "payMethod": "Método de pago", "comment": "Comentario", "ocrStatus": "Estado de OCR", "scanned": "Escaneado", "nonScanned": "No escaneado", "pending": "Pendiente", "delFile": "Eliminar archivo", "edit": "<PERSON><PERSON>", "createFile": "<PERSON><PERSON><PERSON> factura", "analyzeFile": "<PERSON><PERSON>zar archivo", "viewDetail": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "downloadInvoice": "<PERSON><PERSON><PERSON>", "editComment": "<PERSON><PERSON> comenta<PERSON>", "editCommentPlaceholder": "Por favor, escriba un comentario"}, "bkCustomer": {"company": "Empresa", "tel": "Tel.", "email": "Correo electrónico", "receiver": "Receptor (Oficina)", "receiver01": "Persona de Contacto", "address": "Dirección", "address01": "Dirección(Oficina)", "street": "Calle", "city": "Ciudad", "province": "Provincia", "country": "<PERSON><PERSON>", "postalCode": "Código postal", "expenseAccount": "Cuenta de Gastos", "operation": "Acción", "createReceiverTitle": "Crear cliente", "createAllReceiverTitle": "<PERSON><PERSON><PERSON> contacto", "accountType": "Tipo", "editReceiverTitle": "Editar datos de contacto", "msgPhrSelect": "Por favor, seleccione", "officeAddress": "Dirección de la oficina", "shippingAddress": "dirección de envío", "billingAddress": "Dirección de facturación", "general": "Generalidades", "sameAsOffice": "Igual que la oficina", "itemNo": "#", "category": "Categoría", "businessKey": "Clave de Negocio", "debitReceipt": "Recibo de débito", "creditReceipt": "Recibo de crédito", "save": "Guardar", "caterogyRule": "Por favor, seleccione la categoría", "coaJson": "Código de mapeo"}, "bkAccountingAndBanking": {"bpNo": "Número de socio comercial", "bankType": "Método de pago", "countingAndBanking": "Banca", "transitNumber": "Número <PERSON>", "branchNumber": "Número de sucursal", "bankAccount": "Cuenta Bancaria", "receiverName": "Número de cuenta", "routingNumber": "Número de ruta", "IBANNumber": "IBAN Número", "swiftNumber": "SWIFT Número"}, "bkAp": {"invoiceHeader": "Encabezado de la factura", "companyName": "Emitido por", "companyAddr": "Dirección de la empresa", "companyTel": "Empresa Tel.", "companyEmail": "Correo electrónico de la empresa", "companyGst": "GST No.", "companyQst": "QST No.", "companyGstHst": "GST/HST", "companyPst": "PST (QST/--)", "invoiceNo": "Factura No.", "referenceNo": "Referencia de la factura", "purpose": "Objetivo", "purposeStandard": "<PERSON><PERSON><PERSON><PERSON>", "purposeProforma": "<PERSON><PERSON><PERSON>", "purposeCreditMemo": "Nota de crédito", "purposeSubsequentCreditMemo": "Nota de crédito posterior", "poPlaceholder": "Elige el propósito primero", "purchaseOrder": "Orden de compra", "currency": "Moneda", "date": "Fecha de creación", "dueDate": "<PERSON><PERSON>nc<PERSON>o", "fixedDate": "<PERSON><PERSON> fija", "afterDays": "Después de días", "customerInfo": "Información del cliente", "billToCompany": "Facturar a la empresa", "billTo": "Facturar a", "billToReceiver": "Factura al receptor", "billingStreet": "Factura a la calle", "billToCity": "Factura a la ciudad", "billToProvince": "Proyecto de ley a la provincia", "billToZip": "Facturar a codigo", "billToTel": "Facturar al teléfono.", "billToEmail": "Facturar por correo electrónico", "shipToSameAddr": "Enviar a la misma dirección", "shipToCompany": "Enviar a la empresa", "shipToReceiver": "Enviar al destinatario", "shipToStreet": "Enviar a la calle", "shipToCity": "Enviar a la ciudad", "shipToProvince": "Enviar a la provincia", "shipToZip": "Enviar a Zip", "shipToTel": "Enviar al Tel.", "shipToEmail": "Enviar a correo electrónico", "itemsDetail": "Detalle de los artículos", "itemNo": "numero de factura#", "modelNumber": "PRODUCTO/SERVICIO", "description": "DESCRIPCIÓN", "payMethod": "Método de pago", "addPayMethod": "Agregar método de pago", "qty": "QTY", "unitPrice": " PRECIO UNITARIO", "total": "IMPORTE NETO", "type": "Tipo", "bankAccount": "Cuenta Bancaria", "trevenueAccount": "Cuenta de Ingresos", "accountingCategory": "G/L CUENTA", "amountAndTax": "Monto e impuestos", "amount": "NET IMPORTE", "amountRule": "Importe", "shipping": "Envío", "discount": "Descuento", "totalTaxable": "Total Imponible", "tps": "GST/HST", "tvq": "QST", "tvp": "PST", "mxDiscount": "Descuento", "mxIsr": "ISR Retenidos", "mxIva": "VAT Retenidos", "totalTax": "Impuesto Total", "totalCad": "Total(CAD)", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Equilibrio", "accountAndType": "Cuenta y tipo", "invoiceComment": "Comentarios", "createArInvoice": "Crear factura AR", "viewInvoice": "Ver Factura", "preview": "Vista previa", "postingDate": "<PERSON><PERSON> la factura", "apIntegrationPostingDate": "Fecha de publicación", "create": "Guardar", "totalFee": "Total", "msgReferenceNoExisted": "¡El número de referencia ya existe!", "msgReferenceCheckFail": "Nº de referencia Error en la comprobación de repetición. ¡Por favor, inténtalo de nuevo!", "from": "<PERSON><PERSON>", "addItemBtnTxt": "<PERSON><PERSON><PERSON>", "addItemBtnTxtInvoice": "Agregar aprobación de factura", "addItemBtnTxtPayment": "Agregar aprobación de pago", "msgCreditMemoInvoiceBeNegative": "El monto de una factura de tipo Nota de Crédito debe ser negativo.", "paymentDelayDate": "Fecha de pago", "paymentDelaySelect": "Seleccione Plazo de Pago", "originalDocument": "Documento Original", "costObject": "Objeto de costo", "taxCode": "Código <PERSON>", "assigmentSapCostObject": "<PERSON><PERSON><PERSON><PERSON> coste (SAP)", "assigmentGlAccount": "Cuenta de mayor", "assigmentWbs": "WBS", "assigmentCostCenter": "Centro de costos", "assigmentInternalOrder": "Internal Order", "assigmentProfitCenter": "Centro de beneficios", "paymentExportColumnOperation": "OPERACIÓN", "paymentExportColumnIssuerName": "ALIAS / DESTINATARIO", "paymentExportColumnIsserId": "CUENTA DESTINO", "paymentExportColumnImport": "IMPORTE", "paymentExportColumnReferenceNo": "REFERENCIA NUMÉRICA", "paymentExportColumnInvoiceComments": "REFERENCIA / CONCEPTO", "paymentExportColumnReference": "REFERENCIA", "paymentExportColumnInvoiceCurrency": "DIVISA", "paymentExportColumnIva": "IVA", "paymentExportColumnDestinationRfc": "RFC DESTINATARIO", "paymentExportColumnBankAccount": "CUENTA ORIGEN"}, "bkCommonTag": {"confirmation": "Confirmación", "msgDeleteConfirm": "Los datos serán eliminados!", "msgRequireRule": "Por favor, introduzca ", "msgLengthRule": "La longitud debe ser ", "msgEmailRule": "Por favor, introduzca el correo electrónico correcto", "msgSelectRule": "Por favor, seleccione ", "msgDeleteSelectConfirm": "¡Los archivos pdf seleccionados se eliminarán!", "msgNumberRule": "Por favor, introduzca un número válido"}, "bkArInvoice": {"to": " ", "readonly": "Ver detalle de facturación", "create": "Crear facturación", "date": "<PERSON><PERSON> ", "totalCol": "Total", "minFee": "<PERSON><PERSON>", "maxFee": "Para", "billingType": "Tipo de facturación", "billingTypeInvoice": "Factura", "billingTypeCreditNote": "Nota de crédito", "billingTypeDebitMemo": "Nota de d<PERSON>o", "invoiceNo": "Facturación #", "invoiceComment": "Comentarios", "creator": "<PERSON><PERSON><PERSON>", "createTime": "<PERSON><PERSON><PERSON>", "createDate": "Fecha de creación", "issuerCol": "<PERSON><PERSON><PERSON> ", "issuer": "Facturar a", "total": "Total", "type": "Tipo", "br": "Estado", "referenceNo": "Referencia", "dueDate": "<PERSON><PERSON>nc<PERSON>o", "postingDate": "Fecha de facturación", "balance": "Equilibrio", "operation": "Acción", "payerPayee": "Emparejando/obteniendo", "brStatus0": "<PERSON>bie<PERSON>o", "brStatus1": "<PERSON><PERSON> parcialmente", "brStatus2": "Todos", "brStatus3": "Todos", "sapStatus": "Estado de la reserva", "search": "Buscar facturara", "billing": "Facturación personalizada", "bill2Customer": "Facturación al cliente", "pb": "Reserva Periódica", "billingNumber": "Facturación No", "referenceNumber": "No de referencia", "currency": "Divisa", "paymentDue": "<PERSON><PERSON>", "postDate": "Fecha de publicación", "billingDate": "Fecha de facturación", "originalBillingNumber": "Número de Facturación Original", "due": "Pendiente", "uuid": "UUID"}, "workTimeManager": {"projectName": "Nombre del proyecto: ", "msgInput": "Por favor, introduzca", "applicant": "Empleado: ", "applicationDate": "<PERSON><PERSON> de trabajo: ", "approvalDate": "Fecha de aprobación: ", "status": "Estado: ", "memberNature": "Naturaleza del personal: ", "company": "Compañía: ", "export": "Exportar", "workingHourCode": "Número de orden de horas de trabajo", "applicantName": "Nombre del solicitante", "applicantPhone": "Número de teléfono móvil del reportero", "consultantType": "Tipo de Consultor", "projectNameIntern": "Nombre del proyecto", "wbs": "WBS", "whMonths": "Mes de Horas de Trabajo", "totalWHPerDay": "Total de horas de trabajo/día", "preTotalIncome": "Ingresos totales estimados", "applicationTime": "Tiempo de trabajo", "approvalTime": "tiempo de proceso", "emailApprover": "Aprobador", "approvalStatus": "Estado de aprobación", "passed": "Pasado", "toBeSubmitted": "Para enviar", "toBeApproved": "Pendiente", "failed": "No se pudo aprobar", "abandoned": "<PERSON><PERSON><PERSON>", "canceled": "¡Vacío", "msgCancelReimburseInfo1": "Después de ser invalidado, el formulario de informe de tiempo dejará de ser válido y el sistema enviará automáticamente un correo electrónico de notificación no válido a", "msgCancelReimburseInfo2": "and the person in charge of approval. This Operation is irreversible, confirm to continue?", "cancelReimburse": "Formulario de informe de tiempo nulo", "msgCancelSuccess": "Desguazado con éxito!"}, "bkAr": {"invoiceHeader": "Encabezado de facturación", "companyName": "Dirigido a:", "companyAddr": "Dirección", "companyTel": "Tel.", "companyEmail": "Correo electrónico", "companyGst": "GST No.", "companyQst": "QST No.", "companyGstHst": "GST/HST", "companyPst": "PST (QST/--)", "invoiceNo": "Facturación No.", "currency": "Divisa", "date": "Fecha de creación", "dueDate": "<PERSON><PERSON>nc<PERSON>o", "fixedDate": "<PERSON><PERSON> fija", "afterDays": "Después de los días", "customerInfo": "Información del cliente", "billToCompany": "Facturar a la empresa", "billToReceiver": "Factura al síndico", "billingStreet": "Calle", "billToCity": "Ciudad", "billToProvince": "Provincia", "billToZip": "Facturar a Zip", "billToTel": "Facturar a Tel.", "billToEmail": "Correo electrónico", "shipToSameAddr": "Enviar a la misma dirección", "shipToCompany": "Enviar a la empresa", "shipToReceiver": "Enviar al destinatario", "shipToStreet": "De barco a la calle", "shipToCity": "Enviar a la ciudad", "shipToProvince": "Enviar a la provincia", "shipToZip": "Enviar a Zip", "shipToTel": "Enviar a Tel.", "shipToEmail": "Enviar a correo electrónico", "itemsDetail": "Detalle de los artículos", "itemNo": "#", "modelNumber": "PRODUCTO/SERVICIO", "to": "para", "description": "DESCRIPCIÓN", "accountingCategory": "CATEGORÍA CONTABLE", "payMethod": "Forma de pago", "qty": "QTY", "unitPrice": "PRECIO POR UNIDAD", "total": "Importe neto", "type": "Tipo", "bankAccount": "Cuenta bancaria", "amountAndTax": "Monto e impuestos", "amount": "IMPORTE NETO", "amountRule": "Cantidad", "shipping": "envio", "discount": "Descuento", "totalTaxable": "Total Imponible", "tps": "GST/HST", "tvq": "QST", "tvp": "PST", "totalTax": "Impuesto Total", "totalCad": "Total(CAD)", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Equilibrar", "accountAndType": "Cuenta y tipo", "invoiceComment": "Comentarios", "createArInvoice": "Crear facturación de cuentas por cobrar", "viewInvoice": "Ver facturación", "preview": "Vista previa", "create": "Guardar", "postingDate": "Fecha de facturación", "totalFee": "Total", "msgReferenceNoExisted": "¡El número de referencia ya existe!", "msgReferenceCheckFail": "Referencia No. Error en la comprobación de repetición. ¡Por favor, inténtalo de nuevo!", "from": "<PERSON><PERSON>", "addItemBtnTxt": "Agre<PERSON><PERSON><PERSON>", "billingDoc": "Doc de facturación", "referenceNo": "Referencia de facturación"}, "esUpload": {"bankAccount": "Cuenta bancaria", "bankMonth": "Período de estado de cuenta", "buttonSubmited": "<PERSON>ado", "buttonEdit": "<PERSON><PERSON>"}, "bankInfo": {"bankName": "Banco", "bankAccount": "Cuenta Bancaria", "accountType": "Tipo de Banco", "currency": "Moneda", "coa": "CoA", "operation": "Acción", "createBankTitle": "Crear un nuevo banco", "editBankTitle": "<PERSON>ar da<PERSON> bancar<PERSON>", "msgPhrSelect": "Por favor, seleccione", "authorizaiton": "Autorización", "fetchFrom": "Buscar de"}, "esMain": {"date ": "fecha", "description": "Descripción", "inOutType": "Entrada/Salida", "amount": "cantidad", "bankAccount": "Cuenta bancaria", "statementPeriod": "Período de estado de cuenta", "cashIn": "Cobrar", "cashOut": "Retiro de efectivo", "createCashStatementTitle": "<PERSON><PERSON><PERSON>", "editCashStatementTitle": "Editar el detalle del estado de cuenta", "cashStatement": "Estado de Caja", "withdrawal": "retirada", "withdraw": "retirarse", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "postingDate": "Fecha de publicación", "reference": "Referencia", "payerPayee": "pagadora/beneficiaria ", "debit": "<PERSON><PERSON><PERSON><PERSON>", "debitManu": "Débito", "credit": "Re<PERSON><PERSON>", "creditManu": "<PERSON><PERSON><PERSON><PERSON>", "sendSapStatus": "SAP Estado", "viewInvoice": "Ver factura", "invoiceDetail": "Detalle de la factura", "balance": "Equilibrar", "chargeFee": "Cargo/Tarifa", "chargeCoa": "Cargo CoA", "reasonMsg": "Código de motivo", "currency": "Moneda", "brType": "Tipo", "transactionDate": "Fecha de la transacción", "delWarn": "La transacción no se puede recuperar después de eliminarla.", "reconcileGL": "Reconciliar G/L", "autoReconcile": "Auto Reconcile", "autoReconcileOn": "Auto Reconcile On", "autoReconcileOff": "Auto Reconcile Off"}, "chartOfAccount": {"coa": "CoA", "btnLoadCoa": "Cargar repositorio de CoA", "btnReloadCoa": "Cerrar repositorio de CoA", "fieldCode": "Código de campo", "account": "Código de cuenta", "accountDes": "Descripción", "alias": "Nombre de la cuenta", "operation": "Acción", "fullCoa": "CoA Depósito", "createCoaTitle": "Crear plan de cuentas", "editCoaTitle": "Editar Plan de Cuenta", "msgCoaDelete01": "Cuenta con descripción: ", "msgCoaDelete02": " ¡Se eliminará!", "msgCoaPause01": "Cuenta con descripción: ", "msgCoaPause02": " se desactivará!", "msgCoaActive01": "Cuenta con descripción: ", "msgCoaActive02": " se reactivará!", "yes": "si", "no": "No", "groupName": "Grupo"}, "taxCalculation": {"msgWarning": "El método de cálculo de la tasa impositiva proporcionado automáticamente por el sistema es solo para un uso conveniente y no garantiza una precisión completa. Los usuarios deben revisar manualmente la precisión del cálculo del tipo de cambio durante el proceso de emisión de facturas.", "provinceName": "Nombre de la provincia", "provinceCode": "Código de Provincia", "applicableSalesTax": "Impuesto sobre las ventas aplicable", "total": "Total"}, "spotCurrency": {"msgWarning": "El tipo de cambio publicado por el banco.", "date": "fecha", "currency": "Divisa(USD/CAD)", "operation": "Acción", "createTitle": "Crear tipo de cambio", "editTitle": "Editar tipo de cambio", "selectTime": "Por favor, seleccione la fecha", "msgInputRate": "Por favor, introduzca el tipo de cambio"}, "connectivity": {"sourceHolder": "Por favor, ingrese", "source": "<PERSON><PERSON>", "username": "Nombre de usuario", "password": "Contraseña", "startDate": "Fecha de inicio", "endDate": "Fecha de finalización", "powerAutomate": "automatización de energia", "server": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "emailHost": "<PERSON><PERSON><PERSON><PERSON>", "disk_secret": "Secreto", "type": "Tipo", "disk_app_id": "Aplicación ID", "disk_key": "Llave", "application_number": "Aplicación Número", "instance_number": "Número de instancia", "userId": "ID de usuario", "version": "Versión", "receivable_integration": "Integración de cuentas por cobrar", "payable_integration": "Integración de pago", "payment_integration": "Ejecución de pagos", "reconciliation_integration": "Integración de la reconciliación", "fts_integration": "Integración FTS", "sap_instance": "Instancia", "sap_general": "General", "sap_setting": "Configuración", "payable_approval_procedure": "Flujo de aprobación de facturas", "payment_approval_procedure": "Flujo de aprobación de pagos"}, "gl": {"search": "Buscar Narración/Entrada de Diario", "glNo": "#", "narration": "Narración", "module": "<PERSON><PERSON><PERSON><PERSON>", "journalEntry": "Anotación en el Diario", "createDate": "Fecha de creación", "total": "Total", "totalDebit": "Débito total", "totalCredit": "Crédito total", "to": "para ", "readonly": "Detalle G/L", "edit": "Editar G/L", "create": "<PERSON><PERSON><PERSON>", "descriptionxxx": "Descripción,xxx", "post": "Exponer", "date": "<PERSON><PERSON> ", "totalCol": "Total ", "minFee": "De", "maxFee": "Para", "operation": "Acción", "postingDate": "Fecha de publicación", "draftDate": "<PERSON><PERSON>", "currency": "Divisa", "itemNo": "#", "description": "DESCRIPCIÓN", "debit": "DÉBITO", "credit": "CRÉDITO", "saveDraft": "Guardar borrador", "glAccount": "G/L CUENTA", "createStartDate": "De", "createEndDate": "Para", "status": "Estado", "sapStatus": "SAP Estado", "editGl": "Editar G/L", "delGl": "Borrar G/L", "viewGl": "Vista G/L Detalle", "draft": "<PERSON><PERSON><PERSON>", "posted": "Posted", "failure ": "<PERSON><PERSON><PERSON>", "msgTotalDebitCannotNull": "El débito total no puede ser nulo, compruébelo de nuevo", "msgTotalCreditCannotNull": "El crédito total no puede ser nulo, compruébelo de nuevo", "msgNotMatch": "El crédito total no coincide con el débito total, compruébelo de nuevo", "msgNumberLimited": "El rango de números de entrada está limitado a 0-13 dígitos", "msgGlDelete": "Esta cuenta de mayor se borrará!", "transactionCurrency": "Moneda local - ", "status0": "borrador", "status1": "Publicada", "status2": "Invertida", "format": "El formato es incorrecto", "payerAndPayee": "pagador/beneficiario"}, "glEntry": {"totalDebit": "DÉBITO TOTAL", "totalCredit": "CRÉDITO TOTAL", "msgAtLeastOne": "G/L debe contener al menos un [ Artículo ]", "transactionCurrency": "Moneda local - "}, "bkSupplier": {"company": "Compañía", "tel": "Tel", "email": "Correo electrónico", "receiver": "<PERSON><PERSON><PERSON><PERSON>", "receiver01": "Receptor(Office)", "supplierAddr": "Persona de contacto", "address": "Dirección", "address01": "Dirección(Oficina)", "street": "Calle", "city": "Ciudad", "province": "Provincia", "country": "<PERSON><PERSON>", "postalCode": "Código Postal", "expenseAccount": "Cuenta de Gastos", "operation": "Acción", "createReceiverTitle": "<PERSON><PERSON><PERSON> proveedor", "editReceiverTitle": "Editar datos de contacto", "msgPhrSelect": "Por favor, seleccione", "sameAsOffice": "Igual que la oficina"}, "taxInfo": {"companyLogo": "Logotipo", "companyName": "Nombre", "companyAddress": "Dirección", "companyEmail": "Correo electrónico", "companyPhone": "Tel.", "gstNo": "GST / HST No.", "qstNo": "QST / PST No.", "limit": "Nota: solo se pueden cargar archivos jpg / png, no más de 2M.", "editLogoBtn": "Editar logotipo", "rmLogoBtn": "Eliminar logotipo"}, "account": {"userId": "C<PERSON><PERSON>", "password": "Contraseña", "cate": "Categoría", "role": "Rol", "add": "Agregar usuario"}, "taxRates": {"countryNotSet": "Establezca el país y/o la provincia del proveedor (o cliente)"}, "ApComponents": {"notpaid": "NO PAGADO", "bank": "Cuenta de Débito Bancaria", "credit": "Tarjeta de crédito", "cashpaid": "Efectivo", "check": "Cheque", "confirm": "Confirm Reversing Date", "confirm2": "Esta operación también revertirá la conciliación.", "confirm3": "Por favor, invierta primero la conciliación bancaria", "referenceError": "No de referencia duplicado", "contactFirstUsing": "El nuevo contacto necesita actualizar la información bancaria.", "atleast": "La factura debe contener al menos un [ Artículo ]", "mustone": "La factura debe ser solo un [ Artículo ]", "lackNetAmount": "No se puede crear una factura sin [ Importe neto ]", "notEqual": "El importe neto más impuestos no es igual al total", "success": "success", "referenceNo": "Referencia No. en los recibos.", "spotCurrency": "La moneda al contado es {tasa} el {fecha}", "contactAdmin": "Por favor, póngase en contacto con el administrador", "NewBP": "Agregar nuevo socio comercial", "subtotal": "Subtotal Impositivo", "taxableSubtotal": "Subtotal imponible", "total": "TOTAL", "difference": "Diferencia", "drag": "Arrastre el archivo aquí o haga clic en Cargar", "uploading": "<PERSON><PERSON>", "sizeExceed": "Nota: límite de tamaño 10Mb por archivo.", "MEAT": "METACAMPOS", "oriDoc": "Documento Original", "download": "<PERSON><PERSON><PERSON>", "auto": "Modo automá<PERSON>", "exempt": "Exento de impuestos", "amount": "IMPORTE TOTAL", "netAmount": "Importe Neto", "GST": "GST/HST", "QST": "QST", "PST": "PST", "search": "Buscar emisor", "submitToPay": "Enviar a pago", "pay": "<PERSON><PERSON>", "repay": "<PERSON><PERSON>", "uploadConfirm": "¡El documento original será reemplazado!", "wbs": "WBS", "costCenter": "Centro de costos", "costObject": "Objeto de costo", "internalOrder": "Orden Interno", "profitCenter": "Centro de beneficio", "payableGL": "Pagable G/L", "testGetPgoTokenSuccess": "Obtener el éxito del token Pgo", "testGetPgoTokenFail": "Obtener el token Pgo falló"}, "ArComponents": {"inavailable": "El archivo de la factura no está disponible.", "notExist": "El cliente no existe, ¿desea agregar un nuevo cliente?", "postingDt": "Fecha de publicación", "billTo": "Facturar a", "shipTo": "Enviar a", "netAmount": "Importe Neto", "shipping": "Envío", "discount": "Discout", "GST": "GST/HST", "QST": "QST", "PST": "PST", "USD": "TOTAL USD", "CAD": "TOTAL CAD", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Equilibrio", "JE": "Anotaciones en el Diario", "SAP": "SAP Documento", "file": "Archivo de Facturas", "download": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "convert": "Pago en efectivo", "confirmConvert": "La factura original se eliminará y no se podrá recuperar"}, "PeriodicalBooking": {"notFound": "¡articulo no encontrada!"}, "EstatementTable": {"selectBank": "Por favor, seleccione el banco", "required": "Campo Cuenta bancaria obligatorio", "inovice": "Factura", "fetch": "<PERSON><PERSON><PERSON>", "upload": "Subir", "acceptAll": "<PERSON><PERSON><PERSON> todo", "autoOn": "La conciliación automática está activada. Haga clic en él para aceptar la conciliación.", "autoOff": "La conciliación automática está desactivada. Haga clic en ella para habilitar la conciliación automática", "reject": "Haga clic en él para rechazar la conciliación automática recomendada y navegue a la página de conciliación manual", "RP": "RP [Compra Regular]", "RS": "RS [Ventas Regulares]", "PR": "PR [Reembolso de Compra]", "SR": "SR [reembolso de ventas]", "FT": "FT [Financiación Transl]", "FX": "FX [Bolsa de Financiación]", "PY": "PY [Nómina]", "BC": "EE [Entrada Exprés]", "SP": "SP [Raya]", "PP": "PP [Prepayment]", "BCMessage": "La factura se creará y conciliará, seleccione el proveedor (o cliente) y la cuenta de mayor.", "GLMessage": "Por favor, elija <PERSON> y Categoría de Contabilidad.", "integrationEEbrTitle": "Entrada Rápida", "account": "Cuenta Bancaria", "cancel": "<PERSON><PERSON><PERSON>", "OK": "OK", "uploadES": "Cargar archivo de estado de cuenta electrónico", "reconcileAll": "Conciliar todo", "payoutIdMsgInput": "Por favor, introduzca payoutId", "confirm": "Confirmar", "updateChargeFee": "Cargo/Tarifa"}, "RcInfoTable": {"account": "Cuenta Bancaria", "date": "Fecha de pago", "desc": "Descripción", "debit": "Débito", "credit": "<PERSON><PERSON><PERSON><PERSON>", "balance": "Balance", "invoiceNo": "No de factura.", "comments": "Comentarios", "paymentDt": "Fecha de pago", "fee": "Tarifa Total", "amount": "Monto BR", "brDoc": "Documento de Pago"}, "CustomerForm": {"USA": "Código postal de EE. UU.: 12345 or 12345-1234", "CA": "Código postal canadiense: H1H 2H2"}, "PyComponents": {"total": "Total"}, "FileUploadResult": {"failed": "Error en la carga de archivos. Por favor, inténtelo de nuevo."}, "UploadFileComp": {"notPaid": "Compra no pagada", "cash": "Compra en efectivo", "reconcile": "conciliar", "uploadLimit": "Límite de carga de archivos", "select": "Por favor, seleccione el archivo", "perfileLimit": "Para el límite de tamaño del archivo", "uploadFile": "por favor, cargue el archivo {tipo}", "success": "éxito de carga", "error_two_files": "Debes cargar dos archivos al mismo tiempo, incluido un archivo XML", "retry": "Errores de carga [{err}]. Por favor, vuelva a intentarlo", "selectBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "Tipo", "note": "Nota: límite de tamaño {fileSize}Mb por archivo. {fileLimit} archivo(s) al mismo tiempo."}, "mainLayout": {"setFY": "<PERSON><PERSON><PERSON> mon<PERSON> y año financiero.", "createBP": "Crear socios comerciales.", "createChart": "Crear plan de cuentas.", "fillin": "<PERSON><PERSON>ar el saldo inicial.", "careteBank": "<PERSON><PERSON><PERSON>.", "setup": "Por favor, siga unos pasos para configurar su empresa"}, "userGuide": {"setFY": "<PERSON><PERSON><PERSON> mon<PERSON> y año financiero.", "createBP": "Crear socios comerciales.", "createChart": "Crear plan de cuentas.", "fillin": "<PERSON><PERSON>ar el saldo inicial.", "careteBank": "<PERSON><PERSON><PERSON>"}, "userPage": {"selectUser": "Debe seleccionar un usuario", "success": "éxito", "error": "error", "companyWithDesc": "Empresa con Descripción ", "companyCd": "Código de empresa", "companyNm": "Nombre de la empresa", "AddAll": "Agregar toda la empresa", "cdnm": "Código/Nombre de la empresa"}, "ApInvoiceFormPdf": {"invoice": "fichero de facturas", "fetch": "Obtener información", "notPaid": "NO PAGADO", "partialPaid": "PAGADO PARCIALMENTE", "paid": "<PERSON><PERSON>", "paidupcase": "PAGADO", "reversed": "Invertido"}, "ApUploadInvoice": {"upload": "Subir archivo de factura", "OCR": "Procedimiento de análisis de OCR", "file": "Archivo seleccionado", "fillError": "El archivo de la factura no está disponible.", "analizando": "<PERSON><PERSON><PERSON><PERSON> ( a través de {tipo} ) ...", "error": "Error", "confirme": "Confirmar"}, "ArInvoiceHistory": {"invoice": "El archivo de facturas no está disponible", "Enviar factura": "enviar"}, "ReconciliationDetails": {"title": "Detalle de conciliación bancaria", "invoiceNo": "Factura #", "Seleccionar": "Por favor, seleccione la factura", "search": "<PERSON><PERSON>", "totals": "Totales amt ${amt}, Amt seleccionado ${amt2}, diff. ${amt3}.", "reconcile": "conciliar", "manual": "Conciliación manual", "manualIntegration": "Conciliación manual para la integración", "Payarandpai": "Buscar guisante/Payi"}, "AccountDescription": {"importAll": "¿Desea importar todos los Coa del grupo seleccionado?", "add": "¿Desea agregar una nueva subcategoría?", "primary": "primario", "create": "Por favor, cree un plan de cuentas.", "step1": "Paso 1: Introducir información", "next": "Siguiente paso", "step2": "Paso 2: <PERSON>ga clic en el botón", "step3": "Paso 3: <PERSON><PERSON> clic en el enlace Seleccionar", "got": "Entendido", "importGroup": "Grupo de importación", "pause": "pausa", "active": "Activo", "copy": "Copiar", "cdnm": "Código de cuenta o nombre"}, "bankInformation": {"BOM": "Banco de Montreal", "CIBC": "Banco Imperial Canadiense de Comercio", "NBC": "Banco Nacional de Canadá", "RBC": "Banco Royal de Canada", "Desjardins": "Desjardins Bank", "TDB": "Toronto-Dominion Bank", "ASPIRE": "Banco Aspire", "NOVA": "Nueva Escocia", "AMEX": "American Express", "ICBC": "Industrial And Commercial Bank Of China", "BONJ": "Bank of NanJing", "SANTANDER": "Santander Bank", "BASEINET": "BASEinet", "MONEX": "Monex Bank", "BBVA": "BBVA Bank", "email": "Por favor, configura tu correo electrónico.", "create": "Por favor, cree bancos"}, "ContactCustomer": {"create": "Por favor, cree socios comerciales.", "got": "Entendido"}, "SettingConnectivities": {"importBilling": "Facturación de importación", "import": "Importar", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "pass": "Contraseña de correo electrónico", "Canal": "Omnichennel", "SMTP": "SMTP", "Netdist": "Disco de red", "approvers": "Aprobadoras/Aprobadores", "lowCode": "<PERSON><PERSON><PERSON> bajo", "pgoPlusSetting": "Pgo Plus Configuración"}, "TaxInformation": {"removed": "éxito para eliminar el logotipo de la empresa", "error": "<PERSON><PERSON>r de carga", "recorrrect": "Por favor, introduzca correctamente `{msg}`, ex: {desc}", "phoneNo": "Ingrese el número de teléfono correcto. ex: **********", "FY": "Por favor, establezca la moneda y el año fiscal."}, "reports": {"balanceSheet": "Balance", "trialBalance": "Balance de Prueba", "cashFlowStatement": "Estado de Flujo de Efectivo", "purchaseReport": "Informe de compra", "incomeStatement": "Estado de Resultados", "salesReport": "Reporte de Ventas", "apReport": "AP Informe", "arReport": "AR Informe", "mxElectronicAccountingCoaReport": "Contable Electrónico - COA", "mxElectronicAccountingTrialBalanceReport": "Contable Electrónico - Trial Balance", "mxInformativeDeclarationReport": "Declaración Informativa de Operaciones con Terceros (DIOT)", "mxValueAddedTaxReport": "Impuesto al Valor Agregado", "mxColumnSeqNo": "Seq. No.", "mxColumnGlAccountDetails": "G/L Account Details", "mxColumnGlAccount": "G/L Account", "mxColumnStartingBalanceAmount": "Starting Balance Amount", "mxColumnDebitAmount": "Debit Amount", "mxColumnCreditAmount": "Credit Amount", "mxColumnEndingBalanceAmount": "Ending Balance Amount", "mxColumnSatCode": "SAT Code", "mxColumnGlAccountDescription": "G/L Account Description", "mxColumnLevel": "Level", "mxColumnNatureOfAccount": "Nature Of Account", "mxDiotColumn1": "Third party type", "mxDiotColumn2": "Tipo de operación", "mxDiotColumn3": "RFC", "mxDiotColumn4": "Tax identification number", "mxDiotColumn5": "Foreigner's name", "mxDiotColumn6": "Country or jurisdiction of tax residence", "mxDiotColumn7": "Specify place of tax jurisdiction", "mxDiotColumn8": "Total value of paid events or activities / Paid events or activities in the northern border region", "mxDiotColumn9": "Refunds, discounts and bonuses / Paid events or activities in the northern border region", "mxDiotColumn10": "Total value of paid events or activities / Paid events or activities in the southern border region", "mxDiotColumn11": "Refunds, discounts and bonuses / Paid events or activities in the southern border region", "mxDiotColumn12": "Total value of paid events or activities / Total events or activities paid at the 16% VAT rate", "mxDiotColumn13": "Refunds, discounts and bonuses / Total events or activities paid at the rate of 16% VAT", "mxDiotColumn14": "Total value of acts or activities paid / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn15": "Refunds, discounts and bonuses / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn16": "Total value of acts or activities paid / Acts or activities paid in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn17": "Refunds, discounts and bonuses / Acts or activities paid for the import of intangible goods and services at the rate of 16% VAT", "mxDiotColumn18": "Exclusively for taxed activities / Acts or activities paid in the northern border region", "mxDiotColumn19": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn20": "Exclusively for taxed activities / Acts or activities paid in the southern border region", "mxDiotColumn21": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn22": "Exclusively from taxable activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn23": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn24": "Exclusively for taxed activities / Acts or activities paid for in the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn25": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn26": "Exclusively for taxed activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn27": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn28": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region", "mxDiotColumn29": "Associated with activities that do not meet requirements / Paid events or activities in the northern border region", "mxDiotColumn30": "Associated with exempt activities / Paid acts or activities in the northern border region", "mxDiotColumn31": "Associated with non-object activities / Paid acts or activities in the northern border region", "mxDiotColumn32": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region", "mxDiotColumn33": "Associated with activities that do not meet requirements / Paid events or activities in the southern border region", "mxDiotColumn34": "Associated with exempt activities / Paid events or activities in the southern border region", "mxDiotColumn35": "Associated with non-object activities / Paid acts or activities in the southern border region", "mxDiotColumn36": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn37": "Associated with activities that do not meet requirements / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn38": "Associated with exempt activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn39": "Associated with non-object activities / Total acts or activities paid at the 16% VAT rate", "mxDiotColumn40": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn41": "Associated with activities that do not comply with requirements / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn42": "Associated with exempt activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn43": "Associated with non-object activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT", "mxDiotColumn44": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn45": "Associated with activities that do not comply with requirements / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn46": "Associated with exempt activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn47": "Associated with non-object activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT", "mxDiotColumn48": "VAT withheld by the taxpayer", "mxDiotColumn49": "Acts or activities paid for in the importation of goods and services for which VAT is not paid (Exempt)", "mxDiotColumn50": "Paid acts or activities for which VAT will not be paid (Exempt)", "mxDiotColumn51": "Other acts or activities paid at the 0% VAT rate", "mxDiotColumn52": "Acts or activities not subject to VAT carried out in national territory", "mxDiotColumn53": "Acts or activities not subject to VAT due to not having an establishment in national territory", "mxDiotColumn54": "I declare that fiscal effects were given to the receipts that support the operations carried out with the supplier", "closingBalance": "Balance de cierre", "itemsDetail": "Detalle de los artículos", "startDate": "Fecha de inicio", "experationDate": "Fecha de Experimentación", "company": "empresa", "targetCurrency": "Moneda de destino", "transactionCurrency": "Moneda de la transacción", "COAAccount": "COA Cuenta", "refresh": "Actualizar", "category": "Categoría", "accountCode": "Código de cuenta", "nameGLCategory": "Nombre de la cuenta", "endDebit": "Debito", "endCredit": "Credito", "all": "todos", "endingAt": "Terminando en", "subTotal": "Sub total", "updatedOn": "Actualizado el", "postingDate": "Fecha de publicación", "documentNo": "Documento No", "invoiceNo": "Factura No", "supplier": "<PERSON><PERSON><PERSON><PERSON>", "customer": "Cliente", "netAmount": "Importe Neto", "currency": "Divisa", "PST": "PST", "GST": "GST/HST", "QST": "QST", "VAT": "VAT", "Discount": "Descuento", "IsrWithholding": "ISR Retenidos", "VatWithholding": "VAT Retenidos", "totalTaxAmount": "Monto total del impuesto", "totalAmount": "Monto total", "invoiceAmount": "Importe de la factura", "payment": "Pago", "totalDue": "Total pendiente", "businessPartner": "Socio comercial", "invoiceReference": "Referencia de la factura", "transactionType": "Tipo de transacción", "current": "Actual", "31To60": "31 to 60", "61To90": "61 to 90", "91+": "91+", "contactName": "Nombre del contacto", "narration": "Narración", "cashFlowName": "Partidas de Flujo de Efectivo", "cashFlowMonth": "mes en curso", "cashFlowYear": "Año en curso", "vatChargeable": "IVA a cargo", "valueOfActsOrActivitiesTaxedAtTheRateOf16": "Valor de los actos o actividades gravados a la tasa del 16%", "valueOfActsOrActivitiesTaxedAtThe0ExportRate": "Valor de los actos o actividades gravados a la tasa del 0% exportación", "valueOfActsOrActivitiesTaxedAtTheRateOf0Others": "Valor de los actos o actividades gravados a la tasa del 0% otros", "sumOfTheTaxedActsOrActivities": "Suma de los actos o actividades gravados", "valueOfActsOrActivitiesForWhichTaxIsNotPayable": "Valor de los actos o actividades por los que no se deba pagar el impuesto (exentos)", "valueOfActsOrActivitiesNotSubjectToTax": "Valor de los actos o actividades no objeto del impuesto", "vatChargeableAtTheRateOf16": "IVA a cargo a la tasa del 16%", "vatCharged": "IVA a cargo", "updatedAmountToBeReimbursedDerivedFromTheAdjustment": "Cantidad actualizada a reintegrarse derivada del ajuste", "totalVatDue": "Total de IVA a cargo", "btCapture": "CAPTURAR", "vatCreditable": "IVA acreditable", "amountOfPaidEventsOrActivities": "Monto de los actos o actividades pagadas", "totalActsPaid16Percent": "Total de los actos o actividades pagados a la tasa del 16% de IVA", "totalActsPaidImport16Percent": "Total de los actos o actividades pagados en la importación de bienes y servicios a la tasa del 16% de IVA", "totalActsPaid0Percent": "Total de los demás actos o actividades pagados a la tasa del 0% de IVA", "totalPaidActsExempt": "Total de los actos o actividades pagados por los que no se pagará el IVA (exentos)", "determinationCreditableVAT": "Determinación del Impuesto al Valor Agregado acreditable", "vatOnActsPaid16Percent": "IVA de actos o actividades pagados a la tasa del 16%", "vatOnImportPaid16Percent": "IVA de actos o actividades pagados en la importación de bienes y servicios a la tasa del 16%", "totalVATTransferred": "Total de IVA trasladado al contribuyente (Efectivamente pagado)", "updatedCreditableAmount": "Monto acreditable actualizado a incrementar derivado del ajuste", "totalCreditableVAT": "Total de IVA acreditable", "determination": "Determinación", "vatWithheld": "IVA retenido", "totalCreditableVat": "Total de IVA acreditable", "otherAmountsPayableByTheTaxpayer": "Otras cantidades a cargo del contribuyente", "otherAmountsInFavorOfTheTaxpayer": "Otras cantidades a favor del contribuyente", "amountDue": "Cantidad a cargo", "creditingOfTheBalanceInFavorOfPreviousPeriods": "Acreditamiento del saldo a favor de periodos anteriores (Sin exceder de la cantidad a cargo)", "taxDue": "Impuesto a cargo", "amountToBeDetailed1": "<PERSON><PERSON> por detallar", "interestChargedAtRate16": "Intereses cobrados a la tasa del 16%", "royaltiesBetweenRelatedPartiesAtRate16": "Regalías entre partes relacionadas a la tasa del 16%", "otherActsOrActivitiesTaxedAtRate16": "Otros actos o actividades gravados a la tasa del 16%", "amountToBeDetailed2": "<PERSON><PERSON> por detallar", "agriculturalLivestockForestryFishingActivitiesTaxedAtRate0": "Actividades agrícolas, ganaderas, silvícolas o pesqueras gravadas a la tasa del 0%", "otherActsOrActivitiesTaxedAtRate0": "Otros actos o actividades gravados a la tasa del 0%", "amountToBeDetailed3": "<PERSON><PERSON> por detallar", "alienationOfLandAndBuildingsForResidentialHousing": "Enajenación de suelo y construcciones adheridas al suelo, destinadas o utilizadas para casa habitación", "saleOfBooksNewspapersMagazinesNotByTaxpayer": "Enajenación de libros, periódicos y revistas (no editados por el contribuyente)", "royaltiesChargedByAuthors": "Regalías cobradas por autores", "disposalOfUsedMovablePropertyExceptByCompanies": "Enajenación de bienes muebles usados, excepto los enajenados por empresas", "alienationOfLotteryTicketsAndReceipts": "Enajenación de billetes y demás comprobantes de loterías, rifas, sorteos o juegos con apuestas y concursos de toda clase", "teachingServices": "Servic<PERSON> en<PERSON>ña<PERSON>", "publicLandTransportationServiceForPeople": "Servicio de transporte público terrestre de personas", "derivativeFinancialTransactions": "Operaciones financieras derivadas", "ticketSalesForPublicShows": "Venta de boletos para espectáculos públicos", "professionalMedicalServices": "Servicios profesionales de medicina", "temporaryUseOfRealEstateForResidentialOrFarming": "Uso o goce temporal de inmuebles para casa habitación y por fincas para fines agrícolas o ganaderos", "otherIncomeExemptFromVat": "Otros ingresos exentos de IVA", "amtDetail1": "<PERSON><PERSON> por detallar", "intPaid16": "Intereses pagados a la tasa del 16%", "royaltyRel16": "Regalías pagadas entre partes relacionadas a la tasa del 16%", "otherPaid16": "Otros actos o actividades pagados a la tasa del 16%", "amtDetail2": "<PERSON><PERSON> por detallar", "impInt16": "Importación de bienes intangibles a la tasa del 16%", "impIntTmp16": "Importación de uso o goce temporal de bienes intangibles a la taasa del 16%", "impServ16": "Importación de servicios a la tasa del 16%", "impOthPaid16": "Otros actos o actividades pagados en la importación de bienes y servicios a la tasa del 16%", "amtDetail3": "<PERSON><PERSON> por detallar", "landBuy": "Adquisición de suelo y construcciones adheridas al suelo, destinadas o utilizadas para casa habitación", "bookBuy": "Adquisición de libros, periódicos o revistas (no editados por el contribuyente)", "royaltyAuth": "Regalías pagadas a los autores", "usedBuy": "Adquisición de bienes muebles usados excepto los adquiridos de empresas", "transSvc": "Servicio de transporte público terrestre de personas", "medSvc": "Servicios profesionales de medicina", "agrInsur": "Aseguramiento contra riesgos agropecuarios", "farmUse": "Uso o goce temporal de fincas para fines agrícolas o ganaderos", "impExm": "Actos o actividades pagados en la importación de bienes y servicios exentos", "othExm": "Otros actos o actividades pagados exentos", "vatCredTot": "Total de IVA acreditable por actividades gravadas a la tasa del 16%, 8% y 0%", "vatPaidTot": "Total de IVA trasladado al contribuyente (efectivamente pagado)", "vatTrf": "IVA trasladado por adquisiciones de bienes distintos de las inversiones, adquisición de servicios o por el uso o goce temporal de bienes que se utilizan exclusivamente para realizar actos o actividades gravados", "vatTrfInv": "IVA trasladado por la adquisición de inversiones destinadas exclusivamente para realizar actos o actividades gravados", "vatImp": "IVA pagado en la importación por adquisición de bienes distintos de las inversiones, adquisición de servicios o por el uso o goce temporal de bienes que se utilizan exclusivamente para realizar actos o actividades gravados", "vatImpInv": "IVA pagado por la importación de inversiones destinadas exclusivamente para realizar actos o actividades gravados", "vatTaxTot": "Total de IVA correspondiente a actos o actividades gravados", "vatCredNo": "Total de IVA acreditable por importación para realizar actos o actividades por los que no están obligados al pago del impuesto", "vatImpNo": "IVA trasladado o pagado en la importación por adquisición de bienes distintos de las inversiones, adquisición de servicios o por el uso o goce temporal de bienes destinados exclusivamente para realizar actos o actividades por los que no se está obligado al pago del impuesto", "vatInvNo": "IVA trasladado o pagado en la importación de inversiones destinadas exclusivamente para realizar actos o actividades por los que no se está obligado al pago del impuesto", "vatMixed": "IVA de bienes utilizados indistintamente para realizar actos o actividades gravados y actos o actividades por los que no se está obligado al pago del impuesto", "vatSel": "Selecciona la proporción de IVA que aplicarás", "vatRatio": "Proporción de IVA", "vatCredMix": "IVA acreditable de bienes utilizados indistintamente para realizar actos o actividades gravados y actos o actividades por los que no se está obligado al pago del impuesto", "vatCred": "IVA acreditable", "amtDetail4": "<PERSON><PERSON> por detallar", "assetSale": "Por enajenación de bienes", "otherOps": "Por otras operaciones"}, "menu": {"task": "Tarea", "purchase": "Compra", "massiveProcess": "Proceso masivo", "invoices": "Facturas", "sales": "Ventas", "billing": "Facturación", "payroll": "Nómina de sueldos", "payrollRecord": "Registro de nómina", "bankReconciliation": "Conciliación Bancaria", "reconcile": "Conciliar", "history": "Historia", "generalLedger": "Libro Mayor", "journalEntries": "Anotaciones en el Diario", "setting": "<PERSON><PERSON><PERSON>", "contact": "Contacto", "profile": "Perfil", "coA": "CoA", "coAMapping": "CoA Cartografía", "productService": "Producto/Servicio", "connectivities": "Conectividades", "reporting": "Informes", "reports": " Negocio Inteligente", "exportTrialBalance": "Informes", "help": "<PERSON><PERSON><PERSON>", "userGuide": "Guía del usuario", "FAQ": "FAQ", "account": "C<PERSON><PERSON>", "user": "Usuario", "dashboard": "Salpicadero", "payment": "Pago"}, "fileTypeList": {"salesNotPaid": "Ventas no pagadas", "notPaid": "No pagado", "salesCash": "Efectivo de ventas", "cashPaid": "Pago en efectivo", "ES": "ES", "yearEnd": "Documento de fin de año"}, "productService": {"createTitle": "Crear Nuevo Producto/Servicio", "editTitle": "Editar <PERSON>/Servicio"}, "task": {"name": "Tarea", "companyCode": "codigo de empresa", "companyName": "nombre de empresa", "last": "Último", "dueDate": "pendiente", "estimatedHour": "Estimado (hora)", "actualHour": "Actual (hora)", "status": "Estado", "assignedTo": "Asignado a", "email": "Correo electrónico", "tag": "Tag", "priority": "Prioridad", "createTime": "<PERSON><PERSON><PERSON>", "updateTime": "Tiempo de actualización", "action": "Acción", "placeholderSearch": "Nombre de la tarea de búsqueda", "placeholderStarting": "<PERSON><PERSON>", "placeholderEnding": "Para", "placeholderMinHour": "<PERSON><PERSON>", "placeholderMaxHour": "<PERSON><PERSON> m<PERSON>xi<PERSON>", "searchTitleEstimated": "Estimado", "statusToDo": "Todo", "statusDoing": "Haciendo", "statusDone": "<PERSON><PERSON>", "statusDeleted": "Eliminado"}, "update": {"items": "articulos", "item": "articulo", "assigment": "Designación", "paymentDueLater": "Pago debido más tarde", "withinDay7": "Within 7 days", "withinDay15": "Within 15 days", "withinDay30": "Within 30 days", "withinDayOther": "Fixed date", "other": "Other", "searchByBankAccount": "Search by bank account", "searchByFaq": "Search", "sapInstance": "Instance", "sapApplicationNumber": "Application Number", "reverseProcessing": "Procesamiento de reversa...", "poNumberNotFound": "Número de orden de compra no encontrado", "purchaseOrderFound": "Orden de compra encontrada", "realizeSuccess": "Factura definitiva generada con éxito", "purchaseOrderFoundButNoOpenItem": "Orden de compra encontrada, pero no hay partidas abiertas para la verificación de facturas.", "excelGenerated": "El archivo Excel se ha generado y descargado", "excelGenerateFailed": "Error al generar el archivo Excel", "noPdfContent": "No hay contenido PDF disponible", "noXmlContent": "No hay contenido XML disponible", "excelOpenFailed": "No se pudo abrir el contenido PDF", "excelDownloadFailed": "No se pudo descargar el contenido XML"}}