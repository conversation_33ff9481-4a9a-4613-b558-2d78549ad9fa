/** @format */
import ExcelJS from 'exceljs'

export const generateExcelFile = async (
    data: any[],
    headers: Record<string, string>,
    filename: string,
): Promise<void> => {
    try {
        const workbook = new ExcelJS.Workbook()
        const worksheet = workbook.addWorksheet('Sheet1', {
            views: [{state: 'frozen', ySplit: 1}],
            properties: {showGridLines: true},
        })

        // 添加表头和数据
        worksheet.addRow(Object.values(headers))
        data.forEach(item => {
            // 转换数据格式
            const rowData = Object.keys(headers).map((key, index) => {
                // balance 列（第4列）特殊处理
                if (index === 3) {
                    // 确保数字格式
                    const value = typeof item[key] === 'string' ? parseFloat(item[key]) : item[key]
                    return value || 0
                }
                return item[key]
            })
            worksheet.addRow(rowData)
        })

        // 设置列宽
        worksheet.columns.forEach((column, index) => {
            column.width = 30
            // balance 列（第4列）设置为会计格式
            if (index === 3) {
                column.numFmt = '#,##0.00;[Red]-#,##0.00'
            }
        })

        const totalRows = worksheet.rowCount
        const totalCols = worksheet.columnCount

        // 设置表格样式
        for (let row = 1; row <= totalRows; row++) {
            // 设置行高
            const currentRow = worksheet.getRow(row)
            currentRow.height = row === 1 ? 25 : 20

            for (let col = 1; col <= totalCols; col++) {
                const cell = worksheet.getCell(row, col)

                // 基础单元格样式 - balance 列（第4列）特殊处理
                cell.alignment = {
                    vertical: 'middle',
                    horizontal: col === 4 && row > 1 ? 'right' : 'center', // balance 列数据右对齐，其他居中
                    wrapText: true,
                }

                // 表头样式
                if (row === 1) {
                    cell.font = {
                        bold: true,
                        size: 11,
                        name: 'Arial',
                    }
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: {argb: 'FFCCCCCC'},
                    }
                    // 表头使用中等粗细的边框
                    cell.border = {
                        top: {style: 'medium', color: {argb: 'FF000000'}},
                        left: {style: 'medium', color: {argb: 'FF000000'}},
                        bottom: {style: 'medium', color: {argb: 'FF000000'}},
                        right: {style: 'medium', color: {argb: 'FF000000'}},
                    }
                } else {
                    // 数据行使用细边框
                    cell.font = {
                        size: 11,
                        name: 'Arial',
                    }
                    cell.border = {
                        top: {style: 'thin', color: {argb: 'FF000000'}},
                        left: {style: 'thin', color: {argb: 'FF000000'}},
                        bottom: {style: 'thin', color: {argb: 'FF000000'}},
                        right: {style: 'thin', color: {argb: 'FF000000'}},
                    }

                    // 对于 balance 列的数据行，确保数字格式
                    if (col === 4) {
                        const value = cell.value
                        if (typeof value === 'string') {
                            cell.value = parseFloat(value) || 0
                        }
                        cell.numFmt = '#,##0.00;[Red]-#,##0.00'
                    }
                }

                // 为最后一行添加较粗的底边框
                if (row === totalRows) {
                    cell.border.bottom = {style: 'medium', color: {argb: 'FF000000'}}
                }

                // 为最后一列添加较粗的右边框
                if (col === totalCols) {
                    cell.border.right = {style: 'medium', color: {argb: 'FF000000'}}
                }
            }
        }

        // 添加表格外框
        const range = {
            top: 1,
            left: 1,
            bottom: totalRows,
            right: totalCols,
        }

        // 加强外框边框
        worksheet.getRow(1).eachCell({includeEmpty: true}, (cell, colNumber) => {
            cell.border.top = {style: 'thick', color: {argb: 'FF000000'}}
        })

        worksheet.getRow(totalRows).eachCell({includeEmpty: true}, (cell, colNumber) => {
            cell.border.bottom = {style: 'thick', color: {argb: 'FF000000'}}
        })

        for (let row = 1; row <= totalRows; row++) {
            const firstCell = worksheet.getCell(row, 1)
            const lastCell = worksheet.getCell(row, totalCols)
            firstCell.border.left = {style: 'thick', color: {argb: 'FF000000'}}
            lastCell.border.right = {style: 'thick', color: {argb: 'FF000000'}}
        }

        // 生成文件
        const buffer = await workbook.xlsx.writeBuffer()
        const blob = new Blob([buffer], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        })

        // 下载文件
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
    } catch (error) {
        console.error('Error generating Excel file:', error)
        throw error
    }
}
