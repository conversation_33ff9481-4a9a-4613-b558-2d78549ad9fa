/** @format */

import type {ActionContext} from 'vuex'
import service from '../../api/request'

const actions = {
    async fetchProjectDetailById(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, id: any) {
        const result = await service.get(`/project/manage/pageNew?id=${id}`)
        return result.data.data.list[0]
    },

    async submitProjectDetail(
        ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {[s: string]: any},
    ) {
        const form = new FormData()
        Object.entries(payload)
            .filter(([_, v]) => v !== null)
            .forEach(([k, v]) => {
                if (k.indexOf('List') > 0) {
                    return
                }
                form.append(k, v)
            })
        if (payload.id) {
            const response = await service.put('/project/manage/new', form)
            return response.data
        } else {
            const response = await service.post('/project/manage/new', form)
            return response.data
        }
    },
}

export default {
    actions,
}
