/** @format */

import axios from 'axios'
import router from '../router'
import {TokenV1} from '@/lib/storage'
import {message} from 'ant-design-vue'

// create an axios instance
const service = axios.create({
    baseURL: import.meta.env.VITE_BASIC_NEW_API_GL, // url = base url + request url
    withCredentials: true, // send cookies when cross-domain requests
    timeout: 90000, // request timeout
})
// request interceptor
service.interceptors.request.use(
    config => {
        config.headers = {
            ...config.headers,
            // Authorization: 'Bearer ' + TokenV1.get() || '',
            'Cache-Control': 'no-cache',
            Pragma: 'no-cache',
            Expires: '0',
        }

        if (TokenV1.get()) config.headers.Authorization = 'Bearer ' + TokenV1.get() || ''

        return config
    },
    error => {
        console.log(error) // for debug
        return Promise.reject(error)
    },
)

// response interceptor
service.interceptors.response.use(
    response => {
        if (response.status === 200 || response.status === 201) {
            return response
        } else {
            message.error({content: response.status, duration: 5})
        }
    },
    error => {
        if (error.code === 'ECONNABORTED' || error.message === 'Network Error' || error.message.includes('timeout')) {
            message.error({content: 'Time Out Error.', duration: 5})
        } else if (error?.response?.status === 401) {
            router.replace({name: 'Login'})
        } else {
            message.error({
                content: error.response.data.errors || 'failed',
                duration: 5,
            })
        }
        return Promise.reject(error)
    },
)

export default service
