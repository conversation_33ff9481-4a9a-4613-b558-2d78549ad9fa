<!-- @format -->

<script lang="ts" setup>
import {computed, createVNode, onBeforeMount, onMounted, reactive, ref, unref} from 'vue'
import {useStore} from 'vuex'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {message, Modal} from 'ant-design-vue'
import {LeftOutlined, ExclamationCircleFilled, PlusOutlined} from '@ant-design/icons-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import AddUserForm from '@/components/bookkeepingComponents/AccountComponents/AddUserForm.vue'
import * as _ from 'lodash'
import {useRouter} from 'vue-router'
import {UserCompany} from '@/lib/storage'

const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const userCompany: any = UserCompany.get() || []
const leftContent = ref<HTMLBaseElement>()
const rightContent = ref<HTMLBaseElement>()
const leftTable = ref<HTMLBaseElement>()
const rightTable = ref<HTMLBaseElement>()
const accountFormLoading = ref(false)
const selectedRowKeys: any = ref([])

const tableLoading = ref(false)

const show = ref(false)
const selectedList: any = ref([])

const rightTableLoading = ref(false)
const userCompanyCode = ref('')
// const searchUserCompanyRow: any = ref({})
// const leftTableTotalNumber: any = ref(0)
const rightSearchValue = ref('')
const checked = ref(false)
// const selectedVal = ref(0)
//
// const selectedAllList = ref('')
// const companCodeList: any = ref([])

// account is string[], entity is object[]
const selectedUsersId: any = ref([] as any)
const selectedUsersEntity = ref([] as any[])

const pageQuery = reactive({
    rightTablePageQuery: {
        pageSize: 10,
        pageIndex: 1,
    },
    leftTablePageQuery: {
        pageSize: 10,
        pageIndex: 1,
    },
})

const VNodes = (_: any, {attrs}: any) => {
    return attrs.vnodes
}

const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}

//new mapmution
const fetchAllUsers = (query?: any) => store.dispatch('UserStore/getAllUsers', query)
const fetchCompanyTableData = (query: any) => store.dispatch('UserStore/getCompanyData', query)
const patchUsersCompany = (payload: any) => store.dispatch('UserStore/updateUsersCompany', payload)
const fetchAllCompanyTableData = (query?: any) => store.dispatch('UserStore/getAllCompanyData', query)

// all users
const accountUserListOptions = computed(() => store.state.UserStore.userList)

// company
const companyDataSource = computed(() => store.state.UserStore.companyList)
const companyAllList = computed(() => store.state.UserStore.companyAllList)
const rightTableTotalNumber = computed(() => store.state.UserStore.companyTotal)
const selectedCompanyCodes = computed(() => selectedList.value.map((x: any) => x.code))

const leftTableTotalNumber = computed(
    () => userCompanyCode.value?.split(',').filter((x: string) => x && x !== '0').length || 0,
)
const leftTableSource = computed(() => {
    const arr: any[] = []
    if (userCompanyCode.value) {
        const pagedCompanyCode = userCompanyCode.value
            .split(',')
            .filter((x: string) => x !== '0')
            .reverse()
            .slice(
                (pageQuery.leftTablePageQuery.pageIndex - 1) * pageQuery.leftTablePageQuery.pageSize,
                pageQuery.leftTablePageQuery.pageIndex * pageQuery.leftTablePageQuery.pageSize,
            )
        pagedCompanyCode.forEach((item: any) => {
            const companyRow = companyAllList.value.find((items: any) => items.code == item) || {
                code: item,
                name: `*${item}`,
            }
            // when code not found in all company list, add * and code as the name for remind
            if (item !== '0') {
                // ignore default 0
                arr.push(companyRow)
            }
            // arr.push(companyRow)
        })
        console.log('arr', arr)
    }
    return arr
})

const queryCompanyTable = async () => {
    try {
        rightTableLoading.value = true
        //const query = {...pageQuery.rightTablePageQuery} as any
        const query: any = {}
        const {pageSize, pageIndex} = pageQuery.rightTablePageQuery
        query['$limit'] = pageSize
        query['$skip'] = (pageIndex - 1) * pageSize
        query['service_has[$like]'] = '%BK%'
        if (rightSearchValue.value) {
            // query['code'] = rightSearchValue.value
            query['$or[0][code][$like]'] = `%${rightSearchValue.value}%`
            query['$or[1][name][$like]'] = `%${rightSearchValue.value}%`
        } else {
            // query['company_code'] = null
        }

        await fetchCompanyTableData(query)
    } catch (e) {
        console.log(e)
    } finally {
        rightTableLoading.value = false
    }
}

const updateAllUsers = async (flag?: boolean) => {
    try {
        const filterObj: any = {}
        filterObj['product[$like]'] = '%BK%'
        tableLoading.value = true
        if (!flag) pageQuery.leftTablePageQuery.pageIndex = 1
        await fetchAllUsers({...filterObj})
    } catch (error) {
        console.log(error)
    } finally {
        updateSelectedUsers() // update local selected users latest value
        tableLoading.value = false
    }
}

const fetchAllCompanyTable = async () => {
    try {
        const filterObj: any = {}
        filterObj['$limit'] = 9999
        filterObj['$skip'] = 0
        filterObj['service_has[$like]'] = '%BK%'

        await fetchAllCompanyTableData(filterObj)
    } catch (error) {
        console.log(error)
    }
}

const updatePage = async () => {
    try {
        await Promise.all([updateAllUsers(), queryCompanyTable(), fetchAllCompanyTable()])
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
        rightTableLoading.value = false
    }
}

const changeRightTablePage = async (page: any, pageSize: any) => {
    pageQuery.rightTablePageQuery.pageSize = pageSize
    pageQuery.rightTablePageQuery.pageIndex = page
    await queryCompanyTable()
}
// 添加user
const addUser = () => {
    show.value = true
    accountFormLoading.value = true
}
// 右侧company table选中回调
const handleSelectionSelect = (record: any, selected: any, selectedRows: any, nativeEvent: any) => {
    if (selected) {
        selectedRowKeys.value.push(record.id)
        selectedList.value.push(record)
    } else {
        selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1)
        selectedList.value.splice(selectedList.value.indexOf(record.id), 1)
    }
}

// 全选按钮
const addAllSelected = () => {
    selectedList.value = []
    selectedRowKeys.value = []

    companyAllList.value.forEach((item: any) => {
        selectedRowKeys.value.push(item.id)
        selectedList.value.push(item)
    })
}

const checkedOnChange = (val: any) => {
    checked.value = val.target.checked
}

//  查询user下的公司
const selectUserChange = (accounts: any, option: any) => {
    if (accounts.length == 0) {
        userCompanyCode.value = ''
    }
    selectedUsersEntity.value = option.map((x: any) => x.userObj)
    if (selectedUsersEntity.value.length === 1) {
        userCompanyCode.value = selectedUsersEntity.value[0].company_code || getCompanyAllListStr()
    } else {
        userCompanyCode.value = ''
    }

    selectedRowKeys.value = []
    selectedList.value = []
}

const updateSelectedUsers = () => {
    const selectedIds = selectedUsersEntity.value.map((x: any) => x.id)
    selectedUsersEntity.value = accountUserListOptions.value.filter((x: any) => selectedIds.includes(x.id))
}
// 给user分配公司
const rightToLeft = async () => {
    console.log('selectedList, ', selectedList.value)
    console.log('selectedListCode, ', selectedCompanyCodes.value)
    console.log('selectedUsersEntity, ', selectedUsersEntity.value)

    if (selectedUsersEntity.value.length === 0) {
        message.warning({
            content: i18n.t('userPage.selectUser'), //'Must select a user',
        })
        return false
    } else {
        const updateReqs = selectedUsersEntity.value.map((x: any) => {
            const company_codes: string[] = _.uniq(
                [
                    ...selectedCompanyCodes.value,
                    ...(x.company_code === null ? getCompanyAllListStr() : x.company_code).split(','),
                ].filter((x: string) => x && x !== '0'),
            ) // remember filter empty element before join
            const company_codes_str = company_codes.join(',')
            const company_codes_sort_str = company_codes.sort().join(',')

            const payload = {
                id: x.id,
                company_code: company_codes_sort_str === getCompanyAllSortListStr() ? null : company_codes_str,
            }
            return patchUsersCompany(payload)
        })
        try {
            tableLoading.value = true
            const responses = await Promise.all(updateReqs)
            await updateAllUsers(true)

            if (responses.length === 1) {
                // only when single user need to render table items

                pageQuery.leftTablePageQuery.pageIndex = Math.ceil(
                    (responses[0].data.company_code === null ? getCompanyAllListStr() : responses[0].data.company_code)
                        .split(',')
                        .filter((x: string) => x !== '0').length / pageQuery.leftTablePageQuery.pageSize,
                )
                userCompanyCode.value =
                    responses[0].data?.company_code === null ? getCompanyAllListStr() : responses[0].data?.company_code
            }

            message.success(i18n.t('userPage.success'))
        } catch (error) {
            // message.error({
            //     content: 'error',
            // })
        } finally {
            selectedRowKeys.value = []
            selectedList.value = []
            tableLoading.value = false
        }
    }
}

const getCompanyAllListStr = () => {
    return companyAllList.value.map((i: any) => i.code).join(',')
}

const getCompanyAllSortListStr = () => {
    return companyAllList.value
        .map((i: any) => i.code)
        .sort()
        .join(',')
}

// 右侧table分页chage回调
const changeUserPage = (page: any, pageSize: any) => {
    pageQuery.leftTablePageQuery.pageSize = pageSize
    pageQuery.leftTablePageQuery.pageIndex = page
    //leftAllSelectedCompanyData()
}

//删除
const remove = (record: any) => {
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content:
            i18n.t('userPage.companyWithDesc') + `${record?.name || ' '}` + i18n.t('chartOfAccount.msgCoaDelete02'),
        icon: createVNode(ExclamationCircleFilled),
        cancelButtonProps: {
            shape: 'round',
            size: 'small',
        },
        okButtonProps: {
            type: 'primary',
            shape: 'round',
            size: 'small',
        },
        async onOk() {
            const company_codes_str = userCompanyCode.value
                .split(',')
                .filter((x: string) => x !== record?.code)
                .filter((x: string) => x !== '0')
                .join(',')
            try {
                const payload = {
                    id: selectedUsersId.value[0],
                    company_code: company_codes_str || '0',
                }
                const response = await patchUsersCompany(payload)

                if (response.status === 200) {
                    await updateAllUsers(true)
                    const maxPageIndex = Math.ceil(
                        response.data?.company_code.split(',').filter((x: string) => x !== '0').length /
                            pageQuery.leftTablePageQuery.pageSize,
                    )
                    if (maxPageIndex < pageQuery.leftTablePageQuery.pageIndex) {
                        pageQuery.leftTablePageQuery.pageIndex = maxPageIndex || 1
                    }

                    userCompanyCode.value = response.data.company_code
                    message.success(i18n.t('userPage.success'))
                } else {
                    // message.error({
                    //     content: 'error',
                    // })
                }
            } catch (error) {
                console.log(error)
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
    })
}

const showDialog = (bool: boolean) => {
    show.value = bool
}
const dismiss = () => {
    showDialog(false)
}
const clearSelection = () => {
    selectedRowKeys.value = []
}
const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

onBeforeMount(async () => {
    await updatePage()
})
</script>
<template>
    <div class="account-description-wrap">
        <div class="content-box">
            <div class="content-box-left">
                <div class="content-box-header">
                    <div class="content">
                        <div id="import-coa-group">
                            <a-select
                                :placeholder="i18n.t('commonTag.msgInput')"
                                v-model:value="selectedUsersId"
                                mode="multiple"
                                :filter-option="filterOption"
                                @change="selectUserChange"
                            >
                                <a-select-option
                                    v-for="item in accountUserListOptions"
                                    :key="item.account"
                                    :value="item.id"
                                    :userObj="item"
                                    >{{ item.account }}</a-select-option
                                >
                                <template #dropdownRender="{menuNode: menu}">
                                    <div style="padding: 4px 8px; cursor: pointer; color: #004fc1" @click="addUser">
                                        <plus-outlined />{{ i18n.t('account.add') }}
                                        <a-divider style="margin: 4px 0" />
                                    </div>
                                    <v-nodes :vnodes="menu" />
                                </template>
                            </a-select>
                        </div>
                    </div>
                </div>
                <div class="content-box-content" ref="leftContent">
                    <div ref="leftTable">
                        <a-table
                            :dataSource="leftTableSource"
                            :loading="tableLoading"
                            :pagination="false"
                            :scroll="
                                !(leftTable?.offsetHeight || 0) ||
                                !(leftContent?.offsetHeight || 0) ||
                                (leftTable?.offsetHeight || 0) < (leftContent?.offsetHeight || 0)
                                    ? {}
                                    : {y: leftContent ? leftContent?.offsetHeight - 44 - 34 : 440, x: false}
                            "
                        >
                            <a-table-column
                                data-index="code"
                                :title="i18n.t('userPage.companyCd')"
                                :ellipsis="true"
                                width="150px"
                                align="center"
                            />

                            <a-table-column
                                data-index="name"
                                :title="i18n.t('userPage.companyNm')"
                                width="100px"
                                :ellipsis="true"
                                align="center"
                            />

                            <a-table-column
                                :title="i18n.t('chartOfAccount.operation')"
                                key="operation"
                                align="center"
                                width="120px"
                            >
                                <template #default="{record}">
                                    <span>
                                        <a-button class="btn-txt" type="link" @click="remove(record)">
                                            <svg-icon name="icon_delete" style="color: #f5222d"></svg-icon>
                                        </a-button>
                                    </span>
                                </template>
                            </a-table-column>
                        </a-table>
                    </div>
                </div>

                <div class="content-box-footer pagination-wrap">
                    <a-pagination
                        v-model:current="pageQuery.leftTablePageQuery.pageIndex"
                        v-model:pageSize="pageQuery.leftTablePageQuery.pageSize"
                        :total="leftTableTotalNumber"
                        :pageSizeOptions="['10', '20', '50', '100']"
                        :showSizeChanger="true"
                        @change="changeUserPage"
                    ></a-pagination>
                    <span
                        >{{ i18n.t('bkApInvoice.total') }} {{ leftTableTotalNumber }}
                        {{ leftTableTotalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                    >
                </div>
            </div>

            <div class="content-box-middle">
                <a-button type="primary" @click="rightToLeft" :disabled="!(selectedList.length > 0)">
                    <template #icon>
                        <left-outlined />
                    </template>
                </a-button>
            </div>

            <!-- right table---- -->
            <!-- @blur="queryCompanyTable"-->
            <div class="content-box-right">
                <div class="content-box-header">
                    <a-input
                        v-model:value="rightSearchValue"
                        :placeholder="$t('userPage.cdnm')"
                        autocomplete="off"
                        clearable
                        class="input-wrap"
                        @pressEnter="queryCompanyTable"
                    >
                        <template #suffix>
                            <svg-icon style="cursor: pointer" name="icon_search" @click="queryCompanyTable"></svg-icon>
                        </template>
                    </a-input>
                    <a-button type="primary" class="right-add-btn" @click="addAllSelected">{{
                        i18n.t('userPage.AddAll')
                    }}</a-button>
                </div>
                <div class="content-box-content" ref="rightContent">
                    <div ref="rightTable">
                        <a-table
                            :dataSource="companyDataSource"
                            :loading="rightTableLoading"
                            rowKey="id"
                            :row-selection="{
                                onSelect: handleSelectionSelect,
                                hideSelectAll: true,
                                selectedRowKeys: unref(selectedRowKeys),
                            }"
                            :pagination="false"
                            :scroll="
                                !(rightTable?.offsetHeight || 0) ||
                                !(rightContent?.offsetHeight || 0) ||
                                (rightTable?.offsetHeight || 0) < (rightContent?.offsetHeight || 0)
                                    ? {}
                                    : {y: rightContent ? rightContent?.offsetHeight - 44 - 34 : 440, x: false}
                            "
                            id="coa-repo-table"
                        >
                            <a-table-column
                                data-index="code"
                                :title="$t('userPage.companyCd')"
                                width="35%"
                                align="center"
                            />
                            <a-table-column
                                data-index="name"
                                :title="$t('userPage.companyNm')"
                                :ellipsis="true"
                                width="65%"
                                align="center"
                            />
                        </a-table>
                    </div>
                </div>

                <div class="content-box-footer pagination-wrap">
                    <a-pagination
                        v-model:current="pageQuery.rightTablePageQuery.pageIndex"
                        v-model:page-size="pageQuery.rightTablePageQuery.pageSize"
                        :total="rightTableTotalNumber"
                        :pageSizeOptions="['10', '20', '50', '100']"
                        :showSizeChanger="true"
                        size="small"
                        @change="changeRightTablePage"
                    ></a-pagination>
                    <span
                        >{{ i18n.t('bkApInvoice.total') }} {{ rightTableTotalNumber }}
                        {{ rightTableTotalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                    >
                </div>
            </div>
        </div>

        <a-modal
            :title="$t('account.add')"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="680"
            :wrapClassName="'modal-wrap'"
        >
            <!-- @updateData="updateAccountDesc({})" -->

            <add-user-form @updateData="updateAllUsers" @dismiss="dismiss"></add-user-form>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
/** @format */

.account-description-wrap {
    width: 100%;
    height: 100%;
    overflow: hidden;
    .content-box {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .content-box-header {
            display: flex;
            justify-content: space-between;
            padding: 12px 20px 10px;
            align-items: center;
            border-bottom: 1px solid #e2e2ea;
            .title {
                font-size: 20px;
                line-height: 26px;
                font-weight: 700;
            }

            .content {
                display: flex;
                width: 100%;
                justify-content: space-between;
            }

            .ant-select {
                min-width: 480px;
            }
            .ant-btn {
                min-width: 167px;
            }

            .input-wrap {
                width: 200px;
                color: #676d7c;
            }
        }
        .content-box-content {
            padding: 24px 20px 10px;
            height: calc(100% - 60px - 68px);
            overflow: hidden;
            :deep(.ant-table-tbody .ant-table-cell) {
                height: 52px;
                padding: 8px 10px;
            }
        }

        .content-box-footer {
            padding: 16px 20px;
            &.pagination-wrap {
                display: flex;
                justify-content: flex-end;
                align-items: center;

                span {
                    font-size: 12px;
                    margin-left: 8px;
                    line-height: 16px;
                    color: #8c8c8c;
                }
                :deep(.ant-pagination-simple) {
                    .ant-pagination-next,
                    .ant-pagination-prev {
                        height: 32px;
                        line-height: 30px;
                        vertical-align: middle;
                        .ant-pagination-item-link {
                            height: 32px;
                        }
                    }
                    .ant-pagination-simple-pager {
                        height: 25px;
                        line-height: 25px;
                        input {
                            border-radius: 12px;
                        }
                    }
                }
            }
        }
        .content-box-left {
            width: calc(100% - 454px - 28px - 8px);
            height: calc(100vh - 82px - 16px);
            background-color: #fff;
            border-radius: 12px;
        }
        .content-box-right {
            width: 600px;
            height: calc(100vh - 82px - 16px);
            background-color: #fff;
            border-radius: 12px;
        }
        .content-box-middle {
            width: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            button {
                width: 28px;
                height: 28px;
                padding: 0;
            }
        }
    }
    .btn-txt {
        padding-left: 0;
        padding-right: 0;
    }
    .btn-txt + .btn-txt {
        margin-left: 12px;
    }
    //new css
    .right-add-btn {
        height: 36px;
        padding: 6px 18px;
        font-size: 14px;
        border-radius: 36px;
    }
    .user-checkbox {
        width: 100%;
        display: flex;
        flex-flow: column;
        margin: 10px 8px;
    }
}
/* 将后面的对钩隐藏 */
:deep(.ant-select-dropdown.ant-select-dropdown-placement-bottomLeft
        .ant-select-item-option-selected
        .ant-select-item-option-state, .ant-select-dropdown.ant-select-dropdown-placement-bottomLeft
        .ant-select-item-option-selected:hover
        .ant-select-item-option-state, .ant-select-dropdown.ant-select-dropdown-placement-bottomLeft
        .ant-select-item-option:hover
        .ant-select-item-option-state) {
    color: #1885ff;
    display: none;
}
/* 将item所有内容前增加未点击背景 */
:deep(.ant-select-dropdown.ant-select-dropdown-placement-bottomLeft .ant-select-item-option:before) {
    content: '';
    width: 16px;
    height: 16px;
    display: inline-block;
    background: url('../../../assets/image/icon/unCheck.png') no-repeat;
    background-size: cover;
}
:deep(.ant-select-item-option) {
    align-items: center;
}
/* 将item所有内容前增加点击背景 */
:deep(.ant-select-dropdown.ant-select-dropdown-placement-bottomLeft .ant-select-item-option-selected:before) {
    content: '';
    width: 16px;
    height: 16px;
    display: inline-block;
    background: url('../../../assets/image/icon/checked.png') no-repeat;
    background-size: cover;
    border-radius: 2px;
}
/* 将item所有内容做内边距向右移动，这样CheckBox不会遮挡内容 */
:deep(.ant-select-dropdown.ant-select-dropdown-placement-bottomLeft .ant-select-item-option) {
    padding-left: 32px;
    padding-right: 0px;
    &::before {
        width: 16px;
        height: 16px;
        margin-right: 5px;
    }
}
</style>
