<!-- @format -->

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {type FormInstance, message} from 'ant-design-vue'
import {ExclamationCircleOutlined} from '@ant-design/icons-vue'
import {useStore} from 'vuex'
import {UserInfo} from '@/lib/storage'
const userInfo: any = UserInfo.get() || {}
const emits = defineEmits(['dismiss'])

const store = useStore()

const i18n: Composer = i18nInstance.global

const form = reactive({newpassword: '', repassword: '', loginAccount: ''})
const formRef = ref<FormInstance>()
const formLoading = ref(false)

const save = async () => {
    try {
        if (await formRef.value?.validateFields()) {
            formLoading.value = true
            const payload = {id: userInfo?.id, password: form.newpassword}
            const response = await store.dispatch('UserManagementStore/changePassword', payload)
            if (response.status === 200) {
                message.success({content: 'success'})
                cancel()
            } else {
                // message.error({content: 'failed'})
            }
        }
    } catch (error) {
        console.log(error)
    } finally {
        formLoading.value = false
    }
}

const cancel = () => {
    emits('dismiss')
}
const getPopupContainer = (trigger: HTMLElement) => {
    return document.body
}

const rules = reactive({
    newpassword: [
        {
            required: true,
            message: i18n.t('login.msgInputPassword'),
            transform: (value: string) => value,
            trigger: 'blur',
        },
        {
            type: 'string',
            message: i18n.t('login.pwdRule1'),
            trigger: 'blur',
            transform(value: string) {
                if (value && value.indexOf(' ') === -1) {
                    return value
                } else {
                    return false
                }
            },
        },
        {
            trigger: 'blur',
            validator: (rule: any, value: string) => {
                const passwordreg = /^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*?]).{6,16}$/
                if (!passwordreg.test(value)) {
                    return Promise.reject(i18n.t('login.pwdRule'))
                } else {
                    return Promise.resolve()
                }
            },
        },
    ],
    repassword: [
        {
            required: true,
            trigger: ['blur', 'input'],
            message: i18n.t('login.repasswordInput'),
        },
        {
            trigger: ['blur', 'input'],
            validator: (rule: any, value: any) => {
                if (!value) {
                    return Promise.reject(i18n.t('login.repasswordInput'))
                } else if (value !== form.newpassword) {
                    return Promise.reject(i18n.t('login.inconsistentPassword'))
                } else {
                    return Promise.resolve()
                }
            },
            message: i18n.t('login.inconsistentPassword'),
        },
    ],
})

onMounted(() => {
    form.loginAccount = store.state.UserInfo.userInfo.loginAccount
})
</script>
<template>
    <div class="page-container-changepwd_form">
        <a-form :layout="'vertical'" ref="formRef" :model="form" :rules="rules" class="form-box" autocomplete="off">
            <a-form-item name="newpassword">
                <template v-slot:label>
                    {{ i18n.t('login.newpassword') }}
                    <a-tooltip :getPopupContainer="getPopupContainer" placement="rightTop">
                        <template #title>{{ i18n.t('login.pwdRule') }}</template>
                        <exclamation-circle-outlined class="icon-exclamation" />
                    </a-tooltip>
                </template>
                <a-input v-model:value="form.newpassword" />
            </a-form-item>
            <a-form-item name="repassword" :label="i18n.t('login.repassword')">
                <a-input v-model:value="form.repassword" />
            </a-form-item>
        </a-form>
    </div>
    <a-divider class="footer-divider" />
    <footer>
        <a-button @click="cancel" size="small" class="cancel-button" shape="round">
            {{ i18n.t('commonTag.cancel') }}
        </a-button>
        <a-button size="small" type="primary" shape="round" :loading="formLoading" @click="save">
            {{ i18n.t('commonTag.confirm') }}
        </a-button>
    </footer>
</template>

<style lang="scss" scoped>
.icon-exclamation {
    margin: 0;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #aab9cb;
    margin-left: 5px;
}
.page-container-changepwd_form {
    padding: 20px 24px 0px;
    .form-box {
        // grid-column-gap: 10px;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: repeat(2, 1fr);
        grid-template-areas:
            'newpassword'
            'repassword';
        .form-box-item-newpassword {
            grid-area: newpassword;
        }
        .form-box-item-repassword {
            grid-area: repassword;
        }
    }
}
.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}
footer {
    padding: 12px 24px;
    text-align: right;
    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }
    .ant-btn {
        min-width: 65px;
    }
    .ant-btn + .ant-btn {
        margin-left: 8px;
    }
}
</style>
