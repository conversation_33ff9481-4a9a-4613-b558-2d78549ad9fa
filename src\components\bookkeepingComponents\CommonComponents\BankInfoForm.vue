<!-- @format -->

<script lang="ts" setup>
import type { Composer } from 'vue-i18n'
import type { BankInfo } from '@/model/bank'
import i18nInstance from '@/locales/i18n'
import { computed, onBeforeMount, reactive, ref } from 'vue'
import { useStore } from 'vuex'

import imgSrcBMO from '@/assets/image/bank/BMO.png'
import imgSrcCIBC from '@/assets/image/bank/CIBC.png'
import imgSrcRBC from '@/assets/image/bank/RBC.png'
import imgSrcDES from '@/assets/image/bank/desjardins.png'
import imgSrcTD from '@/assets/image/bank/TD.png'
import imgSrcJPCB from '@/assets/image/bank/JPCB.png'
import imgSrcFB from '@/assets/image/bank/FB.png'
import imgSrcNBC from '@/assets/image/bank/NBC.svg'
import imgSrcASPIRE from '@/assets/image/bank/ASPIRE.png'
import imgSrcNOVA from '@/assets/image/bank/NOVA.png'
import imgSrcAMEX from '@/assets/image/bank/AMEX.png'
import imgSrcSEB from '@/assets/image/bank/SEB.png'
import imgSrcEWB from '@/assets/image/bank/EWB.png'
import imgSrcCITI from '@/assets/image/bank/CITI.png'
import imgSrcBOC from '@/assets/image/bank/BOC.png'
import imgSrcCMB from '@/assets/image/bank/CMB.png'
import imgSrcICBC from '@/assets/image/bank/ICBC.jpeg'
import imgSrcBONJ from '@/assets/image/bank/BONJ.jpeg'
import imgSrcSANTANDER from '@/assets/image/bank/SANTANDER.png'
import imgSrcBASEINET from '@/assets/image/bank/BASEINET.jpeg'
import imgSrcMONEX from '@/assets/image/bank/MONEX.png'
import imgSrcBBVA from '@/assets/image/bank/BBVA.png'
import { type FormInstance, message } from 'ant-design-vue'
import { UserCompany, Ap_Integration } from '@/lib/storage'
import { v4 as uuidv4 } from 'uuid'
const store = useStore()
const i18n: Composer = i18nInstance.global
const userCompany: any = UserCompany.get() || []
const createBank = (query: any) => store.dispatch('BankInfoStore/createBankv1', query)
const createPlaidBank = (query: any) => store.dispatch('BankInfoStore/createPlaidBankv1', query)
const updateBank = (query: any) => store.dispatch('BankInfoStore/updateBankv1', query)
const updatePlaidBank = (query: any) => store.dispatch('BankInfoStore/updatePlaidBankv1', query)
const fetchSapMasterData = (query?: any) => store.dispatch('ApStore/fetchApSapMasterData', query)
const apIntegration: any = Ap_Integration.get() ?? 0

const fetchAccountDescDropdown1 = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)
// await fetchAccountDescDropdown1('3')

const accountDescOptions = computed(() =>
    store.state.CommonDropDownStore.accountDescList.map((x: any) => {
        return {
            key: x.id,
            label: `${x.account_code.substring(0, 4)} | ${x.name}`,
            value: x.account_code,
        }
    }),
)
const accountTypeOptions = computed(() =>
    store.state.CommonDropDownStore.bankAccountTypeOptions.map((x: any) => {
        return {
            key: x.key,
            value: x.key,
            label: x.value,
        }
    }),
)
const accountCurrencyOptions = computed(() =>
    store.state.CommonDropDownStore.bankCurrencyOptions.map((x: any) => {
        return {
            key: x.key,
            value: x.key,
            label: x.value,
        }
    }),
)

const sapMasterData = computed(() => store.state.ApStore.sapMasterData)

// 判断是否为特殊银行（BBVA等），这些银行不需要account_no格式限制
const isSpecialBank = computed(() => {
    return ['BBVA', 'MONEX', 'BASEINET', 'SANTANDER', 'BONJ', 'ICBC', 'CMB', 'BOC'].includes(formState.code)
})

const emit = defineEmits(['update-fetch', 'dismiss', 'updateData'])
const bankNameTypeOptions = reactive<BankInfo[]>([
    {
        key: '0',
        value: 'BMO',
        label: i18n.t('bankInformation.BOM'), //'Bank of Montreal',
        img: imgSrcBMO,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '1',
        value: 'CIBC',
        label: i18n.t('bankInformation.CIBC'), //'Canadian Imperial Bank of Commerce',
        img: imgSrcCIBC,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '2',
        value: 'RBC',
        label: i18n.t('bankInformation.RBC'), //'Royal Bank Canada',
        img: imgSrcRBC,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '3',
        value: 'Dejardin',
        label: i18n.t('bankInformation.Desjardins'), // 'Desjardins Bank',
        img: imgSrcDES,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '4',
        value: 'TD',
        label: i18n.t('bankInformation.TDB'), // 'Toronto-Dominion Bank',
        img: imgSrcTD,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '5',
        value: 'JPCB',
        label: 'JPMorgan Chase Bank',
        img: imgSrcJPCB,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '6',
        value: 'FB',
        label: 'Fremont Bank',
        img: imgSrcFB,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '7',
        value: 'NBC',
        label: 'National Bank of Canada',
        img: imgSrcNBC,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '8',
        value: 'ASPIRE',
        label: 'Aspire Bank',
        img: imgSrcASPIRE,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '9',
        value: 'NOVA',
        label: 'Nova Scotia',
        img: imgSrcNOVA,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '10',
        value: 'AMEX',
        label: 'American Express',
        img: imgSrcAMEX,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '11',
        value: 'SEB',
        label: 'South East Bank',
        img: imgSrcSEB,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '12',
        value: 'EWB',
        label: 'East West Bank',
        img: imgSrcEWB,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '13',
        value: 'CITI',
        label: 'CITI Bank',
        img: imgSrcCITI,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '14',
        value: 'BOC',
        label: 'Bank Of China',
        img: imgSrcBOC,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '15',
        value: 'CMB',
        label: 'China Merchants Bank',
        img: imgSrcCMB,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '16',
        value: 'ICBC',
        label: i18n.t('bankInformation.ICBC'),
        img: imgSrcICBC,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '17',
        value: 'BONJ',
        label: i18n.t('bankInformation.BONJ'),
        img: imgSrcBONJ,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '18',
        value: 'SANTANDER',
        label: i18n.t('bankInformation.SANTANDER'),
        img: imgSrcSANTANDER,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '19',
        value: 'BASEINET',
        label: i18n.t('bankInformation.BASEINET'),
        img: imgSrcBASEINET,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '20',
        value: 'MONEX',
        label: i18n.t('bankInformation.MONEX'),
        img: imgSrcMONEX,
        isChecked: null,
        fetch_date: null,
    },
    {
        key: '21',
        value: 'BBVA',
        label: i18n.t('bankInformation.BBVA'),
        img: imgSrcBBVA,
        isChecked: null,
        fetch_date: null,
    },
])
const props = withDefaults(
    defineProps<{
        currentBank: object
        // bankList: BankInfo[]
        editMode: boolean
        noPlaidCursor: boolean
        isFetch: boolean
    }>(),
    {
        currentBank: undefined,
        // bankList: bankDataInFetch,
        editMode: false,
        noPlaidCursor: false,
        isFetch: false,
    },
)
const formRef = ref<FormInstance>()
const formState = reactive({
    id: 0,
    company_code: '',
    company_id: '',
    swift: '',
    name: '',
    bank_payer_name: '',
    bank_org_id: '',
    code: '', // bank number
    account_no: '', //bankAccount
    branch_no: '', //'bank branch number'
    // expenseAccountId: '', //coa
    // expenseAccount: '',  // coa
    bankName: '',
    currency: '',
    accountType: '',
    bank_type: '',
    gl_account_id: '', //id of coa
    gl_account: '', // code of coa
    //bankAccount: '',
    // plaidInitFetchDate: null as any,
})

const state = reactive({
    formLoading: false,
})

const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}
const formatValidator = (rule: any, value: any, callback: any) => {
    // 检查是否为特殊公司或特殊银行，如果是则跳过格式验证
    if (['65QW', '8888', '66QW', '67QW', '68QW', '8001', '9999'].includes(formState.company_code) ||
        ['BBVA', 'MONEX', 'BASEINET', 'SANTANDER', 'BONJ', 'ICBC', 'CMB', 'BOC'].includes(formState.code)) {
        return Promise.resolve()
    } else {
        if (formState.code == 'AMEX' && ['3'].includes(formState.accountType) && formState.account_no.length != 17) {
            return Promise.reject(new Error('e.g., xxxx xxxxxx xxxxx'))
        } else if (formState.code != 'AMEX' && ['3'].includes(formState.accountType) && formState.account_no.length != 19) {
            return Promise.reject(new Error('e.g., xxxx xxxx xxxx xxxx'))
        } else if (['1', '2'].includes(formState.accountType) && formState.account_no.length < 11) {
            return Promise.reject(new Error('e.g., xxx-xxxxx-xxxxxxxx'))
        }
        return Promise.resolve()
    }
}
const rules = reactive({
    bankName: [...requireRule(i18n.t('bankInfo.bankName')), ...lengthLimitRule(1, 100)],
    account_no: [
        ...requireRule(i18n.t('bankInfo.bankAccount')),
        ...lengthLimitRule(1, 100),
        { validator: formatValidator, trigger: 'blur' },
    ],
    accountType: requireRule(i18n.t('bankInfo.accountType')),
    currency: requireRule(i18n.t('bankInfo.currency')),
    gl_account: requireRule(i18n.t('bankInfo.coa')),
})

const validateNum = (account: string) => {
    if (
        ['65QW', '8888', '66QW', '67QW', '68QW', '8001', '9999'].includes(formState.company_code) ||
        ['BBVA'].includes(formState.code) ||
        ((formState.code == 'BOC' || formState.code == 'CMB') && ['1', '2'].includes(formState.accountType))
    ) {
        formState.account_no = account
        console.log('formState.account_no', formState.account_no)
    } else {
        if (!account) {
            return
        }
        if (formState.code == 'AMEX' && ['3'].includes(formState.accountType)) {
            setNumFourSixFive(account)
        } else if (formState.code != 'AMEX' && ['3'].includes(formState.accountType)) {
            setNum(account)
        } else if (['1', '2'].includes(formState.accountType)) {
            setNumbers(account)
        }
    }
}

const setNumFourSixFive = (account: string) => {
    const num1 = account.replace(/\s+/g, '').replace(/[^\d]/g, '') + ''
    const lth = num1.length
    let a,
        b,
        c,
        d = ''
    if (lth > 10) {
        a = num1.slice(0, 4)
        b = num1.slice(4, 10)
        c = num1.slice(10, 15)
        d = a + ' ' + b + ' ' + c
    } else if (lth > 4) {
        a = num1.slice(0, 4)
        b = num1.slice(4, 10)
        d = a + ' ' + b
    } else {
        d = num1
    }
    formState.account_no = d
}

const setNum = (account: string) => {
    formState.account_no = account
        .replace(/\s/g, '')
        .replace(/[^\d]/g, '')
        .replace(/(\d{4})(?=\d)/g, '$1 ')
}
const setNumbers = (account: string) => {
    const num1 = account.replace(/\s+/g, '').replace(/[^\d]/g, '') + ''
    const lth = num1.length
    let a,
        b,
        c,
        d = ''
    if (lth > 8) {
        a = num1.slice(0, 3)
        b = num1.slice(3, 8)
        c = num1.slice(8, 16)
        d = a + '-' + b + '-' + c
    } else if (lth > 3) {
        a = num1.slice(0, 3)
        b = num1.slice(3, 8)
        d = a + '-' + b
    } else {
        d = num1
    }
    // this.$set(this.form, 'bankAccount', d)
    formState.account_no = d
}

const changeCOA = (coa_account_code: string) => {
    formState.gl_account = coa_account_code
}

// const changeFetchDate = (time: Dayjs | null | undefined) => {
//     emit('update-fetch', time)
// }

const cancel = () => {
    emit('dismiss')
}

const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const transformBankType = (reverse: boolean) => {
    if (reverse) {
        formState.accountType = accountTypeOptions.value.find((x: any) => x.label === formState.bank_type).key
    }
    formState.bank_type = accountTypeOptions.value.find((x: any) => x.key === formState.accountType)?.label || ''
}

const remappingBankType = (bankType: string): string => {
    const typeMapping = new Map([
        ['Cheque', '1'],
        ['Saving', '2'],
        ['Credit', '3'],
    ])
    return typeMapping.get(bankType) || ''
}

const save = async () => {
    if (await formRef.value?.validateFields()) {
        state.formLoading = true
        // if (props.isFetch && formState.plaidInitFetchDate === null) {
        //     formState.plaidInitFetchDate = dayjs()
        // }
        if (!formState.company_code || !formState.company_id) {
            // assign user company info
            formState.company_id = userCompany[0].id
            formState.company_code = userCompany[0].code
        }
        transformBankType(false)
        const response = !props.editMode ? await createBank(formState) : await updateBank(formState)
        if (response.status === 200 || response.status === 201) {
            message.success('success')
            emit('updateData')
            cancel()

            if (!props.editMode) {
                const payload = {
                    company_code: response.data.company_code,
                    bank_id: response.data.id,
                    bank_account: response.data.account_no,
                    bank_name: response.data.name,
                    currency: response.data.currency,
                    account_type: remappingBankType(response.data.bank_type), //1 cheque 2 saving 3 credit
                    plaid_status: 0,
                }
                await createPlaidBank(payload)
            } else {
                const payload = {
                    company_code: response.data.company_code,
                    bank_id: response.data.id,
                    bank_account: response.data.account_no,
                    currency: response.data.currency,
                    account_type: remappingBankType(response.data.bank_type), //1 cheque 2 saving 3 credit
                }
                await updatePlaidBank(payload)
            }
        } else {
            // message.error({content: response.statusText, duration: 3})
        }
        state.formLoading = false
    }
}

// const submitConnect = () => {
//     changeFetchDate(formState.plaidInitFetchDate)
// }
const checkComp = () => {
    formState.name = bankNameTypeOptions.find((x: any) => x.value === formState.code)?.label || ''
    console.log(formState)
}

onBeforeMount(async () => {
    if (props.editMode) {
        Object.assign(formState, props.currentBank)
    }
    try {
        await fetchAccountDescDropdown1({ company_code: userCompany[0].code, category: 'Bank', $limit: -1, del_flag: 0 })
        if (apIntegration === 1) {
            await fetchSapMasterData({ company_code: userCompany[0].code })
        }
    } catch (e) {
        console.log(e)
    }
})
</script>
<template>
    <div class="bank-account-info-wrap">
        <a-form :disabled="state.formLoading" ref="formRef" :model="formState" :layout="'vertical'" autocomplete="off"
            :rules="rules" @submit="save">
            <div class="bankInfo-form-block">
                <a-row class="row-padding">
                    <a-col :span="24">
                        <a-form-item required :rules="rules.bankName" name="name" :label="i18n.t('bankInfo.bankName')">
                            <a-select :disabled="props.isFetch" v-model:value="formState.code"
                                :options="bankNameTypeOptions" @change="checkComp">
                            </a-select>
                            <div class="bank-img-wrap"
                                v-if="formState.code && bankNameTypeOptions.find(i => i.value == formState.code)">
                                <img v-if="formState.code" style="height: 35px; padding: 5px 10px"
                                    :src="bankNameTypeOptions.find(i => i.value == formState.code)?.img" />
                            </div>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row class="row-padding">
                    <a-col :span="24">
                        <a-form-item required name="accountType" :rules="rules.accountType"
                            :label="i18n.t('bankInfo.accountType')" v-if="!props.isFetch">
                            <a-select @change="validateNum(formState.account_no)" :disabled="props.isFetch"
                                v-model:value="formState.accountType" :options="accountTypeOptions">
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row class="row-padding">
                    <a-col :span="24">
                        <a-form-item required name="account_no" :rules="rules.account_no"
                            :label="i18n.t('bankInfo.bankAccount')">
                            <a-input :maxlength="isSpecialBank ? undefined : 19" @keyup="validateNum(formState.account_no)"
                                v-model:value="formState.account_no" :disabled="props.isFetch" />
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row class="row-padding">
                    <a-col :span="24">
                        <a-form-item required :rules="rules.currency" name="currency"
                            :label="i18n.t('bankInfo.currency')" v-if="!props.isFetch">
                            <a-select :disabled="props.isFetch" v-model:value="formState.currency"
                                :options="accountCurrencyOptions">
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row class="row-padding">
                    <a-col :span="24">
                        <a-form-item required :rules="rules.gl_account" name="gl_account"
                            :label="i18n.t('bankInfo.coa')" v-if="!props.isFetch && apIntegration !== 1">
                            <a-select :placeholder="i18n.t('bankInfo.msgPhrSelect')"
                                v-model:value="formState.gl_account" :options="accountDescOptions" @change="changeCOA">
                            </a-select>
                        </a-form-item>
                        <a-form-item required :rules="rules.gl_account" name="gl_account"
                            :label="i18n.t('bankInfo.coa')" v-if="!props.isFetch && apIntegration === 1">
                            <a-select :placeholder="i18n.t('bankInfo.msgPhrSelect')"
                                v-model:value="formState.gl_account" show-search :filter-option="filterOption"
                                @change="changeCOA">
                                <a-select-option v-for="item in sapMasterData.ET_GL_ACCOUNT"
                                    :key="item.GL_ACCOUNT + ' | ' + item.DESCRIPTION" :value="item.GL_ACCOUNT">
                                    {{ item.GL_ACCOUNT }} | {{ item.DESCRIPTION }}
                                </a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row class="row-padding" v-if="false">
                    <a-col :span="24">
                        <a-form-item name="startDate" :label="i18n.t('bankInfo.fetchFrom')">
                            <!--                            <a-date-picker-->
                            <!--                                v-model:value="formState.plaidInitFetchDate"-->
                            <!--                                :placeholder="i18n.t('bankInfo.fetchFrom')"-->
                            <!--                                value-format="YYYY-MM-DD"-->
                            <!--                                style="width: 100%"-->
                            <!--                            >-->
                            <!--                                &lt;!&ndash;                                                <template #suffixIcon> </template>&ndash;&gt;-->
                            <!--                            </a-date-picker>-->
                        </a-form-item>
                    </a-col>
                </a-row>
            </div>

            <div style="text-align: right; padding-bottom: 20px">
                <a-button shape="round" :disabled="state.formLoading" class="modal-close-btn" @click="cancel">{{
                    i18n.t('commonTag.cancel')
                }}</a-button>
                <a-button shape="round" :disabled="state.formLoading" type="primary" class="modal-ok-btn"
                    @click="save">Save</a-button>
            </div>
        </a-form>
    </div>
</template>
<style scoped>
.row-padding {
    padding-right: 20px;
}

.modal-close-btn {
    width: 65px;
    text-align: center;
    padding: initial;
    margin-right: 10px;
    color: rgba(0, 79, 193, 1);
    border: 1px solid rgba(0, 79, 193, 1);
}

.modal-ok-btn {
    width: 65px;
    text-align: center;
    padding: initial;
    margin-right: 20px;
}

.footer-divider {
    margin-top: 2px;
    margin-bottom: 24px;
}

footer {
    text-align: right;
}

.bank-name {
    position: relative;
}

.bank-img-wrap {
    position: absolute;
    right: 30px;
    top: 0px;
}

.bank-img-wrap {
    position: absolute;
    right: 30px;
    top: 0px;
}
</style>
