<!-- @format -->

<script lang="ts" setup>
import {message, notification} from 'ant-design-vue'
import type {NotificationApi} from 'ant-design-vue/lib/notification'
import {onMounted, reactive, ref, computed} from 'vue'
import {useRouter} from 'vue-router'
import {mapActions, useStore} from 'vuex'
import GlComponent from '../../../components/bookkeepingComponents/GlComponents/GlComponent.vue'

const store = useStore()
const router = useRouter()
const propsPageType = router.currentRoute.value

const payload = reactive({
    description: '',
    postingDate: '',
    currency: '',
    totalDebit: 0,
    totalCredit: 0,
})
const current = ref({})
const itemList = ref([] as Array<string>)
const tableLoading = ref(false)
const invoiceForm: any = ref(null)
// mapActions
const saveGl = (payload: any) => store.dispatch('GlStore/saveGlAction', payload)
// antdv notification
const save = async (form: any) => {
    let response: any = {}
    form.draft = 1
    try {
        tableLoading.value = true
        response = await saveGl(form)
        if (response.data.data.success) {
            message.success('success')
            invoiceForm.value.initFormData()
            document.getElementById('content_id')!.scrollIntoView({behavior: 'smooth'})
            // return router.go(-1)
            return router.push({name: 'GlListing', query: {pageType: 0}})
        } else {
            // message.error({
            //     content: 'failed',
            //     duration: 5,
            // })
        }
    } catch (error: any) {
        // message.error({
        //     content: error.response.data.errors.message ? error.response.data.errors.message : 'failed',
        //     duration: 5,
        // })
    } finally {
        tableLoading.value = false
    }
}
const post = async (form: any) => {
    let response: any = {}
    try {
        tableLoading.value = true
        response = await saveGl(form)
        if (response.data.data.success) {
            message.success('success')
            invoiceForm.value.initFormData()
            document.getElementById('content_id')!.scrollIntoView({behavior: 'smooth'})
            return router.push({name: 'GlListing', query: {pageType: 1}})
        } else {
            // message.error({
            //     content: response.data.errors,
            //     duration: 8,
            // })
        }
    } catch (error: any) {
        // message.error({
        //     content: error.response.data.errors || 'failed',
        //     duration: 8,
        // })
    } finally {
        tableLoading.value = false
    }
}

const dismiss = () => {
    router.push({name: 'GlListing'})
}

onMounted(async () => {
    itemList.value = ['gl', router.currentRoute.value.meta.title as string]
})
</script>
<template>
    <div class="page-container-invoice" id="content_id">
        <!-- <bread-crumb-title-bar :item-list="itemList" /> -->
        <div class="invoice-form-content" :loading="tableLoading">
            <gl-component
                ref="invoiceForm"
                :current-invoice="current"
                :readonly-mode="false"
                :operationMode="'creating'"
                @save="save"
                @post="post"
                @dismiss="dismiss"
            ></gl-component>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.page-container-invoice {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .invoice-form-content {
        width: 100%;
        margin: 0 auto;
    }

    .page-container-ap-invoice-form {
        :deep(.ap-invoice-block) {
            background-color: #fff;
            padding: 32px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            border-bottom: 0;
        }
        :deep(.ap-invoice-amount-block .ap-invoice-amount-block-left) {
            width: 510px;
        }
        :deep(.ap-invoice-amount-block) {
            padding-bottom: 0;
        }

        :deep(.textarea-wrap) {
            margin-bottom: 32px;
        }

        :deep(.ap-invoice-footer) {
            margin-bottom: 40px;
        }
    }
}
</style>
