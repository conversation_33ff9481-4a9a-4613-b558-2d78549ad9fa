<!-- @format -->

<script lang="ts" setup>
import {ref, reactive, computed, onBeforeMount, watch} from 'vue'
import {useStore} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany} from '@/lib/storage'
import {numberToCurrency} from '@/lib/utils'
import {SyncOutlined, ExportOutlined} from '@ant-design/icons-vue'
import type {ColumnsType} from 'ant-design-vue/es/table'

import * as _ from 'lodash'
import moment from 'moment'
import {v4 as uuidv4} from 'uuid'
import type {Dayjs} from 'dayjs'
import SvgIcon from '@/components/SvgIcon.vue'
import GlComponent from '@/components/bookkeepingComponents/GlComponents/GlComponent.vue'

const userCompany: any = UserCompany.get() || []
const store = useStore()
const i18n: Composer = i18nInstance.global
const tableElWrapRef = ref<HTMLElement>()
const headerHeight = 47

const current = ref({})
const showGL = ref(false)

const today = ref(moment().format('YYYY-MM-DD'))
const tableLoading = ref(false)
const treeLoading = ref(false)
const trialBalanceList = computed(() => store.state.ReportStore.trialBalanceList)
const COAAccountOptions = computed(() => {
    const list: {[key: string]: string}[] = trialBalanceList.value.map((i: any) => {
        return {id: i.gl_account, value: i.gl_account}
    })
    return _.uniqBy(list, 'id') as {[key: string]: string}[]
})
const total = reactive({
    debit: '0.00',
    credit: '0.00',
})
const companies: any = ref([])
const VNodes = (_: any, {attrs}: any) => {
    return attrs.vnodes
}
const searchForm = reactive<{[key: string]: string | undefined}>({
    // end_date: today.value,
    start: undefined,
    end: today.value,
    company: undefined,
    transactionCurrency: undefined,
})
const reportsForm = reactive<{[key: string]: string | undefined}>({
    type: 'BS',
    end_date: '',
    start_date: '',
})
const COAAccount = ref<string[]>([])
const selectAll = ref<boolean>(false)
const indeterminate = ref<boolean>(true)

const fetchTBList = (payload: any) => store.dispatch('ReportStore/getTrialBalanceList', payload)
const updateDisplayTrialBalanceList = (list: any) => store.commit('ReportStore/updateDisplayTrialBalanceList', list)

const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const filterCompanyOption = (input: string, option: any) => {
    return (option.company_name || '').toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const disabledDate = (current: Dayjs) => {
    return current && current > moment().endOf('day')
}

const tableScrollY = computed(() => {
    return (tableElWrapRef.value?.offsetHeight || 0) - headerHeight * 2
})
const columnsDetail: ColumnsType = [
    {
        title: i18n.t('reports.accountCode'),
        dataIndex: 'gl_account',
        key: 'gl_account',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.documentNo'),
        dataIndex: 'document_no',
        key: 'document_no',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.postingDate'),
        dataIndex: 'posting_date',
        key: 'posting_date',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.narration'),
        dataIndex: 'narration',
        key: 'narration',
        align: 'center',
        ellipsis: true,
    },
    {
        title: 'BP',
        dataIndex: 'bp_name',
        key: 'bp_name',
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.endDebit'),
        dataIndex: 'debit',
        key: 'debit',
        width: 80,
        align: 'center',
        ellipsis: true,
    },
    {
        title: i18n.t('reports.endCredit'),
        dataIndex: 'credit',
        key: 'credit',
        width: 80,
        align: 'center',
        ellipsis: true,
    },
]
const columns: ColumnsType = [
    {
        title: i18n.t('reports.accountCode'),
        dataIndex: 'gl_account',
        key: 'gl_account',
        align: 'center',
        width: '320px',
    },
    {
        title: i18n.t('reports.nameGLCategory'),
        dataIndex: 'gl_account_name',
        key: 'gl_account_name',
        align: 'left',
        width: '380px',
    },
    {
        title: i18n.t('reports.endDebit'),
        dataIndex: 'debit',
        key: 'debit',
        align: 'right',
        width: '150px',
    },
    {
        title: i18n.t('reports.endCredit'),
        dataIndex: 'credit',
        key: 'credit',
        align: 'right',
        width: '150px',
    },
]
const state = reactive({
    tableData: [] as unknown,
})

const drail = async (title: string) => {
    console.log('title===============', title)
    if (!title || title.length < 4) return

    try {
        const code = title.substring(0, 4)
        if (!+code) return

        // treeLoading.value = true
        state.tableData = await store.dispatch('ReportStore/glExportJson', {
            company: userCompany[0].code,
            start: searchForm.start,
            end: searchForm.end,
            coa: code,
        })
    } catch (err) {
        console.log(err)
    } finally {
        // treeLoading.value = false
    }
}
const onClickRow = (record: any, company: any) => {
    return {
        onclick: async (event: any) => {
            if (!record.account) return
            treeLoading.value = true
            try {
                state.tableData = await store.dispatch('ReportStore/glExportJson', {
                    company: company,
                    start: searchForm.start,
                    end: searchForm.end,
                    coa: record.account,
                })
            } catch (err) {
                console.log(err)
            } finally {
                treeLoading.value = false
            }
        },
    }
}
const fetchGlList = (payload: any) => store.dispatch('GlStore/fetchGlListV1', payload)
const onClickRowDetial = (record: any, company: any) => {
    // console.log('aadfa')
    return {
        onclick: async (event: any) => {
            // current.value = _.cloneDeep(record)
            const response = await fetchGlList({
                company: company,
                document_no: record.document_no,
            })
            current.value = _.cloneDeep(response.data)
            showDialog(true)
        },
    }
}
const showDialog = (bool: boolean) => {
    showGL.value = bool
    if (!bool) {
        current.value = {}
    }
}
const dismiss = () => {
    showDialog(false)
}
const dataSource = computed(() =>
    store.state.ReportStore.displayTrialBalanceList.map((i: any) => {
        // return {
        //     ...i,
        //     debit: i.endBalance > 0 ? numberToCurrency(i.endBalance) : ' - - ',
        //     debitNum: i.endBalance > 0 ? i.endBalance : 0,
        //     credit: i.endBalance < 0 ? numberToCurrency(Math.abs(i.endBalance)) : ' - - ',
        //     creditNum: i.endBalance < 0 ? Math.abs(i.endBalance) : 0,
        // }
        return {
            ...i,
            debit: i.debit > 0 ? numberToCurrency(i.debit) : ' - - ',
            debitNum: i.debit > 0 ? i.debit : 0,
            credit: i.credit > 0 ? numberToCurrency(i.credit) : ' - - ',
            creditNum: i.credit > 0 ? i.credit : 0,
        }
    }),
)
const exportReport = async () => {
    try {
        tableLoading.value = true
        const query = {
            company: userCompany[0].code,
            company_name: userCompany[0].name,
            end: moment(searchForm.end).format('YYYY-MM-DD'), //  searchForm.end_date
        }
        console.log('use company : ', query)
        await store.dispatch('ReportStore/export', query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}

const exportReports = async () => {
    try {
        tableLoading.value = true
        switch (reportsForm.type) {
            case 'BS':
                await store.dispatch('ReportStore/exportBS', {
                    company: userCompany[0].code,
                    company_name: userCompany[0].name,
                    end: reportsForm.end_date,
                })
                break
            case 'PL':
                await store.dispatch('ReportStore/exportPL', {
                    company: userCompany[0].code,
                    company_name: userCompany[0].name,
                    start: reportsForm.start_date,
                    end: reportsForm.end_date,
                })
                break
            case 'AR':
                await store.dispatch('ReportStore/exportArAp', {
                    company_code: userCompany[0].code,
                    company_name: userCompany[0].name,
                    report_type: 'AR',
                    end: reportsForm.end_date,
                })
                break
            case 'AP':
                await store.dispatch('ReportStore/exportArAp', {
                    company_code: userCompany[0].code,
                    company_name: userCompany[0].name,
                    report_type: 'AP',
                    end: reportsForm.end_date,
                })
                break
            case 'Sales':
                await store.dispatch('ReportStore/exportSP', {
                    company_code: userCompany[0].code,
                    company_name: userCompany[0].name,
                    report_type: 'AR',
                    start: reportsForm.start_date,
                    end: reportsForm.end_date,
                })
                break
            case 'Purchase':
                await store.dispatch('ReportStore/exportSP', {
                    company_code: userCompany[0].code,
                    company_name: userCompany[0].name,
                    report_type: 'AP',
                    start: reportsForm.start_date,
                    end: reportsForm.end_date,
                })
                break

            default:
                break
        }
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}

const selectChange = async () => {
    try {
        tableLoading.value = true

        await fetchTBList(searchForm)
        COAAccount.value = COAAccountOptions.value.map((i: any) => i.value)
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

const accountChange = async () => {
    try {
        tableLoading.value = true

        filterAccount()
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

const filterAccount = () => {
    if (COAAccount.value.length) {
        updateDisplayTrialBalanceList(
            trialBalanceList.value.filter((j: any) => COAAccount.value.includes(j.gl_account)),
        )
    } else {
        updateDisplayTrialBalanceList([])
    }
}

const refresh = async () => {
    try {
        tableLoading.value = true

        await fetchTBList(searchForm)
        filterAccount()
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
}

const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)

onBeforeMount(async () => {
    console.log(userCompany[0])

    try {
        tableLoading.value = true
        searchForm.company = userCompany[0].code
        // searchForm.currency = 'CAD'
        companies.value = _.cloneDeep(_.sortBy(userCompany, 'id'))

        await fetchTBList(searchForm)
        COAAccount.value = COAAccountOptions.value.map((i: any) => i.value)
        tableLoading.value = false
    } catch (error) {
        console.log(error)
        tableLoading.value = false
    }
})

const onCheckAllChange = (e: any) => {
    COAAccount.value = e.target.checked ? COAAccountOptions.value.map((i: any) => i.value) : []
    accountChange()
}
watch(
    () => COAAccount.value,
    val => {
        indeterminate.value = !!val.length && val.length < COAAccountOptions.value.length
        selectAll.value = val.length === COAAccountOptions.value.length
    },
)
watch(
    () => dataSource.value,
    val => {
        total.debit = numberToCurrency(
            val.reduce((prev: any, curr: any) => {
                return prev + curr.debitNum
            }, 0) || 0,
        )
        total.credit = numberToCurrency(
            val.reduce((prev: any, curr: any) => {
                return prev + curr.creditNum
            }, 0) || 0,
        )
    },
)
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="header-title-wrap">
                <div class="header-title">{{ i18n.t('reports.trialBalance') }}</div>
                <div class="header-subtitle">{{ i18n.t('reports.endingAt') }}: {{ today }}</div>
            </div>
            <a-divider type="vertical" style="margin-left: 60px"></a-divider>
            <!-- <a-divider type="vertical" /> -->
            <div class="selects-wrap">
                <!-- <a-date-picker class="data-select" v-model:value="searchForm.start" :allowClear="false"
                    @change="selectChange" :inputReadOnly="true" :disabled-date="disabledDate" format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD" :placeholder="i18n.t('reports.startDate')" :disabled="tableLoading">
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker> -->
                <a-date-picker
                    class="data-select"
                    v-model:value="searchForm.end"
                    :allowClear="false"
                    @change="selectChange"
                    :inputReadOnly="true"
                    :disabled-date="disabledDate"
                    format="YYYY-MM-DD"
                    valueFormat="YYYY-MM-DD"
                    :placeholder="i18n.t('reports.experationDate')"
                    :disabled="tableLoading"
                >
                    <template #suffixIcon>
                        <svg-icon name="icon_date"></svg-icon>
                    </template>
                </a-date-picker>
                <!-- <a-select
                    class="company-select"
                    v-model:value="searchForm.company"
                    show-search
                    @change="selectChange"
                    :filter-option="filterCompanyOption"
                    :placeholder="i18n.t('reports.company')"
                    :disabled="tableLoading"
                >
                    <a-select-option
                        v-for="item in companies"
                        :key="item.name + uuidv4()"
                        :company_name="item.name"
                        :value="item.code"
                    >
                        {{ item.name }}
                    </a-select-option>
                </a-select> -->
                <a-select
                    class="coa-account-select"
                    v-model:value="COAAccount"
                    mode="multiple"
                    show-search
                    :filter-option="filterOption"
                    @change="accountChange"
                    :placeholder="i18n.t('reports.COAAccount')"
                    :disabled="tableLoading"
                    :max-tag-count="3"
                >
                    <a-select-option v-for="item in COAAccountOptions" :key="item.id" :value="item.id">{{
                        item.value
                    }}</a-select-option>

                    <template #dropdownRender="{menuNode: menu}">
                        <div class="select-all-item" @mousedown="e => e.preventDefault()">
                            <a-checkbox
                                v-model:checked="selectAll"
                                :indeterminate="indeterminate"
                                @change="onCheckAllChange"
                            >
                                {{ i18n.t('reports.all') }}
                            </a-checkbox>
                        </div>
                        <v-nodes :vnodes="menu" />
                    </template>
                </a-select>
            </div>

            <a-button type="primary" shape="round" :disabled="tableLoading" @click="exportReport()">
                <export-outlined />
                {{ i18n.t('workTimeManager.export') }}
            </a-button>
        </div>
        <div class="page-content">
            <div class="page-content-left">
                <div class="title">{{ i18n.t('') }}</div>
                <a-spin :spinning="treeLoading">
                    <a-table
                        :dataSource="dataSource"
                        :columns="columns"
                        :pagination="false"
                        :loading="tableLoading"
                        :customRow="(row: any) => onClickRow(row, userCompany[0].code)"
                    >
                        <template #summary>
                            <a-table-summary fixed>
                                <a-table-summary-row class="table-summary-wrap">
                                    <a-table-summary-cell></a-table-summary-cell>
                                    <a-table-summary-cell class="table-summary-total">
                                        <a-typography-text>{{ i18n.t('reports.subTotal') }}</a-typography-text>
                                    </a-table-summary-cell>
                                    <a-table-summary-cell class="table-summary-text">
                                        <a-typography-text>{{ total.debit }}</a-typography-text>
                                    </a-table-summary-cell>
                                    <a-table-summary-cell class="table-summary-text">
                                        <a-typography-text>{{ total.credit }}</a-typography-text>
                                    </a-table-summary-cell>
                                </a-table-summary-row>
                            </a-table-summary>
                        </template>
                    </a-table>
                </a-spin>
            </div>
            <div class="page-content-right" ref="tableElWrapRef">
                <div class="title">{{ i18n.t('reports.itemsDetail') }}</div>
                <a-table
                    :dataSource="state.tableData"
                    :columns="columnsDetail"
                    :pagination="false"
                    :loading="tableLoading"
                    :scroll="{y: tableScrollY}"
                    :customRow="(row: any) => onClickRowDetial(row, userCompany[0].code)"
                >
                </a-table>
            </div>
            <a-modal
                :title="i18n.t('gl.readonly')"
                v-model:visible="showGL"
                :footer="null"
                destroyOnClose
                :closeable="true"
                :width="1000"
                style="z-index: 999"
                :dialogStyle="{top: '10px'}"
                :bodyStyle="{padding: '10px 24px 24px'}"
            >
                <gl-component
                    :current-invoice="current"
                    :readonly-mode="true"
                    :operation-mode="'detail'"
                    @dismiss="dismiss"
                ></gl-component>
            </a-modal>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.page-wrap {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    background-color: #fff;

    .page-header {
        min-height: 84px;
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e2e2ea;
        margin-bottom: 14px;

        .header-title-wrap {
            min-width: 142px;
            max-width: 142px;

            .header-title {
                white-space: nowrap;
                line-height: 26px;
                height: 26px;
                color: #004fc1;
                font-size: 20px;
                font-weight: 700;
                letter-spacing: 0;
            }

            .header-subtitle {
                white-space: nowrap;
                font-size: 12px;
                color: #262626;
                letter-spacing: 0;
                line-height: 18px;
                font-weight: 400;
            }
        }

        .ant-divider {
            border-left-color: #e2e2ea;
            height: 36px;
            top: 0;
            margin: 0 18px;
        }

        .selects-wrap {
            width: 100%;
            display: flex;
            align-items: center;

            .target-currency-select,
            .company-select,
            .coa-account-select {
                min-width: 175px;
                width: 175px;
                max-width: 500px;
                margin-right: 8px;
                flex: 1;
            }

            .data-select {
                min-width: 145px;
                width: 145px;
                max-width: 145px;
                margin-right: 8px;
                flex: 1;
            }

            .transaction-currency-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.2;
            }

            .coa-account-select {
                min-width: 175px;
                width: 175px;
                margin-right: 8px;
                flex: 1.5;
                margin-right: 0;

                .select-all-item {
                    padding: 5px 12px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

                    .ant-checkbox-wrapper {
                        width: 100%;
                    }
                }

                :deep(.ant-select-selector) {
                    overflow: hidden;

                    .ant-select-selection-overflow {
                        flex-wrap: nowrap;
                    }
                }
            }
        }

        .ant-btn-round {
            padding: 6px 10px;
            margin-left: 16px;
        }
    }

    // .page-content {
    //     padding: 0 20px;
    //     overflow: hidden;
    //     height: calc(100% - 105px);
    //     :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
    //         padding-top: 15px;
    //         padding-bottom: 15px;
    //     }

    //     // :deep(.ant-table-wrapper .ant-table .ant-table-thead .ant-table-cell) {
    //     //     text-align: center !important;
    //     // }
    // }
    .page-content {
        padding: 0 20px;
        overflow: hidden;
        height: calc(100% - 105px);
        display: flex;
        justify-content: space-between;
        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding: 15px 8px;
        }
        :deep(.ant-table-wrapper .ant-table .ant-table-header .ant-table-cell) {
            padding: 12px 8px;
        }
        .page-content-left {
            width: 52%;
            border-radius: 8px;
            border: 1px solid #c3c7d4;
            padding: 8px;
            overflow: auto;
            height: 100%;
            .title {
                color: #262626;
                text-align: right;
                line-height: 20px;
                font-weight: 700;
                margin-bottom: 10px;
            }
            .ant-spin-spinning {
                height: calc(100% - 30px);
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            :deep(.ant-spin-nested-loading) {
                height: calc(100% - 30px);
                width: 100%;
                .ant-spin-container {
                    height: 100%;
                }
            }
            :deep(.ant-tree) {
                .ant-tree-treenode {
                    width: 100%;
                    border-bottom: 1px solid #f0f0f0;
                    padding: 2px 0;
                    .ant-tree-switcher-noop {
                        display: none;
                    }
                    .ant-tree-switcher-noop + .ant-tree-node-content-wrapper {
                        .tree-details-wrap {
                            font-weight: 400;
                        }
                    }
                    .ant-tree-node-content-wrapper {
                        width: 100%;
                        background-color: transparent !important;
                        display: block;
                        .ant-tree-title {
                            display: block;
                        }
                        &.ant-tree-node-selected {
                            color: #004fc1;
                        }
                    }
                    &.ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
                        color: #262626;
                        cursor: default;

                        .tree-details-wrap {
                            font-weight: 600;
                        }
                    }
                }
            }

            .tree-details-wrap {
                display: flex;
                justify-content: space-between;
                font-weight: 600;
            }
        }
        .page-content-right {
            width: calc(48% - 8px);
            border-radius: 8px;
            border: 1px solid #c3c7d4;
            padding: 8px;
            .title {
                color: #262626;
                text-align: center;
                line-height: 20px;
                font-weight: 700;
                margin-bottom: 10px;
            }
        }
    }

    .table-summary-wrap {
        .table-summary-total {
            text-align: right;
            font-weight: 600;
        }

        .table-summary-text {
            text-align: right;
        }
    }

    .page-footer {
        padding: 5px 8px;
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;
            width: 100%;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                min-width: 45px;
            }

            .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
                min-width: 35px;
            }
        }
    }
}
</style>
