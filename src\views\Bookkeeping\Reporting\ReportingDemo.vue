<!-- @format -->

<script lang="ts" setup>
import {ref, onMounted} from 'vue'
import {TokenV1, Org_Id, UserCompany} from '@/lib/storage'

const url = ref('')
const userCompany: any = UserCompany.get() || []

onMounted(() => {
    let type = 2
    if (window.location.host.indexOf('bookkeeping.inossemtimes.com') > -1) type = 1
    url.value = `https://reporting.inossemtimes.com/report?login_token=${TokenV1.get()}&org_id=${Org_Id.get()}&type=${type}&company=${
        userCompany[0].code
    }`
})
</script>
<template>
    <div class="page-container-pdf_viewer">
        <iframe :src="url" frameborder="0" width="100%" height="100%"></iframe>
    </div>
</template>

<style lang="scss" scoped>
.page-container-pdf_viewer {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}
</style>
