<!-- @format -->

<script setup lang="ts">
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import enGB from 'ant-design-vue/es/locale/en_GB'
import zhHK from 'ant-design-vue/es/locale/zh_HK'
import jaJP from 'ant-design-vue/es/locale/ja_JP'
import frFR from 'ant-design-vue/es/locale/fr_FR'
import esES from 'ant-design-vue/es/locale/es_ES'

import {computed, h, onMounted} from 'vue'
import {RouterView} from 'vue-router'
import {useStore} from 'vuex'

import {Spin} from 'ant-design-vue'
import {LoadingOutlined} from '@ant-design/icons-vue'
import {Loader} from '@googlemaps/js-api-loader'

const store = useStore()

const locale = computed(() => {
    if (store.state.lang === 'en') {
        return enGB
    } else if (store.state.lang === 'zh') {
        return zhCN
    } else if (store.state.lang === 'zh-hk') {
        return zhHK
    } else if (store.state.lang === 'ja') {
        return jaJP
    } else if (store.state.lang === 'fr') {
        return frFR
    } else if (store.state.lang === 'es') {
        return esES
    }

    return enGB
})

const loader = new Loader({
    apiKey: 'AIzaSyDRCKdrVypg2-w-vD-3skT8G2QcxSV8lHM',
    version: 'weekly',
    libraries: ['places'],
})

const indicator = h(LoadingOutlined, {
    style: {
        fontSize: '36px',
    },
    spin: true,
})

Spin.setDefaultIndicator({indicator})

const getPopupContainer = (node: any) => {
    if (node) {
        return node.parentNode
    }
    return document.body
}

onMounted(async () => {
    await loader.load()
})
</script>

<template>
    <a-config-provider :locale="locale" :getPopupContainer="getPopupContainer">
        <RouterView />
    </a-config-provider>
</template>

<style scoped></style>
