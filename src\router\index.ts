/** @format */

import {createRouter, createWebHashHistory} from 'vue-router'
import {UserInfo, LoginTimeout} from '@/lib/storage'
import NProgress from 'nprogress'
import routes from './routes'

const router = createRouter({
    history: createWebHashHistory(),
    routes,
})

const checkTimeout = (lastCallApi: any) => {
    if (Date.now() - lastCallApi > 1800000) {
        //30 min timeout
        return true
    } else {
        return false
    }
}

router.beforeEach((to, from, next) => {
    NProgress.start()

    if (!UserInfo.get() && to.name !== 'Login') next({name: 'Login'})
    else {
        if (to.name !== 'Login') {
            const timeout: any = LoginTimeout.get()
            if (timeout) {
                if (timeout === -1) {
                    LoginTimeout.set(Date.now())
                    next()
                } else {
                    if (checkTimeout(timeout)) {
                        LoginTimeout.set(null)
                        next({name: 'Login'})
                    } else {
                        LoginTimeout.set(Date.now())
                        next()
                    }
                }
            } else {
                next({name: 'Login'})
            }
        } else {
            LoginTimeout.set(null)
            next()
        }
    }
})

router.afterEach(() => {
    NProgress.done()
})

export default router
