/** @format */

import service from '@/api/request'
import servicev1 from '@/api/requestNew'
import serviceGL from '@/api/requestNewGL'
import serviceKYC from '@/api/requestKyc'

import * as R from 'ramda'
import type {ActionContext} from 'vuex'

const http = service
const httpv1 = servicev1
const httpGL = serviceGL
const httpKYC = serviceKYC
const CommonDropDownStore = {
    namespaced: true,
    state: {
        accountDescList: [],
        bankAccountTypeOptions: [],
        bankCurrencyOptions: [],
        bankListOptions: [],
        customerOptions: [],
        supplierOptions: [],
        countryOptions: [],
        provinceOptions: [],
        glTypeOptions: [],
    },
    getters: {
        showExpenseTextByCode: (state: {accountDescList: any}) => (code: string) => {
            const base: {[x: string]: any} = R.indexBy(R.prop('id'), state.accountDescList)
            if (base[code]) {
                return base[code].name + ' | ' + base[code].account_code.substring(0, 4)
            }
            return code
        },
        showTextByCode: (state: {[x: string]: any}) => (type: any, code: string | number) => {
            const base: {[x: string]: any} = R.indexBy(R.prop('key'), state[`bank${type}Options`])
            if (!base[code]) {
                return 'Unknown'
            }
            return base[code].value
        },
        getAccountTypeList: () => {
            return [
                {key: '1', value: 'Cheque'},
                {key: '2', value: 'Saving'},
                {key: '3', value: 'Credit'},
            ]
        },
        getCurrencyTypeList: () => {
            return [
                // {key: '1', value: 'RMB'},
                {key: '2', value: 'CAD'},
                {key: '3', value: 'USD'},
            ]
        },
        getCountriesList: () => {
            return [
                {key: 'CA', value: 'Canada'},
                {key: 'US', value: 'United States'},
            ]
        },
        getProvinceListByCountryCode: () => (code: string) => {
            if (code === 'US') return usProvinces
            return caProvinces
        },
    },
    mutations: {
        updateAccountDescList(state: {accountDescList: any[]}, list: any) {
            state.accountDescList = [...list]
        },
        updateBankAccountTypeOptions(state: {bankAccountTypeOptions: {key: string; value: string}[]}) {
            state.bankAccountTypeOptions = [
                {key: '1', value: 'Cheque'},
                {key: '2', value: 'Saving'},
                {key: '3', value: 'Credit'},
            ]
        },
        updateBankCurrentOptions(state: {bankCurrencyOptions: {key: string; value: string}[]}, list: any) {
            state.bankCurrencyOptions = [...list]
        },
        updateBankListOptions(state: {bankListOptions: any[]}, list: any) {
            state.bankListOptions = [...list]
        },
        updateCustomerOptions(state: {customerOptions: any[]}, list: any) {
            state.customerOptions = [...list]
        },
        updateSupplierOptions(state: {supplierOptions: any[]}, list: any) {
            state.supplierOptions = [...list]
        },
        updateCountryOptions(state: {countryOptions: any[]}, list: any) {
            state.countryOptions = [...list]
        },
        updateProvinceOptions(state: {provinceOptions: any[]}, list: any) {
            state.provinceOptions = [...list]
        },
        updateGlTypeOptions(state: {glTypeOptions: any[]}, list: any) {
            state.glTypeOptions = [...list]
        },
    },
    actions: {
        async fetchAccountDescDropdownV2(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            // const queryObj: any = {}
            // queryObj['company'] = payload?.company_code
            // if (payload?.bk_type === 1) {
            //     queryObj.category = 'Account Receivable'
            // }
            // if (payload?.bk_type === 2) {
            //     queryObj.category = 'Account Payable'
            // }
            // if (payload?.bk_type === 3) {
            //     queryObj.category = 'Bank'
            // }
            const queryParams = new URLSearchParams()
            queryParams.append('company', payload?.company_code)

            queryParams.append('is_archive', 'false')
            // queryParams.append('page_index', '1')
            // queryParams.append('page_size', '9999')
            if (payload.category) {
                queryParams.append('category', payload?.category)
            }
            if (payload.bk_type) {
                queryParams.append('bk_type', payload?.bk_type)
            }
            if (payload.bk_type && payload.bk_type !== 3) {
                queryParams.append('bk_type', '0')
            } // for ap& ar both
            // bk_type: ar use 1,  ap use 2, bank use 3, 0 is for ap & ar both
            const response = await httpGL.get('/bk/coa', {params: queryParams})
            const res = response.data.data.gl_account_list || response.data.data.rows
            store.commit('updateAccountDescList', res)
            return response
        },
        // async fetchAccountDescDropdown(
        //     store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        //     bk_type: undefined,
        // ) {
        //     const response = await http.get('/bk/account/common/dropdown')
        //     if (bk_type != undefined) {
        //         //传入了br_type,需要过滤coa,br_type==1 是ar; br_type==2是ap
        //         //ar 用 list 里的 br_type是 0 和1 的给 accountDescList赋值
        //         const list = response.data.data.filter((item: {bkType: string}) => {
        //             return item.bkType === '0' || item.bkType === bk_type
        //         })
        //         store.commit('updateAccountDescList', list)
        //     } else {
        //         store.commit('updateAccountDescList', response.data.data)
        //     }
        //     return response
        // },
        // async fetchAccountDescDropdownV1(
        //     store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        //     payload: any,
        // ) {
        //     // or[0][archived][$ne]=true&$or[1][roomId]=2
        //     const queryObj: any = {}
        //     let list_flag = false
        //     queryObj['company_code'] = payload?.company_code
        //     queryObj['$or[0][bk_type]'] = payload?.bk_type
        //     queryObj['$or[1][bk_type]'] = 0
        //
        //     if (payload?.$limit === -1) {
        //         queryObj['$limit'] = -1
        //         list_flag = true
        //     }
        //     const response = await httpv1.get('/system-preferences/api/v1/coa', {params: queryObj})
        //     // if (bk_type) {
        //     //     //传入了br_type,需要过滤coa,br_type==1 是ar; br_type==2是ap
        //     //     //ar 用 list 里的 br_type是 0 和1 的给 accountDescList赋值
        //     //     const list = response.data.data.filter((item: {bk_type: number}) => {
        //     //         return item.bk_type == 0 || item.bk_type == bk_type
        //     //     })
        //     const list = list_flag ? response.data : response.data.data
        //     store.commit('updateAccountDescList', list)
        //     return response
        // },
        // async fetchAccountDescDropdown1(
        //     store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        //     bk_type: undefined,
        // ) {
        //     const response = await http.get('/bk/account/common/dropdown')
        //     if (bk_type != undefined) {
        //         //传入了br_type,需要过滤coa,br_type==1 是ar; br_type==2是ap
        //         //ar 用 list 里的 br_type是 0 和1 的给 accountDescList赋值
        //         const list = response.data.data.filter((item: {bkType: string}) => {
        //             return item.bkType === '3' || item.bkType === bk_type
        //         })
        //         store.commit('updateAccountDescList', list)
        //         // console.log(list,'list')
        //     } else {
        //         store.commit('updateAccountDescList', response.data.data)
        //     }
        //     return response
        // },
        // async fetchAccountDescDropdown1V1(
        //     store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        //     payload: any,
        // ) {
        //     // const response = await httpv1.get('/system-preferences/api/v1/coa')
        //     const queryObj: any = {}
        //     let list_flag = false
        //     queryObj['company_code'] = payload?.company_code
        //     // queryObj['company_id'] = payload?.company_id
        //     queryObj['$or[0][bk_type]'] = Number(payload?.bk_type)
        //     queryObj['$or[1][bk_type]'] = 3
        //     if (payload?.$limit === -1) {
        //         queryObj['$limit'] = -1
        //         list_flag = true
        //     }
        //     const response = await httpv1.get('/system-preferences/api/v1/coa', {params: queryObj})
        //     //传入了br_type,需要过滤coa,br_type==1 是ar; br_type==2是ap
        //     //ar 用 list 里的 br_type是 0 和1 的给 accountDescList赋值
        //     //const list = response.data.filter((item: {bk_type: number}) => {
        //     //     return item.bk_type === 3 || item.bk_type === bk_type
        //     // })
        //     const list = list_flag ? response.data : response.data.data
        //
        //     // console.log(list,'list')
        //     store.commit('updateAccountDescList', list)
        //     return response
        // },
        async fetchBankListDropDown(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get('/bk/bank/dropdown')
            store.commit('updateBankListOptions', response.data.data)
            return response
        },
        async fetchCustomerDropDown(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get('/bk/customer/dropdown')
            store.commit('updateCustomerOptions', response.data.data)
            return response
        },
        async fetchSupplierDropDown(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get('/bk/supplier/dropdown')
            store.commit('updateSupplierOptions', response.data.data)
            return response
        },
        async fetchContactDropDown(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const query: any = {}
            query['$sort[update_time]'] = 'desc'
            const response = await httpv1.get(`/system-preferences/api/v1/contact`, {params: {...payload, ...query}})
            const list = response.data.data.map((item: any) => {
                return {
                    ...item,
                }
            })
            store.commit('updateCustomerOptions', list)
            store.commit('updateSupplierOptions', list)

            return response
        },
        async fetchBankCurrent(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await httpKYC.get(`/rate/getCurrency`)
            const list = response.data.body?.map((i: string) => {
                return {key: i, value: i}
            })
            store.commit('updateBankCurrentOptions', list)

            return response
        },
        async fetchCountryInfo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await httpKYC.get(`/dictionary/getCountry`)
            const list = response.data.body?.map((i: any) => {
                return {key: i.countryCode, value: i.countryName}
            })
            store.commit('updateCountryOptions', list)

            return response
        },
        async fetchProvinceInfo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpKYC.get(`/dictionary/getProvince?countryCode=${payload}`)
            const list = response.data.body?.map((i: any) => {
                return {key: i.regionCode, value: i.regionName}
            })
            store.commit('updateProvinceOptions', list)

            return response
        },
        async fetchGlTypeInfo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get('/handleExport/cash-flow-mapping')
            // show all type without filter.
            // const filtered = response.data.filter((x: any) => x.status === '1')
            store.commit('updateGlTypeOptions', response.data)
            return response
        },
    },
}

const caProvinces = [
    {key: 'AB', value: 'Alberta'},
    {key: 'BC', value: 'British Columbia'},
    {key: 'MB', value: 'Manitoba'},
    {key: 'NB', value: 'New Brunswick'},
    {key: 'NL', value: 'Newfoundland & Labr.'},
    {key: 'NS', value: 'Nova Scotia'},
    {key: 'NT', value: 'Northwest Terr.'},
    {key: 'NU', value: 'Nunavut'},
    {key: 'ON', value: 'Ontario'},
    {key: 'PE', value: 'Prince Edward Island'},
    {key: 'QC', value: 'Quebec'},
    {key: 'SK', value: 'Saskatchewan'},
    {key: 'YT', value: 'Yukon Territory'},
]

const usProvinces = [
    {
        key: 'AA',
        value: 'A.Forces Americas',
    },
    {
        key: 'AE',
        value: 'A.Forces Eur./ME/Afr',
    },
    {
        key: 'AK',
        value: 'Alaska',
    },
    {
        key: 'AL',
        value: 'Alabama',
    },
    {
        key: 'AP',
        value: 'Armed Forces Pacific',
    },
    {
        key: 'AR',
        value: 'Arkansas',
    },
    {
        key: 'AS',
        value: 'American Samoa',
    },
    {
        key: 'AZ',
        value: 'Arizona',
    },
    {
        key: 'CA',
        value: 'California',
    },
    {
        key: 'CO',
        value: 'Colorado',
    },
    {
        key: 'CT',
        value: 'Connecticut',
    },
    {
        key: 'DC',
        value: 'District of Columbia',
    },
    {
        key: 'DE',
        value: 'Delaware',
    },
    {
        key: 'FL',
        value: 'Florida',
    },
    {
        key: 'GA',
        value: 'Georgia',
    },
    {
        key: 'GU',
        value: 'Guam',
    },
    {
        key: 'HI',
        value: 'Hawaii',
    },
    {
        key: 'IA',
        value: 'Iowa',
    },
    {
        key: 'ID',
        value: 'Idaho',
    },
    {
        key: 'IL',
        value: 'Illinois',
    },
    {
        key: 'IN',
        value: 'Indiana',
    },
    {
        key: 'KS',
        value: 'Kansas',
    },
    {
        key: 'KY',
        value: 'Kentucky',
    },
    {
        key: 'LA',
        value: 'Louisiana',
    },
    {
        key: 'MA',
        value: 'Massachusetts',
    },
    {
        key: 'MD',
        value: 'Maryland',
    },
    {
        key: 'ME',
        value: 'Maine',
    },
    {
        key: 'MI',
        value: 'Michigan',
    },
    {
        key: 'MN',
        value: 'Minnesota',
    },
    {
        key: 'MO',
        value: 'Missouri',
    },
    {
        key: 'MP',
        value: 'Northern Mariana Isl',
    },
    {
        key: 'MS',
        value: 'Mississippi',
    },
    {
        key: 'MT',
        value: 'Montana',
    },
    {
        key: 'NC',
        value: 'North Carolina',
    },
    {
        key: 'ND',
        value: 'North Dakota',
    },
    {
        key: 'NE',
        value: 'Nebraska',
    },
    {
        key: 'NH',
        value: 'New Hampshire',
    },
    {
        key: 'NJ',
        value: 'New Jersey',
    },
    {
        key: 'NM',
        value: 'New Mexico',
    },
    {
        key: 'NV',
        value: 'Nevada',
    },
    {
        key: 'NY',
        value: 'New York',
    },
    {
        key: 'OH',
        value: 'Ohio',
    },
    {
        key: 'OK',
        value: 'Oklahoma',
    },
    {
        key: 'OR',
        value: 'Oregon',
    },
    {
        key: 'PA',
        value: 'Pennsylvania',
    },
    {
        key: 'PR',
        value: 'Puerto Rico',
    },
    {
        key: 'RI',
        value: 'Rhode Island',
    },
    {
        key: 'SC',
        value: 'South Carolina',
    },
    {
        key: 'SD',
        value: 'South Dakota',
    },
    {
        key: 'TN',
        value: 'Tennessee',
    },
    {
        key: 'TX',
        value: 'Texas',
    },
    {
        key: 'UM',
        value: 'US Minor Outl. Isl.',
    },
    {
        key: 'UT',
        value: 'Utah',
    },
    {
        key: 'VA',
        value: 'Virginia',
    },
    {
        key: 'VI',
        value: 'Virgin Islands',
    },
    {
        key: 'VT',
        value: 'Vermont',
    },
    {
        key: 'WA',
        value: 'Washington',
    },
    {
        key: 'WI',
        value: 'Wisconsin',
    },
    {
        key: 'WV',
        value: 'West Virginia',
    },
    {
        key: 'WY',
        value: 'Wyoming',
    },
]

export default CommonDropDownStore
