<!-- @format -->

<script lang="ts" setup>
import { reactive, ref, onBeforeMount, computed } from 'vue'
import type { Composer } from 'vue-i18n'
import { useStore } from 'vuex'
import i18nInstance from '@/locales/i18n'
import { PyCustomizeTable, UserCompany } from '@/lib/storage'
import { useRouter } from 'vue-router'
import CustomColumns from '@/components/bookkeepingComponents/CommonComponents/CustomColumns.vue'
import PyInvoiceComponent from '@/components/bookkeepingComponents/PyComponents/PyInvoiceComponent.vue'
import { MailOutlined } from '@ant-design/icons-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import ArFullInvoice from '@/views/Bookkeeping/Ar/ArFullInvoice.vue'

const i18n: Composer = i18nInstance.global
const router = useRouter()
const store = useStore()
const userCompany: any = UserCompany.get() || []
const searchForm = reactive({
    companyName: '',
    createStartDate: '',
    createEndDate: '',
    minTotalFee: null,
    maxTotalFee: null,
    brFlag: -1,
})
const tableLoading = ref(false)
const showColumns = ref(false)
const showDetails = ref(false)
const activeTabName = ref('0')
const current = ref({})
const pageQuery = reactive({
    page_index: 1,
    page_size: 10,
    sortField: null,
    sortDirection: 0,
    currentPageNumber: 1,
})
const defaultTable = [
    'issuer',
    'referenceNo',
    'postingDate',
    'total',
    'balance',
    'status',
    'invoiceNo',
    'invoiceComment',
    'createDate',
    'dueDate',
]
let onlyShowOpen = 0
let timer: any = undefined
const customizeTable: any = ref(
    PyCustomizeTable.get() || ['issuer', 'referenceNo', 'postingDate', 'total', 'balance', 'status'],
)

const invoicesHistoryList = computed(() => store.state.PyStore.invoicesHistoryList)
const totalNumber = computed(() => store.state.PyStore.totalNumber)

const dismissColumns = () => {
    showColumns.value = false
}
const customSorter = async (pagination: any, filters: any, sorter: any) => {
    if (sorter.field && sorter.order) {
        const order = sorter.order === 'descend' ? -1 : 1
        pageQuery.sortField = sorter.field
        pageQuery.sortDirection = order
        pageQuery.currentPageNumber = 1
    } else {
        pageQuery.sortField = null
        pageQuery.sortDirection = 0
        pageQuery.currentPageNumber = 1
    }
    // clearInterval(timer.value)
    await updateTable()
}

const edit = (row: any) => {
    current.value = { ...row }
    showDetails.value = true
}

const dismissDetails = async (action: any) => {
    showDetails.value = false
    current.value = {}
    if (action) {
        await updateTable()
    }
}

const search = async () => {
    pageQuery.page_index = 1
    clearInterval(timer)
    await updateTable()
}
const brFlagOptions = reactive([
    {
        value: -1,
        label: i18n.t('bkArInvoice.brStatus3'), //'All',
    },
    {
        value: 0,
        label: i18n.t('ApInvoiceFormPdf.notPaid'), //'NOT PAID',
    },
    {
        value: 1,
        label: i18n.t('ApInvoiceFormPdf.partialPaid'), //'NOT PAID',
    },
    {
        value: 2,
        label: i18n.t('ApInvoiceFormPdf.paid'), //'CASH PAID',
    },
    {
        value: 3,
        label: i18n.t('ApInvoiceFormPdf.reversed'),
    },
])
const saveColumns = (list: any) => {
    customizeTable.value = list
    showColumns.value = false
    PyCustomizeTable.set(list)
}

const visible = ref(false)
const switchTab = () => {
    if (activeTabName.value === '0') {
        onlyShowOpen = 1
    } else {
        onlyShowOpen = 0
    }
    pageQuery.page_index = 1
    updateTable()
}
const getQueryParams = (searchForm: any) => {
    //used for transform searchForm property to api query format
    const result: any = {}
    // desc sort by create_date as default
    // result['sort[create_time]'] = 'desc'
    if (pageQuery.sortDirection === 0) {
        result['sort[create_time]'] = 'desc'
    }
    if (searchForm.createStartDate !== '' && searchForm.createEndDate !== '') {
        result['posting_date[$bw]'] = `[${searchForm.createStartDate},${searchForm.createEndDate}]`
    }
    if (searchForm.createStartDate !== '' && searchForm.createEndDate == '') {
        result['posting_date[$gte]'] = searchForm.createStartDate
    }
    if (searchForm.createStartDate == '' && searchForm.createEndDate !== '') {
        result['posting_date[$lte]'] = searchForm.createEndDate
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee !== null) {
        result['total_fee[$bw]'] = `[${searchForm.minTotalFee},${searchForm.maxTotalFee}]`
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee == null) {
        result['total_fee[$gte]'] = searchForm.minTotalFee
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee == null) {
        result['total_fee[$lte]'] = searchForm.maxTotalFee
    }
    if (searchForm.companyName) {
        result['issuer_name[$like]'] = `${searchForm.companyName}`
    }
    if (searchForm.brFlag !== -1) {
        result['br_flag'] = searchForm.brFlag
    }

    return result
}
const updateTable = async () => {
    try {
        tableLoading.value = true
        const query = {
            ...getQueryParams(searchForm),
            // ...pageQuery,
            ...{ page_index: pageQuery.page_index, page_size: pageQuery.page_size },
            company_code: userCompany[0].code,
            // ...(activeTabName.value != 'null' ? {br_flag: activeTabName.value} : null),
        }
        query['br_type'] = '5' // exclude payroll type
        // if (activeTabName.value === '0') query['br_flag[$in]'] = '[0,1]'
        if (activeTabName.value === '0' && !query['br_flag'] && query['br_flag'] !== 0) query['br_flag[$in]'] = '[0,1]'
        if (onlyShowOpen === 1) {
            delete query['br_flag']
            query['br_flag[$in]'] = '[0,1]'
        }
        if (pageQuery.sortField) {
            query[`sort[${pageQuery.sortField}]`] = pageQuery.sortDirection === 1 ? 'asc' : 'desc'
        }
        await fetchInvoicesHistoryList(query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}
const fetchInvoicesHistoryList = (query: any) => {
    return store.dispatch('PyStore/fetchInvoicesHistoryList', query)
}

const changePage = () => {
    updateTable()
}

const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer)
    timer = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer)
        }
    }, 1000)
}

const toAddInvoice = () => {
    router.push({ name: 'WoBillsInvoice' })
}

const sendEmail = () => {
    console.log('clicked send mail button')
}

const rowClick = (record: any, rowIndex: any, column: any) => {
    return {
        title: null,
        onClick: (event: any) => {
            if (column.key !== 'operation') {
                edit(record)
            }
        },
    }
}

const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)

onBeforeMount(async () => {
    await updateTable()
})
const cancel = () => (visible.value = false)

const customRow = (record: any) => {
    let rowTitle = ''
    if (record.salary_details && record.salary_details.length > 0) {
        rowTitle += `emp_no:        Net Pay \n`
        record.salary_details.forEach((item: { emp_no: string; pay_item_amount: number }, index: number) => {
            rowTitle += `${item.emp_no}:    ${item.pay_item_amount}`
            if (index < record.salary_details.length - 1) {
                rowTitle += '\n'
            }
        })
    }
    return { title: rowTitle }
}
</script>
<template>
    <div class="history-page-wrap">
        <div class="history-page-content">
            <div style="display: flex" class="main-head">
                <a-tabs v-model:activeKey="activeTabName" @change="switchTab">
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('bkApInvoice.brStatus2')" key="0"></a-tab-pane>
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('bkApInvoice.brStatus')" key="null"></a-tab-pane>
                </a-tabs>
                <div>
                    <div class="history-page-header">
                        <div class="search-group-wrap">
                            <a-input v-model:value="searchForm.companyName" :placeholder="$t('ApComponents.search')"
                                :disabled="tableLoading" class="search-input" @input="inputChange" @pressEnter="search">
                                <template #suffix>
                                    <svg-icon name="icon_search"></svg-icon>
                                </template>
                            </a-input>
                            <a-popover class="popover-wrap" trigger="click" placement="bottom"
                                @visibleChange="visibleChange">
                                <template #content>
                                    <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                                        <div class="search-input-group">
                                            <a-form-item :label="$t('bkApInvoice.date')">
                                                <a-date-picker v-model:value="searchForm.createStartDate"
                                                    :disabled="tableLoading" format="YYYY-MM-DD"
                                                    valueFormat="YYYY-MM-DD" :placeholder="$t('gl.createStartDate')"
                                                    style="width: 160px" clearable>
                                                    <template #suffixIcon>
                                                        <svg-icon name="icon_date"></svg-icon>
                                                    </template>
                                                </a-date-picker>
                                            </a-form-item>
                                            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                            <a-form-item
                                                >{{ $t('bkApInvoice.to') }}
                                                <a-date-picker v-model:value="searchForm.createEndDate"
                                                    :disabled="tableLoading" format="YYYY-MM-DD"
                                                    valueFormat="YYYY-MM-DD" :placeholder="$t('gl.createEndDate')"
                                                    style="width: 160px" clearable>
                                                    <template #suffixIcon>
                                                        <svg-icon name="icon_date"></svg-icon>
                                                    </template>
                                                </a-date-picker>
                                            </a-form-item>
                                        </div>
                                        <div class="search-input-group">
                                            <a-form-item :label="$t('bkApInvoice.totalCol')">
                                                <a-input-number v-model:value="searchForm.minTotalFee" :controls="false"
                                                    :disabled="tableLoading" :min="0"
                                                    :placeholder="$t('bkApInvoice.minFee')"
                                                    style="width: 160px"></a-input-number>
                                            </a-form-item>
                                            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                            <a-form-item>
                                                <a-input-number v-model:value="searchForm.maxTotalFee" :controls="false"
                                                    :disabled="tableLoading" :min="0"
                                                    :placeholder="$t('bkApInvoice.maxFee')"
                                                    style="width: 160px"></a-input-number>
                                            </a-form-item>
                                        </div>
                                        <div class="search-input-group" v-if="activeTabName === 'null'">
                                            <a-form-item :label="$t('commonTag.status')">
                                                <a-select default-first-option v-model:value="searchForm.brFlag"
                                                    style="width: 355px" :controls="false">
                                                    <a-select-option style="width: 355px" v-for="item in brFlagOptions"
                                                        :key="item.label" :value="item.value">{{ item.label
                                                        }}</a-select-option>
                                                </a-select>
                                            </a-form-item>
                                        </div>

                                        <a-button type="primary" shape="round" :disabled="tableLoading" @click="search">
                                            <template #icon>
                                                <svg-icon name="icon_search"></svg-icon>
                                            </template>
                                            {{ $t('commonTag.search') }}
                                        </a-button>
                                    </a-form>
                                </template>
                                <a-button class="search-button" :disabled="tableLoading">
                                    <template #icon>
                                        <svg-icon name="icon_filter"></svg-icon>
                                    </template>
                                    <!-- {{ i18n.t('commonTag.filter') }} -->
                                </a-button>
                            </a-popover>

                            <a-button class="search-button" :disabled="tableLoading"
                                @click="showColumns = !showColumns">
                                <template #icon>
                                    <svg-icon name="icon_columns"></svg-icon>
                                </template>
                                <!-- {{ i18n.t('commonTag.columns') }} -->
                            </a-button>
                        </div>
                        <a-modal v-model:visible="visible" width="1300px" :title="i18n.t('bkApInvoice.create')"
                            :footer="null">
                            <ar-full-invoice @custom-cancel="cancel" />
                        </a-modal>
                    </div>
                </div>
            </div>
            <a-table :dataSource="invoicesHistoryList" :loading="tableLoading" :pagination="false"
                :scroll="customizeTable.length > 5 ? { x: 1350, y: 'calc(100vh - 300px)' } : { x: 'auto' }"
                @change="customSorter" :customRow="customRow">
                <!-- Invoice number -->
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.issuer')"
                    data-index="issuer_name" :ellipsis="true" width="200px" v-if="customizeTable.includes('issuer')" />
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.referenceNo')"
                    data-index="reference_no" sorter="true" :ellipsis="true"
                    v-if="customizeTable.includes('referenceNo')" width="100px" />
                <!-- postingDate -->
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.postingDate')"
                    data-index="posting_date" sorter="true" v-if="customizeTable.includes('postingDate')" width="150px"
                    :ellipsis="true" />
                <!-- totalFee -->
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.total')"
                    data-index="total_fee" v-if="customizeTable.includes('total')" sorter="true" width="100px"
                    :ellipsis="true">
                    <template #default="{ record }">
                        <span>
                            <!-- {{ Number(record.total_fee).toFixed(2) }} -->
                            {{ $formatNumber(Number(record.total_fee)) }}
                        </span>
                    </template>
                </a-table-column>
                <!-- balance -->
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.balance')"
                    data-index="balance" v-if="customizeTable.includes('balance')" width="100px" sorter="true"
                    :ellipsis="true">
                    <template #default="{ record }">
                        <span>
                            <!-- {{ Number(record.balance).toFixed(2) }} -->
                            {{ $formatNumber(Number(record.balance)) }}
                        </span>
                    </template>
                </a-table-column>
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('commonTag.status')"
                    data-index="br_flag" v-if="customizeTable.includes('status')" sorter="true" width="100px"
                    :ellipsis="true">
                    <!-- <template #default="{record}">
                        <img
                            class="br-icon"
                            v-if="record.send_engine_status === 1"
                            src="@/assets/image/icon/icon_complete.png"
                        />
                        <img
                            class="br-icon"
                            v-else-if="record.send_engine_status === 0"
                            src="@/assets/image/icon/icon_incomplete.png"
                        />
                        <img class="br-icon" v-else src="@/assets/image/icon/icon_in_progress.png" />
                    </template> -->
                    <template #default="{ record }">
                        <span v-if="record.br_flag === 0">
                            <a-tag class="tag-red">{{ i18n.t('ApInvoiceFormPdf.notPaid') }}</a-tag>
                        </span>
                        <span v-else-if="record.br_flag === 1">
                            <a-tag class="tag-orange">{{ i18n.t('ApInvoiceFormPdf.partialPaid') }}</a-tag>
                        </span>
                        <span v-else-if="record.br_flag === 2">
                            <a-tag class="tag-green">{{ i18n.t('ApInvoiceFormPdf.paid') }}</a-tag>
                        </span>
                        <span v-else-if="record.br_flag === 3">
                            <a-tag class="tag-gray">{{ i18n.t('ApInvoiceFormPdf.reversed') }}</a-tag>
                        </span>
                    </template>
                </a-table-column>
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.invoiceNo')"
                    data-index="invoice_no" v-if="customizeTable.includes('invoiceNo')" width="260px"
                    :ellipsis="true" />
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.invoiceComment')"
                    data-index="invoice_comments" :ellipsis="true" v-if="customizeTable.includes('invoiceComment')"
                    width="100px" />
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.createDate')"
                    data-index="invoice_create_date" v-if="customizeTable.includes('createDate')" width="150px"
                    :ellipsis="true" />
                <a-table-column :custom-cell="rowClick" align="center" :title="i18n.t('bkApInvoice.dueDate')"
                    data-index="invoice_due_date" v-if="customizeTable.includes('dueDate')" width="150px"
                    :ellipsis="true" />

                <a-table-column v-if="false" align="center" :title="i18n.t('bkApInvoice.operation')" key="operation"
                    fixed="right" width="100px" :custom-cell="rowClick">
                    <template #default>
                        <span>
                            <a-button class="btn-txt" type="link" @click="sendEmail">
                                <!-- TODO:new function -->
                                <!--                                <svg-icon name="icon_unreconcile"></svg-icon>-->
                                <mail-outlined />
                            </a-button>
                        </span>
                    </template>
                </a-table-column>
            </a-table>
            <div class="pagination-wrap">
                <a-pagination v-model:current="pageQuery.page_index" v-model:page-size="pageQuery.page_size"
                    :disabled="tableLoading" :hideOnSinglePage="false" :showSizeChanger="true" :total="totalNumber"
                    @change="changePage" />
                <span
                    >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                    {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                >
            </div>
        </div>
    </div>
    <a-modal :title="i18n.t('columns.modalTitle')" v-model:visible="showColumns" :footer="null" destroyOnClose
        :closeable="true" :width="480" :wrapClassName="'modal-wrap'">
        <custom-columns :defaultTable="defaultTable" :customizeTable="customizeTable" prefix="bkApInvoice"
            @dismiss="dismissColumns" @save="saveColumns"></custom-columns>
    </a-modal>
    <a-modal :title="i18n.t('bkApInvoice.readonly')" v-model:visible="showDetails" :footer="null" destroyOnClose
        :closeable="true" :width="'1110px'" :bodyStyle="{ padding: '20px' }" :wrapClassName="'modal-wrap'">
        <py-invoice-component operation-mode="" :current-invoice="current" :readonly-mode="true"
            @dismiss="dismissDetails">
        </py-invoice-component>
    </a-modal>

    <!--    <a-modal-->
    <!--        :title="readonlyMode ? i18n.t('bkApInvoice.readonly') : i18n.t('bkApInvoice.create')"-->
    <!--        v-model:visible="apShow"-->
    <!--        :footer="null"-->
    <!--        destroyOnClose-->
    <!--        :closeable="true"-->
    <!--        :width="1000"-->
    <!--        :dialogStyle="{top: '10px'}"-->
    <!--        :bodyStyle="{padding: '10px 24px 24px'}"-->
    <!--    >-->
    <!--        <ap-invoice-component :current-invoice="current" :readonly-mode="readonlyMode" @dismiss="dismiss">-->
    <!--        </ap-invoice-component>-->
    <!--    </a-modal>-->
</template>
<style lang="scss" scoped>
.no-scroll {
    overflow-y: hidden;
}

.history-page-wrap {
    border-radius: 10px;
    background-color: #fff;

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 8px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 60px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button+.search-button {
                min-width: 60px;
            }

            .popover-wrap:deep(.ant-popover-placement-bottom) {
                width: 432px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
            margin-left: 24px;
        }
    }

    .history-page-content {
        padding: 12px 20px;

        .main-head {
            display: flex;
            justify-content: space-between;
        }

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
}

.search-input-form {
    display: flex;
    flex-direction: column;
    align-items: end;

    .search-input-group {
        display: flex;

        .ant-form-item {
            margin-bottom: 12px;
        }

        :deep(.ant-form-item-label) {
            width: 55px;
        }

        .ant-form-item+.ant-form-item :deep(.ant-form-item-label) {
            width: 35px;
        }
    }
}

.br-icon {
    width: 20px;
}
</style>
