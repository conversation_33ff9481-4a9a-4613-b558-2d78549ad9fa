/** @format */

import service from '@/api/request'
import servicev1 from '@/api/requestNew'
import serviceKYC from '@/api/requestKyc'
import md5 from 'js-md5'
import type {ActionContext} from 'vuex'
const Utils = {
    namespaced: true,

    state: {
        rate: '',
        rate_date: '',
    },
    mutations: {
        setSpot(state: {rate: any; rate_date: any}, re: {rate: any; rate_date: any}) {
            state.rate = re.rate
            state.rate_date = re.rate_date
        },
    },
    actions: {
        async getSpot(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, queryDate: string) {
            const response = await service.get(`/outer/currency/exchange`, {
                params: {
                    rateDate: queryDate,
                    token: md5(queryDate + 'sap_zhiren_test'),
                },
            })
            if (response.status == 200 && response.data.code == 1000) {
                store.commit('setSpot', response.data.data)
            }
            console.log(response.data.data, 'sport')
            return response.data.data
        },
        async getSpotv1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await serviceKYC.post(`/rate/queryByDate`, payload)

            let spot = {rate: '', rate_date: payload.date}
            if (response.status == 200 && response.data.body && response.data.body.length != 0) {
                const result = response.data.body[0]
                spot = {rate: result.rate, rate_date: result.time}
                store.commit('setSpot', spot)
            }
            return spot
        },
    },
}

export default Utils
