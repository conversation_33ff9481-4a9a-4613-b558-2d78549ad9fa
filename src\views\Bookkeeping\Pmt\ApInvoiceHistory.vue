<!-- @format -->

<script lang="ts" setup>
import {reactive, ref, onBeforeMount, computed, defineComponent} from 'vue'
import type {Composer} from 'vue-i18n'
import {useStore} from 'vuex'
import i18nInstance from '@/locales/i18n'
import {ApCustomizeTable, UserCompany, Ap_Integration, Payment_Integration, UserInfo} from '@/lib/storage'
import {useRouter} from 'vue-router'
import CustomColumns from '@/components/bookkeepingComponents/CommonComponents/CustomColumns.vue'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ApInvoiceComponentIntegration from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegration.vue'
import ApInvoiceComponentIntegrationMx from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegrationMx.vue'
import {
    MailOutlined,
    ExportOutlined,
    DollarOutlined,
    CopyOutlined,
    CheckOutlined,
    UndoOutlined,
    CloseOutlined,
} from '@ant-design/icons-vue'
import {message} from 'ant-design-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import moment from 'moment'
import * as _ from 'lodash'
import WoBillsInvoice from './ApWoBillsInvoice.vue'
import lodash from 'lodash'
import {generateExcelFile} from '@/lib/excelUtils'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'

const updateIntegrationInvoiceStatus = (data: any) => store.dispatch('ApStore/updateIntegrationInvoiceStatusV1', data)
const i18n: Composer = i18nInstance.global
const router = useRouter()
const store = useStore()
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const userType = ref('')
const searchForm = reactive({
    companyName: '',
    createStartDate: '',
    createEndDate: '',
    minTotalFee: null,
    maxTotalFee: null,
    brFlag: -1,
    due: 0,
})
const apIntegration: any = Ap_Integration.get() ?? 0
const paymentIntegration: any = Payment_Integration.get() ?? 0
const visible = ref(false)
const tableLoading = ref(false)
const printLoading = ref(false)
const showColumns = ref(false)
const showDetails = ref(false)
const itemEditModal = ref(false)
const printCheckModal = ref(false)
const activeTabName = ref('0')
const selectedRowKeys = ref<number[]>([])
const selectedRows = ref<any[]>([])
const current = ref({})
const temporaryFrom = ref('')
const payMethodType = ref('')
const issuerName = ref('')
const newChequeNo = ref('')
const modalPrintCheckData = ref<any[]>([])
const pageQuery = reactive({
    page_index: 1,
    page_size: 10,
    sortField: null,
    sortDirection: 0,
})
const defaultTable = [
    'issuer',
    'referenceNo',
    'postingDate',
    'total',
    'balance',
    'status',
    'invoiceNo',
    'payMethod',
    'invoiceComment',
    'creator',
    'createTime',
    'createDate',
    'dueDate',
    'checkPrintTime',
]

const currentEditItem = ref<any>({
    id: '',
    br_flag: '',
})

const invoiceComment = ref<any[]>([])
const checkPrintNoList = ref<any[]>([])
const referenceNoList = ref<any[]>([])

const brFlagOptions = reactive([
    {
        value: -1,
        label: i18n.t('bkArInvoice.brStatus3'), //'All',
    },
    {
        value: 0,
        label: i18n.t('ApInvoiceFormPdf.notPaid'), //'NOT PAID',
    },
    {
        value: 1,
        label: i18n.t('ApInvoiceFormPdf.partialPaid'), //'NOT PAID',
    },
    {
        value: 2,
        label: i18n.t('ApInvoiceFormPdf.paid'), //'CASH PAID',
    },
    {
        value: 3,
        label: i18n.t('ApInvoiceFormPdf.reversed'),
    },
])

const optionPayMethod = [
    {
        value: '1',
        label: i18n.t('ApComponents.bank'), //'Bank',
    },
    {
        value: '3',
        label: i18n.t('ApComponents.check'), //'Cheque',
    },
]

const bankPayMethodOptions = ref<any[]>([])
const selectBankPayMethod = ref()

let timer: any = undefined
const customizeTable: any = ref(
    ApCustomizeTable.get() || ['issuer', 'referenceNo', 'postingDate', 'total', 'balance', 'status'],
)

const invoicesHistoryList = computed(() =>
    store.state.ApStore.invoicesHistoryList.map((i: any) => {
        invoiceComment.value[i.id] = i.invoice_comments
        checkPrintNoList.value[i.id] = i.check_print_no
        referenceNoList.value[i.id] = i.reference_no
        return {
            ...i,
            create_time: moment(i.create_time).format('YYYY-MM-DD HH:mm:SS'),
            check_print_time: i.check_print_time,
        }
    }),
)
const totalNumber = computed(() => store.state.ApStore.totalNumber)

const fetchCompanyTaxInfo = (query?: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
const companyTaxInfo: any = computed(() => store.state.TaxInfoStore.companyTaxInfo)

const rowSelection = computed(() => {
    return activeTabName.value === '0'
        ? {
              selectedRowKeys: selectedRowKeys.value,
              hideSelectAll: true,
              getCheckboxProps: (record: any) => ({
                  disabled:
                      (payMethodType.value !== '' && record.pay_method !== payMethodType.value) ||
                      (record.br_flag !== 12 && record.br_flag !== 0) ||
                      (payMethodType.value !== '' && record.issuer_name !== issuerName.value), // 如果 br_flag 为 11，则禁用复选框
              }),
              onSelect: (record: any, selected: boolean) => {
                  if (selected) {
                      selectedRowKeys.value = [...selectedRowKeys.value, record.id]
                      selectedRows.value = [...selectedRows.value, record]
                      if (selectedRows.value.length === 1) {
                          payMethodType.value = selectedRows.value[0].pay_method
                          issuerName.value = selectedRows.value[0].issuer_name
                      }
                  } else {
                      selectedRowKeys.value = selectedRowKeys.value.filter((id: number) => id !== record.id)
                      selectedRows.value = selectedRows.value.filter((item: any) => item.id !== record.id)
                      if (selectedRows.value.length === 0) {
                          payMethodType.value = ''
                          issuerName.value = ''
                      }
                  }
              },
          }
        : null
})

const dismissColumns = () => {
    showColumns.value = false
}

const edit = (row: any) => {
    current.value = {...row}
    showDetails.value = true
}

const dismissDetails = async (action: any) => {
    showDetails.value = false
    current.value = {}
    if (action) {
        await updateTable()
    }
}

const showItemEditModal = (record: any) => {
    currentEditItem.value = {
        id: record.id,
        br_flag: record.br_flag,
    }
    itemEditModal.value = true
}

const closeItemEditModal = () => {
    itemEditModal.value = false
}

const confirmItemEditModal = (discounts: any) => {
    console.log('confirmItemEditModal', currentEditItem.value)
    itemEditModal.value = false
}

const updateDetails = async (action: any) => {
    await updateTable()
}

const search = async () => {
    pageQuery.page_index = 1
    clearInterval(timer)
    await updateTable()
}

const saveColumns = (list: any) => {
    customizeTable.value = list
    showColumns.value = false
    ApCustomizeTable.set(list)
}
const switchTab = () => {
    pageQuery.page_index = 1
    updateTable()
}
const getQueryParams = (searchForm: any) => {
    //used for transform searchForm property to api query format
    const result: any = {}
    // desc sort by create_date as default
    if (pageQuery.sortDirection === 0) {
        result['sort[create_time]'] = 'desc'
    }
    if (searchForm.createStartDate !== '' && searchForm.createEndDate !== '') {
        result['posting_date[$bw]'] = `[${searchForm.createStartDate},${searchForm.createEndDate}]`
    }
    if (searchForm.createStartDate !== '' && searchForm.createEndDate == '') {
        result['posting_date[$gte]'] = searchForm.createStartDate
    }
    if (searchForm.createStartDate == '' && searchForm.createEndDate !== '') {
        result['posting_date[$lte]'] = searchForm.createEndDate
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee !== null) {
        result['total_fee[$bw]'] = `[${searchForm.minTotalFee},${searchForm.maxTotalFee}]`
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee == null) {
        result['total_fee[$gte]'] = searchForm.minTotalFee
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee == null) {
        result['total_fee[$lte]'] = searchForm.maxTotalFee
    }
    if (searchForm.companyName) {
        result['issuer_name[$like]'] = `${searchForm.companyName}`
    }
    if (searchForm.brFlag !== -1) {
        result['br_flag'] = searchForm.brFlag
    }

    return result
}
const updateTable = async () => {
    try {
        tableLoading.value = true
        const query = {
            company_code: userCompany[0].code,
            ...getQueryParams(searchForm),
            ...{page_index: pageQuery.page_index, page_size: pageQuery.page_size},
        }
        query['br_type[$not]'] = '5' // exclude payroll type
        if (activeTabName.value === '0' && !query['br_flag'] && query['br_flag'] !== 0) {
            query['br_flag[$in]'] = '[11,12,0,1,2]'
            query['pay_method[$in]'] = '[1,3]'
        } else query['br_flag[$in]'] = '[2]'
        if (pageQuery.sortField) {
            query[`sort[${pageQuery.sortField}]`] = pageQuery.sortDirection === 1 ? 'asc' : 'desc'
        }
        await fetchInvoicesHistoryList(query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}
const fetchInvoicesHistoryList = (query: any) => {
    return store.dispatch('ApStore/fetchInvoicesHistoryList', query)
}

const changePage = () => {
    updateTable()
}

const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer)
    timer = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer)
        }
    }, 1000)
}

const toAddInvoice = () => {
    // router.push({name: 'WoBillsInvoice'})
    visible.value = true
    temporaryFrom.value = ''
}

const printInvoice = async () => {
    try {
        printLoading.value = true
        tableLoading.value = true
        const query = _.map(_.groupBy(selectedRows.value, 'issuer_id'), items => {
            return {
                company_id: items[0].company_id,
                company_code: items[0].company_code,
                company_name: companyTaxInfo.value?.name || '',
                issuer_id: items[0].issuer_id,
                issuer_name: items[0].issuer_name,
                issuer_address: items[0].issuer_address || '',
                street: companyTaxInfo.value?.address_line1 || '',
                city: companyTaxInfo.value?.city || '',
                province: companyTaxInfo.value?.province || '',
                country: companyTaxInfo.value?.country || '',
                post_code: companyTaxInfo.value?.post_code || '',
                check_total_fee: Number(
                    items
                        .reduce((prev: any, curr: any) => {
                            return prev + curr.total_fee
                        }, 0)
                        .toFixed(2),
                ),
                check_date: moment().format('YYYY-MM-DD'),
                creator: userInfo?.id,
                items: items.map((k: any) => {
                    return {
                        posting_date: k.posting_date,
                        reference_no: k.reference_no || '',
                        total_fee: k.total_fee,
                        invoice_id: k.id,
                    }
                }),
            }
        })

        await store.dispatch('ApStore/printInvoice', query)
        await updateTable()
        selectedRowKeys.value = []
        selectedRows.value = []
        message.success(i18n.t('ApComponents.success'))
    } catch (error) {
        console.log(error)
    } finally {
        printLoading.value = false
        tableLoading.value = false
    }
}

const exportApInvoices = async () => {
    try {
        tableLoading.value = true
        const query = {
            company_code: userCompany[0].code,
        }
        await store.dispatch('ApStore/exportApInvoiceList', query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}

const sendEmail = () => {
    console.log('clicked send mail button')
}
const copyInvoice = (record: any, event: any) => {
    record.engine_reverse_document_id = ''
    record.file_url = ''
    record.engine_document_id = ''
    record.reference_no = ''
    current.value = {...record}
    temporaryFrom.value = 'copy'
    visible.value = true
}

const rowClick = (record: any, rowIndex: any, column: any) => {
    return {
        onClick: (event: any) => {
            if (column.key !== 'operation') {
                edit(record)
            }
        },
    }
}

const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)
const customSorter = async (pagination: any, filters: any, sorter: any) => {
    if (sorter.field && sorter.order) {
        const order = sorter.order === 'descend' ? -1 : 1
        pageQuery.sortField = sorter.field
        pageQuery.sortDirection = order
        pageQuery.page_index = 1
    } else {
        pageQuery.sortField = null
        pageQuery.sortDirection = 0
        pageQuery.page_index = 1
    }
    await updateTable()
}
// const handleClose = async (done: () => void) => {
//     await updateTable()
//     done()
// }
const handleClose = async () => {
    console.log('refresh table')
    await updateTable()
}
const cancel = async () => {
    console.log('refresh table')
    current.value = {}
    visible.value = false
    await updateTable()
}

const approveToPay = async (record: any) => {
    console.log('-----------------approveToPay', record)
    if (record.br_flag === 11) {
        const approveRequests = {
            id: record.id,
            status: '12',
            creator: userInfo?.id,
            creator_name: userInfo?.account,
        }
        const res = await updateIntegrationInvoiceStatus(approveRequests)
        if (res.data.statusCode !== 201 && res.data.statusCode !== 200) {
            message.error(res.data.message)
        }
        await updateTable()
    } else {
        message.error('Wrong invice br flag')
    }
}
const rejectToPay = async (record: any) => {
    console.log('-----------------rejectToPay', record)
    if (record.br_flag === 11) {
        const approveRequests = {
            id: record.id,
            status: '10',
            creator: userInfo?.id,
            creator_name: userInfo?.account,
        }
        const res = await updateIntegrationInvoiceStatus(approveRequests)
        if (res.data.statusCode !== 201 && res.data.statusCode !== 200) {
            message.error(res.data.message)
        }
        await updateTable()
    } else {
        message.error('Wrong invice br flag')
    }
}
const submitToPay = async (record: any) => {
    const pdfData = []
    pdfData.push(record)

    if (record.check_print_status === 1) {
        modalPrintCheckData.value = pdfData
        printCheckModal.value = true
    } else {
        await chequePdfBatch(pdfData)
    }

    if (record.br_flag === 12) {
        const approveRequests = {
            id: record.id,
            status: '0',
            creator: userInfo?.id,
            creator_name: userInfo?.account,
        }
        const res = await updateIntegrationInvoiceStatus(approveRequests)
        if (res.data.statusCode !== 201 && res.data.statusCode !== 200) {
            message.error(res.data.message)
        }
        await updateTable()
    } else if (record.br_flag === 0) {
        // showItemEditModal(record)
    } else {
        message.error('Wrong invice br flag')
    }
}

const payMethodChange = async (id: number, value: string) => {
    await store.dispatch('ApStore/patchApInvoiceData', {
        id: id,
        pay_method: value,
    })
}

const commentChange = async (id: number, value: string) => {
    await store.dispatch('ApStore/patchApInvoiceData', {
        id: id,
        invoice_comments: value,
    })
}

const referenceNoChange = async (id: number, value: string) => {
    const referenceNoValidation = await referenceNoValid(value)
    if (!referenceNoValidation) {
        updateTable()
        return
    }
    await store.dispatch('ApStore/patchApInvoiceData', {
        id: id,
        reference_no: value,
    })
}
const referenceNoValid = async (referenceNo: string) => {
    if (referenceNo == null) referenceNo = ''
    const query = {
        company_code: userCompany[0].code,
        reference_no: referenceNo.trim(),
    }
    if (referenceNo !== null && referenceNo.length > 0) {
        const resReferenceValid = await store.dispatch('ApStore/checkReferenceNoRepetitionV1', query)
        if (resReferenceValid.data.data.length > 0) {
            message.error({content: i18n.t('ApComponents.referenceError')})
            return false
        }
    }
    return true
}
const specialPaymentExportCodes: string[] = ['66QW', '68QW']
const chequePdfBatch = async (pdfData: any[]) => {
    for (const row of pdfData) {
        if (newChequeNo.value?.trim()) row.check_print_no = newChequeNo.value

        await store.dispatch('ApStore/patchApInvoiceData', {
            id: row.id,
            check_print_status: 1,
            check_print_no: newChequeNo.value?.trim() || row.check_print_no,
            check_print_time: moment().format('YYYY-MM-DD HH:mm:ss'),
            br_flag: 0,
        })
        row.br_flag = 0
    }

    if (pdfData[0].pay_method === '3' && !specialPaymentExportCodes.includes(userCompany[0].code)) {
        const result = await store.dispatch('ApStore/createChequePdf', pdfData)
        if ((result.status === 201 || result.status === 200) && result.data.code === 200) {
            window.open(result.data.data, '_blank')
        } else {
            message.error(result.data?.data)
        }
    }
    if (specialPaymentExportCodes.includes(userCompany[0].code)) {
        // Extract required fields from pdfData and create Excel file
        try {
            const excelData = pdfData.map(item => ({
                operation: '01',
                issuer_name: item.issuer_name,
                issuer_id: item.issuer_id,
                balance: item.balance,
                reference_no: item.reference_no,
                invoice_comments: item.invoice_comments,
                reference: '',
                invoice_currency: item.invoice_currency,
                iva: '',
                recipient_rfc: '',
                bank_account: '145580007353501016',
            }))

            // Define headers for the Excel file (column names in the Excel file)
            const headers = {
                operation: i18n.t('bkAp.paymentExportColumnOperation'),
                issuer_name: i18n.t('bkAp.paymentExportColumnIssuerName'),
                issuer_id: i18n.t('bkAp.paymentExportColumnIsserId'),
                balance: i18n.t('bkAp.paymentExportColumnImport'),
                reference_no: i18n.t('bkAp.paymentExportColumnReferenceNo'),
                invoice_comments: i18n.t('bkAp.paymentExportColumnInvoiceComments'),
                reference: i18n.t('bkAp.paymentExportColumnReference'),
                invoice_currency: i18n.t('bkAp.paymentExportColumnInvoiceCurrency'),
                iva: i18n.t('bkAp.paymentExportColumnIva'),
                recipient_rfc: i18n.t('bkAp.paymentExportColumnDestinationRfc'),
                bank_account: i18n.t('bkAp.paymentExportColumnBankAccount'),
            }

            // Generate filename with current date
            try {
                const currentDate = moment().format('YYYYMMDD')
                const filename = `Invoice_Data_${currentDate}.xlsx`
                await generateExcelFile(excelData, headers, filename)
                message.success(i18n.t('update.excelGenerated'))
            } catch (error) {
                console.error('Error generating Excel file:', error)
                message.error(i18n.t('update.excelGenerateFailed'))
            }
        } catch (error) {
            console.error('Error generating Excel file:', error)
            message.error(i18n.t('update.excelGenerateFailed'))
        }
    }

    printCheckModal.value = false
    await updateTable()
    selectedRowKeys.value = []
    selectedRows.value = []
}

const batchModal = async () => {
    modalPrintCheckData.value = selectedRows.value
    newChequeNo.value = selectedRows.value[0].check_print_no
    const contact = store.state.ContactStore.contactList.find(
        (item: any) => item.contact_id === selectedRows.value[0].issuer_id,
    )
    if (contact) {
        contact.bank_pay_method.forEach((item: any) => {
            bankPayMethodOptions.value.push({label: item.title, value: item.title})
            if (item.is_default === true) {
                selectBankPayMethod.value = item.title
            }
        })
    }

    printCheckModal.value = true
}
const checkNoChange = async (id: number, value: string) => {
    await store.dispatch('ApStore/patchApInvoiceData', {
        id: id,
        check_print_no: value,
    })
    await updateTable()
}
const handleEnterEvent = (event: any) => {
    event.target.blur()
}
const selectInputValues = (e: FocusEvent) => {
    const target = e.target as HTMLInputElement // 使用类型断言告诉TypeScript不为空
    if (target) {
        target.select()
    }
}

onBeforeMount(async () => {
    userType.value = userInfo.roles === '9996' ? 'jushi' : 'common'
    await Promise.all([fetchCompanyTaxInfo({code: userCompany[0].code}), updateTable()])
})
</script>
<template>
    <div>
        <div class="history-page-wrap">
            <div class="history-page-content">
                <div style="display: flex" class="main-head">
                    <a-tabs v-model:activeKey="activeTabName" @change="switchTab">
                        <a-tab-pane :disabled="tableLoading" key="0"></a-tab-pane>
                        <!-- <a-tab-pane :disabled="tableLoading" :tab="i18n.t('bkApInvoice.payInProcess')"
                            key="null"></a-tab-pane> -->
                    </a-tabs>
                    <div>
                        <div class="history-page-header">
                            <div class="search-group-wrap">
                                <a-input
                                    v-model:value="searchForm.companyName"
                                    :placeholder="$t('ApComponents.search')"
                                    :disabled="tableLoading"
                                    class="search-input"
                                    @input="inputChange"
                                    @pressEnter="search"
                                >
                                    <template #suffix>
                                        <svg-icon name="icon_search"></svg-icon>
                                    </template>
                                </a-input>
                                <a-popover
                                    class="popover-wrap"
                                    trigger="click"
                                    placement="bottom"
                                    @visibleChange="visibleChange"
                                >
                                    <template #content>
                                        <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                                            <div class="search-input-group">
                                                <a-form-item>
                                                    {{ $t('bkApInvoice.date') }}<span>&nbsp;&nbsp;</span>
                                                    <a-date-picker
                                                        v-model:value="searchForm.createStartDate"
                                                        :disabled="tableLoading"
                                                        format="YYYY-MM-DD"
                                                        valueFormat="YYYY-MM-DD"
                                                        :placeholder="$t('gl.createStartDate')"
                                                        style="width: 160px"
                                                        clearable
                                                    >
                                                        <template #suffixIcon>
                                                            <svg-icon name="icon_date"></svg-icon>
                                                        </template>
                                                    </a-date-picker>
                                                </a-form-item>
                                                <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                <a-form-item
                                                    >{{ $t('bkApInvoice.to') }}
                                                    <a-date-picker
                                                        v-model:value="searchForm.createEndDate"
                                                        :disabled="tableLoading"
                                                        format="YYYY-MM-DD"
                                                        valueFormat="YYYY-MM-DD"
                                                        :placeholder="$t('gl.createEndDate')"
                                                        style="width: 160px"
                                                        clearable
                                                    >
                                                        <template #suffixIcon>
                                                            <svg-icon name="icon_date"></svg-icon>
                                                        </template>
                                                    </a-date-picker>
                                                </a-form-item>
                                            </div>
                                            <div class="search-input-group">
                                                <a-form-item>
                                                    <span>{{ $t('bkApInvoice.totalCol') }}</span>
                                                    <span>&nbsp;&nbsp;</span>
                                                    <a-input-number
                                                        v-model:value="searchForm.minTotalFee"
                                                        :controls="false"
                                                        :disabled="tableLoading"
                                                        :placeholder="$t('bkApInvoice.minFee')"
                                                        style="width: 160px"
                                                    ></a-input-number>
                                                </a-form-item>
                                                <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                <a-form-item>
                                                    <a-input-number
                                                        v-model:value="searchForm.maxTotalFee"
                                                        :controls="false"
                                                        :disabled="tableLoading"
                                                        :placeholder="$t('bkApInvoice.maxFee')"
                                                        style="width: 160px"
                                                    ></a-input-number>
                                                </a-form-item>
                                            </div>
                                            <div class="search-input-group">
                                                <a-form-item :label="$t('bkArInvoice.br')" v-if="activeTabName !== '0'">
                                                    <a-select
                                                        default-first-option
                                                        v-model:value="searchForm.brFlag"
                                                        style="width: 355px"
                                                        :controls="false"
                                                        :disabled="tableLoading"
                                                    >
                                                        <a-select-option
                                                            style="width: 355px"
                                                            v-for="item in brFlagOptions"
                                                            :key="item.label"
                                                            :value="item.value"
                                                            >{{ item.label }}</a-select-option
                                                        >
                                                    </a-select>
                                                </a-form-item>
                                            </div>
                                            <div class="search-input-group">
                                                <a-form-item>
                                                    {{ i18n.t('bkArInvoice.due') }}
                                                    <span>&nbsp;&nbsp;&nbsp;</span>
                                                    <a-switch v-model:checked="searchForm.due" />
                                                </a-form-item>
                                            </div>
                                            <a-button
                                                type="primary"
                                                shape="round"
                                                :disabled="tableLoading"
                                                @click="search"
                                            >
                                                <template #icon>
                                                    <svg-icon name="icon_search"></svg-icon>
                                                </template>
                                                {{ $t('commonTag.search') }}
                                            </a-button>
                                        </a-form>
                                    </template>
                                    <a-tooltip :title="$t('commonTag.filter')">
                                        <a-button class="search-button" :disabled="tableLoading">
                                            <template #icon>
                                                <svg-icon name="icon_filter"></svg-icon>
                                            </template>
                                            <!--                                        {{ i18n.t('commonTag.filter') }}-->
                                        </a-button>
                                    </a-tooltip>
                                </a-popover>

                                <a-tooltip :title="$t('commonTag.columns')">
                                    <a-button
                                        class="search-button"
                                        :disabled="tableLoading"
                                        @click="showColumns = !showColumns"
                                    >
                                        <template #icon>
                                            <svg-icon name="icon_columns"></svg-icon>
                                        </template>
                                        <!-- {{ activeTabName }} -->
                                        <!--                                       {{ i18n.t('commonTag.columns') }}-->
                                    </a-button>
                                </a-tooltip>

                                <a-tooltip :title="$t('workTimeManager.export')">
                                    <a-button
                                        class="search-button"
                                        :disabled="tableLoading"
                                        @click="exportApInvoices()"
                                    >
                                        <export-outlined />
                                        <!--                                       {{ i18n.t('workTimeManager.export') }}-->
                                    </a-button>
                                </a-tooltip>
                                <a-button
                                    class="search-button"
                                    :disabled="!selectedRowKeys.length"
                                    type="primary"
                                    shape="round"
                                    @click="batchModal"
                                >
                                    <!--                                    <template #icon>-->
                                    <!--                                        <dollar-outlined></dollar-outlined>-->
                                    <!--                                    </template>-->
                                    {{ i18n.t('ApComponents.pay') }}
                                </a-button>
                                <!-- <a-button class="search-button" :disabled="!selectedRowKeys.length"
                                    v-show="activeTabName === '0'" type="primary" @click="printInvoice()" ghost
                                    :loading="printLoading">
                                    <template #icon>
                                        <svg-icon name="icon_print"></svg-icon>
                                    </template>
                                    {{ i18n.t('commonTag.print') }}
                                </a-button> -->
                            </div>
                            <div class="button-wrap">
                                <!-- <a-button type="primary" v-show="userType !== 'jushi'" shape="round" class="add-button"
                                    @click="toAddInvoice()">
                                    <template #icon>
                                        <svg-icon name="icon_add"></svg-icon>
                                    </template>
                                    {{ i18n.t('commonTag.new') }}
                                </a-button> -->

                                <a-modal
                                    v-model:visible="visible"
                                    width="1300px"
                                    :title="i18n.t('bkApInvoice.create')"
                                    :footer="null"
                                    :afterClose="handleClose"
                                    destroyOnClose
                                    @cancel="cancel"
                                >
                                    <!-- <wo-bills-invoice
                                        :currentInvoice="current"
                                        :from="from.value"
                                        @custom-cancel="cancel"
                                    /> -->
                                    <wo-bills-invoice
                                        :currentInvoice="current"
                                        :from="temporaryFrom"
                                        @custom-cancel="cancel"
                                    />
                                    <!-- <wo-bills-invoice @custom-cancel="cancel" /> -->
                                </a-modal>
                            </div>
                        </div>
                    </div>
                </div>

                <a-table
                    :dataSource="invoicesHistoryList"
                    :row-selection="rowSelection"
                    :loading="tableLoading"
                    :pagination="false"
                    :scroll="customizeTable.length > 5 ? {x: 1350, y: 'calc(100vh - 300px)'} : {x: 'auto'}"
                    @change="customSorter"
                    rowKey="id"
                >
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.issuer')"
                        data-index="issuer_name"
                        sorter="true"
                        :ellipsis="true"
                        width="200px"
                        v-if="customizeTable.includes('issuer')"
                    />
                    <a-table-column
                        align="center"
                        :title="i18n.t('bkApInvoice.referenceNo')"
                        data-index="reference_no"
                        sorter="true"
                        v-if="customizeTable.includes('referenceNo')"
                        width="110px"
                    >
                        <template #default="{record}">
                            {{ referenceNoList[record.id] }}
                            <!--                            <a-input-->
                            <!--                                v-model:value="checkPrintNoList[record.id]"-->
                            <!--                                @pressEnter="referenceNoChange(record.id, checkPrintNoList[record.id])"-->
                            <!--                            />-->
                        </template>
                    </a-table-column>
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.postingDate')"
                        data-index="posting_date"
                        sorter="true"
                        v-if="customizeTable.includes('postingDate')"
                        width="100px"
                        :ellipsis="true"
                    />
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.total')"
                        data-index="total_fee"
                        sorter="true"
                        v-if="customizeTable.includes('total')"
                        width="80px"
                        :ellipsis="true"
                    >
                        <template #default="{record}">
                            <span>
                                <!-- {{ Number(record.total_fee).toFixed(2) }} -->
                                {{ $formatNumber(Number(record.total_fee)) }}
                            </span>
                        </template>
                    </a-table-column>
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.balance')"
                        data-index="balance"
                        sorter="true"
                        v-if="customizeTable.includes('balance')"
                        width="80px"
                        :ellipsis="true"
                    >
                        <template #default="{record}">
                            <span>
                                <!-- {{ Number(record.balance).toFixed(2) }} -->
                                {{ $formatNumber(Number(record.balance)) }}
                            </span>
                        </template>
                    </a-table-column>
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.status')"
                        data-index="br_flag"
                        sorter="true"
                        v-if="customizeTable.includes('status')"
                        width="100px"
                        :ellipsis="true"
                    >
                        <template #default="{record}">
                            <span v-if="record.br_flag === 0">
                                <a-tag v-if="apIntegration !== 1" class="tag-red">
                                    {{ i18n.t('bkApInvoice.notPaid') }}</a-tag
                                >
                                <a-tag v-else-if="apIntegration === 1 && paymentIntegration != 1" class="tag-red">
                                    {{ i18n.t('bkApInvoice.posted') }}</a-tag
                                >
                                <a-tag v-else class="tag-green">{{ i18n.t('bkApInvoice.pmntExecuted') }}</a-tag>
                            </span>
                            <span v-else-if="record.br_flag === 1">
                                <a-tag class="tag-orange">{{ i18n.t('bkApInvoice.partialPaid') }}</a-tag>
                            </span>
                            <span v-else-if="record.br_flag === 2">
                                <a-tag class="tag-green">{{ i18n.t('bkApInvoice.paid') }}</a-tag>
                            </span>
                            <span v-else-if="record.br_flag === 3">
                                <a-tag class="tag-gray">{{ i18n.t('bkApInvoice.reversed') }}</a-tag>
                            </span>
                            <span v-else-if="record.br_flag === 10">
                                <a-tag class="tag-red">{{ i18n.t('bkApInvoice.parked') }}</a-tag>
                            </span>
                            <span v-else-if="record.br_flag === 11">
                                <a-tag class="tag-gray">{{ i18n.t('bkApInvoice.posted') }}</a-tag>
                            </span>
                            <span v-else-if="record.br_flag === 12">
                                <a-tag class="tag-orange">{{ i18n.t('bkApInvoice.pmntApproved') }}</a-tag>
                            </span>
                        </template>
                    </a-table-column>
                    <a-table-column
                        align="center"
                        :title="i18n.t('bkApInvoice.payMethod')"
                        data-index="pay_method"
                        width="200px"
                        sorter="true"
                        v-if="customizeTable.includes('payMethod')"
                    >
                        <template #default="{record}">
                            <a-select
                                ref="select"
                                v-model:value="record.pay_method"
                                style="width: 190px"
                                :disabled="
                                    record.pay_method === '2' ||
                                    (record.pay_method !== '2' &&
                                        (record.br_flag === 0 ||
                                            record.br_flag === 1 ||
                                            record.br_flag === 2 ||
                                            record.br_flag === 3))
                                "
                                :options="optionPayMethod"
                                @change="payMethodChange(record.id, record.pay_method)"
                            />
                        </template>
                    </a-table-column>
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.invoiceNo')"
                        data-index="invoice_no"
                        sorter="true"
                        v-if="customizeTable.includes('invoiceNo')"
                        width="220px"
                        :ellipsis="true"
                    />
                    <a-table-column
                        align="center"
                        :title="i18n.t('bkApInvoice.invoiceComment')"
                        data-index="invoice_comments"
                        v-if="customizeTable.includes('invoiceComment')"
                        width="120px"
                    />
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.creator')"
                        data-index="creator_name"
                        sorter="true"
                        :ellipsis="true"
                        v-if="customizeTable.includes('creator')"
                        width="100px"
                    />
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.createTime')"
                        data-index="create_time"
                        sorter="true"
                        :ellipsis="true"
                        v-if="customizeTable.includes('createTime')"
                        width="100px"
                    />
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.createDate')"
                        data-index="invoice_create_date"
                        sorter="true"
                        v-if="customizeTable.includes('createDate')"
                        width="140px"
                        :ellipsis="true"
                    />
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.dueDate')"
                        data-index="invoice_due_date"
                        sorter="true"
                        v-if="customizeTable.includes('dueDate')"
                        width="150px"
                        :ellipsis="true"
                    />
                    <!-- <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.printStatus')"
                        data-index="check_print_status"
                        sorter="true"
                        v-if="customizeTable.includes('printStatus')"
                        width="100px"
                        :ellipsis="true"
                    >
                        <template #default="{record}">
                            <span v-if="record.check_print_status === 1">
                                <a-tag class="tag-green">{{ i18n.t('bkApInvoice.printStatus1') }}</a-tag>
                            </span>
                            <span v-else-if="record.check_print_status === 0">
                                <a-tag class="tag-gray">{{ i18n.t('bkApInvoice.printStatus0') }}</a-tag>
                            </span>
                        </template>
                    </a-table-column> -->
                    <a-table-column
                        align="center"
                        :title="i18n.t('bkApInvoice.checkNo')"
                        data-index="check_print_no"
                        sorter="true"
                        v-if="customizeTable.includes('checkNo')"
                        width="120px"
                    >
                        <template #default="{record}">
                            <a-input
                                v-model:value="checkPrintNoList[record.id]"
                                @blur="checkNoChange(record.id, checkPrintNoList[record.id])"
                                @pressEnter="handleEnterEvent"
                                ref="checkNoDom"
                                :disabled="
                                    record.br_flag === 1 ||
                                    record.br_flag === 2 ||
                                    record.br_flag === 3 ||
                                    record.pay_method != '3'
                                "
                                @focus="selectInputValues"
                            />
                        </template>
                    </a-table-column>
                    <a-table-column
                        :custom-cell="rowClick"
                        align="center"
                        :title="i18n.t('bkApInvoice.checkPrintTime')"
                        data-index="check_print_time"
                        sorter="true"
                        :ellipsis="true"
                        v-if="customizeTable.includes('checkPrintTime')"
                        width="150px"
                    />
                    <!-- <a-table-column
                        align="center"
                        :title="i18n.t('bkApInvoice.operation')"
                        key="operation"
                        fixed="right"
                        width="80px"
                        :custom-cell="rowClick"
                    >
                        <template #default="{record}">
                            <span>
                                <a-button
                                    v-if="record.br_flag === 11"
                                    type="link"
                                    style="color: red"
                                    class="br-icon"
                                    @click="rejectToPay(record)"
                                >
                                    <close-outlined />
                                </a-button>
                                <a-button
                                    v-if="record.br_flag === 11"
                                    type="link"
                                    class="br-icon"
                                    @click="approveToPay(record)"
                                >
                                    <check-outlined />
                                </a-button>
                            </span>
                        </template>
                    </a-table-column> -->

                    <!-- <a-table-column align="center" :title="i18n.t('bkApInvoice.operation')" key="operation"
                        fixed="right" width="90px" :custom-cell="rowClick">
                        <template #default="{ record }">
                            <div style="display: grid; grid-template-columns: 30px 30px 30px; gap: 8px;">
                                <a-button v-if="record.br_flag === 11" type="link" style="color: red;">
                                    <undo-outlined />
                                </a-button>
                                <a-button v-else style="visibility: hidden;"></a-button>
                                <a-button v-if="record.br_flag === 11" type="link">
                                    <check-outlined />
                                </a-button>
                                <a-button v-else style="visibility: hidden;"></a-button>
                                <a-button v-if="record.br_flag === 12 || record.br_flag === 0" type="link">
                                    <dollar-outlined></dollar-outlined>
                                </a-button>
                                <a-button v-else style="visibility: hidden;"></a-button>
                            </div>
                        </template>
                    </a-table-column> -->
                </a-table>
                <div class="pagination-wrap">
                    <a-pagination
                        v-model:current="pageQuery.page_index"
                        v-model:page-size="pageQuery.page_size"
                        :disabled="tableLoading"
                        :hideOnSinglePage="false"
                        :showSizeChanger="true"
                        :total="totalNumber"
                        @change="changePage"
                    />
                    <span
                        >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                        {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                    >
                </div>
            </div>
        </div>
        <a-modal
            :title="i18n.t('columns.modalTitle')"
            v-model:visible="showColumns"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="480"
            :wrapClassName="'modal-wrap'"
        >
            <custom-columns
                :defaultTable="defaultTable"
                :customizeTable="customizeTable"
                prefix="bkApInvoice"
                @dismiss="dismissColumns"
                @save="saveColumns"
            ></custom-columns>
        </a-modal>
        <a-modal
            :title="i18n.t('bkApInvoice.readonly')"
            v-model:visible="showDetails"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="'1110px'"
            :bodyStyle="{padding: '20px'}"
            :wrapClassName="'modal-wrap'"
        >
            <ap-invoice-component
                v-if="apIntegration !== 1"
                operation-mode=""
                :current-invoice="current"
                :readonly-mode="true"
                @dismiss="dismissDetails"
                @update="updateDetails"
            >
            </ap-invoice-component>
            <ap-invoice-component-integration-mx
                v-if="apIntegration === 1 && INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                operation-mode=""
                :current-invoice="current"
                :readonly-mode="true"
                @dismiss="dismissDetails"
                @update="updateDetails"
                @copy="copyInvoiceAfterReverse"
            >
            </ap-invoice-component-integration-mx>
            <ap-invoice-component-integration
                v-if="apIntegration === 1 && !INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                operation-mode=""
                :current-invoice="current"
                :readonly-mode="true"
                @dismiss="dismissDetails"
                @update="updateDetails"
            >
            </ap-invoice-component-integration>
        </a-modal>
        <a-modal
            v-model:visible="itemEditModal"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="520"
            :wrapClassName="'modal-wrap'"
            :bodyStyle="{padding: '10px', 'margin-left': '30px'}"
        >
            <a-form :model="currentEditItem" :layout="'vertical'" autocomplete="off" @submit="confirmItemEditModal">
                <div class="import-form-block">
                    <div>&nbsp;</div>
                    <div>&nbsp;</div>
                    <div>You have download all the docuemnts for this invoice.</div>
                    <div>Are you sure to do again ?</div>
                    <div>&nbsp;</div>
                </div>
                <div style="text-align: right">
                    <a-button shape="round" class="cancel-button" @click="closeItemEditModal">Cancel</a-button>
                    <span style="margin: 0 5px"></span>
                    <a-button shape="round" type="primary" html-type="submit">Confirm</a-button>
                </div>
            </a-form>
        </a-modal>
        <a-modal
            v-model:visible="printCheckModal"
            destroyOnClose
            :closeable="true"
            :width="520"
            :wrapClassName="'modal-wrap'"
            :bodyStyle="{padding: '10px', 'margin-left': '30px'}"
            :title="lodash.find(modalPrintCheckData, ['check_print_status', 1]) ? 'Warning' : 'Print'"
            okText="Pay"
            @ok="chequePdfBatch(modalPrintCheckData)"
        >
            <div v-if="lodash.find(modalPrintCheckData, ['pay_method', '1'])">
                <p v-if="lodash.find(modalPrintCheckData, ['check_print_status', 1])">
                    {{ i18n.t('bkApInvoice.printApCheckWarning') }}
                </p>
                <a-form :layout="'vertical'" class="form-box" autocomplete="off">
                    <a-form-item>
                        <a-select
                            v-model:value="selectBankPayMethod"
                            :options="bankPayMethodOptions"
                            placeholder="Please select bank pay method"
                        />
                    </a-form-item>
                </a-form>
            </div>

            <div class="page-container-changepwd_form" v-if="lodash.find(modalPrintCheckData, ['pay_method', '3'])">
                <a-form :layout="'vertical'" class="form-box" autocomplete="off">
                    <a-form-item>
                        <a-input v-model:value="newChequeNo" placeholder="Please input cheque no" />
                    </a-form-item>
                </a-form>
            </div>
            <a-divider class="footer-divider" />
            <div>
                <strong>{{ i18n.t('ApComponents.amount') + ': ' }}</strong>
                <strong style="color: #0e91ff">{{ lodash.sumBy(modalPrintCheckData, 'balance') }}</strong>
            </div>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
.history-page-wrap {
    border-radius: 12px;
    background-color: #fff;
    height: 100%;
    /* 设置容器的高度为父容器高度的80% */
    overflow-y: hidden;
    /* 隐藏纵向滚动条 */

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100%;
        /* 设置容器的高度为父容器高度的80% */
        overflow-y: hidden;
        /* 隐藏纵向滚动条 */
        // border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 10px 10px;

            .search-input {
                width: 240px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 60px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button + .search-button {
                min-width: 60px;
            }

            .popover-wrap:deep(.ant-popover-placement-bottom) {
                width: 432px;
            }
        }

        .button-wrap {
            display: flex;
            align-items: center;
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;

            &.pay .anticon {
                display: flex;
            }

            & + .add-button {
                margin-left: 8px;
            }
        }
    }

    .history-page-content {
        padding: 12px 20px;
        height: 80%;
        /* 设置容器的高度为父容器高度的80% */
        overflow-y: hidden;

        /* 隐藏纵向滚动条 */
        .main-head {
            display: flex;
            justify-content: space-between;
        }

        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
            padding-left: 8px;
            padding-right: 8px;
        }

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
}

.search-input-form {
    display: flex;
    flex-direction: column;
    align-items: end;

    .search-input-group {
        display: flex;

        .ant-form-item {
            margin-bottom: 12px;
        }

        :deep(.ant-form-item-label) {
            width: 55px;
        }

        .ant-form-item + .ant-form-item :deep(.ant-form-item-label) {
            width: 35px;
        }
    }
}

.br-icon {
    width: 10px;
    font-size: 20px;
}
</style>
