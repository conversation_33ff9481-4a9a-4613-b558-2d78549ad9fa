<!-- @format -->

<script lang="ts" setup>
// 添加导入
import { ref, computed } from 'vue'
import { UserCompany } from '@/lib/storage'

import { useStore } from 'vuex'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'

import { notification, message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { Ap_Integration } from '@/lib/storage'
import ApInvoiceComponent from '../../../components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ApInvoiceComponentIntegration from '../../../components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegration.vue'
import ApInvoiceComponentIntegrationMx from '../../../components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegrationMx.vue'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'

const i18n: Composer = i18nInstance.global
const apIntegration: any = Ap_Integration.get() ?? 0
// 添加 userCompany 获取
const userCompany = UserCompany.get() || []

const props = withDefaults(
    defineProps<{
        from: string
        currentInvoice: any
    }>(),
    {
        currentInvoice: {},
        from: '',
    },
)

interface ApInvoiceFrom {
    create_time: any
    id: any
    pay_method: string
    company_name: string
    reference_no: string
    invoice_due_date: string
    posting_date: string
    invoice_currency: string
    net_amount: number
    items: any[]
    invoice_comments: number
    gst: number
    pst: number
    qst: number
    total_fee: number
    po: string
    purpose: string
    company_code: string
}

const emits = defineEmits(['custom-cancel', 'dismissDetails'])

const store = useStore()
const router = useRouter()

const tableLoading = ref(false)
const current = ref({})

const invoiceForm: any = ref(null)
const delay = (callback: () => void, ms: number) => {
    setTimeout(callback, ms);
}

const save = async (form: ApInvoiceFrom) => {
    let response: any = {}
    try {
        tableLoading.value = true

        // if (userCompany[0].country === 'MX') {
        //     message.success({
        //         content: 'Successfully certified by SAT', //'Invoice must contain at least one [ Item ]',
        //         duration: 5,
        //     })
        //     emits('custom-cancel')
        //     return
        // }
        //TODO: test pay_method === 2 scenario
        // if (form.pay_method === '2') {
        //     const payload = {
        //         company_name: form.company_name,
        //         statement_id: '-1',
        //         invoice_type: '2',
        //         reference_no: form.reference_no,
        //         invoice_due_date: form.invoice_due_date, //2022-01-24
        //         posting_date: form.posting_date, //2022-01-25
        //         bankAccount: '',
        //         invoice_currency: form.invoice_currency,
        //         net_amount: form.net_amount,
        //         expense_account_id: form.items[0].expense_account_id,
        //         invoice_comments: form.invoice_comments,
        //         items: form.items,
        //         gst: form.gst,
        //         pst: form.pst,
        //         qst: form.qst,
        //         expense_account: form.items[0].expense_account, //coa in item
        //         esAmount: form.total_fee, //total
        //         total_fee: form.total_fee, //total
        //         pay_method: form.pay_method,
        //     }
        //     // response = await store.dispatch('ApStore/noestatementBR', payload)
        //
        // } else {
        //     response = await store.dispatch('ApStore/createInvoiceV1', form)
        // }
        // console.log('fadsdfasfafasfdasf', form)
        if (form.id) {
            form.id = null
            delete form.create_time
        }
        form.items.forEach(item => {
            delete item.id
            delete item.create_time
        })
        if (apIntegration === 1 && form.po != null && form.po.length > 0) {
            console.log('ap form with po ============= ', form)
            if (INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && form.purpose === 'CREDIT_MEMO') {
                response = await store.dispatch('ApStore/createIntegrationInvoiceWithCmPoV1', form)
            } else {
                response = await store.dispatch('ApStore/createIntegrationInvoiceWithPoV1', form)
            }
        } else if (apIntegration === 1) {
            response = await store.dispatch('ApStore/createIntegrationInvoiceWithoutPoV1', form)
        } else {
            response = await store.dispatch('ApStore/createInvoiceV1', form)
        }
        if (response.data.statusCode === 200) {
            console.log('response.data========', response.data)
            // get contact if first useing
            const contactFirstUsing = await store.dispatch('ApStore/fetchContactFirstUsingV1', {
                company_code: response.data.data.company_code,
                contact_id: response.data.data.issuer_id,
            })

            if (apIntegration === 1) {
                store.dispatch('ApStore/saveSAPBP', {
                    company_code: response.data.data.company_code,
                    issuer_id: response.data.data.issuer_id,
                })
                await store.dispatch('ApStore/postSapMasterTopV1', {
                    company_code: response.data.data.company_code,
                    contact_id: response.data.data.issuer_id,
                    contact_name: response.data.data.issuer_name,
                    gl_account_code: response.data.data.items[0].sap_gl_account ?? '',
                    wbs_code: response.data.data.items[0].sap_wbs ?? '',
                    cost_center_code: response.data.data.items[0].sap_cost_center ?? '',
                    internal_order_code: response.data.data.items[0].sap_internal_order ?? '',
                    profit_center_code: response.data.data.items[0].sap_profit_center ?? '',
                    gl_account_name: '',
                    wbs_name: '',
                    cost_center_name: '',
                    internal_order_name: '',
                    profit_center_code_name: '',
                })
            }
            // create approval flow
            if (apIntegration === 1 && response.data.data.pay_method !== '2') {
                const resCreateFlow = await store.dispatch('ApStore/createApprovalFlowData', response.data.data)
                if (resCreateFlow.data.statusCode === 200) {
                    const resApproveFlow = await store.dispatch('ApStore/startApprovalFlow', response.data.data)
                    if (resApproveFlow.sap_status !== 2) {
                        message.error({content: resApproveFlow.sap_msg})
                        return
                    }
                }
            }
            // if (apIntegration === 1 && response.data.data.pay_method === '2' && (form.po == null || form.po == '')) {
            //     // send ap data to sap (credit)
            //     const resSap = await store.dispatch('ApStore/sendApDataToSap', response.data.data)
            //     if (resSap.data.sap_status !== 2) {
            //         message.error({content: resSap.data.sap_msg})
            //         return
            //     }
            // }
            if (contactFirstUsing === 0) {
                message.success(`success. ${i18n.t('ApComponents.contactFirstUsing')}`)
            } else {
                message.success(i18n.t('ApComponents.success'))
            }
            invoiceForm.value.initFormData()
            const content_id = document.getElementById('content_id')
            if (content_id) {
                content_id.scrollIntoView({ behavior: 'smooth' })
            }
            await router.push({ path: '/bookkeeping/ap/invoiceHistory' })
        } else {
            // message.error({
            //     content: response.data.message,
            // })
        }
        emits('custom-cancel')
    } catch (error: any) {
        console.log(error)
        // message.error(error.response.data.message)
    } finally {
        tableLoading.value = false
    }
}
const dismiss = () => {
    // router.go(-1)
    emits('custom-cancel')
}
</script>
<template>
    <div class="page-container-invoice" id="content_id">
        <a-spin :tip="apIntegration === 1 ? i18n.t('commonTag.sapTip') : ''" :spinning="tableLoading"
            wrapperClassName="custom-spin">
            <ap-invoice-component v-if="apIntegration !== 1" ref="invoiceForm" :current-invoice="props.currentInvoice"
                :from="props.from" :readonly-mode="false" :operationMode="'creating'" @save="save"
                @dismiss="dismiss"></ap-invoice-component>
            <ap-invoice-component-integration-mx v-if="apIntegration === 1 && INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                ref="invoiceForm" :current-invoice="props.currentInvoice" :from="props.from" :readonly-mode="false"
                :operationMode="'creating'" @save="save" @dismiss="dismiss"></ap-invoice-component-integration-mx>
            <ap-invoice-component-integration v-if="apIntegration === 1 && userCompany[0].country != 'MX'"
                ref="invoiceForm" :current-invoice="props.currentInvoice" :from="props.from" :readonly-mode="false"
                :operationMode="'creating'" @save="save" @dismiss="dismiss"></ap-invoice-component-integration>
        </a-spin>
    </div>
</template>

<style lang="scss" scoped>
.page-container-invoice {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .ap-invoice-page-wrap {
        :deep(.ap-invoice-block) {
            background-color: #fff;
            padding: 32px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            border-bottom: 0;
        }

        :deep(.ap-invoice-amount-block .ap-invoice-amount-block-left) {
            width: 510px;
        }

        :deep(.ap-invoice-amount-block) {
            padding-bottom: 0;
        }

        :deep(.textarea-wrap) {
            margin-bottom: 32px;
        }

        :deep(.ap-invoice-footer) {
            margin-bottom: 40px;
        }
    }
}

:deep(.custom-spin > div > .ant-spin .ant-spin-text) {
    top: calc(50% + 36px);
}
</style>
