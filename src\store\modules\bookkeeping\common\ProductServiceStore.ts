/** @format */

import type {ActionContext} from 'vuex'
// import service from '../../../../api/request'
import service from '../../../../api/requestNew'
import { UnderlineOutlined } from '@ant-design/icons-vue'

const http = service

const ProductServiceStore = {
    namespaced: true,
    state: {
        ProductServiceList: [],
        totalNumber: 0,
        // coaList: [],
    },
    mutations: {
        updateList(state: {ProductServiceList: any[]}, list: any) {
            state.ProductServiceList = [...list]
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        // updateCoaList(state: {coaList: any[]}, list: any) {
        //     state.coaList = [...list]
        // },
    },
    actions: {
        async fetchProductServiceList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get(`/system-preferences/api/v1/product-service`, {params: payload})

            if(payload.$limit == -1) {
                store.commit('updateList', response.data)
                store.commit('updateTotalFoundNumber', response.data.length)
            }else {
                store.commit('updateList', response.data.data)
                store.commit('updateTotalFoundNumber', response.data.total)
            }
            // change to 2w as no total now
            return response
        },
        async createProductService(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.post('/system-preferences/api/v1/product-service', payload)
        },
        async updateProductService(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.patch(`/system-preferences/api/v1/product-service/${payload.id}`, payload)
        },
        async deleteProductService(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/system-preferences/api/v1/product-service/${payload}`)
        },
        // async fetchCoaList(
        //     store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        //     payload: any,
        // ) {
        //     // const queryObj: any = {}
        //     // queryObj['company'] = payload?.company_code
        //     // if (payload?.bk_type === 1) {
        //     //     queryObj.category = 'Account Receivable'
        //     // }
        //     // if (payload?.bk_type === 2) {
        //     //     queryObj.category = 'Account Payable'
        //     // }
        //     // if (payload?.bk_type === 3) {
        //     //     queryObj.category = 'Bank'
        //     // }
        //     const queryParams = new URLSearchParams()
        //     queryParams.append('company', payload?.company_code)

        //     queryParams.append('is_archive', 'false')
        //     // queryParams.append('page_index', '1')
        //     // queryParams.append('page_size', '9999')
        //     if (payload.category) {
        //         queryParams.append('category', payload?.category)
        //     }
        //     if (payload.bk_type) {
        //         queryParams.append('bk_type', payload?.bk_type)
        //     }
        //     if (payload.bk_type && payload.bk_type !== 3) {
        //         queryParams.append('bk_type', '0')
        //     } // for ap& ar both
        //     // bk_type: ar use 1,  ap use 2, bank use 3, 0 is for ap & ar both
        //     const response = await http.get('/bkp-engine/bk/coa', {params: queryParams})
        //     const res = response.data.data.gl_account_list || response.data.data.rows
        //     store.commit('updateCoaList', res)
        //     return response
        // },
    },
}

export default ProductServiceStore
