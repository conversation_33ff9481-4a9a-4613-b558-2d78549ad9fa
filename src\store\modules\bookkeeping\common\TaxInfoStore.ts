/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'
import serviceV1 from '../../../../api/requestNew'

const http = service
const httpV1 = serviceV1

const TaxInfoStore = {
    namespaced: true,
    state: {
        companyTaxInfo: {},
        companyLogoUrl: '',
        taxInfoList: {},
    },
    mutations: {
        updateCompanyTaxInfo(state: {companyTaxInfo: any}, info: any) {
            state.companyTaxInfo = {...info}
        },
        updateCompanyLogoUrl(state: {companyLogoUrl: any}, url: any) {
            state.companyLogoUrl = url
        },
        updatetaxInfoList(state: {taxInfoList: any}, info: any) {
            state.taxInfoList = {...info}
        },
    },
    actions: {
        async fetchCompanyTaxInfo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await http.get('/bk/tax/company')
            const {companyLogo, ...CompanyRestInfo} = response.data.data
            store.commit('updateCompanyTaxInfo', CompanyRestInfo)
            store.commit('updateCompanyLogoUrl', companyLogo)
            return response
        },
        async fetchCompanyTaxInfoV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, query: any) {
            const response = await httpV1.get(`/users/api/v1/company`, {params: query})
            if (response.data.data.length > 0) {
                const {logo, ...CompanyRestInfo} = response.data.data[0]
                store.commit('updateCompanyTaxInfo', CompanyRestInfo)
                store.commit('updateCompanyLogoUrl', logo)
            }

            return response
        },
        async updateCompanyTax(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.put('/bk/tax/company', payload)
        },
        async updateCompanyTaxV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const reqUrl = `/users/api/v1/company/${payload.id ?? ''}`
            return httpV1.put(reqUrl, payload)
        },
        async uploadLogo(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {raw: string | Blob},
        ) {
            const formData = new FormData()
            formData.append('file', payload.raw)
            const response = await http.post('/bk/tax/company/logo', formData)
            return response
        },
        async uploadLogoV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            // payload: {raw: string},
            payload: any,
        ) {
            const response = await httpV1.patch(`/users/api/v1/company/${payload ? payload.id : ''}`, {
                logo: payload.uploadFile,
            })
            return response
        },
        async fetchCompanyLogo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await http.get('/bk/tax/company/logo')
            return response
        },
        async fetchCompanyLogov1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, query: any) {
            return httpV1.get(`/users/api/v1/company/${query ? query.id : ''}`)
        },
        async removelogo(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await http.put('/bk/tax/company', {companyLogo: ''})
            return response
        },
        async fetchTaxInfoList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await httpV1.get('/api/v1/tax-info')
            store.commit('updatetaxInfoList', response.data.data)
            return response
        },
    },
}

export default TaxInfoStore
