/** @format */

import service from '../../api/request'
import {Token} from '@/lib/storage'
import type {ActionContext} from 'vuex'

const state = () => ({
    userInfo: {
        systemType: '',
        loginId: '',
        loginAccount: '',
        userName: '',
        companyOrgId: '',
        companyName: '',
        position: '',
        departmentId: '',
        departmentName: '',
        rosterAccountId: '',
        token: '',
        workNo: '',
        country: '',
        isAdmin: '',
        language: 'ch',
    },
})
const mutations = {
    setUserInfo(state: {userInfo: any}, userInfo: any) {
        state.userInfo = {...userInfo}
    },
}
const actions = {
    async fetchUserInfo({commit}: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
        const response = await service.get('/system/user/info')
        if (response.data.code === 1000) {
            commit('setUserInfo', response.data.data)
        }
        return response
    },
    async zzLogin(_: ActionContext<{[key: string]: any}, Record<string, unknown>>, password: string | Blob) {
        const form = new FormData()
        form.append('loginAccount', 'wjds')
        form.append('loginToken', password)
        const response = await service.post('/system/user/login', form)
        if (response.data.code === 1000) {
            const token = response.data.data
            Token.set(token)
            return 'success'
        } else {
            return response.data.msg
        }
    },
}
export default {
    state,
    mutations,
    actions,
}
