apiVersion: apps/v1
kind: Deployment
metadata:
  name: eo-bkp-frontend-v2
  annotations:
    gke-gcsfuse/volumes: "true"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eo-bkp-frontend-v2
  template:
    metadata:
      labels:
        app: eo-bkp-frontend-v2
      annotations:
        gke-gcsfuse/volumes: "true" # required
    spec:
      serviceAccountName: nt-eo-gcp-cs
      containers:
      - image: northamerica-northeast2-docker.pkg.dev/nt-eo-gb/nt-eo-repo/eo-bkp-frontend-v2:test-latest
#          command: ["/bin/sh", "-c", "cp /opt/nginx-prod.conf /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"]
        name: eo-bkp-frontend-v2
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        volumeMounts:
        - name: files
          mountPath: /home/<USER>/work/upload
          # subPath: bkp/upload
        - name: nginx
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: nginx-ssl-certificate
          mountPath: /home/<USER>/cert/inossemtimes.crt
          subPath: inossemtimes.crt
        - name: nginx-ssl-certificate-key
          mountPath: /home/<USER>/cert/inossemtimes.rsa
          subPath: inossemtimes.rsa
      volumes:
      - name: files
        persistentVolumeClaim:
          claimName: eo-files-test-pvc-bkp
      - name: nginx
        configMap:
          name: nginx-configmap
      - name: nginx-ssl-certificate
        secret:
          secretName: nginx-ssl-certificate
      - name: nginx-ssl-certificate-key
        secret:
          secretName: nginx-ssl-certificate-key


