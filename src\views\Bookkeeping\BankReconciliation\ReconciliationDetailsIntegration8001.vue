<!-- @format -->

<script lang="ts" setup>
import SvgIcon from '@/components/SvgIcon.vue'
import { computed, onBeforeMount, reactive, ref } from 'vue'
import { useStore } from 'vuex'
import i18nInstance from '@/locales/i18n'
import type { TableColumnType } from 'ant-design-vue'
import type { Composer } from 'vue-i18n'
import _ from 'lodash'
import { message } from 'ant-design-vue'
import { UserCompany, UserInfo, Ap_Integration, Ar_Integration } from '@/lib/storage'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ApInvoiceComponentIntegrationMx from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegrationMx.vue'
import ArInvoiceComponent from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponent.vue'

const store = useStore()
const i18n: Composer = i18nInstance.global
const userInfo: any = UserInfo.get() || {}
const userCompany: any = UserCompany.get() || []
const confirmationWrap = ref<HTMLElement | null>(null)
const showPostingDate = ref(false)
const postingDate = ref('')
const props = defineProps({
    eStatement: {
        type: Object,
        default: {} as any,
    },
})
// const postingDate = computed(() => ['6', '4'].includes(props.eStatement?.br_type.toString()))props.eStatement.date
const apIntegration: any = Ap_Integration.get() ?? 0
const arIntegration: any = Ar_Integration.get() ?? 0
const updateIntegrationBrSapStatus = (data: any) => store.dispatch('ReconciliationStore/updateIntegrationBrSapStatusV1', data)
const sendIntergrationBrDataToSap = (data: any) => store.dispatch('ReconciliationStore/sendBrDataToSap', data)

const emits = defineEmits(['update', 'dismiss'])

const submitReconcile = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationV1', payload)
const submitReconcileIntegration = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationV1Integration', payload)
const submitReconcileIntegration8001 = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationV1Integration8001', payload)
const submitReconcileFTIntegration8001 = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationFTV1Integration8001', payload)
const fetchInvoiceList = (payload: any) => store.dispatch('BrStore/fetchManualInvoice', payload)

const isFXFT = computed(() => ['6', '4'].includes(props.eStatement?.br_type.toString()))
const companyBankList = computed(() => store.state.BankInfoStore.bankList)
const manualEStatement = computed(() => store.state.BrStore.manualEStatement)
const filteredReconciliationTableColumns = computed(() => {
    const filtered = isFXFT.value
        ? reconciliationTableColumnsTemplate.value.filter((x: any) => x.key !== 'invoice_no')
        : reconciliationTableColumnsTemplate.value.filter((x: any) => x.key !== 'bank_account')
    return filtered
})
const selectedAmount = computed(() => selectedInovices.value.reduce((n, {balance}) => n + balance, 0).toFixed(2))
const eStatementAmount = computed(() =>
    props.eStatement?.br_type == 0
        ? Math.abs(props.eStatement?.balance + props.eStatement?.charge_fee)
        : props.eStatement?.br_type == 1
        ? Math.abs(props.eStatement?.balance - props.eStatement?.charge_fee)
        : Math.abs(props.eStatement?.balance),
)
const diffAmount = computed(() =>
    props.eStatement?.br_type !== 2 && props.eStatement?.br_type !== 3
        ? (+selectedAmount.value - +eStatementAmount.value).toFixed(2)
        : Math.abs(+selectedAmount.value + +eStatementAmount.value).toFixed(2),
)
const isRsRoncileWithCharge = computed(() => (props.eStatement?.br_type == 0 || props.eStatement?.br_type == 1) && props.eStatement?.charge_fee != 0)
const searchForm = reactive({
    searchText: '',
    startDate: '',
    endDate: '',
    payerAndPayeeArr: [] as string[],
    payerAndPayee: '',
})

const state = reactive({
    showApArDetail: '',
    showInvoiceModal: false,
    invoiceId: '',
})

const selectedInovices = ref<any[]>([])

const selectedRowKeys = ref<string[]>([])

const search = async () => {
    await updateManualTable()

    if (searchForm.searchText) {
        tableData.value = tableData.value.filter((x: any) => x.balance.toString().includes(searchForm.searchText))
    }
    if (searchForm.startDate) {
        tableData.value = tableData.value.filter((x: any) => new Date(searchForm.startDate) <= new Date(x.date))
    }
    if (searchForm.endDate) {
        tableData.value = tableData.value.filter((x: any) => new Date(searchForm.endDate) >= new Date(x.date))
    }
    if (searchForm.payerAndPayee) {
        tableData.value = tableData.value.filter(
            (x: any) =>
                x.payer_payee && x.payer_payee.toLowerCase().indexOf(searchForm.payerAndPayee.toLowerCase()) !== -1,
        )
    }
    if (searchForm.payerAndPayeeArr.length !== 0) {
        tableData.value = tableData.value.filter((x: any) => searchForm.payerAndPayeeArr.includes(x.payer_payee))
    }
    // reset
    if (!searchForm.searchText && !searchForm.startDate && !searchForm.endDate) {
        return
    }
}
const inputChange = () => {
    void 0
}

const isTableLoading = ref(false)
const isIntegrationBrOrReverse = ref(false)

const tableData = ref<any[]>([])
const showInvoiceDetail = (inoviceId: string, brType: string) => {
    state.invoiceId = inoviceId
    if (['0', '3'].includes(brType)) {
        state.showInvoiceModal = true
        state.showApArDetail = 'AR'
    } else if (['1', '2', '5'].includes(brType)) {
        state.showInvoiceModal = true
        state.showApArDetail = 'AP'
    } else {
        return
    }
}
const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)
const popPostingPate = () => {
    showPostingDate.value = true
}
const dismissPostingDate = () => {
    showPostingDate.value = false
}
const getGLAccount = (bank_acocunt: string) => {
    const match = companyBankList.value.find((x: any) => x.account_no === bank_acocunt)?.gl_account
    return match || ''
}
const getBankType = (bank_acocunt: string) => {
    const match = companyBankList.value.find((x: any) => x.account_no === bank_acocunt)?.bank_type
    return match || ''
}
const customCellClickEvent = (record: any, rowIndex: any, column: any) => {
    return {
        onClick: (event: any) => {
            console.log(record)
            showInvoiceDetail(record.id, record.br_type)
        },
    }
}

const filters = computed(() => {
    return _.uniqBy(
        tableData.value.map((i: any) => {
            return {text: i.payer_payee, value: i.payer_payee}
        }),
        item => item.text,
    )
})

const reconciliationTableColumnsTemplate = computed<TableColumnType[]>(() => {
    return [
        {
            title: i18n.t('ReconciliationDetails.invoiceNo'), //'Invoice #',
            dataIndex: 'invoice_no',
            key: 'invoice_no',
            align: 'center',
            width: '15%',
            customCell: customCellClickEvent,
        },
        {
            title: i18n.t('esMain.bankAccount'),
            dataIndex: 'bank_account',
            key: 'bank_account',
            align: 'center',
            width: '15%',
            customCell: customCellClickEvent,
        },
        {
            title: i18n.t('esMain.postingDate'),
            dataIndex: 'date',
            key: 'date',
            align: 'center',
            width: '15%',
            customCell: customCellClickEvent,
        },
        {
            title: i18n.t('esMain.description'),
            dataIndex: 'description',
            key: 'description',
            align: 'center',
            width: '20%',
            customCell: customCellClickEvent,
        },
        {
            title: i18n.t('esMain.reference'),
            dataIndex: 'reference',
            key: 'reference',
            align: 'center',
            width: '10%',
            ellipsis: true,
            customCell: customCellClickEvent,
        },
        {
            title: i18n.t('esMain.payerPayee'),
            dataIndex: 'payer_payee',
            key: 'payer_payee',
            align: 'center',
            width: '15%',
            customCell: customCellClickEvent,
            // filters: filters.value,
            // filterMultiple: true,
            // onFilter: (value: any, record: any) => record.payer_payee.indexOf(value) === 0,
            sorter: (a: any, b: any) => a.payer_payee.length - b.payer_payee.length,
            sortDirections: ['descend', 'ascend'],
        },
        {
            title: i18n.t('esMain.debitManu'),
            dataIndex: 'deposit',
            key: 'deposit',
            align: 'center',
            width: '10%',
            customCell: customCellClickEvent,
        },
        {
            title: i18n.t('esMain.creditManu'),
            dataIndex: 'withdrawal',
            key: 'withdrawal',
            align: 'center',
            width: '10%',
            customCell: customCellClickEvent,
        },
        {
            title: i18n.t('esMain.balance'),
            dataIndex: 'balance',
            key: 'balance',
            align: 'center',
            width: '10%',
            customCell: customCellClickEvent,
            customRender: ({ record }) => {
                return record.br_type === "13" ? record.total_fee : record.balance
            }
        },
    ]
})

const onSelectChange = (_selectedRowKeys: string[]) => {
    console.log('selectedRowKeys changed: ', _selectedRowKeys)
    selectedInovices.value = []
    selectedRowKeys.value = _selectedRowKeys
    selectedRowKeys.value.forEach(x => {
        const {id, balance, mx_isr, mx_iva, reference_no, invoice_comments, bp_number, br_type, br_entity_type, sap_document_id, bank_account, reason_code, total_fee} =
            tableData.value.find(t => t.id === x)
        const itemPush: any = {
            invoice_id: id,
            balance: balance,
            mx_isr: mx_isr,
            mx_iva: mx_iva,
            reference_no: reference_no,
            invoice_comments: invoice_comments,
            issuer_id: bp_number,
            reconcile_amount: balance,
            fidoc: sap_document_id || '',
            gl_account: '',
            br_type,
            reason_code,
            br_entity_type,
            item_total_fee: total_fee,
        }
        if (isFXFT.value) {
            itemPush.bank_account = bank_account
        }
        if (props.eStatement?.br_type.toString() === '13' && itemPush.balance === 0) {
            itemPush.balance = total_fee
        }
        console.log('itemPush=====', itemPush)
        selectedInovices.value.push(itemPush)
    })
    if (selectedInovices.value.length === 1) {
        bpNumber.value = selectedInovices.value[0].issuer_id
    } else if (selectedRowKeys.value.length === 0) {
        bpNumber.value = ''
    }
    console.log(selectedInovices.value)
}
const bpNumber = ref('')
const disableSelection = (record: any) => {
    return {
        disabled: bpNumber.value !== '' && record.bp_number !== bpNumber.value, // selectedAmount.value >= eStatementAmount.value && !selectedRowKeys.value.includes(record.id),
    }
}

const updateManualTable = async () => {
    isTableLoading.value = true

    const bankType = getBankType(props.eStatement?.bank_account)
    if ((apIntegration === 1 || arIntegration === 1) && bankType === 'Credit') {
        await fetchInvoiceList({ company_code: userCompany[0].code, statement_id: props.eStatement?.id, bank_type: 'Credit', ar_integration: arIntegration, ap_integration: apIntegration })
    } else if ((apIntegration === 1 || arIntegration === 1) && bankType !== 'Credit') {
        await fetchInvoiceList({ company_code: userCompany[0].code, statement_id: props.eStatement?.id, bank_type: 'NoCredit', ar_integration: arIntegration, ap_integration: apIntegration })
    } else {
        await fetchInvoiceList({ company_code: userCompany[0].code, statement_id: props.eStatement?.id })
    }
    tableData.value = manualEStatement.value.children
    isTableLoading.value = false
}

const reconcileInvoices = async () => {
    showPostingDate.value = false
    try {
        isTableLoading.value = true
        isIntegrationBrOrReverse.value = true
        console.log('check invoices to be reconcile, ', selectedInovices.value)
        if (selectedInovices.value.length === 0) {
            message.warn(i18n.t('ReconciliationDetails.select'))
            return
        }
        const reconcileList = []
        const {
            br_type,
            br_entity_type,
            company_code,
            id,
            currency,
            bank_account,
            balance,
            date,
            gl_account,
            reason_code,
            deposit,
            withdrawal,
        } = props.eStatement as any
        if (selectedAmount.value > eStatementAmount.value) {
            let counting = 0

            selectedInovices.value.forEach((x: any) => {
                x.gl_account = gl_account
                if (counting + Math.abs(x.balance) <= eStatementAmount.value) {
                    x.reconcile_amount = x.balance
                    counting += Math.abs(+x.balance)
                    reconcileList.push(x)
                    return
                }
                if (counting < eStatementAmount.value && counting + Math.abs(x.balance) > eStatementAmount.value) {
                    x.reconcile_amount = +(+eStatementAmount.value - counting).toFixed(2)
                    counting += +Math.abs(x.balance)
                    reconcileList.push(x)
                    return
                }
                if (counting >= eStatementAmount.value) {
                    x.reconcile_amount = 0
                    return
                }
            })
        } else {
            reconcileList.push(...selectedInovices.value)
            reconcileList.forEach((x: any) => {
                x.reconcile_amount = x.balance
            })
        }
        // gl account
        reconcileList.forEach((x: any) => {
            if (x.bank_account) {
                x.gl_account = getGLAccount(x.bank_account)
            } else {
                x.gl_account = gl_account
            }
        })
        const payload = {
            header: {
                company_code: company_code || userCompany[0].code,
                company_id: userCompany[0].id,
                es_id: id,
                posting_date: postingDate.value ?? date,
                value_date: date,
                issuer_id: reconcileList.length > 0 ? reconcileList[0].issuer_id : '',
                invoice_currency: currency,
                bank_account,
                deposit,
                withdrawal,
                total_fee: balance,
                gl_account,
                reason_code,
                br_type,
                br_entity_type,
                creator: userInfo?.id,
            },
            items: [...reconcileList],
        } as any
        console.log('payload', payload)
        if (apIntegration === 1 && isFXFT.value) {
            const res = await submitReconcileFTIntegration8001(payload)
            if (res.data.statusCode === 200) {
                message.success(i18n.t('ApComponents.success'))
            } else {
                message.error(res.data.message)
            }
        } else if (apIntegration === 1) {
            const res = await submitReconcileIntegration8001(payload)
            if (res.data.statusCode === 200) {
                message.success(i18n.t('ApComponents.success'))
            } else {
                message.error(res.data.message)
            }
        } else {
            message.error('Failed')
        }
    } catch (e) {
        console.log(e)
        // message.error('failed')
    } finally {
        //todo: add loading.
        isTableLoading.value = false
        isIntegrationBrOrReverse.value = false
        emits('dismiss')
        emits('update', 1)
        console.log('finally')
    }
}

const dismiss = () => {
    state.showInvoiceModal = false
}

const payerAngPayeeFilterOptions = computed(() => {
    const res = _.uniqBy(
        tableData.value.map((i: any) => {
            return { value: i.payer_payee }
        }),
        item => item.value,
    )
    return res
})

onBeforeMount(async () => {
    console.log('check', props.eStatement?.children)
    console.log('check', props.eStatement)

    await updateManualTable()
    postingDate.value = props.eStatement?.date
    console.log(tableData.value)
})
</script>
<template>
    <div class="page-wrap">
        <div class="page-header">
            <div class="search-group-wrap">
                <a-input v-model:value="searchForm.searchText" :placeholder="i18n.t('ReconciliationDetails.search')"
                    class="search-input" @input="inputChange" @pressEnter="search">
                    <template #suffix>
                        <svg-icon name="icon_search" @click="search"></svg-icon>
                    </template>
                </a-input>

                <a-input v-model:value="searchForm.payerAndPayee"
                    :placeholder="i18n.t('ReconciliationDetails.payerAndPayee')" class="search-input"
                    @input="inputChange" @pressEnter="search">
                    <template #suffix>
                        <svg-icon name="icon_search" @click="search"></svg-icon>
                    </template>
                </a-input>
                <a-popover class="popover-wrap" trigger="click" placement="bottom" @visibleChange="visibleChange">
                    <template #content>
                        <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                            <div class="search-input-group" id="search-input-group">
                                <a-form-item :label="$t('bkArInvoice.date')">
                                    <a-date-picker v-model:value="searchForm.startDate" format="YYYY-MM-DD"
                                        valueFormat="YYYY-MM-DD" :placeholder="$t('gl.createStartDate')"
                                        style="width: 160px" clearable>
                                        <template #suffixIcon>
                                            <svg-icon name="icon_date"></svg-icon>
                                        </template>
                                    </a-date-picker>
                                </a-form-item>
                                <a-form-item :label="$t('bkArInvoice.to')">
                                    <a-date-picker v-model:value="searchForm.endDate" format="YYYY-MM-DD"
                                        valueFormat="YYYY-MM-DD" :placeholder="$t('gl.createEndDate')"
                                        style="width: 160px" clearable>
                                        <template #suffixIcon>
                                            <svg-icon name="icon_date"></svg-icon>
                                        </template>
                                    </a-date-picker>
                                </a-form-item>
                            </div>
                            <div class="filter-Payer-payee">
                                <a-form-item :label="$t('gl.payerAndPayee')">
                                    <a-select v-model:value="searchForm.payerAndPayeeArr" mode="multiple"
                                        style="width: 355px" :placeholder="$t('gl.payerAndPayee')"
                                        :options="payerAngPayeeFilterOptions" :dropdownStyle="{ height: '130px' }" />
                                </a-form-item>
                            </div>

                            <a-button type="primary" shape="round" @click="search">
                                <template #icon>
                                    <svg-icon name="icon_search"></svg-icon>
                                </template>
                                {{ $t('commonTag.search') }}
                            </a-button>
                        </a-form>
                    </template>
                    <a-button class="search-button">
                        <template #icon>
                            <svg-icon name="icon_filter"></svg-icon>
                        </template>
                        <!-- {{ $t('commonTag.filter') }} -->
                    </a-button>
                </a-popover>
            </div>
            <div class="reconcile-wrap">
                <div class="reconcile-message" v-if="!['6', '13'].includes(props.eStatement?.br_type.toString())">
                    <svg-icon name="icon_shape"></svg-icon>
                    <span>
                        {{
                            i18n.t('ReconciliationDetails.totals', {
                                amt: Number(eStatementAmount).toFixed(2),
                                amt2: selectedAmount,
                                amt3: diffAmount,
                            })
                        }}</span>
                </div>
                <a-button class="reconcile-btn" shape="round" type="primary" @click="popPostingPate"
                    :disabled="parseFloat(selectedAmount) === 0">{{
                        i18n.t('ReconciliationDetails.reconcile')
                    }}</a-button>
            </div>
        </div>
        <div class="page-content">
            <!--  :scroll="{y: 268}"-->
            <a-spin
                :spinning="isTableLoading"
                :tip="apIntegration === 1 && isIntegrationBrOrReverse ? i18n.t('commonTag.sapTip') : ''"
                wrapperClassName="custom-spin">
                <a-table
                    :dataSource="tableData"
                    :row-selection="{
                        selectedRowKeys: selectedRowKeys,
                        onChange: onSelectChange,
                        // onSelect: onSelection,
                        getCheckboxProps: disableSelection,
                    }"
                    :columns="filteredReconciliationTableColumns"
                    rowKey="id"
                    :scroll="{x: 800, y: 'calc(55vh - 300px)'}"
                >
                </a-table>
            </a-spin>
        </div>
    </div>

    <a-modal :title="i18n.t('bkApInvoice.readonly')" v-model:visible="state.showInvoiceModal" :footer="null"
        destroyOnClose :closeable="true" :width="'1110px'" :dialogStyle="{ top: '10px' }"
        :bodyStyle="{ padding: '10px 24px 24px' }" :wrapClassName="'modal-wrap'">
        <ap-invoice-component v-if="state.showApArDetail === 'AP' && apIntegration !== 1" current-invoice="" :invoice-id="state.invoiceId"
            :readonly-mode="true" @dismiss="dismiss"></ap-invoice-component>
        <ap-invoice-component-integration-mx v-if="state.showApArDetail === 'AP' && apIntegration === 1" current-invoice="" :invoice-id="state.invoiceId"
            :readonly-mode="true" @dismiss="dismiss"></ap-invoice-component-integration-mx>
        <ar-invoice-component v-if="state.showApArDetail === 'AR'" current-invoice="" :invoice-id="state.invoiceId"
            :readonly-mode="true"></ar-invoice-component>
    </a-modal>
    <div ref="confirmationWrap">
            <a-modal v-model:visible="showPostingDate" centered destroyOnClose :get-container="confirmationWrap"
                :width="480" :closable="false" :wrapClassName="'modal-wrap'"
                :ok-text="i18n.t('commonTag.confirm')" :ok-type="'primary'" :ok-button-props="{ shape: 'round' }"
                :cancel-text="i18n.t('commonTag.cancel')"
                :cancel-button-props="{ shape: 'round', ghost: true, type: 'primary' }" @ok="reconcileInvoices"
                @cancel="dismissPostingDate">
                <template #title>
                    <div class="confirm-title-wrap">
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('bkCommonTag.confirmation') }}
                        </div>
                    </div>
                </template>
                <div class="confirmation-content-text">{{ i18n.t('EstatementTable.confirmReconcileDate') }}</div>
                <div class="date-select">
                    <a-date-picker v-model:value="postingDate" :allowClear="false" :inputReadOnly="true"
                        format="YYYY-MM-DD" valueFormat="YYYY-MM-DD">
                        <template #suffixIcon>
                            <svg-icon name="icon_date"></svg-icon>
                        </template>
                    </a-date-picker>
                </div>
            </a-modal>
        </div>
</template>
<style lang="scss" scoped>
.page-wrap {
    padding: 20px 20px 12px;
    overflow: hidden;

    .page-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .search-group-wrap {
            display: flex;
            width: 50%;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
                margin-right: 8px;
            }

            .search-button {
                min-width: 60px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button+.search-button {
                min-width: 60px;
            }

            .popover-wrap :deep(.ant-popover-placement-bottom) {
                width: 432px;
            }
        }

        .reconcile-wrap {
            display: flex;
            align-items: center;

            .reconcile-message {
                height: 36px;
                margin-right: 8px;
                background-color: #edf4ff;
                border: 1px solid #abcdff;
                border-radius: 2px;
                padding: 8px 12px 8px 16px;
                display: flex;
                align-items: center;

                .anticon {
                    margin-right: 8px;
                }

                :deep(.anticon svg) {
                    font-size: 16px;
                }
            }
        }
    }

    .page-content {
        padding: 12px 0 0;

        :deep(.ant-table-wrapper) {
            .ant-pagination.ant-table-pagination {
                margin-top: 12px;
                margin-bottom: 0;
            }
        }

        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
            padding-left: 8px;
            padding-right: 8px;
        }
    }

    .search-input-form {
        display: flex;
        flex-direction: column;
        align-items: end;

        .search-input-group {
            display: flex;

            .ant-form-item {
                margin-bottom: 12px;
            }

            :deep(.ant-form-item-label) {
                width: 45px;
            }

            .ant-form-item+.ant-form-item :deep(.ant-form-item-label) {
                width: 35px;
            }
        }
    }
}
</style>
