/** @format */

import service from '@/api/request'
import serviceV1 from '@/api/requestNew'
import servicent from '@/api/requestPayroll'
import servicekyc from '@/api/requestKyc'
import servicepgo from '@/api/requestPgo'
// 登录
export const Login = (params: any) => {
    return service.post(`/system/user/login`, params)
}
export const LoginV1 = (params: any) => {
    return serviceV1.post(`/users/api/authentication`, params)
}

export const LoginPGO = () => {
    const params = {
        username: `${process.env.NODE_ENV === 'development' ? 'bizUser1' : 'eo_bpuser'}`,
        password: `${process.env.NODE_ENV === 'development' ? '111111' : 'biz@Inossemtimes'}`,
    }
    return servicepgo.post(`/api/login`, params)
}

// 登出
export const Logout = (params: any) => {
    return service.get(`/system/user/logout`, params)
}
// payroll login api
export const LoginPayroll = (params: any) => {
    return servicent.post('/api/authentication', params)
}
// kyc get rsa api
export const GetKycRSA = (params?: any) => {
    return servicekyc.get('/getRSAKey', params)
}

export const GetKycProductList = (params: any) => {
    return servicekyc.get('/subscription/validSubs?companyCode=' + params.companyCode, params)
}

export const GETKycMenu = (params: any) => {
    return servicekyc.post('/menuConfig/menu', params)
}
// export const loginKyc = (params: any) => {
//     return servicekyc.post('/loginFromThirdParties', params)
// }

export const ChangeCompany = (params: any) => {
    return service.put(`/system/user/company`, params)
}

// 获取首页公司
export const MainhomeCompany = (params: any) => {
    return service.get(`/system/user/manage/company`, params)
}

export const MenuList = () => {
    return service.get(`/menu/manage/tree`, undefined)
}
