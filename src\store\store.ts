/** @format */

import Vuex, {type ActionContext} from 'vuex'
import type {Composer} from 'vue-i18n'
import i18nInstance, {languageList} from '@/locales/i18n'
import {Language} from '@/lib/storage'

import DropDown from './modules/DropdownStore'
import UserInfo from './modules/UserInfoStore'
import SendOfferFlowStore from './modules/SendOfferFlowStore'
import AnonymousFormStore from './modules/AnonymousFormStore'
import CandidateListStore from './modules/CandidateListStore'
import EmployeeInfoStore from './modules/EmployeeInfoStore'
import ProjectDetailStore from './modules/ProjectDetailStore'
import MenuList from './modules/MenuList'
import CustomerStore from './modules/bookkeeping/common/CustomerStore'
import TaxCalculationStore from './modules/bookkeeping/common/TaxCalculationStore'
import TaxInfoStore from './modules/bookkeeping/common/TaxInfoStore'
import AccountDescriptionStore from './modules/bookkeeping/common/AccountDescriptionStore'
import SupplierStore from './modules/bookkeeping/common/SupplierStore'
import CommonDropDownStore from './modules/bookkeeping/common/CommonDropDownStore'
import BankInfoStore from './modules/bookkeeping/common/BankInfoStore'
import ArStore from './modules/bookkeeping/ar/ArStore'
import ApStore from './modules/bookkeeping/ap/ApStore'
import PyStore from './modules/bookkeeping/py/PyStore'
import BrStore from './modules/bookkeeping/br/BrStore'
import ReportStore from './modules/bookkeeping/reporting/ReportStore'
import ReconciliationStore from './modules/bookkeeping/br/ReconciliationStore'
import ArApBrStore from './modules/bookkeeping/ArApBrStore'
import UserManagementStore from './modules/bookkeeping/UserManagementStore'
import GlStore from './modules/bookkeeping/gl/GlStore'
import Utils from './modules/Utils'
import spotCurrencyStore from './modules/bookkeeping/common/spotCurrencyStore'
import ContactStore from './modules/bookkeeping/common/ContactStore'
import CoaMappingStore from './modules/bookkeeping/common/CoaMappingStore'
import HelpStore from './modules/bookkeeping/help/HelpStore'
import UserStore from './modules/bookkeeping/account/UserStore'
import ProductServiceStore from './modules/bookkeeping/common/ProductServiceStore'

import dayjs from 'dayjs'
import moment from 'moment'
import ConnectivityStore from '@/store/modules/bookkeeping/common/ConnectivityStore'
import TaskStore from './modules/bookkeeping/task/TaskStore'

const i18n: Composer = i18nInstance.global

const state = {
    // 系统语言
    lang: Language.get() || 'en',
    disableScroll: false,
}

const mutations = {
    setLang(state: {lang: string}, lang: string) {
        state.lang = lang
    },
    setDisableScroll(state: {disableScroll: boolean}, disable: boolean) {
        state.disableScroll = disable
    },
}

const actions = {
    // 设置系统语言
    setLanguage(ctx: ActionContext<{lang: string}, Record<string, unknown>>, lang: string) {
        const language = languageList.find(item => item.values.includes(lang)) || {
            id: 'en',
            dayjsLocale: 'en',
            momentLocale: 'en',
        }

        i18n.locale.value = language.id
        dayjs.locale(language.dayjsLocale)
        moment.locale(language.momentLocale)
        Language.set(lang)
        ctx.commit('setLang', lang)
    },
}
export default new Vuex.Store({
    state,
    mutations,
    actions,
    modules: {
        DropDown,
        UserInfo,
        SendOfferFlowStore,
        AnonymousFormStore,
        CandidateListStore,
        EmployeeInfoStore,
        ProjectDetailStore,
        MenuList,
        CustomerStore,
        TaxCalculationStore,
        TaxInfoStore,
        AccountDescriptionStore,
        SupplierStore,
        CommonDropDownStore,
        BankInfoStore,
        ArStore,
        ApStore,
        TaskStore,
        BrStore,
        PyStore,
        ArApBrStore,
        ReconciliationStore,
        UserManagementStore,
        GlStore,
        ReportStore,
        Utils,
        spotCurrencyStore,
        ContactStore,
        CoaMappingStore,
        ConnectivityStore,
        HelpStore,
        UserStore,
        ProductServiceStore,
    },
    strict: import.meta.env.NODE_ENV !== 'production',
})
