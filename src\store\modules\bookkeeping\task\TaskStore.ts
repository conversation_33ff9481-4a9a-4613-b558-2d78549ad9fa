/** @format */

import type {ActionContext} from 'vuex'
import service from '@/api/request'
import serviceV1 from '@/api/requestNew'

const http = service
// v1 is new api.
const httpv1 = serviceV1

const TaskStore = {
    namespaced: true,
    state: {
        taskList: [],
        totalNumber: 0,
        estimatedHour: 0,
        task: {},
    },
    mutations: {
        updateTaskList(state: {taskList: any[]}, list: any) {
            state.taskList = [...list]
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateEstimatedHour(state: {estimatedHour: any}, num: number) {
            state.estimatedHour = num
        },
        sendEmail(state: {task: any}, data: any) {
            state.task = data
        },
        sendNewEmail(state: {task: any}, data: any) {
            state.task = data
        },
    },
    actions: {
        async fetchTaskListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get('invoice-statement/api/v1/task', {params: payload})
            const totalNumber = response.data.paginated ? response.data.paginated.total : response.data.data.length
            store.commit('updateTaskList', response.data.data)
            store.commit('updateTotalFoundNumber', totalNumber)
            return response
        },
        async createTaskData(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: number; filed: string; data: number | string},
        ) {
            // console.log('the payload is ', payload)
            const response = await httpv1.post(`invoice-statement/api/v1/task`, payload)
            return response
        },
        async updateTableData(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: number; filed: string; data: number | string},
        ) {
            // console.log('the payload is ', payload)
            const data: any = {}
            data[payload.filed as string] = payload.data
            await httpv1.patch(`invoice-statement/api/v1/task/${payload.id}`, data)
            store.commit('updateEstimatedHour', payload.data)
        },
        async sendTaskEmail(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: {data: any}) {
            // console.log('the payload is ', payload)
            const record = payload.data
            const data: any = {
                from: null,
                to: record.email,
                cc: record.assign_to,
                subject: 'Task',
                data: {
                    task: record.name,
                    email: record.assign_to,
                },
                template: 'gstQstYeEmail',
            }
            const res = await httpv1.post(`koa/email`, data)
            store.commit('sendEmail', payload.data)
            return res
        },
        async sendNewTaskEmail(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {data: any},
        ) {
            // console.log('the payload is ', payload)
            const record = payload.data
            const data: any = {
                from: null,
                to: record.assign_to,
                subject: `New Task (${record.name})`,
                data: {
                    task: record.name,
                },
                template: 'newTaskEmail',
            }
            const res = await httpv1.post(`koa/email`, data)
            store.commit('sendNewEmail', payload.data)
            return res
        },
    },
}

export default TaskStore
