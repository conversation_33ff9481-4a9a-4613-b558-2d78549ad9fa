<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import {computed, onBeforeMount, reactive, ref} from 'vue'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {message, notification} from 'ant-design-vue'
import type {FormInstance} from 'ant-design-vue'
import SvgIcon from '@/components/SvgIcon.vue'

import moment from 'moment'
import * as _ from 'lodash'
import {UserCompany, UserInfo} from '@/lib/storage'

const store = useStore()
const i18n: Composer = i18nInstance.global
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const showModal = ref(false)
const submitProcessing = ref(false)
const tableLoading = ref(false)

const form = reactive({
    rate_date: moment().format('YYYY-MM-DD'),
    id: null,
    rate: '',
    update_time: '',
    create_time: '',
})
const formRef = ref<FormInstance>()
const formRules = reactive({
    rate_date: [{required: true, message: i18n.t('spotCurrency.selectTime')}],
    rate: [{required: true, message: i18n.t('spotCurrency.msgInputRate'), trigger: 'blur'}],
})

const fetchSpotCurrency = () => store.dispatch('spotCurrencyStore/fetchSpotCurrencyV1')
const upsertSpotCurrency = (query: any) => store.dispatch('spotCurrencyStore/upsertSpotCurrencyV1', query)
const editSpotCurrency = (payload: any) => store.dispatch('spotCurrencyStore/editSpotCurrencyV1', payload)

const updateSpotCurrencyStoreList = () => store.commit('spotCurrencyStore/updateSpotCurrencyStoreList')

const spotCurrencyList = computed(() => store.state.spotCurrencyStore.spotCurrencyList)

// const currentUserInfo = computed(() => store.state.UserInfo.userInfo)
const privilegeRole: boolean = userInfo.roles === '1'
const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}
const currencyData = computed(() => {
    const uniqueArr = _.cloneDeep(spotCurrencyList.value)
    for (let i = 0; i < uniqueArr.length; i++) {
        for (let j = i + 1; j < uniqueArr.length; j++) {
            if (uniqueArr[i].rate_date === uniqueArr[j].rate_date) {
                uniqueArr.splice(j, 1)
                j--
            }
        }
    }
    return uniqueArr
})

const updateData = async () => {
    try {
        tableLoading.value = true
        await fetchSpotCurrency()
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const add = () => {
    form.id = null
    form.rate = ''
    form.rate_date = moment().format('YYYY-MM-DD')
    form.create_time = moment().toISOString()
    form.update_time = moment().toISOString()
    showModal.value = true
}
const edit = (row: any) => {
    form.rate = row.rate
    form.id = row.id
    form.rate_date = row.rate_date
    form.create_time = row.create_time || moment().toISOString()
    form.update_time = moment().toISOString()
    showModal.value = true
}
const cancel = () => {
    showModal.value = false
}
const submit = async () => {
    try {
        submitProcessing.value = true
        // const payload: any = {rate_date: form.rate_date, rate: form.rate}
        const payload = {...form}
        const response = form.id ? await editSpotCurrency(payload) : await upsertSpotCurrency(payload)

        if (response.status === 201 || response.status === 200) {
            message.success(i18n.t('ApComponents.success'))
            formRef.value?.resetFields()
            showModal.value = false
        } else {
            // message.error({
            //     content: 'failed',
            //     duration: 3,
            // })
        }
        await updateData()
    } catch (error) {
        console.log(error)
    } finally {
        submitProcessing.value = false
    }
}
onBeforeMount(async () => {
    await updateData()
})
</script>
<template>
    <div class="spot-currency-wrap">
        <div class="add-button-wrap">
            <a-button :disabled="!privilegeRole" type="primary" shape="round" class="add-button" @click="add()">
                <template #icon>
                    <svg-icon name="icon_add"></svg-icon>
                </template>
                {{ i18n.t('commonTag.new') }}
            </a-button>
        </div>
        <a-alert
            type="info"
            :closable="false"
            show-icon
            :message="i18n.t('spotCurrency.msgWarning')"
            class="alert-wrap"
        />
        <a-table
            class="spot-currency-table-wrap"
            :dataSource="currencyData"
            :loading="tableLoading"
            :pagination="false"
            :row-class-name="(_: any, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
        >
            <a-table-column data-index="rate_date" :title="i18n.t('spotCurrency.date')" align="center" width="660px" />
            <a-table-column data-index="rate" :title="i18n.t('spotCurrency.currency')" align="center" width="430px" />
            <a-table-column key="operation" :title="i18n.t('spotCurrency.operation')" align="center" width="245px">
                <template #default="{record}">
                    <a-button :disabled="!privilegeRole" type="link" @click="edit(record)">
                        <template #icon>
                            <svg-icon name="icon_edit2"></svg-icon>
                        </template>
                    </a-button>
                </template>
            </a-table-column>
        </a-table>

        <a-modal
            :title="form.id ? i18n.t('spotCurrency.editTitle') : i18n.t('spotCurrency.createTitle')"
            v-model:visible="showModal"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="500"
            :wrapClassName="'modal-wrap'"
        >
            <div class="spot-currency-modal">
                <a-form :model="form" :rules="formRules" ref="formRef" class="spot-currency-form">
                    <a-form-item name="rate_date" :label="i18n.t('spotCurrency.date')">
                        <a-date-picker
                            v-model:value="form.rate_date"
                            type="date"
                            format="YYYY-MM-DD"
                            valueFormat="YYYY-MM-DD"
                            :placeholder="i18n.t('spotCurrency.selectTime')"
                            style="width: 100%"
                            clearable
                        >
                        </a-date-picker>
                    </a-form-item>
                    <a-form-item name="rate" :label="i18n.t('spotCurrency.currency')">
                        <a-input-number
                            v-model:value="form.rate"
                            :controls="false"
                            :min="0 || null"
                            :placeholder="i18n.t('spotCurrency.msgInputRate')"
                            style="width: 100%"
                        ></a-input-number>
                    </a-form-item>
                </a-form>
                <a-divider class="footer-divider"></a-divider>
                <footer>
                    <a-button class="cancel-button" size="small" shape="round" @click="cancel">{{
                        i18n.t('commonTag.cancel')
                    }}</a-button>
                    <a-button
                        size="small"
                        type="primary"
                        shape="round"
                        @click="submit"
                        :loading="submitProcessing"
                        :disabled="!form.rate_date || !form.rate"
                        >{{ i18n.t('commonTag.confirm') }}</a-button
                    >
                </footer>
            </div>
        </a-modal>
    </div>
</template>
<style lang="scss" scoped>
.spot-currency-wrap {
    .add-button-wrap {
        text-align: right;
        margin-bottom: 20px;
        .add-button {
            font-size: 16px;
            padding: 6px 16px;
        }
    }
    .alert-wrap {
        margin-bottom: 16px;
        :deep(.anticon-info-circle) {
            font-size: 16px;
            margin-top: 3px;
            margin-bottom: 3px;
        }
    }
    .spot-currency-table-wrap {
        :deep(.ant-table .ant-table-thead .ant-table-cell) {
            background-color: #e0e4ea;
            border-bottom-left-radius: 0px;
            border-bottom-right-radius: 0px;
        }
        :deep(.ant-table .ant-table-tbody .ant-table-cell) {
            height: 52px;
        }
        :deep(.table-striped) td {
            background-color: #f2f4f7;
        }
    }
}
.spot-currency-modal {
    .spot-currency-form {
        padding: 20px 24px 0px;
        :deep(.ant-form-item-label) {
            min-width: 140px;
        }
    }
    .footer-divider {
        margin-top: 0px;
        margin-bottom: 0px;
    }
    footer {
        padding: 12px 24px;
        text-align: right;
        .cancel-button {
            border-color: #004fc1;
            color: #004fc1;
        }
        .ant-btn {
            min-width: 65px;
        }
        .ant-btn + .ant-btn {
            margin-left: 8px;
        }
    }
}
</style>
