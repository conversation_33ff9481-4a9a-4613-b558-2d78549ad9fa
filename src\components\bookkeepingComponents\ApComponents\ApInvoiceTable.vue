<!-- @format -->

<template>
    <div class="page-container_ap_table">
        <div :class="props.activeName === 'pending' ? 'table-pending' : 'table-created'">
            <div class="table-container">
                <a-table
                    :dataSource="state.tableData"
                    :loading="isLoading"
                    :pagination="false"
                    :scroll="{y: 'calc(100vh - 300px)'}"
                    @change="customSorter"
                >
                    <a-table-column
                        key="file_name"
                        dataIndex="file_name"
                        sorter="true"
                        :title="i18n.t('bkApUpload.fileName')"
                        align="center"
                        header-align="center"
                        width="25%"
                    >
                    </a-table-column>
                    <a-table-column
                        key="file_type"
                        dataIndex="file_type"
                        sorter="true"
                        :title="i18n.t('bkApUpload.payMethod')"
                        align="center"
                        header-align="center"
                        width="10%"
                        v-if="!ApIntegration.get()"
                    >
                        <template #default="{record}">
                            <span>
                                {{ getFileTypeById(record.file_type) }}
                            </span>
                        </template>
                    </a-table-column>
                    <!-- <a-table-column
                        key="comment"
                        dataIndex="comment"
                        sorter="true"
                        :title="i18n.t('bkApUpload.comment')"
                        align="center"
                        header-align="center"
                        width="20%"
                    >
                    </a-table-column> -->
                    <a-table-column
                        key="update_time"
                        dataIndex="update_time"
                        sorter="true"
                        :title="i18n.t('bkApUpload.updateTime')"
                        align="center"
                        header-align="center"
                        min-width="25%"
                    >
                    </a-table-column>
                    <a-table-column
                        key="create_time"
                        v-if="activeName === 'finished'"
                        dataIndex="create_time"
                        sorter="true"
                        :title="i18n.t('bkApUpload.createTime')"
                        align="center"
                        header-align="center"
                        min-width="25%"
                    >
                    </a-table-column>
                    <a-table-column
                        key="creator_name"
                        v-if="activeName === 'finished'"
                        dataIndex="creator_name"
                        sorter="true"
                        :title="i18n.t('bkApUpload.creator')"
                        align="center"
                        header-align="center"
                        min-width="10%"
                    >
                    </a-table-column>
                    <!-- <a-table-column key="is_identified" v-if="activeName === 'pending'" dataIndex="is_identified"
                        sorter="true" :title="i18n.t('bkApUpload.ocrStatus')" align="center" header-align="center"
                        min-width="10%">
                        <template #default="{ record }">
                            <span v-if="record.is_identified === 1">
                                <a-tag class="tag-green" type="success" color="green">{{
                                    i18n.t('bkApUpload.scanned')
                                }}</a-tag>
                            </span>
                            <span v-else>
                                <a-tag type="danger" class="tag-red" color="red">{{
                                    i18n.t('bkApUpload.pending')
                                }}</a-tag>
                            </span>
                        </template>
                    </a-table-column> -->
                    <a-table-column
                        v-if="userCompany[0].code === '8888'"
                        key="operation"
                        dataIndex="operation"
                        :title="i18n.t('bkCustomer.operation')"
                        align="center"
                        header-align="center"
                        min-width="40%"
                    >
                        <template #default="{record}">
                            <div v-if="record.invoice_created_status === 0">
                                <a-button
                                    class="btn-txt"
                                    type="link"
                                    :disabled="record.is_identified === 0"
                                    :title="i18n.t('bkApUpload.createFile')"
                                    @click="edit(record)"
                                >
                                    <edit-outlined />
                                </a-button>

                                <a-divider type="vertical" />
                                <a-button
                                    v-show="record.invoice_created_status === 0"
                                    :disabled="record.is_identified === 1"
                                    class="btn-txt"
                                    :title="i18n.t('bkApUpload.analyzeFile')"
                                    type="link"
                                    @click="ocrFile(record)"
                                >
                                    <scan-outlined />
                                </a-button>
                                <a-divider type="vertical" />
                                <a-button
                                    v-show="record.invoice_created_status === 0"
                                    class="btn-txt"
                                    :title="i18n.t('bkApUpload.delFile')"
                                    type="link"
                                    @click="remove(record)"
                                    :disabled="record.invoice_created_status !== 0"
                                >
                                    <!-- <delete-outlined
                                        :style="record.invoice_created_status !== 0 ? {color: '#b8b9bc'} : {color: '#f5222d'}"
                                    /> -->
                                    <svg-icon
                                        name="icon_delete"
                                        :style="
                                            record.invoice_created_status !== 0
                                                ? {color: '#b8b9bc'}
                                                : {color: '#f5222d'}
                                        "
                                    ></svg-icon>
                                </a-button>
                            </div>
                            <div v-else>
                                <a-button
                                    class="btn-txt"
                                    :title="i18n.t('bkApUpload.viewDetail')"
                                    type="link"
                                    :disabled="record.file_type == '8'"
                                    @click="view(record)"
                                >
                                    <file-search-outlined />
                                </a-button>
                                <a-divider type="vertical" />
                                <a-button
                                    :title="i18n.t('bkApUpload.downloadInvoice')"
                                    class="btn-txt"
                                    type="link"
                                    @click="download(record)"
                                >
                                    <download-outlined />
                                </a-button>
                            </div>
                        </template>
                    </a-table-column>
                    <a-table-column
                        v-if="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                        key="xml_status"
                        dataIndex="xml_status"
                        sorter="true"
                        :title="i18n.t('bkApUpload.xmlStatus')"
                        align="center"
                        header-align="center"
                        min-width="25%"
                    ></a-table-column>
                    <a-table-column
                        v-if="userCompany[0].code !== '8888'"
                        key="operation"
                        dataIndex="operation"
                        :title="i18n.t('bkCustomer.operation')"
                        align="center"
                        min-width="100px"
                        :ellipsis="true"
                    >
                        <template #default="{record}">
                            <span>
                                <a-button
                                    class="btn-icon-txt"
                                    :title="i18n.t('bkApUpload.edit')"
                                    type="link"
                                    @click="eidtClickRow(record)"
                                    :disabled="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && (record.xml_status !== 'SUCCESS')"
                                >
                                    <edit-outlined />
                                </a-button>
                                <!-- <a-button class="btn-icon-txt" :title="i18n.t('bkApUpload.comment')" type="link"
                                    @click="editComment(record)">
                                    <comment-outlined />
                                </a-button> -->
                                <a-button
                                    v-show="record.invoice_created_status === 0"
                                    class="btn-icon-txt"
                                    :title="i18n.t('bkApUpload.delFile')"
                                    type="link"
                                    @click="remove(record)"
                                    :disabled="record.invoice_created_status !== 0"
                                >
                                    <delete-outlined style="color: red" />
                                    <!-- <delete-outlined
                                        :style="record.invoice_created_status !== 0 ? {color: '#b8b9bc'} : {color: '#f5222d'}"
                                    /> -->
                                    <!-- <svg-icon name="icon_delete" :style="record.invoice_created_status !== 0
                                        ? { color: '#b8b9bc' }
                                        : { color: '#f5222d' }
                                        "></svg-icon> -->
                                </a-button>
                            </span>
                        </template>
                    </a-table-column>
                </a-table>
            </div>
        </div>

        <div class="pagination-wrap">
            <a-pagination
                v-model:current="state.pageQuery.page_index"
                v-model:page-size="state.pageQuery.page_size"
                :disabled="isLoading"
                :hideOnSinglePage="false"
                :showSizeChanger="true"
                :total="totalNumber"
                @change="changePage"
                class="paginpage table-pagination"
                layout="total, sizes, prev, pager, next, jumper"
                background
                small
                @current-change="changeCurrentPageNumber"
                @size-change="changePageSize"
            ></a-pagination>
            <span>{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }} {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span>
        </div>
        <a-modal
            :title="i18n.t('bkApUpload.editComment')"
            v-model:visible="show"
            destroyOnClose
            :closeable="true"
            :width="680"
            :wrapClassName="'modal-wrap'"
            :model="fileCommntObj"
        >
            <template #footer>
                <a-button class="modal-close-btn" @click="dismiss" shape="round">{{
                    i18n.t('EstatementTable.cancel')
                }}</a-button>
                <a-button class="modal-ok-btn" type="primary" shape="round" @click="updateCommentEmit">{{
                    i18n.t('EstatementTable.confirm')
                }}</a-button>
            </template>
            <div>
                <a-form autocomplete="off" ref="currentForm">
                    <a-form-item name="file_name">
                        <a-label class="input-txt">{{ i18n.t('bkApUpload.fileName') }}: </a-label>
                        <a-label>{{ fileCommntObj.file_name }}</a-label>
                    </a-form-item>
                    <a-form-item name="comment">
                        <a-input
                            class="input-txt"
                            v-model:value="fileCommntObj.comment"
                            :placeholder="i18n.t('bkApUpload.editCommentPlaceholder')"
                        />
                    </a-form-item>
                </a-form>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import {useStore} from 'vuex'
import {reactive, ref, computed, onBeforeMount} from 'vue'
import {
    FileSearchOutlined,
    ScanOutlined,
    DownloadOutlined,
    EditOutlined,
    CommentOutlined,
    DeleteOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import * as _ from 'lodash'
// import dayjs from 'dayjs'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {UserCompany, ApIntegration} from '@/lib/storage'
import SvgIcon from '@/components/SvgIcon.vue'
import moment from 'moment'
import {useRouter} from 'vue-router'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'

const i18n: Composer = i18nInstance.global
const store = useStore()
const userCompany: any = UserCompany.get() || []
const props = defineProps({
    tableList: {
        type: Array,
        default: [] as any[],
    },
    activeName: {
        type: String,
        default: 'pending',
    },
})
const isLoading = ref(true)
const router = useRouter()
const pageSizeDefault = 600
// let isClickRow = true
//parent method
const emit = defineEmits([
    'edit',
    'delete',
    'updateComment',
    'ocrFiles',
    'view',
    'download',
    'pageChange',
    'updateSelected',
])
const edit = (record: any) => {
    // isClickRow = false
    console.log('before emit item', record)
    changePagination()
    emit('edit', record)
}
const editMode = ref(false)
const show = ref(false)
const fileCommntObj: any = reactive({
    file_name: '',
    comment: '',
    id: -1,
})

const editComment = (record: any) => {
    fileCommntObj.file_name = record.file_name
    fileCommntObj.comment = record.comment
    fileCommntObj.id = record.id
    show.value = true
    editMode.value = true
}

const updateCommentEmit = () => {
    updateComment(fileCommntObj)
    dismiss()
}

const showDialog = (bool: boolean) => {
    show.value = bool
}
const dismiss = () => {
    showDialog(false)
    editMode.value = false
}
const remove = (record: any) => emit('delete', record)
const updateComment = (record: any) => emit('updateComment', record)
const ocrFileAbbyy = (record: any) => emit('ocrFiles', [record], 'ABBYY')
const view = (record: any) => emit('view', record)
const download = (record: any) => emit('download', record)
const changePagination = () =>
    emit('pageChange', {pageIndex: state.pageQuery.page_index, pageSize: state.pageQuery.page_size})
const updateSelectedItem = (records: any[]) => emit('updateSelected', records)
const updateFileOcrStatus = (data: any) => store.dispatch('ApStore/updateFileOcrStatusV1', data)

const state = reactive({
    tableData: [] as any,
    pageQuery: {
        page_index: 1,
        page_size: 10,
        sortField: null,
        sortDirection: 0,
    },
    selectedRowKeys: [] as any[],
    sortOrder: 'descend',
})

const rowSelection = computed(() => {
    return props.activeName === 'pending'
        ? {
              selectedRowKeys: state.selectedRowKeys,
              onChange: (selectedRowKeys: string[], selectedRows: any[]) => {
                  console.log(selectedRowKeys, selectedRows)
                  state.selectedRowKeys = selectedRowKeys
                  updateSelectedItem(selectedRows)
              },
          }
        : null
})

const sortDatetime = (a: any, b: any) => new Date(b.create_time).getTime() - new Date(a.create_time).getTime()
const sortString = (a: any, b: any) => (a.fileName as string).localeCompare(b.fileName)
const sortingTable = (pagination: any, filters: any, sorter: any, extra: any) => {
    state.sortOrder = state.sortOrder === 'descend' ? 'ascend' : 'descend'
}

const changeCurrentPageNumber = (pageNumber: number) => {
    state.pageQuery.page_index = pageNumber
    changePagination()
}

const changePage = () => {
    changePagination()
    fetchTableData(props.activeName, state.pageQuery)
}

const changePageSize = (pageSize: number) => {
    state.pageQuery.page_size = pageSize
    state.pageQuery.page_index = 1
    changePagination()
}

const ocrFile = async (record: any) => {
    // Show loading message
    const loadingKey = 'ocrLoading'
    message.loading({
        content: 'OCR processing...',
        key: loadingKey,
        duration: 0,
    })

    const approveRequests = {
        id: record.id,
        is_identified: 1,
    }
    const res = await updateFileOcrStatus(approveRequests)
    if (res.data.statusCode !== 201 && res.data.statusCode !== 200) {
        message.error(res.data.message)
    }

    // Wait for 5 seconds
    setTimeout(() => {
        // Close loading message and show success message
        message.success({
            content: 'OCR identified successfully!',
            key: loadingKey,
            duration: 2,
        })

        // Update the record to show it's been OCR'd (if needed)
        // This is optional and depends on your application logic
        record.is_identified = 1

        // Refresh the table data if needed
        fetchTableData(props.activeName, state.pageQuery)
    }, 5000)
}

const fileTypeList = computed(() => store.getters['ArApBrStore/fileTypeList'])
const getFileTypeById = (id: any) => {
    return fileTypeList.value.find((x: any) => x.id === Number(id)).full_name
}
const totalNumber = computed(() => store.state.ApStore.totalNumber)
const fetchTableData = async (index: string, pageQuery?: any) => {
    try {
        isLoading.value = true
        const invoiceQuery = {
            // file_type: '1',
            invoice_created_status: '0',
        }
        pageQuery = pageQuery || {
            page_index: state.pageQuery.page_index,
            page_size: state.pageQuery.page_size,
        }
        const sortObj = {} as any
        invoiceQuery.invoice_created_status = ['finished', 'failure'].includes(index) ? '1' : '0'
        console.log('================== page index=============', state.pageQuery.page_index)
        const query = {
            ...sortObj,
            ...invoiceQuery,
            ...{page_index: Number(state.pageQuery.page_index ?? '1'), page_size: pageQuery.page_size},
            company_code: userCompany[0].code,
        }
        if (pageQuery.sortField) {
            query[`sort[${pageQuery.sortField}]`] = pageQuery.sortDirection === 1 ? 'asc' : 'desc'
        } else {
            query['sort[update_time]'] = 'desc'
        }

        // if (index !== 'finished') query['file_type[$in]'] = '[0,1]'
        // else query['file_type[$in]'] = '[0,1,2,8]'
        query['file_type[$in]'] = '[1,3]'

        await store.dispatch('ApStore/handleDuplicatedFile', {company_code: userCompany[0].code})
        const res = await store.dispatch('ApStore/fetchInvoicesListV1', query)

        // state.tableData = _.cloneDeep(store.state.ApStore.invoicesList)
        state.tableData = _.cloneDeep(res.data.data)
        state.tableData.forEach((x: any) => {
            x.key = x.id
            x.update_time = moment(x.update_time).format('YYYY-MM-DD HH:mm:SS')
            x.create_time = moment(x.create_time).format('YYYY-MM-DD HH:mm:SS')
        })
        console.log('the state.tableDate', state.tableData)
        // isLoading.value = false
    } catch (e) {
        console.log('fetch invoice list error: ', e)
    } finally {
        isLoading.value = false
    }
}
const customSorter = async (pagination: any, filters: any, sorter: any) => {
    if (sorter.field && sorter.order) {
        const order = sorter.order === 'descend' ? -1 : 1
        state.pageQuery.sortField = sorter.field
        state.pageQuery.sortDirection = order
        state.pageQuery.page_index = 1
    } else {
        state.pageQuery.sortField = null
        state.pageQuery.sortDirection = 0
        state.pageQuery.page_index = 1
    }
    await fetchTableData(props.activeName, state.pageQuery)
}

const clickRow = (record: any, index: number) => {
    return {
        onClick: (event: any) => {
            // if (isClickRow === false) {
            //     isClickRow = true
            //     return
            // }
            edit(record)
            view(record)
        },
    }
}

const eidtClickRow = (record: any) => {
    // if (isClickRow === false) {
    //     isClickRow = true
    //     return
    // }
    edit(record)
    view(record)
}

defineExpose({
    fetchTableData,
})
onBeforeMount(async () => {
    if (router.currentRoute.value.query.pageIndex) {
        state.pageQuery.page_index = Number(router.currentRoute.value.query.pageIndex ?? '1')
        state.pageQuery.page_size = Number(router.currentRoute.value.query.pageSize ?? '10')
    }
    await fetchTableData(props.activeName, state.pageQuery)
    state.tableData = _.cloneDeep(store.state.ApStore.invoicesList)
    state.tableData.forEach((x: any) => {
        x.key = x.id
        x.update_time = moment(x.update_time).format('YYYY-MM-DD HH:mm:SS')
        x.create_time = moment(x.create_time).format('YYYY-MM-DD HH:mm:SS')
    })
})
</script>
<style lang="scss" scoped>
.page-container_ap_table {
    padding: 0px 20px 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    border-radius: 12px;

    .table-pending {
        position: relative;
    }

    .table-created {
        position: relative;
        max-height: calc(100% - 32px - 42px);
    }

    .table-container {
        overflow-y: auto;
        max-height: 100%;
    }

    .pagination-wrap {
        display: flex;
        margin-top: 12px;
        justify-content: flex-end;
        align-items: center;
        min-height: 32px;

        span {
            font-size: 12px;
            margin-left: 8px;
            line-height: 16px;
            color: #8c8c8c;
        }
    }
}

.btn-txt {
    padding-left: 0px;
    padding-right: 0px;
    font-size: 16px;
}

.btn-icon-txt {
    padding-left: 10px;
    padding-right: 10px;
}

.input-txt {
    margin-left: 20px;
    width: calc(100% - 40px);
    // margin-right: 10px;
}

.tag-green {
    background-color: rgb(0, 182, 10, 0.1);
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #00b60a;
    line-height: 20px;
    font-weight: 400;
}

.tag-red {
    background-color: rgba(255, 55, 47, 0.1);
    border-radius: 2px;
    border: none;
    font-size: 12px;
    color: #ff372f;
    line-height: 20px;
    font-weight: 400;
}

//:deep(.ant-checkbox-inner) {
//    background: #ffffff;
//    border: 1px solid rgba(103, 109, 124, 1);
//    border-radius: 2px;
//}
</style>
