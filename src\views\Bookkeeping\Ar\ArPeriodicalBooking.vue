<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import ArInvoiceComponent from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponent.vue'
import PeriodicalBooking from '@/components/bookkeepingComponents/ArComponents/PeriodicalBooking.vue'
import {ref, reactive, onMounted, onBeforeMount} from 'vue'
import {useRouter} from 'vue-router'
import {message, notification} from 'ant-design-vue'
import type {NotificationApi} from 'ant-design-vue/lib/notification'
import {UserInfo} from '@/lib/storage'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'

// data() in old version
const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const props = withDefaults(
    defineProps<{
        from: string
        currentInvoice: any
    }>(),
    {
        from: '',
        currentInvoice: {},
    },
)
const userInfo: any = UserInfo.get() || {}
// const payload = reactive({
//     companyName: '',
//     referenceNo: '',
//     statementId: '-1',
//     invoiceType: '1',
//     paymentDate: '', //2022-01-25
//     postingDate: '', //2022-01-25
//     dueDate: '', //2022-01-25
//     bank_account: '',
//     currency: '',
//     // netAmount: 0,
//     gst: 0,
//     pst: 0,
//     qst: 0,
//     debit_coa_code: '', //coa in item
//     net_amount: 0, //total
//     items: [],
//     debit_coa_id: 0,
// })
const current = ref({})
const itemList = ref([] as Array<string>)
const tableLoading = ref(false)
const activeName = ref('first')
const ap_invoiceForm = ref()
const ar_invoiceForm = ref<{initFormData: () => void}>()

const updateArTopCoa = (data: any) => store.commit('ArStore/updateArTopCoa', data)

// mapActions
interface ProductItem {
    item_no: number
    model: string
    description: string
    qty: number
    unit_price: number
    total: number
    type?: string
    dr_cr: string // ar -> debit & dr
    debit_coa_id: number
    debit_coa_code: string
}

interface PeriodicalInvoiceForm {
    pay_method: string
    company_name: string
    company_code: string
    company_tel: string
    company_id: number
    reference_no: string
    invoice_currency: string
    invoice_create_date: string
    invoice_due_date: string
    posting_date: string
    items: Array<ProductItem>
    tax_content: {[key: string]: number | null}
    net_amount: number | null
    total_tax: number | null
    total_fee: number | null
    total_fee_local: number | null
    invoice_comments: string
    supplierId: string
    bill_to_company: string
    bill_to_customer_id: string
    bill_to_email: string
    bill_to_receiver: string
    bill_to_street: string
    bill_to_city: string
    bill_to_province: string
    bill_to_postal_code: string
    bill_to_tel: string
    company_email: string
    fileId: string | null
    shipping: string | null
    exchange_rate: string | null
}

interface BillToCustomerInvoiceForm {
    pay_method?: string
    print?: string
    left_shipto?: string
    right_Default?: string
    right_Data?: string
    right_PaymentTerms?: string
    right_DueDate?: string
    reference_no: string
    company_name: string
    company_address?: string
    company_tel?: string
    company_email?: string
    invoice_create_date?: string
    invoice_due_date?: string
    items?: Array<ProductItem>
    company_gst_no?: string
    company_pst_no?: string
    gst: number | null
    pst: number | null
    qst?: number | null
    invoice_currency?: string
    net_amount: number
    //amount?: number
    //totalTaxable?: number
    total_tax?: number
    total_fee?: number
    total_fee_local?: number
    invoice_no?: string
    invoice_comments?: string
    posting_date: string
    bill_to_receiver?: string
    bill_to_company?: string
    bill_to_street?: string
    bill_to_city?: string
    bill_to_province?: string
    bill_to_country?: string
    bill_to_postal_code?: string
    bill_to_customer_id?: string
    bill_to_tel?: string
    bill_to_email?: string
    ship_to_receiver?: string
    ship_to_street?: string
    ship_to_city?: string
    ship_to_province?: string
    ship_to_postal_code?: string
    ship_to_email?: string
    ship_to_tel?: string
    bank_name?: string
    bank_account: string
    bank_id?: string
    ship_to_company?: string
    amountFee?: number
    fileId?: string | null
    creator?: string
}
// map actions
const createArInvoice = (form: PeriodicalInvoiceForm | BillToCustomerInvoiceForm) =>
    store.dispatch('ArStore/createInvoicev1', form)
// const noestatementBR = (form: BillToCustomerInvoiceForm) => store.dispatch('ArStore/noestatementBR', form)
// const createApInvoice = (form: any) => store.dispatch('ApStore/createInvoice', form)

// antdv notification
const openNotificationWithIcon = (type: string, message: string, description: string, duration: number) => {
    notification[type as keyof NotificationApi]({
        message: message,
        description: description,
        duration: duration,
    } as any)
}

const emits = defineEmits(['custom-cancel', 'saved'])
const backPage = () => {
    // router.go(-1)
    emits('custom-cancel')
}
const saveData = () => {
    emits('saved')
}
const ar_save = async (form: any) => {
    let response: any = {}
    try {
        tableLoading.value = true
        if (form.pay_method === '2') {
            // noestatement payment will be figure out by backend
            //call noestatement br
            // payload.companyName = form.companyName
            // payload.itemList = form.itemList
            // payload.referenceNo = form.referenceNo
            // payload.paymentDate = form.invoiceDueDate
            // payload.postingDate = form.postingDate
            // payload.expenseAccount = form.itemList[0].expenseAccount
            // payload.expenseAccountId = form.itemList[0].expenseAccountId
            // payload.esAmount = form.totalFee
            // payload.dueDate = form.invoiceDueDate
            // payload.currency = form.invoiceCurrency
            // payload.netAmount = form.amount
            // payload.gst = form.tps
            // payload.pst = form.tvp
            // payload.qst = form.tvq
            //response = await noestatementBR(payload)
        } else {
            //response = await createArInvoice(form)
        }
        // frontend will use only api for creating
        if (form.id) {
            form.id = null
            delete form.create_time
        }
        form.items.forEach((item: {id: any; create_time: any}) => {
            delete item.id
            delete item.create_time
        })
        response = await createArInvoice(form)
        if (response.data.statusCode === 200 || response.data.statusCode === 201) {
            message.success(i18n.t('ApComponents.success'))
            if (activeName.value === 'pending') ap_invoiceForm.value?.initFormData()
            else ar_invoiceForm.value?.initFormData()
            document.getElementById('content_id')!.scrollIntoView({behavior: 'smooth'})
            if (props.from === 'massive_process') {
                await router.push({name: 'UploadInvoice', query: {activeName: 'finished'}})
            } else {
                await router.push({path: '/bookkeeping/ar/invoiceHistory'})
            }
        } else {
            // message.error(response.data.message ?? 'failed')
        }
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
        saveData()
    }
}
const ar_import_save = () => {
    return
}
// const ap_save = async (form: any) => {
//     try {
//         tableLoading.value = true
//         const response = await createApInvoice(form)
//         if (response.data.code === 1000) {
//             openNotificationWithIcon('success', 'Success', '', 6000)
//             ap_invoiceForm.value?.initFormData()
//             document.getElementById('content_id')!.scrollIntoView({behavior: 'smooth'})
//         } else {
//             openNotificationWithIcon('error', 'Error', '', 6000)
//         }
//     } catch (error) {
//         console.log(error)
//     } finally {
//         tableLoading.value = false
//     }
// }
const handleClick = (tab: any, event: any) => {
    //console.log(tab, event);
    updateArTopCoa([])
}

onBeforeMount(() => {
    if (userInfo.roles === '9997') {
        activeName.value = 'second'
    }
})

onMounted(() => {
    itemList.value = ['accountReceivable', router.currentRoute.value.meta.title as string]
})
</script>
<template>
    <div class="page-container-invoice" id="content_id">
        <!-- <bread-crumb-title-bar :item-list="itemList" /> -->
        <div class="content-box">
            <div class="content-box-tables-block">
                <a-tabs v-model:activeKey="activeName" @change="handleClick" :destroyInactiveTabPane="true">
                    <a-tab-pane :tab="i18n.t('bkArInvoice.pb')" v-if="userInfo.roles !== '9997'" key="first">
                        <a-spin class="invoice-form-content" :spinning="tableLoading">
                            <periodical-booking
                                ref="ap_invoiceForm"
                                :currentInvoice="currentInvoice"
                                :readonly-mode="false"
                                :operationMode="'creating'"
                                :from="from"
                                @save="ar_save"
                                @dismiss="backPage"
                            ></periodical-booking>
                        </a-spin>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.page-container-invoice {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .invoice-form-content {
        width: 100%;
        margin: 0 auto;
    }

    .content-box {
        padding: 10px 14px 6px 14px;
        gap: 10px;
    }

    .page-container-ap-invoice-form {
        :deep(.ap-invoice-block) {
            background-color: #fff;
            padding: 32px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            border-bottom: 0;
        }
        :deep(.ap-invoice-amount-block .ap-invoice-amount-block-left) {
            width: 510px;
        }
        :deep(.ap-invoice-amount-block) {
            padding-bottom: 0;
        }

        :deep(.textarea-wrap) {
            margin-bottom: 32px;
        }

        :deep(.ap-invoice-footer) {
            margin-bottom: 40px;
        }
    }

    .page-container-ar-invoice-form {
        :deep(.ar-invoice-block) {
            background-color: #fff;
            padding: 32px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            border-bottom: 0;
        }
        :deep(.ar-invoice-amount-block .ar-invoice-amount-block-left) {
            width: 510px;
        }
        :deep(.ar-invoice-amount-block) {
            padding-bottom: 0;
        }

        :deep(.textarea-wrap) {
            margin-bottom: 32px;
        }

        :deep(.ar-invoice-footer) {
            margin-bottom: 40px;
        }
    }
}
</style>
