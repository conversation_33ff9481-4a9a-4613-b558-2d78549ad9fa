<!-- @format -->

<script lang="ts" setup>
import { computed, onBeforeMount, onMounted, reactive, ref } from 'vue'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {
    SearchOutlined,
    MailOutlined,
    PlusOutlined,
    ExportOutlined,
    CopyOutlined,
    DownloadOutlined,
    FilterOutlined,
    LayoutOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
} from '@ant-design/icons-vue'
import { ArCustomizeTable, UserCompany, Ar_Integration } from '@/lib/storage'
import { useStore } from 'vuex'
import { message } from 'ant-design-vue'
import FileSaver from 'file-saver'
import { useRouter } from 'vue-router'
import CustomColumns from '@/components/bookkeepingComponents/CommonComponents/CustomColumns.vue'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ArInvoiceComponent from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponent.vue'
import ArInvoiceComponentMX from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponentMX.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import moment from 'moment'
import ArFullInvoice from './ArFullInvoice.vue'
import ArPeriodicalInvoice from './ArPeriodicalBooking.vue'
import ArBillToCustomerInvoice from './ArBillingToCustomer.vue'

const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const userCompany: any = UserCompany.get() || []
const arIntegration: any = Ar_Integration.get() ?? 0
const showColumns = ref(false)
const defaultTable = [
    'invoiceNo',
    'invoiceComment',
    'createDate',
    'issuer',
    'total',
    'balance',
    //'sapStatus',
    'br',
    'referenceNo',
    'creator',
    'createTime',
    'postingDate',
    'dueDate',
    ...(arIntegration === 1 ? ['uuid'] : []),
]

const saveColumns = (list: any) => {
    customizeTable.value = list
    showColumns.value = false
    ArCustomizeTable.set(list)
}

const visible = ref(false)
const showTabType = ref()
const itemList = ref([] as any[])
const tableLoading = ref(false)
const submitLoading = ref(false)
const activeTabName = ref('0')
const isCustomize = ref(false)
const customizeTable: any = ref(
    ArCustomizeTable.get() || [
        'issuer',
        'total',
        'balance',
        'br',
        'referenceNo',
        'postingDate',
        ...(arIntegration === 1 ? ['uuid'] : []),
    ],
)
const timer = ref()
const brFlagOptions = reactive([
    {
        value: -1,
        label: i18n.t('bkArInvoice.brStatus3'), //'All',
    },
    {
        value: 0,
        label: i18n.t('ApInvoiceFormPdf.notPaid'), //'NOT PAID',
    },
    {
        value: 1,
        label: i18n.t('ApInvoiceFormPdf.partialPaid'), //'NOT PAID',
    },
    {
        value: 2,
        label: i18n.t('ApInvoiceFormPdf.paid'), //'CASH PAID',
    },
    {
        value: 3,
        label: i18n.t('ApInvoiceFormPdf.reversed'),
    },
])
const searchForm = reactive({
    companyName: '',
    createStartDate: '',
    createEndDate: '',
    minTotalFee: null,
    maxTotalFee: null,
    brFlag: -1,
})
const downloadPdfForm = reactive({
    createStartDate: '',
    createEndDate: '',
})
const current = ref({} as any)
const arShow = ref(false)
const apShow = ref(false)
const emailShow = ref(false)
const billToEmailData = reactive({
    row: {} as any,
    emailShow: false,
    billToEmail: '',
})
const billToEmail = ref('')
const showRc = ref(false)
const readonlyMode = ref(false)
const pageQuery = reactive({
    page_index: 1,
    page_size: 10,
    currentPageNumber: 1,
    sortField: null,
    sortDirection: 0,
})
const value1 = ref('')
const expandKeys = ref([] as any[])
const tableData = ref([] as any[])
const childTableHeight = ref('100%')
const isManual = ref(false)
const loading = ref(false)

const getQueryParams = (searchForm: any) => {
    //used for transform searchForm property to api query format
    const result: any = {}
    if (pageQuery.sortDirection === 0) {
        result['sort[create_time]'] = 'desc'
    }
    if (searchForm.companyName) {
        result['bill_to_company[$like]'] = searchForm.companyName
    }
    if (searchForm.createStartDate !== '' && searchForm.createEndDate !== '') {
        result['create_time[$bw]'] = `[${searchForm.createStartDate},${searchForm.createEndDate}]`
    }
    if (searchForm.createStartDate !== '' && searchForm.createEndDate == '') {
        result['create_time[$gte]'] = searchForm.createStartDate
    }
    if (searchForm.createStartDate == '' && searchForm.createEndDate !== '') {
        result['create_time[$lte]'] = searchForm.createEndDate
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee !== null) {
        result['total_fee[$bw]'] = `[${searchForm.minTotalFee},${searchForm.maxTotalFee}]`
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee == null) {
        result['total_fee[$gte]'] = searchForm.minTotalFee
    }
    if (searchForm.minTotalFee !== null && searchForm.maxTotalFee == null) {
        result['total_fee[$lte]'] = searchForm.maxTotalFee
    }
    if (searchForm.brFlag !== -1) {
        result['br_flag'] = searchForm.brFlag
    }
    return result
}

const fetchInvoicesHistoryList = (payload: any) => store.dispatch('ArStore/fetchInvoicesHistoryListV1', payload)
// const submitReconciliationWithEsList = (payload: any) =>
//     store.dispatch('ReconciliationStore/submitReconciliationWithEsList', payload)
// const getReconciliationWithEsList = (payload: {id: any}) =>
//     store.dispatch('ReconciliationStore/getReconciliationWithEsList', payload)
// const getReconciliationWithEsListManual = (payload: {id: any}) =>
//     store.dispatch('ReconciliationStore/getReconciliationWithEsListManual', payload)
const fetchCustomerDropDown = (payload: any) => store.dispatch('CommonDropDownStore/fetchContactDropDown', payload)
const fetchAccountDescDropdown = (payload: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', payload)

const updateTable = async () => {
    try {
        tableLoading.value = true
        const query = {
            company_code: userCompany[0].code,
            ...getQueryParams(searchForm),
            ...{ page_index: pageQuery.currentPageNumber, page_size: pageQuery.page_size },
            // ...(activeTabName.value != 'null' ? {br_flag: activeTabName.value} : null),
        }
        if (activeTabName.value === '0' && !query['br_flag'] && query['br_flag'] !== 0) query['br_flag[$in]'] = '[0,1]'
        if (pageQuery.sortField) {
            query[`sort[${pageQuery.sortField}]`] = pageQuery.sortDirection === 1 ? 'asc' : 'desc'
        }

        const res = await fetchInvoicesHistoryList(query)
        tableData.value = JSON.parse(JSON.stringify(res.data.data))
        tableData.value.map((item: any) => {
            item.eslist = []
            item.create_time = moment(item.create_time).format('YYYY-MM-DD HH:mm:SS')
        })
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}
// const updatePosting = async (row: any) => {
//     console.log('updatePosting...')
//     showRcDialog(false)
//     const payload = {
//         invoice_no: row.id,
//         invoice_type: '1',
//         invoice_currency: row.invoiceCurrency,
//         bpNumber: row.billToCustomerId,
//         sapDocumentId: row.sapDocumentId,
//         paymentDate: row.invoiceDueDate,
//         postingDate: row.postingDate,
//         invoiceTotalFee: row.totalFee,
//         brAmount: row.totalFee,
//         esList: [] as any[],
//         // esList: row.eslist,
//         // esList: [{
//         //   id: "",
//         //   bankAccount: "",
//         //   deposit: 0,
//         //   withdrawal: 0,
//         //   expenseAccount: 1011,
//         //   currencyType: "",
//         //   date: "",  //银行流水的 posting_date
//         // }],
//     }
//     row.eslist.map((item: any) => {
//         item.withdrawal = item.withdrawal === null ? 0 : item.withdrawal
//         item.deposit = item.deposit === null ? 0 : item.deposit
//         if (item.expenseAccount === null) {
//             message.error({
//                 content: `${item.bankAccount}: expenseAccount is null`,
//                 duration: 3,
//             })
//             return false
//         }
//         payload.esList.push(item)
//     })
//     console.log('payload', payload)
//
//     try {
//         const response = await submitReconciliationWithEsList(payload)
//         console.log(response.data, 'response')
//         if (response.data.code === 1000) {
//             message.success({
//                 content: response.data.msg,
//                 duration: 3,
//             })
//             row.brFlag = '2'
//             expandKeys.value = []
//             updateTable()
//             console.log('update table')
//         } else {
//             message.error({
//                 content: response.data.msg,
//                 duration: 3,
//             })
//         }
//     } catch (error) {
//         console.log(error)
//     }
// }
// const reconcile = async (row: any) => {
//     current.value = {...row}
//     showRcDialog(true)
// }
const search = async () => {
    pageQuery.currentPageNumber = 1
    clearInterval(timer.value)
    await updateTable()
}
const downloadPdf = async () => {
    if (downloadPdfForm.createStartDate === '' 
        || downloadPdfForm.createStartDate === null
        || downloadPdfForm.createEndDate === ''
        || downloadPdfForm.createEndDate === null
    ) {
        message.error({
            content: i18n.t('ArInvoiceHistory.msg_error_no_date'),
            duration: 3,
        })
        return
    }

    tableLoading.value = true
    const response =  await downloadArPdfIntegration({
        "company_code": userCompany[0].code,
        "param": {
            "start_date": downloadPdfForm.createStartDate,
            "end_date": downloadPdfForm.createEndDate,
        },
    })
    tableLoading.value = false
    const contentType = response.headers['content-type']
    if (contentType.includes('application/json')) {
        // 读取 Blob 内容并转换为文本
        const blob = response.data
        const text = await blob.text()
        try {
            // 尝试将文本解析为 JSON
            const errorData = JSON.parse(text)
            message.error({
                content: errorData.message,
                duration: 3,
            })
        } catch (e) {
            // 如果解析失败，保留原始错误
            console.error('解析错误响应失败:', e)
        }
    } else {
        const filename = `${userCompany[0].code} ${downloadPdfForm.createStartDate} ${downloadPdfForm.createEndDate}.zip`
        console.log('=======', filename)
        console.log('export      ->', response.headers)
        const blob = new Blob([response.data], {type: response.headers['content-type']})
        FileSaver.saveAs(blob, filename)
        message.success({
            content: i18n.t('ApComponents.success'),
            duration: 3,
        })
    }
}
const customRow = (record: any, rowIndex: any, column: any) => {
    return {
        onClick: (event: any) => {
            if (column.key !== 'operation') {
                edit(record)
            } else {
                //TODO
                console.log('Send Email')
                if (record.bill_to_company === 'DUMMY') {
                    showTabType.value = 'DUMMY'
                } else {
                    showTabType.value = 'BillToCustomer'
                }
                current.value = { ...record }
                current.value.reference_no = ''
                current.value.create_time = null
                toAddInvoice()
            }
        },
    }
}
const edit = (row: any) => {
    // console.log("sale history",row)
    current.value = { ...row }
    readonlyMode.value = true
    // const flag = current.value.br_flag === '2' && !current.value.send_engine_status ? 'ap' : 'ar'
    const flag = 'ar'
    showDialog(true, flag)
}
const download = (record: any, event: any) => {
    event.stopPropagation()
    if (record.invoiceUrl) {
        FileSaver.saveAs(record.invoiceUrl)
    } else {
        message.error({
            content: i18n.t('ArInvoiceHistory.invoice'), //'Invoice File is not available!',
            duration: 3,
        })
    }
}
const copyInvoice = (record: any, event: any) => {
    console.log('================', record)
}

const saveBillToEmail = async () => {
    console.log('saveBillToEmail', billToEmailData)
    if (!billToEmailData.billToEmail) {
        message.info('Email can not be empty')
        return
    }
    //保存email到contact
    await store.dispatch('ContactStore/fetchContacts', { company_code: billToEmailData.row.company_code, $limit: -1 })
    const contacts = store.state.ContactStore.contactList
    const contact = contacts.find((item: any) => item.contact_id === billToEmailData.row.bill_to_customer_id)
    await store.dispatch('ContactStore/pitchUpdateContact', { id: contact.id, email: billToEmailData.billToEmail })

    //保存email到相应的invoice
    await store.dispatch('ArStore/updateBillToEmail', {
        bill_to_email: billToEmailData.billToEmail,
        bill_to_customer_id: billToEmailData.row.bill_to_customer_id,
    })

    //发送email
    await sendInvoiceInvoice(billToEmailData.row.id)
    await updateTable()
    //清空billToEmailData数据
    billToEmailData.row = {}
    billToEmailData.emailShow = false
    billToEmailData.billToEmail = ''
}

const sendInvoice = async (record: any, event: any) => {
    console.log(record, event)
    if (!record.bill_to_email) {
        billToEmailData.row = record
        billToEmailData.emailShow = true
        return
    }
    event.stopPropagation()
    loading.value = true
    await sendInvoiceInvoice(record.id)
    loading.value = false
    // try {
    //     event.stopPropagation()
    //     loading.value = true
    //     const response = await sendArInvoiceEmail(record.id)
    //     if (response.status === 201 && response.data.statusCode === 200) {
    //         message.success(i18n.t('ApComponents.success'))
    //     } else {
    //         message.error({
    //             content: 'failed',
    //             duration: 3,
    //         })
    //     }
    // } catch (error) {
    //     console.log(error)
    // } finally {
    //     loading.value = false
    // }
}
const sendInvoiceForIntegration = async (record: any, event: any) => {
    console.log(record, event)
    event.stopPropagation()
    loading.value = true
    await sendInvoiceInvoiceIntegration({
        "company_code": record.company_code,
        "ar_id": record.id,
    })
    loading.value = false
}

const sendInvoiceInvoice = async (id: any) => {
    try {
        const response = await sendArInvoiceEmail(id)
        if (response.status === 201 && response.data.statusCode === 200) {
            message.success(i18n.t('ApComponents.success'))
        } else {
            message.error({
                content: 'failed',
                duration: 3,
            })
        }
    } catch (error) {
        console.log(error)
    }
}
const sendInvoiceInvoiceIntegration = async (body: any) => {
    try {
        const response = await sendArInvoiceEmailIntegration(body)
        if (response.status === 200 && response.data.code === 200) {
            message.success(i18n.t('ApComponents.success'))
        } else {
            message.error({
                content: 'failed',
                duration: 3,
            })
        }
    } catch (error) {
        console.log(error)
    }
}

const sendArInvoiceEmail = (payload: string) => store.dispatch('ArStore/sendArInvoiceEmailv1', payload) // payload is ar_id
const sendArInvoiceEmailIntegration = (payload: any) => store.dispatch('ArStore/sendArInvoiceEmailv1Integration', payload) // payload is ar_id
const downloadArPdfIntegration = (payload: any) => store.dispatch('ArStore/downloadArPdfv1Integration', payload)

const showDialog = (bool: boolean, flag: string) => {
    if (flag === 'ar') {
        arShow.value = bool
        apShow.value = false
    }
    if (flag === 'ap') {
        apShow.value = bool
        arShow.value = false
    }
    // this.show = bool
    if (!bool) {
        current.value = {}
        apShow.value = false
        arShow.value = false
    }
}
const showRcDialog = (bool: boolean) => {
    showRc.value = bool
}
const dismiss = (action: any) => {
    showDialog(false, '')
    showRcDialog(false)
    readonlyMode.value = false
    if (action) {
        updateTable()
    }
}
const changePage = () => {
    updateTable()
}
const changeCurrentPageNumber = (pageNumber: number) => {
    pageQuery.page_index = pageNumber
    pageQuery.currentPageNumber = pageNumber
    updateTable()
}
const changePageSize = (pageSize: number) => {
    pageQuery.page_size = pageSize
    pageQuery.currentPageNumber = 1
    updateTable()
}

const rowClassName = (record: any, index: number) => {
    return record?.xml_status === 'FAILURE' ? 'custom-row-bg' : ''
}
// const expandChange = async (row: any, expandedRows: any) => {
//     expandKeys.value = expandKeys.value[0] === row.id ? [] : [row.id]
//     if (expandKeys.value.length === 0) {
//         return
//     }
//
//     const payload = {
//         id: row.id,
//         br_type: '1', //AR-0, AP-1 change to 1-AR 2-AP
//         company_name: '',
//         createEndDate: '',
//         createStartDate: '',
//         maxTotalFee: null,
//         minTotalFee: null,
//         pageIndex: pageQuery.page_index,
//         //pageSize: this.pageQuery.pageSize,
//         pageSize: 200,
//         invoiceCurrency: '',
//         balance: '',
//     }
//
//     if (row.eslist.length > 0) {
//         childTableHeight.value = 60 + 50 * (row.eslist.length > 5 ? 5 : row.eslist.length) + 'px'
//         return
//     }
//
//     tableData.value.forEach((tmp, index) => {
//         if (tmp.id === row.id) {
//             //payload["orgId"] = this.tableData[index].orgId
//             payload['invoiceCurrency'] = tableData.value[index].invoiceCurrency
//             payload['balance'] = tableData.value[index].totalFee
//         }
//     })
//
//     row.loading = true
//
//     let dispData: any[] = []
//     let response = await getReconciliationWithEsList(payload)
//     if (response.data.code != 1000 || response.data.data.length == 0) {
//         response = await getReconciliationWithEsListManual(payload)
//         dispData = response.data.data.list
//         isManual.value = true
//     } else {
//         dispData = response.data.data[0]
//         isManual.value = false
//     }
//
//     if (response.data.code === 1000) {
//         if (dispData.length > 0) {
//             childTableHeight.value = 60 + 50 * (dispData.length > 5 ? 5 : dispData.length) + 'px'
//             dispData.forEach(item => {
//                 item.withdrawal = item.withdrawal === 0 ? null : item.withdrawal
//                 item.deposit = item.deposit === 0 ? null : item.deposit
//             })
//         } else {
//             childTableHeight.value = '210px'
//         }
//
//         tableData.value.forEach((tmp, index) => {
//             if (tmp.id === row.id) {
//                 console.log(tmp.id, tmp)
//                 tableData.value[index].eslist = dispData || []
//             }
//         })
//     }
//     console.log('this.tableData', tableData.value)
//     row.loading = false
// }
const switchTab = () => {
    pageQuery.page_index = 1
    pageQuery.currentPageNumber = 1
    updateTable()
}
const customize = () => {
    isCustomize.value = !isCustomize.value
    if (!isCustomize.value) {
        localStorage.setItem('arCustomizeTable', JSON.stringify(customizeTable.value))
    }
}
const changeCustomize = (key: any) => {
    const index = customizeTable.value.findIndex((i: any) => i == key)
    if (index >= 0) {
        customizeTable.value = customizeTable.value.filter((i: any) => i != key)
    } else {
        customizeTable.value.push(key)
    }
}
const inputChange = () => {
    delayTimer(0)
}
const delayTimer = (i: number) => {
    const _timer = 2
    clearInterval(timer.value)
    timer.value = setInterval(() => {
        ++i
        if (i == _timer) {
            search()
            clearInterval(timer.value)
        }
    }, 1000)
}
const invoicesHistoryList = computed(() => store.state.ArStore.invoicesHistoryList)
const invoiceDetail = computed(() => store.state.ArStore.invoiceDetail)
const totalNumber = computed(() => store.state.ArStore.totalNumber)

const visibleChange = (visible: any) => store.commit('setDisableScroll', visible)

onBeforeMount(async () => {
    await Promise.all([
        fetchCustomerDropDown({}),
        fetchAccountDescDropdown({ company_code: userCompany[0].code, bk_type: 1 }),
    ])
})
onMounted(async () => {
    itemList.value = ['accountReceivable', router.currentRoute.value.meta.title]
    await updateTable()
})
const toAddInvoice = () => {
    // router.push({name: 'FullInvoice'})
    visible.value = true
}
const dismissColumns = () => {
    showColumns.value = false
}
const exportArInvoices = async () => {
    try {
        tableLoading.value = true
        const query = {
            company_code: userCompany[0].code,
        }
        await store.dispatch('ArStore/exportArInvoiceList', query)
    } catch (err) {
        console.log(err)
    } finally {
        tableLoading.value = false
    }
}
const customSorter = async (pagination: any, filters: any, sorter: any) => {
    if (sorter.field && sorter.order) {
        const order = sorter.order === 'descend' ? -1 : 1
        pageQuery.sortField = sorter.field
        pageQuery.sortDirection = order
        pageQuery.currentPageNumber = 1
    } else {
        pageQuery.sortField = null
        pageQuery.sortDirection = 0
        pageQuery.currentPageNumber = 1
    }
    clearInterval(timer.value)
    await updateTable()
}
const getBillingTypeById = (record: any) => {
    const billingTypeString =
        record.sap_billing_type === 'M'
            ? i18n.t('bkArInvoice.billingTypeInvoice')
            : record.sap_billing_type === 'O'
            ? i18n.t('bkArInvoice.billingTypeCreditNote')
            : record.sap_billing_type === 'P'
            ? i18n.t('bkArInvoice.billingTypeDebitMemo')
            : ''
    return billingTypeString
}
// const cancel = () => (visible.value = false; current.value = {};)
const cancel = () => {
    visible.value = false
    current.value = {}
    showTabType.value = ''
}
const saved = async () => {
    visible.value = false
    current.value = {}
    showTabType.value = ''
    await updateTable()
}
</script>
<template>
    <div class="history-page-wrap">
        <div class="history-page-content">
            <div style="display: flex" class="main-head">
                <a-tabs v-model:activeKey="activeTabName" @change="switchTab">
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('bkArInvoice.brStatus0')" key="0"></a-tab-pane>
                    <a-tab-pane :disabled="tableLoading" :tab="i18n.t('bkArInvoice.brStatus2')" key="null"></a-tab-pane>
                </a-tabs>
                <div>
                    <div class="history-page-header">
                        <div class="search-group-wrap">
                            <a-input v-model:value="searchForm.companyName" :placeholder="$t('bkArInvoice.search')"
                                :disabled="tableLoading || isCustomize" class="search-input" @input="inputChange"
                                @pressEnter="search">
                                <template #suffix>
                                    <svg-icon name="icon_search"></svg-icon>
                                </template>
                            </a-input>
                            <a-popover class="popover-wrap" trigger="click" placement="bottom"
                                @visibleChange="visibleChange">
                                <a-tooltip :title="$t('commonTag.filter')">
                                    <a-button class="search-button" :disabled="tableLoading">
                                        <template #icon>
                                            <svg-icon name="icon_filter"></svg-icon>
                                        </template>
                                        <!-- {{ i18n.t('commonTag.filter') }} -->
                                    </a-button>
                                </a-tooltip>
                                <template #content>
                                    <a-form :model="searchForm" ref="searchRef" class="search-input-form">
                                        <div class="search-input-group">
                                            <!-- <a-form-item :label="$t('bkArInvoice.date')"> -->
                                            <a-form-item>{{ $t('bkArInvoice.date') }}<span>&nbsp;&nbsp;&nbsp;</span>
                                                <a-date-picker v-model:value="searchForm.createStartDate"
                                                    :disabled="isCustomize" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                                    :placeholder="$t('gl.createStartDate')" style="width: 160px"
                                                    clearable>
                                                </a-date-picker>
                                            </a-form-item>
                                            <!-- <a-form-item :label="$t('bkArInvoice.to')"> -->
                                            <a-form-item><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                <a-date-picker v-model:value="searchForm.createEndDate"
                                                    :disabled="isCustomize" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                                    :placeholder="$t('gl.createEndDate')" style="width: 160px"
                                                    clearable>
                                                </a-date-picker>
                                            </a-form-item>
                                        </div>
                                        <div class="search-input-group">
                                            <!-- <a-form-item :label="$t('bkArInvoice.totalCol')"> -->
                                            <a-form-item>{{ $t('bkArInvoice.totalCol') }}
                                                <span>&nbsp;&nbsp;&nbsp;</span>
                                                <a-input-number v-model:value="searchForm.minTotalFee" :controls="false"
                                                    :disabled="isCustomize" :placeholder="$t('bkArInvoice.minFee')"
                                                    style="width: 160px"></a-input-number>
                                            </a-form-item>
                                            <!-- <a-form-item :label="$t('bkArInvoice.to')" label-width="35px"> -->
                                            <a-form-item><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                <a-input-number v-model:value="searchForm.maxTotalFee" :controls="false"
                                                    :disabled="isCustomize" :placeholder="$t('bkArInvoice.maxFee')"
                                                    style="width: 160px"></a-input-number>
                                            </a-form-item>
                                        </div>
                                        <div class="search-input-group">
                                            <a-form-item :label="$t('bkArInvoice.br')" v-if="activeTabName !== '0'">
                                                <a-select default-first-option v-model:value="searchForm.brFlag"
                                                    style="width: 355px" :controls="false" :disabled="isCustomize">
                                                    <a-select-option style="width: 355px" v-for="item in brFlagOptions"
                                                        :key="item.label" :value="item.value">{{ item.label
                                                        }}</a-select-option>
                                                </a-select>
                                            </a-form-item>
                                        </div>

                                        <a-button type="primary" shape="round" :disabled="tableLoading || isCustomize"
                                            @click="search">
                                            <template #icon>
                                                <search-outlined />
                                            </template>
                                            {{ $t('commonTag.search') }}
                                        </a-button>
                                    </a-form>
                                </template>
                            </a-popover>

                            <a-tooltip :title="$t('commonTag.columns')">
                                <a-button class="search-button" :disabled="tableLoading"
                                    @click="showColumns = !showColumns">
                                    <template #icon>
                                        <svg-icon name="icon_columns"></svg-icon>
                                    </template>
                                    <!-- {{ i18n.t('commonTag.columns') }} -->
                                </a-button>
                            </a-tooltip>

                            <a-tooltip :title="$t('workTimeManager.export')">
                                <a-button class="search-button" :disabled="tableLoading" @click="exportArInvoices()">
                                    <export-outlined />
                                    <!-- {{ i18n.t('workTimeManager.export') }} -->
                                </a-button>
                            </a-tooltip>
                            <a-popover class="popover-wrap" trigger="click" placement="bottom"
                                @visibleChange="visibleChange">
                                <a-tooltip :title="$t('commonTag.download')">
                                    <a-button class="search-button" :disabled="tableLoading">
                                        <template #icon>
                                            <download-outlined />
                                        </template>
                                    </a-button>
                                </a-tooltip>
                                <template #content>
                                    <a-form :model="downloadPdfForm" ref="searchRef" class="search-input-form">
                                        <div class="search-input-group">
                                            <a-form-item>
                                                <a-date-picker v-model:value="downloadPdfForm.createStartDate"
                                                    :disabled="isCustomize" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                                    :placeholder="$t('gl.createStartDate')" style="width: 160px"
                                                    clearable>
                                                </a-date-picker>
                                            </a-form-item>
                                            <a-form-item><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                <a-date-picker v-model:value="downloadPdfForm.createEndDate"
                                                    :disabled="isCustomize" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                                                    :placeholder="$t('gl.createEndDate')" style="width: 160px"
                                                    clearable>
                                                </a-date-picker>
                                            </a-form-item>
                                        </div>

                                        <a-button type="primary" shape="round" :disabled="tableLoading || isCustomize"
                                            @click="downloadPdf">
                                            <template #icon>
                                                <download-outlined />
                                            </template>
                                            {{ $t('commonTag.download') }}
                                        </a-button>
                                    </a-form>
                                </template>
                            </a-popover>
                        </div>
                        <a-button
                            v-if="arIntegration !== 1"
                            type="primary"
                            shape="round"
                            class="add-button"
                            @click="toAddInvoice()"
                        >
                            <template #icon>
                                <!-- <plus-outlined /> -->
                            </template>
                            {{ i18n.t('commonTag.new') }}
                        </a-button>

                        <a-modal v-model:visible="visible" width="1300px" :title="i18n.t('bkApInvoice.create')"
                            :footer="null" destroyOnClose @cancel="cancel">
                            <!-- <ar-full-invoice @custom-cancel="cancel" /> -->
                            <!-- <ar-periodical-invoice @custom-cancel="cancel" /> -->
                            <!-- <ar-bill-to-customer-invoice @custom-cancel="cancel" /> -->
                            <ar-periodical-invoice v-if="showTabType === 'DUMMY'" @custom-cancel="cancel" @saved="saved"
                                :currentInvoice="current" from="copy" />

                            <ar-bill-to-customer-invoice v-else-if="showTabType === 'BillToCustomer'"
                                @custom-cancel="cancel" @saved="saved" :currentInvoice="current" from="copy" />
                            <ar-full-invoice v-else @custom-cancel="cancel" />
                            <!-- <div>{{ current }}</div> -->
                        </a-modal>
                    </div>
                </div>
            </div>
            <a-table :dataSource="tableData" :loading="tableLoading" :pagination="false"
                :scroll="customizeTable.length > 5 ? { x: 1350, y: 'calc(100vh - 300px)' } : { x: 'auto' }"
                @change="customSorter" :rowClassName="rowClassName">
                <!-- Billing Type -->
                <a-table-column align="left" :title="i18n.t('bkArInvoice.billingType')" data-index="sap_billing_type"
                    sorter="true" width="120px" v-if="arIntegration === 1"
                    :custom-cell="customRow" :ellipsis="true" >
                    <template #default="{ record }">
                        <div>
                            {{ getBillingTypeById(record) }}
                            <a-tooltip v-if="record.xml_status === 'FAILURE'">
                                <template #title>
                                    <div class="tooltip-content">{{ record.xml_message }}</div>
                                </template>
                                <ExclamationCircleOutlined style="color: red; font-size: 16px; margin-left: 8px" />
                            </a-tooltip>
                        </div>
                    </template>
                </a-table-column>
                <!-- Invoice number -->
                <a-table-column align="center" :title="i18n.t('bkArInvoice.invoiceNo')" data-index="invoice_no"
                    sorter="true" width="180px" v-if="customizeTable.includes('invoiceNo') || isCustomize"
                    :custom-cell="customRow" :ellipsis="true" />
                <!-- companyName -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.issuer')"
                    data-index="bill_to_company" sorter="true" width="120px" :ellipsis="true"
                    v-if="customizeTable.includes('issuer') || isCustomize">
                    <template #default="{ record }">
                        <span>
                            {{ record.bill_to_company?.toUpperCase() }}
                        </span>
                    </template>
                </a-table-column>
                <!-- referenceNo -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.referenceNo')"
                    data-index="reference_no" sorter="true" width="100px" :ellipsis="true"
                    v-if="customizeTable.includes('referenceNo') || isCustomize" />
                <!-- postingDate -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.postingDate')"
                    data-index="posting_date" sorter="true" width="120px" :ellipsis="true"
                    v-if="customizeTable.includes('postingDate') || isCustomize" />
                <!-- totalFee -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.total')"
                    data-index="total_fee" sorter="true" width="100px" :ellipsis="true"
                    v-if="customizeTable.includes('total') || isCustomize">
                    <template #default="{ record }">
                        <span>
                            <!-- {{ Number(record.total_fee).toFixed(2) }} -->
                            {{ $formatNumber(Number(record.total_fee)) }}
                        </span>
                    </template>
                </a-table-column>
                <!-- balance -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.balance')"
                    data-index="balance" sorter="true" width="100px" :ellipsis="true"
                    v-if="customizeTable.includes('balance') || isCustomize">
                    <template #default="{ record }">
                        <span>
                            <!-- {{ Number(record.balance).toFixed(2) }} -->
                            {{ $formatNumber(Number(record.balance)) }}
                        </span>
                    </template>
                </a-table-column>
                <!-- Comments -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.invoiceComment')"
                    data-index="invoice_comments" sorter="true" width="150px"
                    v-if="customizeTable.includes('invoiceComment') || isCustomize" :ellipsis="true" />
                <!-- Creator -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.creator')"
                    data-index="creator_name" sorter="true" width="120px" :ellipsis="true"
                    v-if="customizeTable.includes('creator') || isCustomize" />
                <!-- Create time -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.createTime')"
                    data-index="create_time" sorter="true" width="120px" :ellipsis="true"
                    v-if="customizeTable.includes('createTime') || isCustomize" />
                <!-- Create date -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.createDate')"
                    data-index="invoice_create_date" sorter="true" width="120px" :ellipsis="true"
                    v-if="customizeTable.includes('createDate') || isCustomize" />

                <!-- sendSapStatus -->
                <!-- <a-table-column
                    :custom-cell="customRow"
                    align="center"
                    :title="i18n.t('commonTag.status')"
                    data-index="send_engine_status"
                    width="80px"
                    v-if="customizeTable.includes('sapStatus') || isCustomize"
                >
                    <template #default="{record}">
                        <span v-if="record.send_engine_status === 0">
                            <a-tag color="success">Not sended</a-tag>
                        </span>
                        <span v-else-if="record.send_engine_status === 1">
                            <a-tag color="success">Success</a-tag>
                        </span>
                        <span v-else>
                            <a-tag color="danger">Pending</a-tag>
                        </span>
                    </template>
                </a-table-column> -->
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('commonTag.status')"
                    data-index="br_flag" sorter="true" width="80px" :ellipsis="true"
                    v-if="customizeTable.includes('br') || isCustomize">
                    <!-- <template #default="{record}">
                        <span>
                            <img
                                class="br-icon"
                                v-if="record.br_flag == '2'"
                                src="@/assets/image/icon/icon_complete.png"
                            />
                            <img
                                class="br-icon"
                                v-else-if="record.br_flag == '1'"
                                src="@/assets/image/icon/icon_in_progress.png"
                            />
                            <img class="br-icon" v-else src="@/assets/image/icon/icon_incomplete.png" />
                        </span>
                    </template> -->

                    <template #default="{ record }">
                        <span v-if="record.br_flag === 0">
                            <a-tag class="tag-red">{{ i18n.t('ApInvoiceFormPdf.notPaid') }}</a-tag>
                        </span>
                        <span v-else-if="record.br_flag === 1">
                            <a-tag class="tag-orange">{{ i18n.t('ApInvoiceFormPdf.partialPaid') }}</a-tag>
                        </span>
                        <span v-else-if="record.br_flag === 2">
                            <a-tag class="tag-green">{{ i18n.t('ApInvoiceFormPdf.paid') }}</a-tag>
                        </span>
                        <span v-else-if="record.br_flag === 3">
                            <a-tag class="tag-gray">{{ i18n.t('ApInvoiceFormPdf.reversed') }}</a-tag>
                        </span>
                    </template>
                </a-table-column>
                <!-- Due date -->
                <!-- <a-table-column
                    align="center"
                    :title="i18n.t('bkArInvoice.dueDate')"
                    data-index="invoiceDueDate"
                    width="120px"
                    v-if="customizeTable.includes('dueDate') || isCustomize"
                /> -->
                <!-- operation -->
                <a-table-column
                    v-if="arIntegration !== 1"
                    :custom-cell="customRow"
                    align="center"
                    :title="i18n.t('bkArInvoice.operation')"
                    key="operation" fixed="right" width="100px" :ellipsis="true">
                    <template #default="{ record }">
                        <span>
                            <a-button title="Copy Invoice" class="btn-txt" type="link" :disabled="isCustomize"
                                @click="copyInvoice(record, $event)">
                                <copy-outlined />
                            </a-button>
                            <a-divider type="vertical" />
                            <!--    :disabled="record.send_engine_status !== 1 || !record.bill_to_email"-->
                            <a-button title="Send Bill" class="btn-txt" type="link"
                                :disabled="record.br_flag === 3 || record.bill_to_email === ''"
                                @click="sendInvoice(record, $event)">
                                <mail-outlined />
                            </a-button>
                        </span>
                    </template>
                </a-table-column>
                <a-table-column :custom-cell="customRow" align="center" :title="i18n.t('bkArInvoice.uuid')"
                    data-index="xml_uuid" :sorter="false" width="150px"
                    v-if="arIntegration === 1 && (customizeTable.includes('uuid') || isCustomize)" :ellipsis="true" />
                    <a-table-column
                    v-if="arIntegration === 1"
                    :custom-cell="customRow"
                    align="center"
                    :title="i18n.t('bkArInvoice.operation')"
                    key="operation" fixed="right" width="100px" :ellipsis="true">
                    <template #default="{ record }">
                        <span>
                            <a-button title="Send Bill" class="btn-txt" type="link"
                                :disabled="record.bill_to_email === ''"
                                @click="sendInvoiceForIntegration(record, $event)">
                                <mail-outlined />
                            </a-button>
                        </span>
                    </template>
                </a-table-column>
            </a-table>
            <div class="pagination-wrap">
                <a-pagination v-model:current="pageQuery.currentPageNumber" v-model:page-size="pageQuery.page_size"
                    :disabled="tableLoading || isCustomize" :hideOnSinglePage="false" :showSizeChanger="true"
                    :total="totalNumber" @change="changePage" />
                <span
                    >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                    {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
                >
            </div>
        </div>
    </div>
    <a-modal :title="i18n.t('columns.modalTitle')" v-model:visible="showColumns" :footer="null" destroyOnClose
        :closeable="true" :width="480" :wrapClassName="'modal-wrap'">
        <custom-columns :defaultTable="defaultTable" :customizeTable="customizeTable" prefix="bkArInvoice"
            @dismiss="dismissColumns" @save="saveColumns"></custom-columns>
    </a-modal>
    <a-modal :title="readonlyMode ? i18n.t('bkArInvoice.readonly') : i18n.t('bkArInvoice.create')"
        v-model:visible="arShow" :footer="null" destroyOnClose :closeable="true" :width="1000"
        :dialogStyle="{ top: '10px' }" :bodyStyle="{ padding: '10px 24px 24px' }">
        <ar-invoice-component-m-x v-if="arIntegration === 1"
            :invoice-id="current.id" :current-invoice="current" :readonly-mode="readonlyMode"
            :operation-mode="''" @dismiss="dismiss">
        </ar-invoice-component-m-x>
        <ar-invoice-component v-if="arIntegration !== 1"
            :invoice-id="current.id" :current-invoice="current" :readonly-mode="readonlyMode"
            :operation-mode="''" @dismiss="dismiss">
        </ar-invoice-component>
    </a-modal>

    <a-modal title="Bill To Email" v-model:visible="billToEmailData.emailShow" destroyOnClose :closeable="true"
        :width="500" :dialogStyle="{ top: '10px' }" :bodyStyle="{ padding: '10px 24px 24px' }" :onOk="saveBillToEmail">
        <div>
            <a-input v-model:value="billToEmailData.billToEmail" placeholder="please input email" addonBefore="Email" />
        </div>
    </a-modal>

    <!--    <a-modal-->
    <!--        :title="readonlyMode ? i18n.t('bkApInvoice.readonly') : i18n.t('bkApInvoice.create')"-->
    <!--        v-model:visible="apShow"-->
    <!--        :footer="null"-->
    <!--        destroyOnClose-->
    <!--        :closeable="true"-->
    <!--        :width="1000"-->
    <!--        :dialogStyle="{top: '10px'}"-->
    <!--        :bodyStyle="{padding: '10px 24px 24px'}"-->
    <!--    >-->
    <!--        <ap-invoice-component :current-invoice="current" :readonly-mode="readonlyMode" @dismiss="dismiss">-->
    <!--        </ap-invoice-component>-->
    <!--    </a-modal>-->
</template>
<style lang="scss" scoped>
.no-scroll {
    overflow-y: hidden;
}

.history-page-wrap {
    border-radius: 10px;
    background-color: #fff;

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 8px 0;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 60px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button+.search-button {
                min-width: 60px;
            }

            .popover-wrap:deep(.ant-popover-placement-bottom) {
                width: 432px;
            }
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;
            margin-left: 24px;
        }
    }

    .history-page-content {
        padding: 12px 20px;

        .main-head {
            display: flex;
            justify-content: space-between;
        }

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
}

.search-input-form {
    display: flex;
    flex-direction: column;
    align-items: end;

    .search-input-group {
        display: flex;

        .ant-form-item {
            margin-bottom: 12px;
        }

        :deep(.ant-form-item-label) {
            width: 55px;
        }

        .ant-form-item+.ant-form-item :deep(.ant-form-item-label) {
            width: 35px;
        }
    }
}

.br-icon {
    width: 20px;
}

:deep(.custom-row-bg) {
    background-color: #fff4dd; // XML验证失败背景颜色
}
</style>
