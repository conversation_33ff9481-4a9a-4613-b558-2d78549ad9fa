<!-- @format -->

<script lang="ts" setup>
/** @format */

import {ref, onMounted, reactive, computed, toRef, watch} from 'vue'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import axios from 'axios'
import {useStore} from 'vuex'
import {Empty} from 'ant-design-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import {EnterOutlined} from '@ant-design/icons-vue'
import {UserCompany} from '@/lib/storage'
import * as _ from 'lodash'

const store = useStore()
const userCompany: any = UserCompany.get() || []
const i18n: Composer = i18nInstance.global
const searchForm = reactive({
    message_ext0: '',
})
const tableLoading = ref(false)
const currentPageNumber = ref(1)
const pageSize = ref(10)
const faqList: any = ref([] as [])
const enterId = ref('')
const enterArrow = ref(false)
const nullData = ref(false)
const emptyImage = ref(Empty.PRESENTED_IMAGE_SIMPLE)
const pageQuery = reactive({
    skip: 0,
    limit: 10,
})
// mapActions
const fetchFaqList = (payload: any) => store.dispatch('HelpStore/getFaqList', payload)
// computed
const totalNumber = computed(() => store.state.HelpStore.totalNumber)

const changePage = () => {
    console.log('changepage')
    updateData()
}

const inputChange = (e: any) => {
    console.log('inputchange', e.target.value)
    if (e.target.value == '') {
        updateData()
    }
}
const search = async () => {
    currentPageNumber.value = 1
    const search = {
        message_ext0: searchForm.message_ext0,
        $limit: currentPageNumber.value * pageSize.value,
        $skip: (currentPageNumber.value - 1) * pageSize.value,
    }
    const response = await fetchFaqList(search)
    faqList.value = _.cloneDeep(response.data.data)
    if (faqList.value.length == 0) {
        nullData.value = true
    } else {
        nullData.value = false
    }
}

const handlerArrow = (id: any) => {
    enterId.value = id
    enterArrow.value = !enterArrow.value
}
//更新页面
const updateData = async () => {
    const query = {
        ...searchForm,
        $limit: currentPageNumber.value * pageSize.value,
        $skip: (currentPageNumber.value - 1) * pageSize.value,
    }
    try {
        const response = await fetchFaqList(query)
        faqList.value = _.cloneDeep(response.data.data)
        if (faqList.value.length == 0) {
            nullData.value = true
        } else {
            nullData.value = false
        }
    } catch (error) {
        console.log(error)
    }
}
// const fetchJsonDataList = () => {
//   return store.dispatch('FaqStore/fetchJsonDataList', any)
// }
// const faqsList = computed(() => store.state.FaqStore.JsonDataList)
// console.log('faqlist---', faqsList)

onMounted(async () => {
    await updateData()
})
</script>
<template>
    <div class="page-container-faq">
        <div class="faq-page-header">
            <div class="search-group-wrap">
                <a-input
                    v-model:value="searchForm.message_ext0"
                    :placeholder="i18n.t('update.searchByFaq')"
                    :disabled="tableLoading"
                    class="search-input"
                    @pressEnter="search"
                    @change="inputChange"
                >
                    <template #suffix>
                        <svg-icon name="icon_search" @click="search"></svg-icon>
                    </template>
                </a-input>
            </div>
        </div>

        <div class="faq-page-body">
            <div class="faq-item" v-for="item in faqList" :key="item.id">
                <div class="faq-item-header">
                    <div>
                        <svg-icon name="icon_question"></svg-icon>
                        {{ item.message }}？
                    </div>
                    <!-- <div @click="handlerArrow(item.id)" v-if="item.context.length > 224"> -->
                    <div @click="handlerArrow(item.id)">
                        <svg-icon :name="enterArrow && enterId == item.id ? 'icon_dropUp' : 'icon_dropdown'"></svg-icon>
                    </div>
                </div>
                <div
                    :class="enterArrow && enterId == item.id ? 'faq-item-footer-context2' : 'faq-item-footer-context1'"
                >
                    {{ item.message_ext0 }}
                </div>
            </div>
            <a-empty :image="emptyImage" v-if="nullData" class="faq-default" />
        </div>
        <div class="pagination-wrap">
            <a-pagination
                v-model:current="currentPageNumber"
                v-model:page-size="pageSize"
                :disabled="tableLoading"
                :hideOnSinglePage="false"
                :showSizeChanger="true"
                :total="totalNumber"
                @change="changePage"
            />
            <span
                >{{ i18n.t('bkApInvoice.total') }} {{ totalNumber }}
                {{ totalNumber > 1 ? i18n.t('update.items') : i18n.t('update.item') }}</span
            >
        </div>
    </div>
</template>

<style lang="scss" scoped>
/** @format */

.page-container-faq {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 12px;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    .pagination-wrap {
        display: flex;
        margin-top: 12px;
        justify-content: flex-end;
        align-items: center;
        border-top: 1px solid #e2e2e2;
        padding: 20px;

        span {
            font-size: 12px;
            margin-left: 8px;
            line-height: 16px;
            color: #8c8c8c;
        }
    }
}
.faq-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e2ea;
    padding: 0 20px;

    .search-group-wrap {
        display: flex;
        padding: 24px 0;

        .search-input {
            width: 400px;
            border-radius: 4px;
            color: #676d7c;
        }
    }
}
.faq-default {
    padding: 10% 0;
}
.faq-page-body {
    overflow: hidden;
    .faq-item {
        padding: 10px 0px;
        border-bottom: 1px solid #e3e3e3;
        margin: 0 20px;

        .faq-item-header {
            font-weight: bold;
            font-size: xx-large;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .faq-item-footer-context1 {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            word-break: normal;
        }
        .faq-item-footer-context2 {
            overflow: hidden;
            white-space: pre-wrap;
            text-overflow: ellipsis;
            word-break: break-word;
        }
    }
}
</style>
