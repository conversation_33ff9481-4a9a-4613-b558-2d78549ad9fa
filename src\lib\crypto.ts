/** @format */

import CryptoJS from 'crypto-js'

const key = CryptoJS.enc.Utf8.parse('as-to-js')

export const encrypt = (encryptData: string) => {
    const srcs = CryptoJS.enc.Utf8.parse(encryptData)
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    })
    return encrypted.toString()
}

export const decrypt = (encryptData: string) => {
    const decrypt = CryptoJS.AES.decrypt(encryptData, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    })
    return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}
