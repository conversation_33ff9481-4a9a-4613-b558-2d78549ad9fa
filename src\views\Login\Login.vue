<!-- @format -->

<script lang="ts" setup>
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {reactive, ref, onMounted, onBeforeMount, computed} from 'vue'
import {useRouter} from 'vue-router'
import {decrypt, encrypt} from '@/lib/crypto'
import {
    TokenPGO,
    TokenV1,
    UserCompany,
    BookkeepingMenu,
    KycMenu,
    FirstListMenu,
    FlatMenuListObj,
    UserInfo,
    Language,
    CurrentOpenKeys,
    CurrentSidebarItem,
    Org_Id,
    RemoteLogin,
    LocalCurrency,
    FinancialYear,
    LoginTimeout,
    ApIntegration,
    ArIntegration,
    FtsIntegration,
    PaymentIntegration,
    ReconciliationIntegration,
    ReceivableIntegration,
    SapPayableInternalOrder,
    SapPayableProfitCenter,
    SapPayableGl,
    SapPayableWbs,
    SapPayableCostCenter,
} from '@/lib/storage'

import {message, Modal, type FormInstance, type SelectProps} from 'ant-design-vue'
import {UserOutlined, LockOutlined} from '@ant-design/icons-vue'
import {GetKycRSA, LoginPayroll, LoginPGO, LoginV1, MenuList, GETKycMenu, GetKycProductList} from '@/api'
import {ShowTutorial} from '@/lib/storage'
import axios from 'axios'
import moment from 'moment'
import {useStore} from 'vuex'
import GoogleAuthInput from '@/components/bookkeepingComponents/CommonComponents/GoogleAuthInput.vue'
import lodash from 'lodash'
const store = useStore()

const i18n: Composer = i18nInstance.global
const router = useRouter()
const currentUser = ref({} as any)
const formRef = ref<FormInstance>()
const loginForm = reactive({userName: '', passWord: ''})
const siteList = ref<SelectProps['options']>([
    {
        value: '0',
        label: 'Bookkeeping',
    },
    {
        value: '1',
        label: 'Payroll',
    },
    {
        value: '2',
        label: 'KYC',
    },
])
const state = reactive({
    msgDiv: '',
    loading: false,
    hidePage: false,
})
const googleAuth = ref(false)
const googleAuthData = ref({})
const remoteId = ref('0')
const loginRules = reactive({
    userName: [{required: true, message: i18n.t('login.msgInputUsername'), trigger: 'blur'}],
    passWord: [{required: true, message: i18n.t('login.msgInputPassword'), trigger: 'blur'}],
})

// const rolePermissionMapping = computed(() => store.getters.rolePermissionMapping)
const getFirstPage = (kycMenu: any) => store.dispatch('getFirstPage', kycMenu)

const dismissGoogleAuth = () => {
    googleAuth.value = false
}

const afterGoogleAuth = async (success: boolean) => {
    if (success) {
        await getMenuList()
        googleAuth.value = false
    } else {
        googleAuth.value = false
        userLogout()
    }
}
const onSubmit = async () => {
    try {
        state.loading = true
        if (remoteId.value === '1') {
            LoginPayroll({account: loginForm.userName, password: loginForm.passWord, strategy: 'local'})
                .then(async (res: any) => {
                    if (res.data.accessToken !== null) {
                        const payRollUrl = 'http://payroll-dev.inossemcanada.com/#/loginwithtoken?accessToken='
                        window.location.href = `${payRollUrl}${res.data.accessToken}&user=${JSON.stringify(
                            res.data.user,
                        )}`
                    } else {
                        message.error({content: 'Invalid login.'})
                    }
                })
                .catch((err: any) => {
                    if (err.message.includes('code 401') || err.message.includes('code 500')) {
                        message.error({content: 'Invalid login.'})
                    }
                })
            state.loading = false
            return
        } else if (remoteId.value === '2') {
            GetKycRSA()
                .then(async (res: any) => {
                    if (res.data.code === '0') {
                        const kycRSAUrl = 'http://20.104.192.187/loginFromThirdParties?userAccount='
                        window.location.href = `${kycRSAUrl}${loginForm.userName}&token=${loginForm.passWord}`
                    } else {
                        message.error({content: 'Invalid login.'})
                    }
                })
                .catch((err: any) => {
                    console.log('error', err)
                    message.error({content: 'Invalid login.'})
                })
            state.loading = false
            return
        } else {
            const para = {
                strategy: 'local',
                account: loginForm.userName,
                password: loginForm.passWord,
            }
            LoginPGO().then((res: any) => {
                if (res.status === 200) {
                    TokenPGO.set(res.data?.data?.token || '')
                }
            })
            LoginV1(para)
                .then(async (res: any) => {
                    if (res.status === 200 || res.status === 201) {
                        const {category} = res.data.user

                        if (category === 'app') {
                            popupAppUser()
                            return
                        }

                        persistUserLogin(res.data)

                        BookkeepingMenu.del()
                        KycMenu.del()
                        FirstListMenu.del()
                        FlatMenuListObj.del()
                        if (res.data.user.google_secret) {
                            openGoogleAuthModal(res.data)
                        } else {
                            await getMenuList()
                        }
                    } else {
                        message.error({content: res.data.msg})
                    }

                    ShowTutorial.set(true)
                })
                .catch(err => {
                    //  state.msgDiv = err.response.status === 401 ? 'Incorrect user name or password' : '服务器异常！'
                    state.msgDiv = err.response.data.message
                    console.log(err)
                })
        }
    } catch (error) {
        message.error({content: 'Invalid login.'})
    } finally {
        state.loading = false
    }
}

const persistUserLogin = (resData: any) => {
    TokenV1.set(resData.accessToken)
    // const {roles} = resData.user
    const {company, ...userInfo} = resData.user

    if (company && company.length > 0) {
        UserCompany.set(company)
        ApIntegration.set(company[0].sap_payable_integration)
        ArIntegration.set(company[0].sap_receivable_integration)
        FtsIntegration.set(company[0].sap_fts_integration)
        PaymentIntegration.set(company[0].sap_paymente_integration)
        ReconciliationIntegration.set(company[0].sap_reconciliation_integration)
        ReceivableIntegration.set(company[0].sap_receivable_integration)
        SapPayableInternalOrder.set(company[0].sap_payable_internal_order)
        SapPayableWbs.set(company[0].sap_payable_wbs)
        SapPayableProfitCenter.set(company[0].sap_payable_profit_center)
        SapPayableCostCenter.set(company[0].sap_payable_cost_center)
        SapPayableGl.set(company[0].sap_payable_gl)
        // if (roles !== '9995' && roles !== '1') {
        //     popupEmptyCompany()
        //     return
        // }
    } else {
        // if (roles !== '9995' && roles !== '1') {
        //     popupEmptyCompany()
        //     return
        // }
        UserCompany.set([{code: '0', id: 0}])
    }
    UserInfo.set({...userInfo})
    currentUser.value = {...userInfo}
}

const popupEmptyCompany = () => {
    Modal.info({
        title: 'No Company Found',
        content: 'Please contact Admin to add company for you',
        onOk() {
            userLogout()
        },
    })
}

const popupAppUser = () => {
    Modal.info({
        title: 'Note',
        content: 'App account can not login, please use web account to login.',
        onOk() {
            userLogout()
        },
    })
}

const getMenuList = async () => {
    // const firstListMenu: any[] = []
    const bookkeepingMenu: any[] = [] // 簿记
    let apiUrl = '/menuList.json'

    // if (currentUser.value.account === '3000-test') {
    //     apiUrl = '/menuListWithDashboard.json'
    // }
    if (
        currentUser.value.account === 'kycadmin' ||
        currentUser.value.account === '<EMAIL>' ||
        currentUser.value.account === '<EMAIL>' ||
        currentUser.value.account === '<EMAIL>'
    ) {
        apiUrl = '/menuListWithTask.json'
    }
    //api获取一级菜单 更改成请求本地json数据
    axios
        .get(apiUrl)
        .then(res => {
            console.log('json res data ---', res.data.data)

            GETKycMenu({account: currentUser.value.account}).then(async menuRes => {
                let kycMenus: any = []
                console.log('menuRes:', menuRes)
                if (menuRes?.data?.body?.BK) {
                    kycMenus = [...menuRes?.data?.body?.BK]
                }

                const userHiddenMenu = currentUser.value.hidden_menu || []

                //获取一级菜单
                res.data.data.forEach((item: any) => {
                    if (
                        // bookkeeping 簿记获取菜单
                        item.menuUrl == '/bookkeeping' &&
                        item.childMenus.length > 0
                    ) {
                        let hiddenMenuItemIdList = ['94', '98', '104', '105'] // add 94 for AP later.
                        hiddenMenuItemIdList = [...hiddenMenuItemIdList, ...userHiddenMenu]
                        // const allowed = rolePermissions.admit
                        const allowed = kycMenus
                        item.childMenus.forEach((itemChild: any) => {
                            if (itemChild.childMenus && itemChild.childMenus.length > 0) {
                                itemChild.childMenus.forEach((item: any) => {
                                    if (hiddenMenuItemIdList.includes(item.id)) {
                                        return (item.hidden = 'hidden')
                                    }
                                    item.hidden = 'show'
                                })
                            }
                            // TODO:: start temporary for remove/add menu items
                            // remove connect under BR
                            if (itemChild.kycName === '/BankReconciliation') {
                                itemChild.childMenus = itemChild.childMenus.filter((x: any) => x.id !== '83')
                            }
                            // add connectivities under settings
                            if (itemChild.kycName === '/Setting') {
                                const lastItem = JSON.parse(JSON.stringify(itemChild.childMenus.slice(-1).pop()))
                                if (lastItem.id !== '999') {
                                    lastItem.id = '999'
                                    lastItem.menuName = 'connectivities'
                                    lastItem.menuNameEn = 'Connectivities'
                                    lastItem.menuUrl = '/bookkeeping/common/connectivities'
                                    lastItem.positionSort = '9'
                                    itemChild.childMenus.push(lastItem)
                                }
                            }
                            // TODO:: end temporary for Connectivities
                            if (allowed.includes(itemChild.kycName)) {
                                bookkeepingMenu.push(itemChild)
                            }
                        })
                        const settingMenu = lodash.find(bookkeepingMenu, ['menuName', 'setting'])
                        if (settingMenu) {
                            settingMenu.childMenus = lodash.reject(settingMenu.childMenus, [
                                'menuName',
                                'productService',
                            ])
                        }

                        BookkeepingMenu.set(bookkeepingMenu)
                        KycMenu.set(bookkeepingMenu)
                    }

                    // 临时添加 创建公司及账号页面。 删除 flag = 'company-account'
                    // res.data.data[1].childMenus.push(TaxData[0])
                    // res.data.data[1].childMenus.push(TaxData[1])
                })

                let expire = false
                const companyList: any = UserCompany.get()
                if (companyList.length > 0) {
                    const res = await GetKycProductList({companyCode: companyList[0].code})
                    if (res?.data?.body?.BK) {
                        const curDate = moment().format('YYYY-MM-DD')
                        if (
                            res?.data?.body?.BK.expireDate >= curDate ||
                            (res?.data?.body?.BK.freeTrialStart <= curDate &&
                                res?.data?.body?.BK.freeTrialEnd >= curDate)
                        ) {
                            expire = true
                        }
                    }
                }
                if (companyList[0].code === '0001' || companyList[0].code === '0000') {
                    expire = true
                }
                if (!expire) {
                    kycMenus = ['/Subscribe']
                    BookkeepingMenu.set([])
                }
                const firstPage = await getFirstPage(kycMenus)
                LoginTimeout.set(-1)

                const flatMenuListObj = flattenArr(res.data.data)
                FlatMenuListObj.set(flatMenuListObj)
                console.log('=======', firstPage)
                roleBasedNavigate(firstPage)
                // router.push('/bookkeeping/ap/uploadInvoice').then()
            })
        })
        .catch(err => {
            console.log(err)
        })
}

const roleBasedNavigate = (firstPage: any) => {
    if (!firstPage.path || currentUser.value.category === 'app') {
        userLogout()
    } else {
        // 获取保存的域名
        const targetDomain = localStorage.getItem('originalDomain')
        if (targetDomain) {
            // 使用相对路径进行导航
            const baseUrl = `https://${targetDomain}`
            // 设置 API 基础路径
            axios.defaults.baseURL = baseUrl
            // 使用 router.replace 进行导航
            router.replace(firstPage.path)
        } else {
            router.replace(firstPage.path)
        }
    }
}
const userLogout = () => {
    //设置i18n语言(4-5)
    Language.del()
    CurrentOpenKeys.del()
    CurrentSidebarItem.del()
    FlatMenuListObj.del()

    ShowTutorial.del()
    UserCompany.del()
    UserInfo.del()
    BookkeepingMenu.del()
    KycMenu.del()
    FirstListMenu.del()
    TokenV1.del()
    Org_Id.del()
    LocalCurrency.del()
    FinancialYear.del()

    if (RemoteLogin.get() === '1') {
        RemoteLogin.del()
        console.log('====window.location.hostname', window.location.hostname)
        if (window.location.hostname.includes('int.netex.co.jp')) {
            window.location.replace('https://es.int.netex.co.jp/#/login')
        } else {
            window.location.replace(import.meta.env.VITE_PORTAL_URL)
        }

        // todo RemoteLogin
    } else {
        router.replace({name: 'Login'})
    }
}

// flatten菜单Array
const flattenArr = (arr: {menuUrl: any; id: any; parentId: any; childMenus: any}[]) => {
    let result = {}
    arr.forEach((item: {menuUrl: any; id: any; parentId: any; childMenus: any}) => {
        const {menuUrl, id, parentId, childMenus} = item
        result = {
            ...result,
            ...{
                [menuUrl]: {
                    id,
                    parentId,
                },
            },
        }

        if (childMenus.length > 0) {
            result = {...result, ...flattenArr(childMenus)}
        }
    })
    return result
}

const keydownEvent = () => {
    document.onkeydown = async (e: any) => {
        if (e.defaultPrevented) {
            return
        }
        const body = document.getElementsByTagName('body')[0]

        if (e.keyCode === 13 && e.target.baseURI.match('/') && e.target === body) {
            try {
                if (await formRef.value?.validateFields()) {
                    onSubmit()
                }
            } catch (errorInfo) {
                console.log('Failed:', errorInfo)
            }
        }
    }
}

onBeforeMount(async () => {
    const curTimestamp = parseInt(String(new Date().getTime() / 1000))
    try {
        if (window.location.href.indexOf('uuid=') > 0) {
            state.hidePage = true
            const uuidList = window.location.href.split('uuid=')
            if (uuidList.length > 1) {
                const {
                    account,
                    password,
                    strategy,
                    timestamp,
                    mfaCode,
                    mfaFrom = 'Frontend',
                } = JSON.parse(decrypt(uuidList[1]))
                if (account && password && strategy && timestamp && curTimestamp - timestamp < 20) {
                    // 保存原始域名信息
                    const currentDomain = window.location.hostname
                    console.log('=====currentDomain', currentDomain)
                    if (currentDomain.includes('.netex.co.jp')) {
                        localStorage.setItem('originalDomain', 'bookkeeping.int.netex.co.jp')
                    } else {
                        localStorage.setItem('originalDomain', 'bookkeeping.inossem.com')
                    }

                    LoginPGO().then((res: any) => {
                        if (res.status === 200) {
                            TokenPGO.set(res.data?.data?.token || '')
                        }
                    })

                    console.log('bk login 483:', {
                        strategy,
                        account,
                        password,
                        mfaCode,
                        mfaFrom,
                    })
                    LoginV1({
                        strategy,
                        account,
                        password,
                        mfaCode,
                        mfaFrom,
                    })
                        .then(async (res: any) => {
                            if (res.status === 200 || res.status === 201) {
                                const {category} = res.data.user

                                if (category === 'app') {
                                    popupAppUser()
                                    return
                                }

                                persistUserLogin(res.data)

                                BookkeepingMenu.del()
                                KycMenu.del()
                                FirstListMenu.del()
                                FlatMenuListObj.del()
                                RemoteLogin.set('1')

                                if (res.data.user.google_secret) {
                                    openGoogleAuthModal(res.data)
                                } else {
                                    await getMenuList()
                                }
                            } else {
                                message.error({content: res.data.msg})
                            }

                            ShowTutorial.set(true)
                        })
                        .catch(err => {
                            state.msgDiv = err.response.data.message
                            console.log(err)
                        })
                }
            }
        } else {
            if (process.env.NODE_ENV != 'development') {
                state.hidePage = true
                window.location.replace(import.meta.env.VITE_PORTAL_URL)
            }
            // console.log("import.meta.env.VITE_USER_NODE_ENV ",import.meta.env.VITE_USER_NODE_ENV )
            // if (typeof import.meta.env.VITE_USER_NODE_ENV === "undefined"){
            //     state.hidePage = false
            // } else if(import.meta.env.VITE_USER_NODE_ENV != "development"){
            //     state.hidePage = true
            //     window.location.replace(import.meta.env.VITE_PORTAL_URL);
            // }
        }
    } catch (error) {
        console.log(error)
    }
})

onMounted(() => {
    keydownEvent()
})

const openGoogleAuthModal = (value: any) => {
    googleAuthData.value = value
    googleAuth.value = true
}
</script>

<template>
    <div id="main-container" v-if="!state.hidePage">
        <div class="logo-block">
            <div class="logo">
                <img id="logo-image" src="@/assets/image/logo/companyNtLogo.png" />
            </div>
        </div>
        <div id="main-container-content">
            <section class="container-left"></section>
            <section class="container-middle">
                <div class="login-form-container">
                    <h1 class="title">{{ i18n.t('login.title') }}</h1>
                    <a-form
                        ref="formRef"
                        :rules="loginRules"
                        :model="loginForm"
                        class="form"
                        @finish="onSubmit"
                        autocomplete="off"
                    >
                        <a-form-item>
                            <a-select size="large" v-model:value="remoteId" :options="siteList"></a-select>
                        </a-form-item>
                        <a-form-item name="userName">
                            <a-input
                                data-cy="login_username"
                                size="large"
                                v-model:value="loginForm.userName"
                                :placeholder="i18n.t('login.username')"
                            >
                                <template #prefix>
                                    <user-outlined />
                                </template>
                            </a-input>
                        </a-form-item>
                        <a-form-item name="passWord">
                            <a-input-password
                                data-cy="login_password"
                                size="large"
                                v-model:value="loginForm.passWord"
                                :placeholder="i18n.t('login.password')"
                                allowClear
                            >
                                <template #prefix>
                                    <lock-outlined />
                                </template>
                            </a-input-password>
                        </a-form-item>
                        <a-form-item>
                            <a-button
                                data-cy="login_submit"
                                size="large"
                                type="primary"
                                class="login-form-btn"
                                html-type="submit"
                                :loading="state.loading"
                            >
                                {{ i18n.t('login.logIn') }}
                            </a-button>
                            <div v-if="state.msgDiv" class="server-exception-text">
                                <span id="msgDiv">{{ state.msgDiv }}</span>
                            </div>
                        </a-form-item>
                    </a-form>
                </div>
            </section>
            <section class="container-right"></section>
            <footer>
                <p class="copyright-txt">Created by Canada NT - Inossem Times. &copy; 2020. All Rights Reserved.</p>
            </footer>
        </div>

        <a-modal
            :title="i18n.t('login.gQr')"
            v-model:visible="googleAuth"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="440"
            :wrapClassName="'modal-wrap'"
        >
            <google-auth-input
                :initialData="googleAuthData"
                @dismiss="dismissGoogleAuth"
                @after-auth="afterGoogleAuth"
            ></google-auth-input>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
/** @format */

#main-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;

    .logo-block {
        background-color: #1a94d0;
        height: 74px;
        padding-left: 34px;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        #logo-image {
            width: auto;
            height: 46px;
        }
    }
}

#main-container-content {
    height: 100%;
    display: grid;
    grid-template-columns: 1.25fr 2fr 1.25fr;
    grid-template-rows: 1fr auto;
    grid-template-areas:
        'main-left main-middle main-right'
        'footer footer footer';
    // grid-template-rows: 1fr 1fr;
    // background: linear-gradient(135deg, #f5f7fa, #dce9fb, #c3dafd, #aacbfe, #8fbdfe, #73aeff, #509fff, #0e91ff);
    background: linear-gradient(
        0deg,
        #ffffff,
        #ebf3fa,
        #d7e6f5,
        #c3daf0,
        #aeceea,
        #98c2e5,
        #81b6e0,
        #68abdb,
        #4a9fd5,
        #1a94d0
    );
    // background-color: #ecf0f3;

    section.container-left {
        grid-area: main-left;
        background: url('@/assets/image/login/bg-left-business_medium.svg') center center no-repeat;
        background-size: 60%;
    }

    section.container-right {
        grid-area: main-right;
        background: url('@/assets/image/login/bg-right-done_medium.svg') center center no-repeat;
        background-size: 60%;
    }

    section.container-middle {
        grid-area: main-middle;
        align-self: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url('@/assets/image/login/bg-middle-walker_medium.svg') center center no-repeat;
        background-size: 100% 100%;

        .login-form-container {
            position: relative;
            width: 360px;
            height: 460px;
            padding: 60px 35px 70px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #ecf0f3;
            border-radius: 10px;
            justify-content: space-between;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);

            .form {
                width: 100%;

                .server-exception-text {
                    width: 100%;
                    text-align: left;

                    #msgDiv {
                        font-size: 15px;
                        color: red;
                    }
                }
            }

            .title,
            .subtitle {
                padding-top: 4px;
                padding-bottom: 6px;
                text-transform: capitalize;
                text-align: center;
            }

            .title {
                position: relative;
                //  font-family: 'Open Sans', sans-serif;
                font-size: 28px;

                &::before {
                    content: '';
                    position: absolute;
                    background-color: #004fc5;
                    height: 2px;
                    width: 100%;
                    left: 0;
                    bottom: 0;
                }
            }

            .subtitle {
                //  font-family: 'nunito', sans-serif;
                font-size: 16px !important;
                color: #777;
            }

            .copyright-txt {
                position: absolute;
                bottom: 0;
            }

            .login-form-btn {
                margin-top: 14px;
                font-size: 16px;
                letter-spacing: 0.5px;
                width: 100%;
            }
        }
    }

    footer {
        display: grid;
        grid-area: footer;
        //  font-family: 'DIN Regular', sans-serif;
        align-self: end;
        background: transparent;

        p {
            color: #333637;
            padding: 8px 12px 16px 12px;
            margin: auto;
        }
    }
}
</style>
