<!-- @format -->

<template>
    <div class="page-container-upload_result">
        <a-alert message="resultTitle" type="error" :closable="false"></a-alert>
        <div class="list-container">
            <div class="list-item" v-for="(item, index) in failedResultList" :key="index">
                {{ index + 1 }}. {{ item.fileName }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {computed} from 'vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
const i18n: Composer = i18nInstance.global

const props = defineProps({
    resultList: {
        type: Array,
        required: true,
        default: () => [],
    },
    resultTitle: {
        type: String,
        required: true,
        default: 'File upload failed. Please try again.',
    },
})

const failedResultList = computed(() => props.resultList.filter((item: any) => !item.uploaded) as any[])
</script>

<style lang="scss" scoped>
.page-container-upload_result {
    width: 100%;
    height: 100%;
}

.list-container {
    .list-item {
        padding: 2px 10px 2px 22px;
        color: red;
    }

    .list-item:nth-of-type(1) {
        margin-top: 10px;
    }
}
</style>
