<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import {computed, onBeforeMount, ref} from 'vue'
import currency from 'currency.js'

const store = useStore()

const tableLoading = ref(false)

const fetchTaxRates = () => store.dispatch('TaxCalculationStore/fetchTaxRates')

const taxRatesList = computed(() => store.state.TaxCalculationStore.taxRatesList)
const taxTableData = computed(() => formatTableData(taxRatesList.value))

const updateData = async () => {
    try {
        tableLoading.value = true
        await fetchTaxRates()
    } catch (error) {
        console.log(error)
    } finally {
        tableLoading.value = false
    }
}
const formatTableData = (list: any) => {
    if (!list) {
        return []
    }
    return list.map((item: any) => {
        const {province_name, province_code, gst = '', hst = '', qst = '', pst = '', total = ''} = item
        const applicableSalesTax: string[] = []
        Object.entries(item).forEach(([key, value]) => {
            if (parseFloat(value as string) > 0 && ['gst', 'hst', 'qst', 'pst'].indexOf(key) > -1) {
                applicableSalesTax.push(key.toUpperCase())
            }
        })
        return {
            province_name,
            province_code,
            applicableSalesTax: applicableSalesTax.join(' + '),
            gst: convertStrToPercentageNum(gst) ? convertStrToPercentageNum(gst) + '%' : '',
            hst: convertStrToPercentageNum(hst) ? convertStrToPercentageNum(hst) + '%' : '',
            qst: convertStrToPercentageNum(qst, 3) ? convertStrToPercentageNum(qst, 3) + '%' : '',
            pst: convertStrToPercentageNum(pst) ? convertStrToPercentageNum(pst) + '%' : '',
            total: convertStrToPercentageNum(total, 3) ? convertStrToPercentageNum(total, 3) + '%' : '',
        }
    })
}
const convertStrToPercentageNum = (decimalNumb: any, precision = 0) => {
    const str = decimalNumb
        ? currency(currency(decimalNumb, {precision: precision + 2}).multiply(100), {
              precision,
          })
        : 0
    if (!str) {
        return 0
    }
    return str.value
}
onBeforeMount(async () => {
    await updateData()
})
</script>
<template>
    <div class="tax-calculation-wrap">
        <a-alert
            type="info"
            :closable="false"
            show-icon
            :message="$t('taxCalculation.msgWarning')"
            class="alert-wrap"
        />
        <a-table
            class="tax-calculation-table-wrap"
            :dataSource="taxTableData"
            :loading="tableLoading"
            :pagination="false"
            :row-class-name="(_: any, index: number) => (index % 2 === 1 ? 'table-striped' : null)"
        >
            <a-table-column
                data-index="province_name"
                :title="$t('taxCalculation.provinceName')"
                align="center"
                width="300px"
            >
            </a-table-column>
            <a-table-column
                data-index="province_code"
                :title="$t('taxCalculation.provinceCode')"
                align="center"
                width="120px"
            ></a-table-column>
            <a-table-column
                data-index="applicableSalesTax"
                :title="$t('taxCalculation.applicableSalesTax')"
                align="center"
                width="150px"
            >
            </a-table-column>
            <a-table-column data-index="gst" title="GST" align="center" width="100px"></a-table-column>
            <a-table-column data-index="hst" title="HST" align="center" width="100px"></a-table-column>
            <a-table-column data-index="qst" title="QST" align="center" width="100px"></a-table-column>
            <a-table-column data-index="pst" title="PST" align="center" width="100px"></a-table-column>
            <a-table-column
                data-index="total"
                :title="$t('taxCalculation.total')"
                align="center"
                width="126px"
            ></a-table-column>
        </a-table>
    </div>
</template>
<style lang="scss" scoped>
.tax-calculation-wrap {
    .alert-wrap {
        margin-bottom: 16px;
        display: flex;
        align-items: flex-start;
        :deep(.anticon-info-circle) {
            font-size: 16px;
            margin-top: 3px;
            margin-bottom: 3px;
        }
    }
    .tax-calculation-table-wrap {
        :deep(.ant-table .ant-table-thead .ant-table-cell) {
            background-color: #e0e4ea;
            border-bottom-left-radius: 0px;
            border-bottom-right-radius: 0px;
        }
        :deep(.ant-table .ant-table-tbody .ant-table-cell) {
            height: 52px;
        }
        :deep(.table-striped) td {
            background-color: #f2f4f7;
        }
    }
}
</style>
