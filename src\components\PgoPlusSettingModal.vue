<template>
    <a-modal :visible="visible" @update:visible="$emit('update:visible')" style="width: 80%; height: auto">
        <template #footer>
            <a-button key="format" @click="formatJson">Format</a-button>
            <a-button key="test" @click="handleTest">Test</a-button>
            <a-button key="submit" type="primary" @click="handleOk">Save</a-button>
        </template>
        <a-form :layout="'vertical'" autocomplete="off">
            <a-form-item label="PGO Configuration">
                <a-textarea
                    v-model:value="jsonContent"
                    :rows="20"
                    :placeholder="'请输入 JSON 格式的配置'"
                />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useStore } from 'vuex'
import i18nInstance from '@/locales/i18n'

const i18n = i18nInstance.global
const store = useStore()
const props = defineProps<{
    visible: boolean
    pgoForm: any
    companyCode: string
}>()

const emit = defineEmits(['update:visible', 'save'])

const jsonContent = ref('')

// 监听 pgoForm 变化，更新 jsonContent
watch(() => props.pgoForm, (newVal) => {
    if (newVal) {
        jsonContent.value = JSON.stringify(newVal, null, 2)
    }
}, { immediate: true, deep: true })

const formatJson = () => {
    try {
        const obj = JSON.parse(jsonContent.value)
        jsonContent.value = JSON.stringify(obj, null, 2)
    } catch (e) {
        message.error('Invalid JSON format')
    }
}

const handleTest = async () => {
    try {
        const config = JSON.parse(jsonContent.value)
        const res = await store.dispatch('ApStore/postPgoLoginTest', {
            url: config.auth_url,
            username: config.auth_username,
            password: config.auth_password,
            company_code: props.companyCode,
        })
        
        if (res.status === 200 || res.status === 201) {
            message.success(i18n.t('ApComponents.testGetPgoTokenSuccess'))
        } else {
            message.error(i18n.t('ApComponents.testGetPgoTokenFail'))
        }
    } catch (e) {
        message.error('Invalid JSON format')
    }
}

const handleOk = () => {
    try {
        const config = JSON.parse(jsonContent.value)
        emit('save', config)
        emit('update:visible', false)
    } catch (e) {
        message.error('Invalid JSON format')
    }
}
</script>