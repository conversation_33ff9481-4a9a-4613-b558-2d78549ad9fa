<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import lodash from 'lodash'
import validator from 'validator'
import {computed, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch, onBeforeMount, createVNode} from 'vue'
import {message, type FormInstance, Modal} from 'ant-design-vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useRouter} from 'vue-router'
import {
    ApIntegration,
    FtsIntegration,
    PaymentIntegration,
    ReconciliationIntegration,
    ReceivableIntegration,
    BookkeepingMenu,
    KycMenu,
    UserId,
    Version,
    Password,
    ApplicationNumber,
    InstanceNumber,
    UserCompany,
    UserInfo,
    SapPayableGl,
    SapPayableWbs,
    SapPayableCostCenter,
    SapPayableInternalOrder,
    SapPayableProfitCenter,
} from '@/lib/storage'
import SvgIcon from '@/components/SvgIcon.vue'
import {PlusOutlined} from '@ant-design/icons-vue'
import http from '@/api/requestNew'
import {Org_Id} from '@/lib/storage'
import {any} from 'ramda'
import PgoPlusSettingModal from '@/components/PgoPlusSettingModal.vue'
import ReasonCodeModal from '@/components/ReasonCodeModal.vue'

interface Approver {
    level: number
    type: string
    email_list: string
    creator: string
    company_code: string
    low_code: string
}

const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const org = Org_Id.get()
const activeTab = ref('sap')

const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
store.dispatch('ApStore/fetchInvoicesHistoryList', {company_code: userCompany[0].code})
const totalNumber = computed(() => store.state.ApStore.totalNumber)
const form = reactive({
    applicationNumber: '',
    instanceNumber: '',
    userId: '',
    password: '',
    version: '',
    receivableIntegration: false,
    payableIntegration: false,
    reconciliationIntegration: false,
    paymentIntegration: false,
    ftsIntegration: false,
    invoiceApprovers: [] as Approver[],
    paymentApprovers: [] as Approver[],
    sapPayableGl: false,
    sapPayableWbs: false,
    sapPayableInternalOrder: false,
    sapPayableProfitCenter: false,
    sapPayableCostCenter: false,
    sapPayableGlCode: '',
    sapPayableWbsCode: '',
    sapPayableInternalOrderCode: '',
    sapPayableProfitCenterCode: '',
    sapPayableCostCenterCode: '',
})
const pgoForm = reactive({
    auth_url: '',
    auth_username: '',
    auth_password: '',
    sap_company_code: '',
    bp: '',
    ee: '',
    fi: '',
    ft: '',
    po: '',
    master: '',
    post_fi: '',
    reverse_fi: '',
    reverse_post_fi: '',
    reverse_post_fi_ft: '',
    reverse_post_fi_ee_with_invoice: '',
    reverse_post_fi_ee_with_gl: '',
    po_fi_doc: '',
    reverse_po_fi_doc: '',
})

const isEnableSubmit = ref(false)
const showPgoPlusSetting = ref(false)
const showReasonCode = ref(false)
const syncArLoading = ref(false)
const removeInvoiceApprover = (index: number) => {
    form.invoiceApprovers.splice(index, 1)
    form.invoiceApprovers.forEach((item: any, index: number) => {
        item.item_no = index + 1
    })
}

const removePaymentApprover = (index: number) => {
    form.paymentApprovers.splice(index, 1)
    form.paymentApprovers.forEach((item: any, index: number) => {
        item.item_no = index + 1
    })
}
const addItemInvoice = () => {
    if (!form.invoiceApprovers) {
        form.invoiceApprovers = []
    }

    form.invoiceApprovers.push({
        level: form.invoiceApprovers.length + 1,
        type: 'INVOICE',
        email_list: '',
        creator: userInfo.account,
        company_code: userCompany[0].code,
        low_code: '',
    })
}

const addItemPayment = () => {
    if (!form.paymentApprovers) {
        form.paymentApprovers = []
    }

    form.paymentApprovers.push({
        level: form.paymentApprovers.length + 1,
        type: 'PAYMENT',
        email_list: '',
        creator: userInfo.account,
        company_code: userCompany[0].code,
        low_code: '',
    })
}

const checkAvaliable: any = computed(() => {
    return !(form.applicationNumber && form.instanceNumber && form.userId && form.password && form.version)
})

const disabledSave = computed(() => {
    if (
        form.receivableIntegration ||
        form.payableIntegration ||
        form.paymentIntegration ||
        form.reconciliationIntegration ||
        form.ftsIntegration
    ) {
        if (!form.applicationNumber || !form.instanceNumber || !form.userId || !form.password || !form.version) {
            return true
        }
    }

    return false
})

const integration: any = async (obj: any, value: any) => {
    // console.log('integration:', obj.name, value)
    let bookkeepingMenu: any = BookkeepingMenu.get()
    if (obj.name === 'PaymentIntegration') {
        if (!value) {
            bookkeepingMenu = lodash.reject(bookkeepingMenu, ['menuName', 'payment'])
        } else {
            bookkeepingMenu = KycMenu.get()
        }
    }
    obj.set(value ? 1 : 0)

    const data = {
        sap_receivable_integration: form.receivableIntegration,
        sap_payable_integration: form.payableIntegration,
        sap_paymente_integration: form.paymentIntegration,
        sap_reconciliation_integration: form.reconciliationIntegration,
        sap_fts_integration: form.ftsIntegration,
        sap_app_no: form.applicationNumber,
        sap_ins_no: form.instanceNumber,
        sap_user_id: form.userId,
        sap_password: form.password,
        sap_version: form.version,
    }
    await http.patch(`/users/api/v1/company/${org}`, data)

    userCompany[0].sap_receivable_integration = form.receivableIntegration ? 1 : 0
    userCompany[0].sap_payable_integration = form.payableIntegration ? 1 : 0
    userCompany[0].sap_paymente_integration = form.paymentIntegration ? 1 : 0
    userCompany[0].sap_reconciliation_integration = form.reconciliationIntegration ? 1 : 0
    userCompany[0].sap_fts_integration = form.ftsIntegration ? 1 : 0
    UserCompany.set(userCompany)

    if (
        form.receivableIntegration ||
        form.payableIntegration ||
        form.paymentIntegration ||
        form.reconciliationIntegration ||
        form.ftsIntegration
    ) {
        const settingMenu = lodash.find(bookkeepingMenu, ['menuName', 'setting'])
        settingMenu.childMenus = lodash.reject(settingMenu.childMenus, ['menuName', 'coA'])
        settingMenu.childMenus = lodash.reject(settingMenu.childMenus, ['menuName', 'coAMapping'])
    } else {
        bookkeepingMenu = KycMenu.get()
        bookkeepingMenu = lodash.reject(bookkeepingMenu, ['menuName', 'payment'])
    }
    BookkeepingMenu.set([...bookkeepingMenu])
    updateMenuList(bookkeepingMenu)
}

const updateMenuList = (data: any) => store.commit('updateMenuList', data, {root: true})

const save = async () => {
    const data = {
        companyCode: userCompany[0].code,
        flowList: [] as Approver[],
    }
    form.invoiceApprovers.forEach((item: any) => {
        if (item.email_list) {
            data.flowList.push({
                level: item.level,
                type: 'INVOICE',
                email_list: item.email_list,
                creator: userInfo.account,
                company_code: userCompany[0].code,
                low_code: item.low_code,
            })
        }
    })

    form.paymentApprovers.forEach((item: any) => {
        if (item.email_list) {
            data.flowList.push({
                level: item.level,
                type: 'PAYMENT',
                email_list: item.email_list,
                creator: userInfo.account,
                company_code: userCompany[0].code,
                low_code: item.low_code,
            })
        }
    })

    const res = await http.post('/invoice-statement/api/v1/company-flow/batch', data)
    if (res.status >= 200 && res.status < 300) {
        message.success('Submit invoice and payment approver success')
    } else {
        message.error('Submit invoice and payment approver failed')
    }
}

onMounted(async () => {
    const res = await http.get(`/users/api/v1/company/${org}`)
    if (res.status === 200) {
        form.applicationNumber = res.data.sap_app_no
        form.instanceNumber = res.data.sap_ins_no
        form.version = res.data.sap_version
        form.userId = res.data.sap_user_id
        form.password = res.data.sap_password
        form.receivableIntegration = res.data.sap_receivable_integration
        form.payableIntegration = res.data.sap_payable_integration
        form.paymentIntegration = res.data.sap_paymente_integration
        form.reconciliationIntegration = res.data.sap_reconciliation_integration
        form.ftsIntegration = res.data.sap_ftp_integration
        form.sapPayableGl = res.data.sap_payable_gl
        form.sapPayableWbs = res.data.sap_payable_wbs
        form.sapPayableInternalOrder = res.data.sap_payable_internal_order
        form.sapPayableProfitCenter = res.data.sap_payable_profit_center
        form.sapPayableCostCenter = res.data.sap_payable_cost_center
        form.sapPayableGlCode = res.data.sap_payable_gl_code
        form.sapPayableWbsCode = res.data.sap_payable_wbs_code
        form.sapPayableInternalOrderCode = res.data.sap_payable_internal_order_code
        form.sapPayableProfitCenterCode = res.data.sap_payable_profit_center_code
        form.sapPayableCostCenterCode = res.data.sap_payable_cost_center_code
    }

    const resApprover = await http.get(`/invoice-statement/api/v1/company-flow?company_code=${userCompany[0].code}`)
    if (resApprover.status === 200) {
        resApprover.data.data.forEach((item: any) => {
            if (item.type === 'INVOICE') {
                form.invoiceApprovers.push({
                    level: item.level,
                    email_list: item.email_list,
                    type: 'INVOICE',
                    creator: item.creator,
                    company_code: item.company_code,
                    low_code: item.low_code,
                })
            } else if (item.type === 'PAYMENT') {
                form.paymentApprovers.push({
                    level: item.level,
                    email_list: item.email_list,
                    type: 'PAYMENT',
                    creator: item.creator,
                    company_code: item.company_code,
                    low_code: item.low_code,
                })
            }
        })

        form.invoiceApprovers = lodash.orderBy(form.invoiceApprovers, ['level'], ['asc'])
        form.paymentApprovers = lodash.orderBy(form.paymentApprovers, ['level'], ['asc'])
    }

    const resPgoLogininfo = await store.dispatch('ApStore/getPgoLoginInfo', {company_code: userCompany[0].code})
    if (resPgoLogininfo.status === 200) {
        pgoForm.auth_url = resPgoLogininfo.data?.url || ''
        pgoForm.auth_username = resPgoLogininfo.data?.para.username || ''
        pgoForm.auth_password = resPgoLogininfo.data?.para.password || ''
    }

    const resPgoUrl = await store.dispatch('ApStore/getPgoUrl', {company_code: userCompany[0].code})
    if (resPgoUrl.status === 200 && resPgoUrl.data) {
        // 直接将整个响应数据赋值给pgoForm，而不是逐个字段赋值
        // 这样可以确保所有字段（包括可能的新增字段）都能被正确显示
        Object.assign(pgoForm, resPgoUrl.data)
    }
})

const showPayableIntegrationSetting = ref(false)

const handlePayableSetting = async () => {
    const res = await http.patch(`/users/api/v1/company/${org}`, {
        sap_payable_gl: form.sapPayableGl,
        sap_payable_wbs: form.sapPayableWbs,
        sap_payable_cost_center: form.sapPayableCostCenter,
        sap_payable_internal_order: form.sapPayableInternalOrder,
        sap_payable_profit_center: form.sapPayableProfitCenter,
        sap_payable_gl_code: form.sapPayableGlCode,
        sap_payable_wbs_code: form.sapPayableWbsCode,
        sap_payable_internal_order_code: form.sapPayableInternalOrderCode,
        sap_payable_profit_center_code: form.sapPayableProfitCenterCode,
        sap_payable_cost_center_code: form.sapPayableCostCenterCode,
    })
    if (res.status === 200) {
        SapPayableGl.set(form.sapPayableGl ? 1 : 0)
        SapPayableWbs.set(form.sapPayableWbs ? 1 : 0)
        SapPayableCostCenter.set(form.sapPayableCostCenter ? 1 : 0)
        SapPayableInternalOrder.set(form.sapPayableInternalOrder ? 1 : 0)
        SapPayableProfitCenter.set(form.sapPayableProfitCenter ? 1 : 0)
        userCompany[0].sap_payable_cost_center = form.sapPayableCostCenter ? 1 : 0
        userCompany[0].sap_payable_internal_order = form.sapPayableInternalOrder ? 1 : 0
        userCompany[0].sap_payable_profit_center = form.sapPayableProfitCenter ? 1 : 0
        userCompany[0].sap_payable_gl = form.sapPayableGl ? 1 : 0
        userCompany[0].sap_payable_wbs = form.sapPayableWbs ? 1 : 0
        UserCompany.set(userCompany)
        message.success(i18n.t('ApComponents.success'))
    } else {
        message.error(i18n.t('ApComponents.failed'))
    }
    showPayableIntegrationSetting.value = false
}
const handlePgoPlusSettingSubmit = async (config: any) => {
    // 完全替换pgoForm对象的内容，确保删除的属性不会保留
    // 先清空pgoForm中的所有属性
    Object.keys(pgoForm).forEach(key => {
        delete pgoForm[key]
    })
    // 然后添加config中的所有属性
    Object.assign(pgoForm, config)
    handlePgoPlusSetting()
}

const handleSyncArFromSap = async () => {
    syncArLoading.value = true
    try {
        await store.dispatch('ArStore/syncArFromSap', {
            company_code: userCompany[0].code,
        })
        message.success('Success')
    } catch (error: any) {
        console.log(error)
    } finally {
        syncArLoading.value = false
    }
}

const handlePgoPlusSetting = async () => {
    Modal.confirm({
        title: 'Warning',
        content:
            'Please ensure that the URL is correct and accessible. Failing to verify this may cause data synchronization with SAP to fail or result in errors.\n' +
            'Are you sure you want to save the changes?',
        okText: 'Yes',
        okType: 'danger',
        cancelText: 'No',
        async onOk() {
            const res = await testGetPgoToken()
            if (res) {
                await store.dispatch('ApStore/postPgoLoginInfo', {
                    url: pgoForm.auth_url,
                    username: pgoForm.auth_username,
                    password: pgoForm.auth_password,
                    company_code: userCompany[0].code,
                })

                // 直接传递pgoForm对象，不使用展开运算符，避免创建新对象
                await store.dispatch('ApStore/postPgoUrl', {
                    company_code: userCompany[0].code,
                    data: pgoForm
                })
                await store.dispatch('ApStore/postPgoConfig', {
                    company_code: userCompany[0].code,
                    account:userInfo.account,
                    data: pgoForm
                })
            }
        },
        onCancel() {},
    })
}
const testGetPgoToken = async () => {
    const res = await store.dispatch('ApStore/postPgoLoginTest', {
        url: pgoForm.auth_url,
        username: pgoForm.auth_username,
        password: pgoForm.auth_password,
        company_code: userCompany[0].code,
    })
    if (res.status === 200 || res.status === 201) {
        message.success(i18n.t('ApComponents.testGetPgoTokenSuccess'))
        return true
    } else {
        message.error(i18n.t('ApComponents.testGetPgoTokenFail'))
        return false
    }
}

const handleReasonCodeSubmit = async (config: any) => {
    message.success('Reason code saved successfully')
}
</script>
<template>
    <div class="page-container-gl_list">
        <div class="content-box-import-form">       
                    <div class="import-form-wrap">
                        <a-form :layout="'Horizontal'" autocomplete="off" :label-col="{ span: 8 }">
                            <div class="import-form-block">
                        <a-row :gutter="24">
                            <a-col :span="12">
                                <h2>{{ i18n.t('connectivity.sap_instance') }}</h2>
                            </a-col>
                            <a-col :span="12">
                                <h2>{{ i18n.t('connectivity.sap_general') }}</h2>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="4"> {{ i18n.t('connectivity.application_number') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="applicationNumber">
                                    <a-input
                                        v-model:value="form.applicationNumber"
                                        @change="integration(ApplicationNumber, form.applicationNumber)"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="2" />
                            <a-col :span="4">{{ i18n.t('connectivity.receivable_integration') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="receivable_integration">
                                    <a-checkbox
                                        v-model:checked="form.receivableIntegration"
                                        :disabled="!['kycadmin', 'ntadmin'].includes(userInfo.account)"
                                        @change="integration(ReceivableIntegration, form.receivableIntegration)"
                                    ></a-checkbox>
                                    <span v-if="form.receivableIntegration" style="padding-left: 20px">
                                        <a-button type="primary" :loading="syncArLoading" @click="handleSyncArFromSap">
                                            Sync
                                        </a-button>
                                    </span>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="4"> {{ i18n.t('connectivity.instance_number') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="instanceNumber">
                                    <a-input
                                        v-model:value="form.instanceNumber"
                                        @change="integration(InstanceNumber, form.instanceNumber)"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="2" />
                            <a-col :span="4"> {{ i18n.t('connectivity.payable_integration') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="payable_integration">
                                    <a-checkbox
                                        v-model:checked="form.payableIntegration"
                                        @change="integration(ApIntegration, form.payableIntegration ? 1 : 0)"
                                    ></a-checkbox>
                                    <span style="padding-left: 20px">
                                        <a-button
                                            :disabled="!form.payableIntegration"
                                            type="primary"
                                            @click="showPayableIntegrationSetting = !showPayableIntegrationSetting"
                                        >
                                            {{ i18n.t('connectivity.sap_setting') }}
                                        </a-button>
                                    </span>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="4"> {{ i18n.t('connectivity.userId') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="userId">
                                    <a-input v-model:value="form.userId" @change="integration(UserId, form.userId)" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="2" />
                            <a-col :span="4"> {{ i18n.t('connectivity.payment_integration') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="payment_integration">
                                    <a-checkbox
                                        v-model:checked="form.paymentIntegration"
                                        @change="integration(PaymentIntegration, form.paymentIntegration)"
                                    ></a-checkbox>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="4"> {{ i18n.t('connectivity.password') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="password">
                                    <a-input-password
                                        v-model:value="form.password"
                                        @change="integration(Password, form.password)"
                                    />
                                </a-form-item>
                            </a-col>
                            <a-col :span="2" />
                            <a-col :span="4"> {{ i18n.t('connectivity.reconciliation_integration') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="reconciliation_integration">
                                    <a-checkbox
                                        v-model:checked="form.reconciliationIntegration"
                                        :disabled="checkAvaliable"
                                        @change="integration(ReconciliationIntegration, form.reconciliationIntegration)"
                                    ></a-checkbox>
                                    <span style="padding-left: 20px">
                                        <a-button type="primary" @click="showReasonCode = true"> Reason Code </a-button>
                                    </span>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="4"> {{ i18n.t('connectivity.version') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="version">
                                    <a-select
                                        :placeholder="i18n.t('connectivity.sourceHolder')"
                                        v-model:value="form.version"
                                        @change="integration(Version, form.version)"
                                    >
                                        <a-select-option value="ECC">ECC</a-select-option>
                                        <a-select-option value="S4HANA">S4HANA</a-select-option>
                                    </a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="2" />
                            <a-col :span="4"> {{ i18n.t('connectivity.fts_integration') }} </a-col>
                            <a-col :span="6">
                                <a-form-item name="fts_integration">
                                    <a-checkbox
                                        v-model:checked="form.ftsIntegration"
                                        :disabled="checkAvaliable"
                                        @change="integration(FtsIntegration, form.ftsIntegration)"
                                    ></a-checkbox>
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24" v-if="['kycadmin', '8001', '8888', 'ntadmin'].includes(userInfo.account)">
                            <a-col :span="4"> Pgo Plus </a-col>
                            <a-col :span="6">
                                <a-form-item name="pgo_plus">
                                    <a-button type="primary" @click="showPgoPlusSetting = true">
                                        {{ i18n.t('SettingConnectivities.pgoPlusSetting') }}</a-button
                                    >
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </div>
                    <div>
                        <h2>{{ i18n.t('connectivity.payable_approval_procedure') }}</h2>

                        <a-table :dataSource="form.invoiceApprovers" :pagination="false">
                            <a-table-column
                                :title="i18n.t('bkAp.itemNo')"
                                data-index="level"
                                width="3%"
                                :ellipsis="true"
                            />
                            <a-table-column
                                :title="i18n.t('SettingConnectivities.approvers')"
                                data-index="email_list"
                                :ellipsis="true"
                                width="15%"
                            >
                                <template #default="{record}">
                                    <a-input
                                        v-model:value="record.email_list"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </template>
                            </a-table-column>
                            <a-table-column
                                :title="i18n.t('SettingConnectivities.lowCode')"
                                data-index="low_code"
                                :ellipsis="true"
                                width="12%"
                            >
                                <template #default="{record}">
                                    <a-input
                                        v-model:value="record.low_code"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </template>
                            </a-table-column>
                            <a-table-column :title="''" key="operation" width="5%">
                                <template #default="{index}">
                                    <span>
                                        <a-button type="link" danger @click="removeInvoiceApprover(index)">
                                            <svg-icon name="icon_delete"></svg-icon>
                                        </a-button>
                                    </span>
                                </template>
                            </a-table-column>
                        </a-table>

                        <div style="text-align: center">
                            <a-button class="invoice-add" type="primary" ghost @click="addItemInvoice">
                                <template #icon><plus-outlined /></template>
                                {{ i18n.t('bkAp.addItemBtnTxtInvoice') }}
                            </a-button>
                        </div>
                    </div>
                    <div style="padding-top: 40px">
                        <h2>{{ i18n.t('connectivity.payment_approval_procedure') }}</h2>

                        <a-table :dataSource="form.paymentApprovers" :pagination="false">
                            <a-table-column
                                :title="i18n.t('bkAp.itemNo')"
                                data-index="level"
                                width="3%"
                                :ellipsis="true"
                            />
                            <a-table-column
                                :title="i18n.t('SettingConnectivities.approvers')"
                                data-index="email_list"
                                :ellipsis="true"
                                width="15%"
                            >
                                <template #default="{record}">
                                    <a-input
                                        v-model:value="record.email_list"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </template>
                            </a-table-column>
                            <a-table-column
                                :title="i18n.t('SettingConnectivities.lowCode')"
                                data-index="low_code"
                                :ellipsis="true"
                                width="12%"
                            >
                                <template #default="{record}">
                                    <a-input
                                        v-model:value="record.low_code"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </template>
                            </a-table-column>
                            <a-table-column :title="''" key="operation" width="5%">
                                <template #default="{index}">
                                    <span>
                                        <a-button type="link" danger @click="removePaymentApprover(index)">
                                            <svg-icon name="icon_delete"></svg-icon>
                                        </a-button>
                                    </span>
                                </template>
                            </a-table-column>
                        </a-table>

                        <div style="text-align: center">
                            <a-button
                                class="invoice-add"
                                type="primary"
                                ghost
                                @click="addItemPayment"
                                :disabled="form.paymentApprovers.length > 5"
                            >
                                <template #icon><plus-outlined /></template>
                                {{ i18n.t('bkAp.addItemBtnTxtPayment') }}
                            </a-button>
                        </div>
                    </div>
                    <div style="text-align: right; margin-top: 20px">
                        <a-button shape="round" type="primary" @click="save">
                            {{ i18n.t('SettingConnectivities.save') }}
                        </a-button>
                    </div>
                </a-form>
            </div>
            <a-modal
                v-model:visible="showPayableIntegrationSetting"
                :ok-text="i18n.t('commonTag.save')"
                @ok="handlePayableSetting"
                style="width: 50%; height: auto"
            >
                <a-form :layout="'Horizontal'" autocomplete="off" :label-col="{span: 8}">
                    <div class="import-form-block">
                        <a-row
                            :gutter="24"
                            style="margin-bottom: 30px; background-color: #f2f4f7; height: 50px; align-items: center"
                        >
                            <a-col :span="3"></a-col>
                            <a-col :span="8">
                                <strong>SAP Cost Object</strong>
                            </a-col>
                            <a-col :span="12">
                                <strong>{{ i18n.t('SettingConnectivities.lowCode') }}</strong>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="1"></a-col>
                            <a-col :span="2">
                                <a-form-item>
                                    <a-checkbox v-model:checked="form.sapPayableGl"></a-checkbox>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <h4 style="padding-top: 4px">{{ i18n.t('gl.glAccount') }}</h4>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item>
                                    <a-input v-model:value="form.sapPayableGlCode" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="1"></a-col>
                            <a-col :span="2">
                                <a-form-item>
                                    <a-checkbox v-model:checked="form.sapPayableWbs"></a-checkbox>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <h4 style="padding-top: 4px">{{ i18n.t('ApComponents.wbs') }}</h4>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item>
                                    <a-input v-model:value="form.sapPayableWbsCode" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="1"></a-col>
                            <a-col :span="2">
                                <a-form-item>
                                    <a-checkbox v-model:checked="form.sapPayableCostCenter"></a-checkbox>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <h4 style="padding-top: 4px">{{ i18n.t('ApComponents.costCenter') }}</h4>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item>
                                    <a-input v-model:value="form.sapPayableCostCenterCode" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="1"></a-col>
                            <a-col :span="2">
                                <a-form-item>
                                    <a-checkbox v-model:checked="form.sapPayableInternalOrder"></a-checkbox>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <h4 style="padding-top: 4px">{{ i18n.t('ApComponents.internalOrder') }}</h4>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item>
                                    <a-input v-model:value="form.sapPayableInternalOrderCode" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                        <a-row :gutter="24">
                            <a-col :span="1"></a-col>
                            <a-col :span="2">
                                <a-form-item>
                                    <a-checkbox v-model:checked="form.sapPayableProfitCenter"></a-checkbox>
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <h4 style="padding-top: 4px">{{ i18n.t('ApComponents.profitCenter') }}</h4>
                            </a-col>
                            <a-col :span="12">
                                <a-form-item>
                                    <a-input v-model:value="form.sapPayableProfitCenterCode" />
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </div>
                </a-form>
            </a-modal>

            <PgoPlusSettingModal
                v-model:visible="showPgoPlusSetting"
                :pgo-form="pgoForm"
                :company-code="userCompany[0].code"
                @save="handlePgoPlusSettingSubmit"
            />
            <reason-code-modal
                v-model:visible="showReasonCode"
                :company-code="userCompany[0].code"
                @save="handleReasonCodeSubmit"
            />
        </div>
    </div>
</template>
<style lang="scss" scoped>
.company-info-logo-img-placeholder,
.company-info-logo-wrapper {
    img {
        max-height: 105px;
    }
}

:deep(.ant-upload-list) {
    display: inline-block;
    margin-left: 22px;
}

.ap-invoice-block {
    padding-left: 8px;
}

.page-container-connectivities {
    height: 100%;

    .tabs-contents {
        border-radius: 12px;
        flex: 1;
    }
}

.content-box-import-form,
.content-box-smtp {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    padding: 24px 20px 20px;
    background-color: #fff;
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    //.content-box-smtp-wrap {
    //    width: 40%;
    //    .btn-auth {
    //        margin-top: 20px;
    //        width: 120px;
    //        height: 36px;
    //    }
    //}
}

.import-form-wrap {
}

.import-form-title {
    //  font-family: Calibri;
    font-size: 44px;
    color: #262626;
    line-height: 54px;
    font-weight: 400;
}

.import-form-block {
    padding: 24px 0;
}

.shopify-logo-wrap {
    height: 71px;
    // width: 237px;
}

.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover {
    //@extend .ant-btn-primary;
    opacity: 50%;
    color: #fff;
    border-color: #004fc1;
    background: #004fc1;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.img-wrap {
    position: absolute;
    right: 30px;
    top: 0px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}
</style>

const handleReasonCodeSubmit = async (config: any) => { message.success('Reason code saved successfully') }
