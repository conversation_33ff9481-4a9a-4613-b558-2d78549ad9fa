/** @format */

import {createApp} from 'vue'
import {setDirectives} from './directives'
import App from './App.vue'
import router from './router'
import PrimeVue from 'primevue/config'

import i18nInstance from './locales/i18n'
import store from './store/store'

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './styles/main.scss'

import 'virtual:svg-icons-register'
import {LocalCurrency, CurrencyCountry, CurrencyLanguage} from '@/lib/storage'

// dayjs 国际化库
import 'dayjs/locale/zh-cn'
import 'dayjs/locale/en'
import 'dayjs/locale/zh-hk'
import 'dayjs/locale/ja'
import 'dayjs/locale/fr'
import 'dayjs/locale/es'

const app = createApp(App)

// 数据仓库
app.use(store)

// 路由
app.use(router)

app.use(PrimeVue)

// 国际化
app.use(i18nInstance)

// ant design
app.use(Antd)

// currency format
app.config.globalProperties.$moneyFormat = (value: number) => {
    if (!value) return ''
    const localCurrency = LocalCurrency.get() || 'CAD'
    const currencyCountry = CurrencyCountry.get() || 'CA'
    const currencyLanguage = CurrencyLanguage.get() || 'EN'
    return new Intl.NumberFormat(`${currencyLanguage.toLowerCase()}-${currencyCountry}`, {
        style: 'decimal',
        currency: localCurrency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(value)
}

// 格式化函数，将数字转换为带千分位的显示格式
app.config.globalProperties.$formatNumber = (value: any) => {
    if (value === undefined || value === null || value === '') return ''
    // const localCurrency = LocalCurrency.get() || 'CAD'
    const currencyCountry = CurrencyCountry.get() || 'CA'
    // const currencyLanguage = CurrencyLanguage.get() || 'EN'
    // 保留两位小数
    const formattedValue = parseFloat(value).toFixed(2)
    const [integerPart, decimalPart] = formattedValue.split('.')
    if (currencyCountry.toUpperCase() === 'FR') {
        // 欧元格式
        const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
        return `${formattedInteger},${decimalPart}`
    }
    // 加币格式
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return `${formattedInteger}.${decimalPart}`
}

// 解析函数，将带千分位的字符串转换为纯数字
app.config.globalProperties.$parseNumber = (value: any) => {
    if (!value) return ''
    const localCurrency = LocalCurrency.get() || 'CAD'
    const currencyCountry = CurrencyCountry.get() || 'CA'
    const currencyLanguage = CurrencyLanguage.get() || 'EN'
    if (currencyCountry.toUpperCase() === 'FR') {
        // 欧元格式
        return value.replace(/\./g, '').replace(',', '.')
    }
    // 加币格式
    return value.replace(/(,*)/g, '')
}
// 千分位格式函数，返回指定的千分位格式
app.config.globalProperties.$decimalFormat = () => {
    const currencyCountry = CurrencyCountry.get() || 'CA'
    if (currencyCountry.toUpperCase() === 'FR') {
        // 欧元格式
        return 'de-DE'
    }
    // 加币格式
    return 'en-CA'
}

app.mount('#app')
setDirectives(app)
