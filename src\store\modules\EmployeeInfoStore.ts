/** @format */

import type {ActionContext} from 'vuex'
import service from '../../api/request'

const state = () => ({
    detail: {
        headImgUrl: '',
        name: '',
        jobName: '',
        orgName: '',
        entryDate: '',
        siLing: '',
        workStateStr: '',
    },
    candidate: {},
    check: false,
    jobMode: false,
})
const mutations = {
    setDetailTitle(state: {detail: any}, payload: any) {
        state.detail = {...payload}
    },
    resetDetailTitle(state: {
        detail: {
            headImgUrl: string
            name: string
            jobName: string
            orgName: string
            entryDate: string
            siLing: string
            workStateStr: string
        }
    }) {
        state.detail = {
            ...{
                headImgUrl: '',
                name: '',
                jobName: '',
                orgName: '',
                entryDate: '',
                siLing: '',
                workStateStr: '',
            },
        }
    },
    setCheckMode(state: {check: any}, bool: any) {
        state.check = bool
    },
    setJobMode(state: {jobMode: any}, bool: any) {
        state.jobMode = bool
    },
    setCandidate(state: {candidate: any}, payload: any) {
        state.candidate = {...payload}
    },
}
const actions = {
    async submitEmployeeDetailForm(_: ActionContext<{[key: string]: any}, Record<string, unknown>>, form: any) {
        return service.post('/recruitment/updateInduction', form)
    },
    async findEmployeeByName(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, name: any) {
        const response = await service.get(`/emp/like/list?param=${name}`)
        if (response.data.code === 1000) {
            return response.data.data
        } else {
            return []
        }
    },
    async connectEmployeeProject(
        ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>,
        payload: {projectId: string | Blob; employeeIds: string | Blob},
    ) {
        const form = new FormData()
        form.append('projectId', payload.projectId)
        form.append('employeeIds', payload.employeeIds)
        const r = await service.post('/project/manage/relation', form)
        return r.data
    },
    async connectAll(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: {[s: string]: any}) {
        const form = new FormData()
        Object.entries(payload).forEach(([k, v]) => {
            form.append(k, v)
        })
        const r = await service.post('/project/manage/relation/joinAll', form)
        return r.data
    },
}
export default {
    state,
    actions,
    mutations,
}
