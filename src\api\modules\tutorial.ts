/** @format */

import service from '@/api/requestNew'
import serviceGL from '@/api/requestNewGL'

export const getCompanyTaxInfo = (params: any) => {
    return service.get(`/users/api/v1/company`, {params: params})
}

export const getCustomers = (params: any) => {
    return service.get(`/system-preferences/api/v1/contact`, {params: params})
}

export const getCoa = (params: any) => {
    return serviceGL.get('/bk/coa', {params: params})
}

export const getCompanyBankList = (params: any) => {
    return service.get(`/users/api/v1/company-bank`, {params: params})
}

export const getGlList = (params: any) => {
    if (params.status == '1') params.status = '1,2'

    return serviceGL.get('/bk/post-journal-entry', {params: params})
}
