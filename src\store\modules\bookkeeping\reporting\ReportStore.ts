/** @format */

import type {ActionContext} from 'vuex'
import serviceGL from '@/api/requestNewGL'
import service from '@/api/requestNew'
import FileSaver from 'file-saver'

const httpGL = serviceGL
const httpNew = service

const ReportStore = {
    namespaced: true,
    state: {
        trialBalanceList: [],
        displayTrialBalanceList: [],
        trialBalanceTotalNumber: 0,
        arApReportsList: [],
        arApReportsSummary: [],
        arApReportsTotalNumber: 0,
        sPReportsList: [],
        sPReportsSummary: [],
        sPReportsTotalNumber: 0,
        balanceSheet: {},
        profitLoss: {},
        dashBoard: {},
        dashBoard01: {},
        dashBoard02: {},
        dashBoard03: {},
        dashBoard04: {},
        dashBoard05: {},
        dashBoard06: {},
        dashBoard07: {},
        dashBoard08: {},
        dashBoard09: {},
        dashBoard10: {},
        dashBoard11: {},
        dashBoard12: {},
        cashFlowStatementList: [],
        displayCashFlowStatementList: [],
        cashFlowStatementTotalNumber: 0,
        mxElectronicAccountingCoaReport: [],
        mxElectronicAccountingTrialBalanceReport: [],
        mxInformativeDeclarationReport: [],
        mxValueAddedTaxReport: {},
    },
    mutations: {
        updateTotalFoundNumber(state: {trialBalanceTotalNumber: any}, num: any) {
            state.trialBalanceTotalNumber = num
        },
        updateTrialBalanceList(state: {trialBalanceList: any[]}, list: any) {
            state.trialBalanceList = list
        },
        updateDisplayTrialBalanceList(
            state: {displayTrialBalanceList: any[]; trialBalanceTotalNumber: number},
            list: any[],
        ) {
            state.displayTrialBalanceList = list
            state.trialBalanceTotalNumber = list.length
        },

        updateCashFlowStatementTotalFoundNumber(state: {cahsFlowStatementTotalNumber: any}, num: any) {
            state.cahsFlowStatementTotalNumber = num
        },
        updateCashFlowStatementList(state: {cashFlowStatementList: any[]}, list: any) {
            state.cashFlowStatementList = list
        },
        updateDisplayCashFlowStatementList(
            state: {displayCashFlowStatementList: any[]; cashFlowStatementTotalNumber: number},
            list: any[],
        ) {
            state.displayCashFlowStatementList = list
            state.cashFlowStatementTotalNumber = list.length
        },

        updateArApReportsTotalNumber(state: {arApReportsTotalNumber: any}, num: any) {
            state.arApReportsTotalNumber = num
        },
        updateArApReportsList(state: {arApReportsList: any[]}, list: any) {
            state.arApReportsList = list
        },
        updateArApReportsSummary(state: {arApReportsSummary: any[]}, list: any) {
            state.arApReportsSummary = list
        },
        updateSPReportsTotalNumber(state: {sPReportsTotalNumber: any}, num: any) {
            state.sPReportsTotalNumber = num
        },
        updateSPReportsSummary(state: {sPReportsSummary: any[]}, list: any) {
            state.sPReportsSummary = list
        },
        updateSPReportsList(state: {sPReportsList: any[]}, list: any) {
            state.sPReportsList = list
        },
        updateBalanceSheet(state: {balanceSheet: any}, data: any) {
            state.balanceSheet = data
        },
        updateProfitLoss(state: {profitLoss: any}, data: any) {
            state.profitLoss = data
        },
        updateDashBoard(state: {dashBoard: any}, data: any) {
            state.dashBoard = data
        },
        updateDashBoard1(state: {dashBoard01: any}, data: any) {
            state.dashBoard01 = data
        },
        updateDashBoard2(state: {dashBoard02: any}, data: any) {
            state.dashBoard02 = data
        },
        updateDashBoard3(state: {dashBoard03: any}, data: any) {
            state.dashBoard03 = data
        },
        updateDashBoard4(state: {dashBoard04: any}, data: any) {
            state.dashBoard04 = data
        },
        updateDashBoard5(state: {dashBoard05: any}, data: any) {
            state.dashBoard05 = data
        },
        updateDashBoard6(state: {dashBoard06: any}, data: any) {
            state.dashBoard06 = data
        },
        updateDashBoard7(state: {dashBoard07: any}, data: any) {
            state.dashBoard07 = data
        },
        updateDashBoard8(state: {dashBoard08: any}, data: any) {
            state.dashBoard08 = data
        },
        updateDashBoard9(state: {dashBoard09: any}, data: any) {
            state.dashBoard09 = data
        },
        updateDashBoard10(state: {dashBoard10: any}, data: any) {
            state.dashBoard10 = data
        },
        updateDashBoard11(state: {dashBoard11: any}, data: any) {
            state.dashBoard11 = data
        },
        updateDashBoard12(state: {dashBoard12: any}, data: any) {
            state.dashBoard12 = data
        },
        updateMxElectronicAccountingCoaReport(state: {mxElectronicAccountingCoaReport: any[]}, list: any) {
            state.mxElectronicAccountingCoaReport = list
        },
        updateMxElectronicAccountingTrialBalanceReport(
            state: {mxElectronicAccountingTrialBalanceReport: any[]},
            list: any,
        ) {
            state.mxElectronicAccountingTrialBalanceReport = list
        },
        updateMxInformativeDeclarationReport(state: {mxInformativeDeclarationReport: any[]}, list: any) {
            state.mxInformativeDeclarationReport = list
        },
        updateMxValueAddedTaxReport(state: {mxValueAddedTaxReport: any}, list: any) {
            state.mxValueAddedTaxReport = list
        },
    },
    actions: {
        //获取Trial Balance  Api
        async getTrialBalanceList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/trial-balance`, {params: payload})
                let list = response.data
                if (list.length > 0) {
                    list = list.slice(0, list.length - 1) //the last record is the statistical result
                } else {
                    store.commit('updateTrialBalanceList', [])
                    store.commit('updateDisplayTrialBalanceList', [])
                    store.commit('updateTotalFoundNumber', 0)
                    return
                }
                const num = list.length

                store.commit('updateTrialBalanceList', list)
                store.commit('updateDisplayTrialBalanceList', list)
                store.commit('updateTotalFoundNumber', num)
                return response
            } catch (error) {
                store.commit('updateTrialBalanceList', [])
                store.commit('updateDisplayTrialBalanceList', [])
                store.commit('updateTotalFoundNumber', 0)
            }
        },
        async getCashFlowStatementList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                //https://eo-gateway-test.inossemtimes.com/handleExport/cash-flow-report-json?company_code=2605&end_date=2023-07-31
                const response: any = await httpNew.get(`handleExport/cash-flow-report-json`, {params: payload})
                let list: any[] = response.data
                // console.log(list)
                list = list.map(item => {
                    const monthString = item['Current Month'].replace(/,/g, '') // 使用正则表达式去除所有逗号
                    const yearString = item['Current Year'].replace(/,/g, '') // 使用正则表达式去除所有逗号
                    const month = parseFloat(monthString)
                    const year = parseFloat(yearString)
                    return {
                        name: item['Cash Flow Items'],
                        month: isNaN(month) ? 0 : month,
                        year: isNaN(year) ? 0 : year,
                    }
                })
                // console.log(list)
                const num = list.length

                store.commit('updateCashFlowStatementList', list)
                store.commit('updateDisplayCashFlowStatementList', list)
                store.commit('updateCashFlowStatementTotalFoundNumber', num)
                return response
            } catch (error) {
                store.commit('updateCashFlowStatementList', [])
                store.commit('updateDisplayCashFlowStatementList', [])
                store.commit('updateCashFlowStatementTotalFoundNumber', 0)
            }
        },

        async getArApReportsList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`invoice-statement/api/v1/report/ar-ap`, {params: payload})

                const list = response.data.data.detail
                const summary = response.data.data.summary
                const num = list.length

                store.commit('updateArApReportsList', list)
                store.commit('updateArApReportsSummary', summary)

                store.commit('updateArApReportsTotalNumber', num)
                return response
            } catch (error) {
                store.commit('updateArApReportsList', [])
                store.commit('updateArApReportsSummary', [])
                store.commit('updateArApReportsTotalNumber', 0)
            }
        },
        async getSPReportsList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`invoice-statement/api/v1/report/sales-purchase`, {
                    params: payload,
                })

                const list = response.data.data.list
                const summary = response.data.data.summary
                const num = list.length

                store.commit('updateSPReportsList', list)
                store.commit('updateSPReportsSummary', summary)
                store.commit('updateSPReportsTotalNumber', num)
                return response
            } catch (error) {
                store.commit('updateSPReportsList', [])
                store.commit('updateSPReportsSummary', [])
                store.commit('updateSPReportsTotalNumber', 0)
            }
        },
        async getIntegrationSPReportsList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                const response: any = await httpNew.get(`invoice-statement/api/v1/report/integration-sales-purchase`, {
                    params: payload,
                })

                const list = response.data.data.list
                const summary = response.data.data.summary
                const num = list.length

                store.commit('updateSPReportsList', list)
                store.commit('updateSPReportsSummary', summary)
                store.commit('updateSPReportsTotalNumber', num)
                return response
            } catch (error) {
                store.commit('updateSPReportsList', [])
                store.commit('updateSPReportsSummary', [])
                store.commit('updateSPReportsTotalNumber', 0)
            }
        },
        async getBalanceSheetData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/balance-sheet-json`, {params: payload})

                const data = response.data
                store.commit('updateBalanceSheet', data)
                return response
            } catch (error) {
                store.commit('updateBalanceSheet', {})
            }
        },
        async getProfitLossData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/profit-loss-json`, {params: payload})

                const data = response.data

                store.commit('updateProfitLoss', data)
                return response
            } catch (error) {
                store.commit('updateProfitLoss', {})
            }
        },

        async getDashBoardData(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard', {})
            }
        },
        async getDashBoard1Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard1', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard1', {})
            }
        },
        async getDashBoard2Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard2', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard2', {})
            }
        },
        async getDashBoard3Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard3', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard3', {})
            }
        },
        async getDashBoard4Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard4', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard4', {})
            }
        },
        async getDashBoard5Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard5', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard5', {})
            }
        },
        async getDashBoard6Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard6', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard6', {})
            }
        },
        async getDashBoard7Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard7', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard7', {})
            }
        },
        async getDashBoard8Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard8', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard8', {})
            }
        },
        async getDashBoard9Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard9', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard9', {})
            }
        },
        async getDashBoard10Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard10', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard10', {})
            }
        },
        async getDashBoard11Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard11', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard11', {})
            }
        },
        async getDashBoard12Data(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`handleExport/bkp_reports_dash`, {params: payload})
                const data = response.data
                store.commit('updateDashBoard12', data)
                return response
            } catch (error) {
                store.commit('updateDashBoard12', {})
            }
        },

        async export(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company: any; company_name: any; end: any},
        ) {
            const subPath = `handleExport/trial-balance-csv?company=${payload.company}&company_name=${payload.company_name}&end=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Trail_Balance_Export'
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const filename = `${formattedEndDate} TB ${payload.company_name}.xlsx`

            console.log('export      ->', response.headers)

            const blob = new Blob(
                [response.data],
                // {type: 'text/csv, charset=UTF-8'})
                {type: response.headers['content-type']},
            )
            FileSaver.saveAs(blob, filename)
        },
        async exportBS(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company: any; company_name: any; end: any},
        ) {
            const subPath = `handleExport/balance-sheet-csv-new?company=${payload.company}&end=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Balance_Sheet_Export'
            const filename = (response.headers['content-disposition'] || '')
                .split('filename=')[1]
                .replace('"', '')
                .replace('"', '')

            // console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportBSPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company: any; company_name: any; end: any},
        ) {
            const subPath = `handleExport/balance-sheet-pdf?company=${payload.company}&company_name=${payload.company_name}&&end=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Balance_Sheet_Export'
            const formattedEndDate = payload.end.replace(/-/g, '')
            const filename = `${formattedEndDate} BS ${payload.company_name}.pdf`

            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')

            // console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportPL(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company: any; company_name: any; start: any; end: any},
        ) {
            const subPath = `handleExport/profit-loss-csv-new?company=${payload.company}&start=${payload.start}&end=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Income_Statement_Export'
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')
            const formattedStartDate = payload.start.replace(/-/g, '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const filename = `${formattedStartDate}-${formattedEndDate} PL ${payload.company_name}.xlsx`

            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportPLPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company: any; company_name: any; start: any; end: any},
        ) {
            const subPath = `handleExport/profit-loss-pdf?company=${payload.company}&company_name=${payload.company_name}&start=${payload.start}&end=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Income_Statement_Export'
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')
            const formattedStartDate = payload.start.replace(/-/g, '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const filename = `${formattedStartDate}-${formattedEndDate} PL ${payload.company_name}.pdf`

            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportSP(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any; company_name: any; report_type: any; start: any; end: any},
        ) {
            const subPath = `handleExport/sp_report?company_code=${payload.company_code}&company_name=${payload.company_name}&report_type=${payload.report_type}&start_date=${payload.start}&end_date=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Report_Export'
            const formattedStartDate = payload.start.replace(/-/g, '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const reportTypeReplacement =
                payload.report_type === 'AP' ? 'Purchase' : payload.report_type === 'AR' ? 'Sales' : payload.report_type
            const filename = `${formattedStartDate}-${formattedEndDate} ${reportTypeReplacement} ${payload.company_name}.xlsx`
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')

            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportSPPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any; company_name: any; report_type: any; start: any; end: any},
        ) {
            const subPath = `handleExport/sp_report_pdf?company_code=${payload.company_code}&company_name=${payload.company_name}&report_type=${payload.report_type}&start_date=${payload.start}&end_date=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Report_Export'
            const formattedStartDate = payload.start.replace(/-/g, '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const reportTypeReplacement =
                payload.report_type === 'AP' ? 'Purchase' : payload.report_type === 'AR' ? 'Sales' : payload.report_type
            const filename = `${formattedStartDate}-${formattedEndDate} ${reportTypeReplacement} ${payload.company_name}.pdf`
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')

            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportArAp(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any; company_name: any; report_type: any; end: any; start: any},
        ) {
            const subPath = `handleExport/arap_report?company_code=${payload.company_code}&company_name=${payload.company_name}&report_type=${payload.report_type}&start=${payload.start}&end_date=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Report_Export'
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const filename = `${formattedEndDate} ${payload.report_type} ${payload.company_name}.xlsx`
            console.log('=======', filename)
            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportArApPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any; company_name: any; report_type: any; end: any; start: any},
        ) {
            const subPath = `handleExport/arap_report_pdf?company_code=${payload.company_code}&company_name=${payload.company_name}&report_type=${payload.report_type}&start=${payload.start}&end_date=${payload.end}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Report_Export'
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const filename = `${formattedEndDate} ${payload.report_type} ${payload.company_name}.pdf`
            console.log('=======', filename)
            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },

        async exportCashFlow(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any; end_date: any},
        ) {
            const subPath = `handleExport/cash-flow-report-json?company_code=${payload.company_code}&end_date=${payload.end_date}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            const filename = (response.headers['content-disposition'] || '')
                .split('filename=')[1]
                .replace('"', '')
                .replace('"', '')

            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },
        async exportCashFlowPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company_code: any; company_name: any; end_date: any},
        ) {
            const subPath = `handleExport/cash-flow-report-pdf?company_code=${payload.company_code}&company_name=${payload.company_name}&end_date=${payload.end_date}`
            const response = await httpNew.get(subPath, {
                responseType: 'blob',
            })
            // const filename = 'Income_Statement_Export'
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')
            // const formattedStartDate = payload.start.replace(/-/g, '')
            const formattedEndDate = payload.end_date.replace(/-/g, '')
            const filename = `${formattedEndDate} Cash Flow ${payload.company_name}.pdf`

            console.log('export      ->', response.headers)

            const blob = new Blob([response.data], {type: response.headers['content-type']})
            FileSaver.saveAs(blob, filename)
        },

        async glExportJson(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {company: any; start: any; coa: any},
        ) {
            const response: any = await httpNew.get(`handleExport/gl-export-json`, {
                params: payload,
            })
            // debugger;
            const list = response.data
            return list
        },
        async getMxElectronicAccountingCoaReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                const response: any = await httpNew.get(`ESHubService/reports/mx-electronic-accounting-coa`, payload)

                const list = response.data.data || []

                store.commit('updateMxElectronicAccountingCoaReport', list)
                return response
            } catch (error) {
                console.error('Error fetching MX Electronic Accounting COA Report:', error)
                store.commit('updateMxElectronicAccountingCoaReport', [])
            }
        },
        async getMxElectronicAccountingTrialBalanceReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                const response: any = await httpNew.get(
                    `ESHubService/reports/mx-electronic-accounting-trial-balance`,
                    payload,
                )

                const list = response.data.data || []

                store.commit('updateMxElectronicAccountingTrialBalanceReport', list)
                return response
            } catch (error) {
                console.error('Error fetching MX Electronic Accounting Trial Balance Report:', error)
                store.commit('updateMxElectronicAccountingTrialBalanceReport', [])
            }
        },
        async deleteMxElectronicAccountingCoaReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                return await httpNew.delete(`ESHubService/reports/mx-electronic-accounting-coa`, payload)
            } catch (error) {
                console.error('Error deleting MX Electronic Accounting COA Report:', error)
            }
        },
        async deleteMxElectronicAccountingTrialBalanceReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                return await httpNew.delete(`ESHubService/reports/mx-electronic-accounting-trial-balance`, payload)
            } catch (error) {
                console.error('Error deleting MX Electronic Accounting Trial Balance Report:', error)
            }
        },
        async triggerMxElectronicAccountingCoaReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                return await httpNew.post(
                    `ESHubService/reports/trigger-pgo-mx-electronic-accounting-coa?company_code=${payload.params.company_code}&report_period=${payload.params.report_period}`,
                )
            } catch (error) {
                console.error('Error triggering MX Electronic Accounting COA Report:', error)
            }
        },
        async triggerMxElectronicAccountingTrialBalanceReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                return await httpNew.post(
                    `ESHubService/reports/trigger-pgo-mx-electronic-accounting-trial-balance?company_code=${payload.params.company_code}&report_period=${payload.params.report_period}`,
                )
            } catch (error) {
                console.error('Error triggering MX Electronic Accounting Trial Balance Report:', error)
            }
        },
        async getMxReportFile(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            try {
                const response: any = await httpNew.get(`ESHubService/reports/report-file`, payload)
                return response.data
            } catch (error) {
                console.error('Error fetching MX Report file:', error)
            }
        },
        async getMxInformativeDeclarationReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                const response: any = await httpNew.get(`prefectClient/diot_report_v2`, payload)

                const list = response.data.data || []

                store.commit('updateMxInformativeDeclarationReport', list)
                return response
            } catch (error) {
                console.error('Error fetching MX Informative Declaration Report:', error)
                store.commit('updateMxInformativeDeclarationReport', [])
            }
        },
        async getMxValueAddedTaxReport(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            try {
                const response: any = await httpNew.get(`prefectClient/value_added_tax`, payload)

                const list = response.data || {}

                store.commit('updateMxValueAddedTaxReport', list)
                return response
            } catch (error) {
                console.error('Error fetching MX Value Added Tax Report:', error)
                store.commit('updateMxValueAddedTaxReport', {})
            }
        },
    },
}

export default ReportStore
