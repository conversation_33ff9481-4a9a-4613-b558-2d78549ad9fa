<!-- @format -->

<script setup lang="ts">
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {
    UserCompany,
    UserInfo,
    LocalCurrency,
    Ap_Integration,
    SapPayableGl,
    SapPayableWbs,
    SapPayableCostCenter,
    SapPayableInternalOrder,
    SapPayableProfitCenter,
} from '@/lib/storage'
import {computed, onBeforeMount, reactive, ref, watch, createVNode} from 'vue'
import {useStore} from 'vuex'
import {message, type FormInstance} from 'ant-design-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import dayjs from 'dayjs'
import * as _ from 'lodash'

const userInfo: any = UserInfo.get() || {}
const localCurrency = LocalCurrency.get() || 'CAD'
const i18n: Composer = i18nInstance.global
const formRef = ref<FormInstance>()
const store = useStore()
const props = defineProps<{
    reconciliation: {[key: string]: any}
}>()
const emits = defineEmits(['update', 'dismiss'])
const userCompany: any = UserCompany.get() || []
const apIntegration: any = Ap_Integration.get() ?? 0
const type = ref('')
const userType = ref('')
const postingDate = ref(dayjs().format('YYYY-MM-DD'))
const sapPayableGl: any = SapPayableGl.get() || {} || undefined
const sapPayableProfitCenter: any = SapPayableProfitCenter.get() || {}
const sapPayableWbs: any = SapPayableWbs.get() || {} || undefined
const sapPayableCostCenter: any = SapPayableCostCenter.get() || {}
const sapPayableInternalOrder: any = SapPayableInternalOrder.get() || {}

const arForm = ref<any>({
    id: null,
    pay_method: '1',
    company_name: undefined as string | undefined,
    company_code: '',
    company_id: '',
    company_email: '',
    company_phone: '',
    issuer_name: null,
    issuer_tel: '',
    issuer_email: '',
    reference_no: '',
    tax_content: [],
    invoice_currency: localCurrency,
    invoice_create_date: '',
    invoice_due_date: '',
    posting_date: '',
    items: [] as any[],
    amount: null as number | null,
    net_amount: null as number | null,
    file_id: '',
    file_url: '',
    file_name: '',
    creator: '',
    engine_document_id: '' || [] || null,
    cash_engine_payment_no: '' || null,
    engine_reverse_document_id: '' || (null as any),
    total_tax: 0 as number | null,
    total_fee: 0 as number | null,
    total_fee_local: 0 as number | null,
    shipping: null,
    invoice_comments: '',
    issuer_id: '',
    issuer_address: '',
    invoice_no: '',
    br_type: '',
    po: '',
    payment_term_discount: '',
    exchange_rate: null,
    gl_type: undefined,
    sap_gl_account: '',
    sap_wbs: '',
    sap_cost_center: '',
    sap_internal_order: '',
    sap_profit_center: '',
    reference: '',
})
const formLoading = ref(false)
const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}

const arRules = reactive({
    issuer_name: [...requireRule(i18n.t('bkAp.companyName'))],
    invoice_currency: [...requireRule(i18n.t('bkAp.currency'))],
    invoice_due_date: [...requireRule(i18n.t('bkAp.dueDate'))],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur', 'change'],
        },
    ],
    requireGLType: [...requireRule('gl_type')],
    requireGLAccount: [...requireRule(i18n.t('bkAp.accountingCategory'))],
    posting_date: [...requireRule(i18n.t('bkAp.postingDate'))],
    invoice_comments: [...lengthLimitRule(1, 200)],
    pay_method: [...requireRule(i18n.t('bkAp.payMethod'))],
})
const updateTaxRates = (data: any) => store.commit('TaxCalculationStore/updateTaxRatesList', data)
const fetchAllBankList = (query?: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', query)
const updateApTopCoa = (data: any) => store.commit('ApStore/updateApTopCoa', data)
const submitReconcileEEDeposit = (payload: any) => store.dispatch('ReconciliationStore/submitReconciliationEEDepositV1Integration', payload)
const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}

const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const getType = (debit: number, credit: number) => {
    if (Math.abs(debit) > 0) {
        return 'AR'
    } else if (Math.abs(credit) > 0) {
        return 'AP'
    } else {
        return ''
    }
}
onBeforeMount(async () => {
    formLoading.value = true
    userType.value = userInfo.roles === '9996' ? 'jushi' : 'common'

    updateTaxRates([])
    updateApTopCoa([])

    type.value = getType(props.reconciliation.deposit, props.reconciliation.withdrawal)

    await formInit()
})

const cancel = () => {
    emits('dismiss')
}

const confirm = async () => {
    try {
        // create GL
        await glSave()
    } catch (err: any) {
        console.log(err)
    } finally {
        emits('dismiss')
        emits('update')
    }
}
const fetchSapMasterData = (query?: any) => store.dispatch('ApStore/fetchApSapMasterData', query)
const state = reactive({
    supplierList: apIntegration === 1 ? [] : computed(() => store.state.CommonDropDownStore.supplierOptions),
    localSupplierList: computed(() => store.state.CommonDropDownStore.supplierOptions),
    masterData: computed(() => store.state.ApStore.sapMasterData),
})
const glSave = async () => {
    const {br_type} = props.reconciliation
    if (await formRef.value?.validateFields()) {
        formLoading.value = true
        // const queryForm = prepareApInvoice()
        try {
            const res = await reconcileInvoices({invoice_detail: null, br_entity_type: br_type})
            if (res.data.statusCode === 200) {
                message.success({content: i18n.t('Success')})
            } else {
                message.error({content: i18n.t('Error')})
            }
        } catch (error) {
            // await reversePurchase(invoice.id)
        } finally {
            formLoading.value = false
        }
    } else {
        message.error({
            content: i18n.t('Error'), //"Can't create invoice without [ Net Amount ]",
            duration: 6,
        })
    }
    formLoading.value = false
}

const formInit = async () => {
    arForm.value.posting_date = props.reconciliation.date
    arForm.value.net_amount = props.reconciliation.balance
    arForm.value.total_fee = props.reconciliation.balance
    arForm.value.total_fee_local = props.reconciliation.balance
    arForm.value.invoice_currency = props.reconciliation.currency

    arForm.value.reconcile_balance = props.reconciliation.balance
    try {
        await Promise.all([
            fetchAllBankList({company_code: userCompany[0].code}),
            fetchSapMasterData({...currentUserCompanyQuery}),
        ])
    } catch (e) {
        console.log(e)
    } finally {
        formLoading.value = false
    }
}

const reconcileInvoices = async (inovice: any) => {
    try {
        const {
            br_type,
            br_entity_type,
            company_code,
            id,
            currency,
            bank_account,
            balance,
            date,
            gl_account,
            reason_code,
        } = props.reconciliation
        if (!gl_account) {
            message.error({
                content: "Can't reconcile invoice because GL Account is empty",
                duration: 6,
            })
            return
        }
        return submitReconcileEEDeposit({
            company_code: company_code || userCompany[0].code,
            statement_id: id,
            posting_date: arForm.value.posting_date ?? date,
            currency,
            bank_account,
            balance,
            gl_account,
            sap_gl_reason_code: reason_code,
            br_type,
            br_entity_type,
            creator: userInfo?.id,
            invoice_detail: null,
            sap_gl_account: arForm.value.sap_gl_account ?? '',
            sap_wbs: arForm.value.sap_wbs ?? '',
            sap_cost_center: arForm.value.sap_cost_center ?? '',
            sap_internal_order: arForm.value.sap_internal_order ?? '',
            sap_profit_center: arForm.value.sap_profit_center ?? '',
            reference: arForm.value.reference,
        })
    } catch (e) {
        console.log(e)
    }
}

const changeGlAccount = (value: any) => {
    console.log('Selected:', value, 'sap_gl_account:', arForm.value.sap_gl_account)
}
</script>

<template>
    <a-spin :spinning="formLoading">
        <a-form ref="formRef" :layout="'vertical'" :model="arForm" :rules="arRules">
            <div class="title">
                <span>{{ i18n.t('EstatementTable.integrationEEbrTitle') }}</span>
                <p />
            </div>
            <a-form-item 
                class="form-item"
                :label="i18n.t('esMain.postingDate')"
                name="posting_date"
            >
                <div class="table-input" style="width: 100%">
                    <a-date-picker v-model:value="arForm.posting_date" :allowClear="false" :inputReadOnly="true"
                        format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" class="table-input" style="width: 100%">
                        <template #suffixIcon>
                            <svg-icon name="icon_date"></svg-icon>
                        </template>
                    </a-date-picker>
                </div>
            </a-form-item>
            <a-form-item
                class="form-item"
                :label="i18n.t('bkAp.accountingCategory')"
                name="sap_gl_account"
                :rules="arRules['requireGLAccount']"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="arForm.sap_gl_account"
                    show-search
                    :dropdownMatchSelectWidth="200"
                    :filter-option="filterOption"
                    class="table-input"
                    @change="changeGlAccount"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_GL_ACCOUNT"
                        :key="item.GL_ACCOUNT + ' | ' + item.DESCRIPTION"
                        :value="item.GL_ACCOUNT"
                        >{{ item.GL_ACCOUNT }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="sapPayableWbs === 1"
                class="form-item"
                :label="i18n.t('ApComponents.wbs')"
                name="sap_wbs"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="arForm.sap_wbs"
                    show-search
                    :dropdownMatchSelectWidth="200"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_WBS"
                        :key="item.WBS + ' | ' + item.DESCRIPTION"
                        :value="item.WBS"
                        >{{ item.WBS }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="sapPayableCostCenter === 1"
                class="form-item"
                :label="i18n.t('ApComponents.costCenter')"
                name="sap_cost_center"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="arForm.sap_cost_center"
                    show-search
                    allow-clear
                    :dropdownMatchSelectWidth="200"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_COST_CENTER"
                        :key="item.COST_CENTER + ' | ' + item.DESCRIPTION"
                        :value="item.COST_CENTER"
                        >{{ item.COST_CENTER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                v-if="sapPayableInternalOrder === 1"
                class="form-item"
                :label="i18n.t('ApComponents.internalOrder')"
                name="sap_internal_order"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="arForm.sap_internal_order"
                    show-search
                    allow-clear
                    :dropdownMatchSelectWidth="200"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_INTERNAL_ORDER"
                        :key="item.INTERNAL_ORDER + ' | ' + item.DESCRIPTION"
                        :value="item.INTERNAL_ORDER"
                    >
                        {{ item.INTERNAL_ORDER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
                <!-- <a-input
                    v-model:value="arForm.sap_internal_order"
                    :placeholder="i18n.t('commonTag.msgInput')"
                    class="table-input"
                ></a-input> -->
            </a-form-item>
            <a-form-item
                v-if="sapPayableProfitCenter === 1"
                class="form-item"
                :label="i18n.t('ApComponents.profitCenter')"
                name="sap_profit_center"
            >
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="arForm.sap_profit_center"
                    show-search
                    :dropdownMatchSelectWidth="200"
                    :filter-option="filterOption"
                    class="table-input"
                >
                    <a-select-option
                        v-for="item in state.masterData.ET_PROFIT_CENTER"
                        :key="item.PROFIT_CENTER + ' | ' + item.DESCRIPTION"
                        :value="item.PROFIT_CENTER"
                        >{{ item.PROFIT_CENTER }} | {{ item.DESCRIPTION }}
                    </a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item
                class="form-item"
                :label="i18n.t('esMain.reference')"
                name="reference"
            >
                <a-input v-model:value="arForm.reference" :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"></a-input>
            </a-form-item>
            <a-divider class="footer-divider"></a-divider>

            <footer>
                <a-button shape="round" class="cancel-button" @click="cancel" size="small">
                    {{ i18n.t('commonTag.cancel') }}
                </a-button>
                <a-button type="primary" :loading="formLoading" size="small" shape="round" @click="confirm"
                    >{{ i18n.t('commonTag.confirm') }}
                </a-button>
            </footer>
        </a-form>
    </a-spin>
</template>

<style scoped lang="scss">
.title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 24px;
    margin-left: 24px;
}

.form-item {
    margin-left: 24px;
    width: 80%;
}

.gl-switch-wrap {
    margin-right: 8px;
}

.ap-invoice-amount-block {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 0 0;

    .ap-invoice-amount-block-left {
        width: 300px;
    }

    .ap-invoice-amount-block-right {
        width: 300px;
        min-width: 300px;
        padding-bottom: 30px;

        // max-width: calc(100% - 285px);
        .amount-block {
            width: 100%;

            .title {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                text-align: right;
            }

            .switch-wrap {
                margin-right: 8px;
            }

            .amount-item-wrap {
                padding: 20px 16px;
                width: 100%;
                background-color: #f5f7f9;
                border-radius: 8px;

                .amount-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0px;
                    }

                    .amount-lable {
                        width: calc(100% - 151px - 8px);
                        min-width: 70px;
                        text-align: right;

                        &.bold {
                            font-weight: 700;
                        }
                    }

                    .amount-input {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: #fff;
                        }
                    }

                    .amount-display-alert {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: rgba(255, 0, 0, 0.2);
                            color: red;
                        }
                    }
                }
            }
        }
    }
}

.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}

footer {
    padding: 24px 0px 0;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn {
        min-width: 65px;
    }

    .ant-btn + .ant-btn {
        margin-left: 8px;
    }
}
</style>
