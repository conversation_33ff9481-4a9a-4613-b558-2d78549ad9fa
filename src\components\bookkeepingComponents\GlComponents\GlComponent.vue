<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import moment from 'moment'
import {computed, onBeforeMount, onMounted, reactive, ref, watch, getCurrentInstance} from 'vue'
import {message, type FormInstance} from 'ant-design-vue'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {PlusOutlined, QuestionCircleOutlined} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

import * as _ from 'lodash'
import {UserCompany, UserInfo, LocalCurrency} from '@/lib/storage'
import SvgIcon from '@/components/SvgIcon.vue'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ArInvoiceComponent from '@/components/bookkeepingComponents/ArComponents/ArInvoiceComponent.vue'
import InputNumber from 'primevue/inputnumber'

const {appContext} = getCurrentInstance()!
const formatNumber = appContext.config.globalProperties.$formatNumber
const parseNumber = appContext.config.globalProperties.$parseNumber
const decimalFormat = appContext.config.globalProperties.$decimalFormat
const state = reactive({
    showApArDetail: '',
    showInvoiceModal: false,
    invoiceNo: '',
})

const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const localCurrency = LocalCurrency.get() || 'CAD'
const i18n: Composer = i18nInstance.global
const store = useStore()
const props = defineProps({
    currentInvoice: {
        type: Object,
    },
    readonlyMode: {
        type: Boolean,
        default: false,
    },
    operationMode: {
        type: String,
    },
})
const formRef = ref<FormInstance>()
const tableWrapRef = ref()
const form = ref<any>({
    company_code: userCompany[0].code,
    header_text: '',
    currency: localCurrency,
    posting_date: '',
    line_items: [],
    totalCredit: undefined,
    totalDebit: undefined,
    totalCreditCAD: undefined,
    totalDebitCAD: undefined,
})
const isDisable = ref(false)
const formLoading = ref(false)
const debitDom = ref(null)
const show = ref(false)
const spot = ref({
    rate: '',
    rate_date: '',
})
const isWeekend = ref(false)
const postingDate = ref(dayjs().format('YYYY-MM-DD'))

const currencyLoading = ref(false)
const amount_tc_a = ref('')
const amount_tc_b = ref('')
const showSaveButton = ref(false)
const showTransactionCurrency = ref(false)
// mapActions
const fetchAccountDescDropdown = (payload: any) => store.dispatch('GlStore/getCoaListV1', payload)
const getSpot = (payload: any) => store.dispatch('Utils/getSpotv1', payload)

// computed
const accountDescList = computed(() =>
    store.state.GlStore.accountDescList?.filter(
        (item: any) => item.account_code !== '1060' && item.account_code !== '2621',
    ),
)
const accountCurrencyOptions = computed(() => store.state.CommonDropDownStore.bankCurrencyOptions)

const creditChange = (index: number) => {
    if (!form.value.line_items[index].amount_tc_2) return
    form.value.line_items[index].amount_tc_1 = 0
    form.value.line_items[index].dr_cr = 'cr'
}
const debitChange = (index: number) => {
    if (!form.value.line_items[index].amount_tc_1) return
    form.value.line_items[index].amount_tc_2 = 0
    form.value.line_items[index].dr_cr = 'dr'
}
const selectInputValues = (e: FocusEvent | Event) => {
    const target = e.target as HTMLInputElement // 使用类型断言告诉TypeScript不为空
    if (target) {
        target.select()
    }
}

const addItem = () => {
    if (!form.value.line_items) {
        form.value.line_items = []
    }
    // 添加item
    form.value.line_items.push({
        itemNo: form.value.line_items.length + 1,
        description: '', //暂时先用这个
        gl_account: undefined,
        neg_posting: false,
        amount_tc: 0,
        amount_tc_1: 0,
        amount_tc_2: 0,
        dr_cr: 'dr', // 'cr'
        business_partner: '',
    })
}

const getPopupContainer = (node: any) => {
    return tableWrapRef.value
}

const expenseAccountAlias = (row: any, accountDescList: any) => {
    let alias = ''
    accountDescList?.forEach((item: any) => {
        if (item.account_code === row.gl_account) {
            // alias = item.category ? item.category : item.name
            alias = item.name || ''
        }
    })
    return row.gl_account + ' | ' + alias
}
const remove = (index: number) => {
    form.value.line_items.splice(index, 1)
    form.value.line_items.forEach((item: any, index: number) => {
        item.itemNo = index + 1
    })
}
const emit = defineEmits(['save', 'dismiss', 'post', 'reverse', 'saveDraft'])

const saveDraft = async () => {
    if (!form.value.line_items || (form.value.line_items && form.value.line_items.length === 0)) {
        message.error({
            content: i18n.t('glEntry.msgAtLeastOne'),
            duration: 6,
        })
        return
    }
    if (await formRef.value?.validateFields()) {
        if (form.value.line_items[0].debit > ************* || form.value.line_items[0].credit > *************) {
            message.error({
                content: i18n.t('gl.msgNumberLimited'),
                duration: 6,
            })
            return
        }

        const queryForm = {..._.cloneDeep(form.value)}
        queryForm.line_items = queryForm.line_items.filter((i: any) => i.gl_account)
        queryForm.line_items.forEach((item: any) => {
            item.amount_tc = item.dr_cr === 'dr' ? item.amount_tc_1 : item.amount_tc_2
            delete item.amount_tc_1
            delete item.amount_tc_2
        })

        emit('saveDraft', queryForm)
        cancel()
    } else {
        return false
    }
}

const save = async () => {
    if (!form.value.line_items || (form.value.line_items && form.value.line_items.length === 0)) {
        message.error({
            content: i18n.t('glEntry.msgAtLeastOne'),
            duration: 6,
        })
        return
    }
    if (await formRef.value?.validateFields()) {
        if (form.value.line_items[0].debit > ************* || form.value.line_items[0].credit > *************) {
            message.error({
                content: i18n.t('gl.msgNumberLimited'),
                duration: 6,
            })
            return
        }

        const queryForm = {..._.cloneDeep(form.value)}
        queryForm.line_items = queryForm.line_items.filter((i: any) => i.gl_account)
        queryForm.line_items.forEach((item: any) => {
            item.amount_tc = item.dr_cr === 'dr' ? item.amount_tc_1 : item.amount_tc_2
            delete item.amount_tc_1
            delete item.amount_tc_2
        })

        emit('save', queryForm)
        cancel()
    } else {
        return false
    }
}
const post = async () => {
    if (!form.value.totalDebit || form.value.totalDebit == 0.0) {
        message.error({
            content: i18n.t('gl.msgTotalDebitCannotNull'),
            duration: 6,
        })
        return
    }
    if (!form.value.totalCredit || form.value.totalCredit == 0.0) {
        message.error({
            content: i18n.t('gl.msgTotalCreditCannotNull'),
            duration: 6,
        })
        return
    }
    if (form.value.totalCredit !== form.value.totalDebit) {
        message.error({
            content: i18n.t('gl.msgNotMatch'),
            duration: 6,
        })
        return
    }
    if ((await formRef.value?.validateFields()) && showSaveButton.value) {
        if (form.value.line_items[0].debit > ************* || form.value.line_items[0].credit > *************) {
            message.error({
                content: i18n.t('gl.msgNumberLimited'),
                duration: 8,
            })
            return
        }

        const queryForm = {..._.cloneDeep(form.value), creator: userInfo?.id || ''}
        queryForm.line_items = queryForm.line_items.filter((i: any) => i.gl_account)
        queryForm.line_items.forEach((item: any) => {
            item.amount_tc = item.dr_cr === 'dr' ? item.amount_tc_1 : item.amount_tc_2
            delete item.amount_tc_1
            delete item.amount_tc_2
        })

        emit('post', queryForm)
        cancel()
    } else {
        message.error({
            content: i18n.t('gl.msgNotMatch'),
            duration: 8,
        })
        return false
    }
}
const cancel = () => {
    emit('dismiss')
}
const reverse = async () => {
    emit('reverse')
}
const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}
const initFormData = () => {
    ;(formRef.value as any).resetFields()
    form.value.description = null
    form.value.posting_date = getToday()
    form.value.totalCredit = 0.0
    form.value.totalDebit = 0.0
    form.value.totalCreditCAD = 0.0
    form.value.totalDebitCAD = 0.0
    form.value.createDate = getToday()
    form.value.currency = localCurrency
    form.value.id = null
    form.value.line_items = []
    addItem()
}
const changeItemListRowExpenseAccount = (value: any, index: number) => {
    form.value.line_items[index].gl_account = String(value)
}

// currency change回调
const updateSpot = async () => {
    if (localCurrency === form.value.currency) return
    const baseCurrency = form.value.currency
    const quoteCurrency = localCurrency
    spot.value = await getSpot({baseCurrency, quoteCurrency, date: form.value.posting_date})
    // if (form.value.currency !== localCurrency) {
    // form.value.line_items.map((i: any) => {
    //     i.amount_tc_1 = +(i.amount_tc_1 * parseFloat(spot.value?.rate)).toFixed(2)
    //     i.amount_tc_2 = +(i.amount_tc_2 * parseFloat(spot.value?.rate)).toFixed(2)
    // })
    // }
}
const getSpotInputDateStatus = (date: moment.MomentInput) => {
    const weekOfday = moment(date, 'YYYY-MM-DD').format('E')
    return 5 - +weekOfday
}
const requireRule = (propName: any) => [
    {
        required: true,
        message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
        trigger: ['blur', 'change'],
    },
]
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}

const requireGLAccount = (rule: any, value: string | undefined) => {
    const index = Number(rule.field.match(/\.(\S*)\./)[1])
    const item = form.value.line_items[index]

    if (!value && (item.amount_tc_1 || item.amount_tc_2)) {
        return Promise.reject()
    } else {
        return Promise.resolve()
    }
}

const rules = reactive({
    header_text: [...requireRule(i18n.t('gl.narration'))],
    currency: [...requireRule(i18n.t('bkAp.currency'))],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            validator: requireGLAccount,
            trigger: ['blur', 'change'],
        },
    ],
    posting_date: [...requireRule(i18n.t('bkAp.postingDate'))],
})

//计算借贷差距
const differenceComputed = (deb: any, cre: any) => {
    let differNumber: any = ''
    const messageText = 'Difference:'
    if (deb > cre) {
        differNumber = messageText + (deb - cre)
    } else if (deb < cre) {
        differNumber = messageText + (cre - deb)
    }
    return differNumber
}
watch(
    () => form.value.line_items,
    newVal => {
        console.log('newVal', newVal)

        if (props.readonlyMode || !newVal) return
        const sumDebit = newVal.reduce((prev: any, curr: any, index: any) => {
            return parseFloat(prev) + parseFloat(curr.amount_tc_1)
        }, 0)
        console.log('watch sumDebit', +sumDebit.toFixed(2))

        form.value.totalDebit = sumDebit ? +sumDebit.toFixed(2) : undefined
        form.value.totalDebitCAD =
            form.value.totalDebit == undefined ? undefined : form.value.totalDebit * parseFloat(spot.value?.rate)

        const sumCredit = newVal.reduce((prev: any, curr: any, index: any) => {
            return parseFloat(prev) + parseFloat(curr.amount_tc_2)
        }, 0)
        form.value.totalCredit = sumCredit ? +sumCredit.toFixed(2) : undefined
        console.log('watch sumCredit', +sumCredit.toFixed(2))
        // 比较借贷金额
        if (
            form.value.totalCredit === form.value.totalDebit &&
            (form.value.totalCredit !== undefined || form.value.totalDebit !== undefined)
        ) {
            showSaveButton.value = true
        }
        form.value.totalCreditCAD =
            form.value.totalCredit == undefined ? undefined : form.value.totalCredit * parseFloat(spot.value?.rate)
    },
    {deep: true},
)
onBeforeMount(async () => {
    if (props.operationMode === 'apDetail') {
        form.value = {
            document_no: props.currentInvoice?.document_no,
            company_code: props.currentInvoice?.company,
            currency: props.currentInvoice?.currency,
            header_text: props.currentInvoice?.narration,
            line_items: props.currentInvoice?.ledger_entry_line,
            posting_date: props.currentInvoice?.posting_date,
            status: props.currentInvoice?.status,
            document_type: props.currentInvoice?.document_type,
        }
        form.value.line_items.forEach((item: any) => {
            item.ori_amount_tc = item.amount_in_document_currency

            item.amount_tc_1 = item.dr_cr === 'dr' ? item.amount_lc : '0' // 显示的时候 amount_tc 变成 amount_lc
            item.amount_tc_2 = item.dr_cr === 'cr' ? item.amount_lc : '0' // 显示的时候 amount_tc 变成 amount_lc

            item.ori_amount_tc_1 = item.dr_cr === 'dr' ? item.amount_in_document_currency : '0'
            item.ori_amount_tc_2 = item.dr_cr === 'cr' ? item.amount_in_document_currency : '0'
        })

        form.value.totalDebit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.amount_tc_1
        }, 0)
        form.value.oriTotalDebit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.ori_amount_tc_1
        }, 0)
        form.value.totalCredit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.amount_tc_2
        }, 0)
        form.value.oriTotalCredit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.ori_amount_tc_2
        }, 0)
    } else if (props.operationMode === 'editing') {
        form.value = {
            document_no: props.currentInvoice?.document_no,
            company_code: props.currentInvoice?.company,
            currency: props.currentInvoice?.currency,
            header_text: props.currentInvoice?.narration,
            line_items: props.currentInvoice?.ledger_entry_line,
            posting_date: props.currentInvoice?.posting_date,
            totalCredit: props.currentInvoice?.totalCrLc,
            totalCreditCAD: undefined,
            totalDebit: props.currentInvoice?.totalDrLc,
            totalDebitCAD: undefined,
        }
        form.value.line_items.forEach((item: any) => {
            item.amount_tc_1 = item.dr_cr === 'dr' ? item.amount_tc : '0'
            item.amount_tc_2 = item.dr_cr === 'cr' ? item.amount_tc : '0'
        })
    } else if (props.operationMode === 'detail') {
        form.value = {
            document_no: props.currentInvoice?.document_no,
            company_code: props.currentInvoice?.company,
            currency: props.currentInvoice?.currency,
            header_text: props.currentInvoice?.narration,
            line_items: props.currentInvoice?.ledger_entry_line,
            posting_date: props.currentInvoice?.posting_date,
            status: props.currentInvoice?.status,
            document_type: props.currentInvoice?.document_type,
        }
        form.value.line_items.forEach((item: any) => {
            item.ori_amount_tc = item.amount_in_document_currency

            item.amount_tc_1 = item.dr_cr === 'dr' ? item.amount_lc : '0' // 显示的时候 amount_tc 变成 amount_lc
            item.amount_tc_2 = item.dr_cr === 'cr' ? item.amount_lc : '0' // 显示的时候 amount_tc 变成 amount_lc

            item.ori_amount_tc_1 = item.dr_cr === 'dr' ? item.amount_in_document_currency : '0'
            item.ori_amount_tc_2 = item.dr_cr === 'cr' ? item.amount_in_document_currency : '0'
        })

        form.value.totalDebit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.amount_tc_1
        }, 0)
        form.value.oriTotalDebit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.ori_amount_tc_1
        }, 0)
        form.value.totalCredit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.amount_tc_2
        }, 0)
        form.value.oriTotalCredit = form.value.line_items.reduce((prev: any, curr: any, index: any) => {
            return prev + +curr.ori_amount_tc_2
        }, 0)
    } else if (props.operationMode === 'creating') {
        form.value.line_items.push({
            itemNo: form.value.line_items.length + 1,
            description: '', //暂时先用这个
            gl_account: undefined,
            neg_posting: false,
            amount_tc: 0,
            amount_tc_1: 0,
            amount_tc_2: 0,
            dr_cr: 'dr', // 'cr'
            business_partner: '',
        })

        addItem()
    }
    if (!props.readonlyMode) {
        form.value.createDate = getToday()
    }
    if (props.operationMode === 'creating') {
        form.value.posting_date = getToday()
    }
    await fetchAccountDescDropdown({company: userCompany[0].code})

    updateSpot()
})
onMounted(() => {
    ;(formRef.value as any).clearValidate()
})
const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}
defineExpose({initFormData})

const showInvoiceDetail = (inoviceNo: string, documentType: number, ledgerEntryLineElement: {dr_cr: string}) => {
    state.invoiceNo = inoviceNo
    if ('cr' === ledgerEntryLineElement.dr_cr) {
        state.showInvoiceModal = true
        state.showApArDetail = 'AP'
    } else if ('dr' === ledgerEntryLineElement.dr_cr) {
        state.showInvoiceModal = true
        state.showApArDetail = 'AR'
    } else {
        return
    }
}
const dismiss = () => {
    state.showInvoiceModal = false
}
</script>
<template>
    <div class="page-container-ap-invoice-form" id="scroll-box">
        <a-spin :spinning="formLoading">
            <a-form
                ref="formRef"
                :model="form"
                :rules="readonlyMode ? {} : rules"
                :layout="'vertical'"
                label-width="auto"
                label-position="top"
                class="form-box"
            >
                <div class="ap-invoice-block">
                    <a-row :gutter="24">
                        <a-col :span="8">
                            <!-- 第一个输入框值 -->
                            <a-form-item
                                :label="$t('gl.narration')"
                                name="header_text"
                                required
                                class="form-box-item_reference_no"
                            >
                                <template v-slot:label>
                                    {{ i18n.t('gl.narration') }}
                                    <a-tooltip placement="top">
                                        <template #title>
                                            <div>{{ i18n.t('gl.descriptionxxx') }}</div>
                                        </template>
                                        <question-circle-outlined class="el-icon-question" />
                                    </a-tooltip>
                                </template>
                                <a-input
                                    v-model:value="form.header_text"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    :disabled="readonlyMode"
                                >
                                </a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第二个输入框值 -->
                            <a-form-item :label="i18n.t('gl.currency')" class="form-box-item_currency">
                                <a-select
                                    :placeholder="i18n.t('workTimeManager.msgInput')"
                                    v-model:value="form.currency"
                                    @change="updateSpot"
                                    style="width: 100%"
                                    :disabled="readonlyMode"
                                >
                                    <a-select-option
                                        style="width: 100%"
                                        v-for="item in accountCurrencyOptions"
                                        :key="item.key"
                                        :value="item.key"
                                        clearable
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                                <div v-show="form.currency !== localCurrency" style="color: red; padding: 2px 5px">
                                    {{
                                        i18n.t('ApComponents.spotCurrency', {
                                            rate: spot.rate || 'null',
                                            date: form.posting_date ? form.posting_date : postingDate,
                                        })
                                    }}
                                    {{ !spot.rate ? ', ' + i18n.t('ApComponents.contactAdmin') : '' }}
                                </div>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第三个输入框时间 -->
                            <a-form-item
                                :label="i18n.t('gl.postingDate')"
                                :name="!readonlyMode ? 'postingDate' : ''"
                                class="form-box-item_posting_date"
                            >
                                <a-date-picker
                                    v-model:value="form.posting_date"
                                    type="date"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :disabled="readonlyMode"
                                    :placeholder="i18n.t('gl.postingDate')"
                                    style="width: 100%"
                                    clearable
                                    @change="updateSpot"
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="24" v-if="props.readonlyMode && props.currentInvoice?.checkButton">
                        <a-col :span="8" :offset="16">
                            <!-- TODO -->
                            <a-checkbox v-model:checked="showTransactionCurrency"
                                >{{ i18n.t('gl.transactionCurrency') }}{{ localCurrency }}</a-checkbox
                            >
                        </a-col>
                    </a-row>
                </div>

                <div class="ap-invoice-block" ref="tableWrapRef">
                    <a-table :dataSource="form.line_items" style="width: 100%" size="small" :pagination="false">
                        <a-table-column
                            data-index="itemNo"
                            :title="i18n.t('gl.itemNo')"
                            align="center"
                            header-align="center"
                            width="5%"
                        >
                            <template #default="{index}">
                                <span>
                                    {{ index + 1 }}
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column
                            data-index="description"
                            :title="i18n.t('gl.description')"
                            align="center"
                            header-align="left"
                            width="20%"
                            :ellipsis="true"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    style="margin-bottom: 0"
                                    :name="['line_items', index, 'description']"
                                    v-if="!readonlyMode"
                                >
                                    <a-input
                                        v-model:value="record.description"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :style="{width: operationMode === 'editing' ? '95%' : '100%'}"
                                        class="table-input"
                                    ></a-input>
                                </a-form-item>
                                <span class="" v-else>
                                    {{ record.description }}
                                </span>
                            </template>
                        </a-table-column>
                        <!-- gl_account枚举值 -->
                        <a-table-column
                            data-index="glAccount"
                            :title="i18n.t('gl.glAccount')"
                            align="center"
                            header-align="center"
                            width="27%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    style="margin-bottom: 0"
                                    :name="['line_items', index, 'gl_account']"
                                    :rules="rules['requireItemTypeSelect']"
                                    v-if="!readonlyMode"
                                >
                                    <a-select
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        v-model:value="record.gl_account"
                                        show-search
                                        :dropdownMatchSelectWidth="400"
                                        :getPopupContainer="getPopupContainer"
                                        :filter-option="filterOption"
                                        class="table-input"
                                        :style="{width: operationMode === 'editing' ? '95%' : '100%'}"
                                        @change="changeItemListRowExpenseAccount(record.gl_account, index)"
                                    >
                                        <a-select-option
                                            v-for="item in accountDescList"
                                            :key="item.account_code.substring(0, 4) + ' | ' + `${item.name || ''}`"
                                            :value="item.account_code"
                                            >{{
                                                item.account_code.substring(0, 4) + ' | ' + `${item.name || ''}`
                                            }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                                <span v-else>
                                    {{ expenseAccountAlias(record, accountDescList) }}
                                </span>
                            </template>
                        </a-table-column>

                        <a-table-column
                            data-index="amount_tc"
                            :title="i18n.t('gl.debit')"
                            align="center"
                            header-align="center"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    style="margin-bottom: 0"
                                    :name="['line_items', index, 'amount_tc']"
                                    v-if="!readonlyMode"
                                >
                                    <!-- <a-input-number
                                        ref="debitDom"
                                        v-model:value="record.amount_tc_1"
                                        :precision="2"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :formatter="formatNumber"
                                        :parser="parseNumber"
                                        class="w'e'we'wet"
                                        :style="{width: operationMode === 'editing' ? '95%' : '100%'}"
                                        @change="debitChange(index)"
                                        @focus="selectInputValues"
                                    ></a-input-number> -->
                                    <InputNumber
                                        ref="debitDom"
                                        class="amount-input-prime"
                                        v-model="record.amount_tc_1"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :disabled="props.readonlyMode"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        :locale="decimalFormat()"
                                        fluid
                                        @value-change="debitChange(index)"
                                        @focus="selectInputValues"
                                    />
                                </a-form-item>
                                <span v-else>
                                    {{
                                        showTransactionCurrency
                                            ? record.dr_cr === 'cr'
                                                ? '0.00'
                                                : $formatNumber(Number(record.amount_lc))
                                            : record.dr_cr === 'cr'
                                            ? '0.00'
                                            : $formatNumber(Number(record.ori_amount_tc))
                                    }}
                                </span>
                            </template>
                        </a-table-column>

                        <a-table-column
                            data-index="amount_tc"
                            :title="i18n.t('gl.credit')"
                            align="center"
                            header-align="center"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    style="margin-bottom: 0"
                                    :name="['line_items', index, 'amount_tc']"
                                    v-if="!readonlyMode"
                                >
                                    <!-- <a-input-number
                                        v-model:value="record.amount_tc_2"
                                        :precision="2"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :formatter="formatNumber"
                                        :parser="parseNumber"
                                        class="table-input"
                                        style="width: 100%"
                                        @change="creditChange(index)"
                                        @focus="selectInputValues"
                                    ></a-input-number> -->
                                    <InputNumber
                                        ref="debitDom"
                                        class="amount-input-prime"
                                        v-model="record.amount_tc_2"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        :locale="decimalFormat()"
                                        fluid
                                        @value-change="creditChange(index)"
                                        @focus="selectInputValues"
                                    />
                                </a-form-item>
                                <span v-else>
                                    {{
                                        showTransactionCurrency
                                            ? record.dr_cr === 'dr'
                                                ? '0.00'
                                                : $formatNumber(Number(record.amount_lc))
                                            : record.dr_cr === 'dr'
                                            ? '0.00'
                                            : $formatNumber(Number(record.ori_amount_tc))
                                    }}
                                </span>
                            </template>
                        </a-table-column>

                        <a-table-column v-if="!readonlyMode" key="operation" title="" align="center">
                            <template #default="{index}">
                                <span>
                                    <a-button
                                        :disabled="form.line_items.length === 1"
                                        type="link"
                                        danger
                                        @click="remove(index)"
                                    >
                                        <svg-icon name="icon_delete"></svg-icon>
                                    </a-button>
                                </span>
                            </template>
                        </a-table-column>
                    </a-table>
                    <a-button
                        v-show="!readonlyMode"
                        class="invoice-add"
                        type="primary"
                        ghost
                        @click="addItem"
                        :disabled="isDisable"
                    >
                        <template #icon>
                            <plus-outlined />
                        </template>
                        {{ i18n.t('bkAp.addItemBtnTxt') }}
                    </a-button>
                    <div class="total-wrap">
                        <div class="total-item">
                            <div class="amount">
                                <span>{{ i18n.t('glEntry.totalDebit') }}: </span>
                            </div>
                            <div class="total_box">
                                <a-input-number
                                    :value="
                                        readonlyMode
                                            ? showTransactionCurrency
                                                ? form.totalDebit
                                                : form.oriTotalDebit
                                            : form.totalDebit
                                    "
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    :disabled="true"
                                    size="mini"
                                    style="width: 100%"
                                    class="hover-input-class"
                                ></a-input-number>
                            </div>
                            <!-- <div
                                class="difference_message"
                                v-if="form.totalDebit > form.totalCredit && props.operationMode !== 'apDetail'"
                            >
                                <span>
                                    {{ differenceComputed(form.totalDebit, form.totalCredit) }}
                                </span>
                            </div> -->
                        </div>
                        <div class="total-item">
                            <div class="totalCad">
                                <span>{{ i18n.t('glEntry.totalCredit') }}: </span>
                            </div>
                            <div class="total_box">
                                <a-input-number
                                    :value="
                                        readonlyMode
                                            ? showTransactionCurrency
                                                ? form.totalCredit
                                                : form.oriTotalCredit
                                            : form.totalCredit
                                    "
                                    :controls="false"
                                    :precision="2"
                                    :formatter="formatNumber"
                                    :parser="parseNumber"
                                    :disabled="true"
                                    size="mini"
                                    class="hover-input-class"
                                    style="width: 100%"
                                ></a-input-number>
                            </div>
                            <!-- <div
                                class="difference_message"
                                v-if="form.totalCredit > form.totalDebit && props.operationMode !== 'apDetail'"
                            >
                                <span>
                                    {{ differenceComputed(form.totalDebit, form.totalCredit) }}
                                </span>
                            </div> -->
                        </div>
                    </div>
                    <!-- <a-row :gutter="24" v-show="form.currency == '3'">
                        <a-col :span="8">
                            <div
                                class="amount"
                                style="font-weight: bold; position: relative; top: 5px; margin: 20px 0 10px"
                            >
                                {{ i18n.t('glEntry.totalDebit') }} CAD:
                            </div>
                            <div class="amount">
                                <a-input-number
                                    v-model:value="form.totalDebitCAD"
                                    :controls="false"
                                    :precision="2"
                                    :disabled="true"
                                    size="mini"
                                    style="width: 100%"
                                    class="hover-input-class"
                                ></a-input-number>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <div
                                class="totalCad"
                                style="font-weight: bold; position: relative; top: 5px; margin: 20px 0 10px"
                            >
                                {{ i18n.t('glEntry.totalCredit') }} CAD:
                            </div>
                            <div>
                                <a-input-number
                                    v-model:value="form.totalCreditCAD"
                                    :controls="false"
                                    :precision="2"
                                    :disabled="true"
                                    size="mini"
                                    class="hover-input-class"
                                    style="width: 100%"
                                ></a-input-number>
                            </div>
                        </a-col>
                    </a-row> -->
                </div>

                <div class="amount-block"></div>
                <div class="amount-block"></div>
            </a-form>
        </a-spin>

        <div class="ap-invoice-footer">
            <span
                v-if="props.currentInvoice?.ref_document"
                class="aClass"
                @click="
                    showInvoiceDetail(
                        props.currentInvoice?.ref_document,
                        props.currentInvoice?.document_type,
                        props.currentInvoice?.ledger_entry_line[0],
                    )
                "
            >
                Invoice - {{ props.currentInvoice?.ref_document }}
            </span>

            <div class="divClass">
                <a-button v-if="readonlyMode" class="cancel-button" type="text" shape="round" @click="cancel"
                    >{{ i18n.t('commonTag.cancel') }}
                </a-button>
                <a-button
                    v-if="readonlyMode && props.operationMode !== 'apDetail'"
                    :disabled="form.document_type !== 0 || form.status == 2"
                    :class="form.document_type !== 0 || form.status == 2 ? 'reverse-button-disable' : 'reverse-button'"
                    type="text"
                    shape="round"
                    @click="reverse"
                    >{{ i18n.t('commonTag.reverse') }}
                </a-button>
                <a-button v-if="!readonlyMode" type="primary" shape="round" @click="saveDraft" :loading="formLoading">{{
                    i18n.t('gl.saveDraft')
                }}</a-button>
                <a-button
                    v-show="!readonlyMode"
                    :disabled="form.currency !== localCurrency && !spot.rate"
                    shape="round"
                    type="primary"
                    @click="post"
                    :loading="formLoading"
                >
                    {{ i18n.t('gl.post') }}
                </a-button>

                <a-button
                    v-show="!readonlyMode"
                    :disabled="form.currency !== localCurrency && !spot.rate"
                    shape="round"
                    type="primary"
                    @click="cancel"
                    :loading="formLoading"
                >
                    {{ i18n.t('commonTag.cancel') }}
                </a-button>
            </div>
        </div>
    </div>

    <a-modal
        :title="i18n.t('bkApInvoice.readonly')"
        v-model:visible="state.showInvoiceModal"
        :footer="null"
        destroyOnClose
        :closeable="true"
        :width="'1110px'"
        :dialogStyle="{top: '10px'}"
        :bodyStyle="{padding: '10px 24px 24px'}"
        :wrapClassName="'modal-wrap'"
    >
        <ap-invoice-component
            v-if="state.showApArDetail === 'AP'"
            current-invoice=""
            :invoice-no="state.invoiceNo"
            :readonly-mode="true"
            @dismiss="dismiss"
        ></ap-invoice-component>
        <ar-invoice-component
            v-if="state.showApArDetail === 'AR'"
            :current-invoice="state.invoiceNo"
            :invoice-no="state.invoiceNo"
            :readonly-mode="true"
        ></ar-invoice-component>
    </a-modal>
</template>
<style lang="scss" scoped>
.ap-invoice-block {
    padding: 24px 0;
    border-bottom: 1px solid #e2e2ea;

    &:first-child {
        padding-top: 0;
    }

    .total-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 20px;
        margin-bottom: 10px;

        .total-item {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            .totalCad,
            .amount {
                font-weight: bold;
            }
        }
    }
}

:deep(.ap-invoice-block .ant-col.ant-form-item-label) {
    height: 31.5px;
}

.difference_message {
    color: #ff0000;
}

.el-icon-question {
    margin: auto;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #2ead2b;
    margin-left: 5px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}

.ap-invoice-footer {
    width: 100%;
    display: flex;
    margin-top: 20px;

    .aClass {
        text-align: left;
        width: 500px;
        line-height: 30px;
        justify-content: center;
        color: #004fc1;
        cursor: pointer;
    }

    .divClass {
        justify-content: flex-end;
        display: flex;
        width: 100%; //calc(100% - 200px);

        .cancel-button {
            border-color: #004fc1;
            color: #004fc1;
        }

        .reverse-button {
            background-color: #004fc1;
            color: #fff;
        }

        .reverse-button-disable {
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.25);
            border-color: #c3c7d4;
        }

        .ant-btn + .ant-btn {
            margin-left: 12px;
        }
    }
}

.total_box {
    display: flex;
    align-items: center;
    margin: 0px 10px;
}

.inputWidth_f_o {
    width: 200px;
}

.inputWidth_f_t {
    width: 272px;
}

:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -10px;
    font-size: 12px;
    line-height: 12px;
    min-height: 12px;
    display: none;
}

.amount-input-prime {
    width: 162px;
    height: 33px;
    border-radius: 6px;
    color: #333;
    background-color: #fff;
    &.item-amount {
        max-width: 100%;
        background-color: #f5f7f9;
    }
}

:deep(.p-inputnumber input) {
    font-size: 14px;
    padding-left: 10px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border: 1px solid #4096ff;
        box-shadow: 0 0 0 2px #0591ff1a;
    }
}

:deep(.p-inputnumber.item-amount input) {
    border-color: #f5f7f9;
    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border-color: #216fcf;
    }
}

:deep(.p-inputnumber.item-amount input::placeholder) {
    color: #bbb;
}
</style>
