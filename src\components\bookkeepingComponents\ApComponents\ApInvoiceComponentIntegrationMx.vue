<!-- @format -->

<script setup lang="ts">
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {
    computed,
    nextTick,
    onBeforeMount,
    onMounted,
    onUnmounted,
    reactive,
    ref,
    watch,
    watchEffect,
    provide,
    type CSSProperties,
    getCurrentInstance,
} from 'vue'
import {useStore} from 'vuex'
import {
    PlusCircleOutlined,
    ExclamationCircleOutlined,
    PlusOutlined,
    ExclamationCircleFilled,
    DownOutlined,
    CheckOutlined,
    CloseOutlined,
    SendOutlined,
} from '@ant-design/icons-vue'
import {message, Modal, type FormInstance, type UploadChangeParam, type MenuProps} from 'ant-design-vue'
import DateDelay from '../CommonComponents/DateDelay.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import PdfViewer from '@/components/bookkeepingComponents/PdfViewer.vue'
import moment from 'moment'
import * as _ from 'lodash'
import {
    UserCompany,
    UserInfo,
    LocalCurrency,
    FinancialYear,
    Ap_Integration,
    Payment_Integration,
    SapPayableGl,
    SapPayableWbs,
    SapPayableCostCenter,
    SapPayableInternalOrder,
    SapPayableProfitCenter,
} from '@/lib/storage'
import dayjs from 'dayjs'
import {useDraggable} from '@vueuse/core'
import SupplierForm from '@/components/bookkeepingComponents/CommonComponents/SupplierForm.vue'
import GlComponent from '@/components/bookkeepingComponents/GlComponents/GlComponent.vue'
import FileSaver from 'file-saver'
import PaymentDiscountTerm from '@/components/bookkeepingComponents/ApComponents/PaymentDiscountTerm.vue'
import UploadFileComp from '@/components/bookkeepingComponents/UploadFileComp.vue'
import UploadFileCompMx from '@/components/bookkeepingComponents/UploadFileCompMx.vue'

import Decimal from 'decimal.js'
import {} from 'vue'
import InputNumber from 'primevue/inputnumber'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'

const {appContext} = getCurrentInstance()!
const formatNumber = appContext.config.globalProperties.$formatNumber
const parseNumber = appContext.config.globalProperties.$parseNumber
const decimalFormat = appContext.config.globalProperties.$decimalFormat
const restrictFileType: string[] = ['pdf', 'jpg', 'jpeg', 'png']
const showUploadModall = ref(false)
const uploadProcessing = ref(false)
const uploadComp = ref<{uploadFiles: () => void}>()

const modalTitleRef = ref<HTMLElement | null>(null)
const {x, y, isDragging} = useDraggable(modalTitleRef)
const startedDrag = ref(false)
const startX = ref<number>(0)
const startY = ref<number>(0)
const transformX = ref(0)
const transformY = ref(0)
const preTransformX = ref(0)
const preTransformY = ref(0)
const dragRect = ref({left: 0, right: 0, top: 0, bottom: 0})

const userCompany: any = UserCompany.get() || []
const financialYear = FinancialYear.get() ? moment(FinancialYear.get()).month() : null
const userInfo: any = UserInfo.get() || {}
const apIntegration: any = Ap_Integration.get() ?? 0
const sapPayableGl: any = SapPayableGl.get() || {} || undefined
const sapPayableProfitCenter: any = SapPayableProfitCenter.get() || {}
const sapPayableWbs: any = SapPayableWbs.get() || {} || undefined
const sapPayableCostCenter: any = SapPayableCostCenter.get() || {}
const sapPayableInternalOrder: any = SapPayableInternalOrder.get() || {}
const paymentIntegration: any = Payment_Integration.get() ?? 0
const localCurrency = LocalCurrency.get() || 'CAD'
const i18n: Composer = i18nInstance.global
const store = useStore()
const fileNameList = computed(() => {
    if (!form.value.file_name) return []
    return form.value.file_name.split(',').map(name => name.trim())
})
const props = withDefaults(
    defineProps<{
        currentInvoice: any
        readonlyMode: boolean
        operationMode: string
        invoiceId?: string
        invoiceNo?: string
        from: string
        // pageType: string
    }>(),
    {
        readonlyMode: false,
        operationMode: 'creating',
        invoiceId: '',
        inoviceNo: '',
        from: '',
        // pageType: '',
    },
)

const emits = defineEmits(['save', 'dismiss', 'reverse', 'update', 'realize', 'copy'])
const accountQuery = {bk_type: 2, company_code: userCompany[0].code, $limit: -1, del_flag: 0}
const VNodes = (_: any, {attrs}: any) => {
    return attrs.vnodes
}
const reverseDt = ref('')
const reverseSapDocNo = ref('')
const formRef = ref<FormInstance>()
const tableWrapRef = ref()
const compNameValue = ref('')
const showFileViewer = ref(false)
const selectedFileUrl = ref('') // selected file url
const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}

const userType = ref('')
const enableReverse = ref(false)
// const form_diff = computed(() => Number(form.value.total_fee_local) - Number(form.value.total_fee))
const form_diff = computed(() =>
    Math.abs(
        Number(form.value.total_fee_local) -
            Number((Number(form.value.total_fee || 0) * parseFloat(spot.value.rate || '1')).toFixed(2)),
    ),
)
const currentEditItem = ref<any>({
    item_no: '',
    model: '',
    description: '',
    costObject: '',
    qty: null,
    unit_price: null,
    total: null,
    type: '',
    dr_cr: 'dr',
    credit_coa_id: null,
    credit_coa_code: '',
    credit_coa_name: '',
    sap_gl_account: '',
    sap_wbs: '',
    sap_cost_center: '',
    sap_internal_order: '',
    sap_profit_center: '',
})
const form = ref<any>({
    id: null,
    purpose: null,
    pay_method: apIntegration === 1 ? '2' : '1',
    company_name: undefined as string | undefined,
    company_code: '',
    company_id: '',
    company_email: '',
    company_phone: '',
    issuer_name: '',
    issuer_tel: '',
    issuer_email: '',
    reference_no: '',
    tax_content: [],
    invoice_currency: localCurrency,
    invoice_create_date: '',
    invoice_due_date: '',
    posting_date: '',
    invoice_date: '',
    items: [] as any[],
    amount: null as number | null,
    net_amount: null as number | null,
    file_id: 0,
    file_url: '',
    xml_url: '',
    file_name: '',
    creator: '',
    engine_document_id: '' || [] || null,
    cash_engine_payment_no: '' || null,
    engine_reverse_document_id: '' || (null as any),
    // amountFee: null as number | null,
    //totalTaxable: null as number | null,
    total_tax: 0 as number | null,
    total_fee: 0 as number | null,
    total_fee_local: 0 as number | null,
    shipping: null,
    // discount: null,
    // balance: null,
    // deposit: null,
    invoice_comments: '',
    issuer_id: '',
    issuer_address: '',
    invoice_no: '',
    br_type: '',
    po: '',
    payment_term_discount: '',
    exchange_rate: null,
    mx_discount: 0,
    mx_isr: 0,
    mx_iva: 0,
})
const invoiceDate = ref('')

// const taxCodeOptions = [
//     { label: 'I0, A/P 0%', value: 'I0' },
//     { label: 'I3, A/P 5% GST, 9.975% QST', value: 'I3' },
//     { label: 'I4, A/P 13% HST', value: 'I4' },
//     { label: 'I6, A/P 5% GST', value: 'I6' },
//     { label: 'I7, A/P 5% GST 7% PST', value: 'I7' },
//     { label: 'IB, A/P 15% HST', value: 'IB' },
//     { label: '', value: '' } // 空行选项
// ];

// 辅助函数：获取展示的标签（在 readonly 模式下显示完整标签）
const getTaxCodeLabel = computed(() => {
    return (value: string) => {
        if (!state.masterData || !state.masterData.ET_TAX_CODE || !Array.isArray(state.masterData.ET_TAX_CODE)) {
            return value
        }
        const option = state.masterData.ET_TAX_CODE.find((opt: any) => opt.TAXCODE === value)
        return option ? option.TAXCODE + ', ' + option.DESCRIPTION : value
    }
})

provide('invoiceDate', invoiceDate)

watch(
    () => form.value.posting_date,
    newValue => {
        // 当 form.value.posting_date 变化时更新提供的值
        invoiceDate.value = newValue
    },
)
const payMethodOptions = reactive([
    {
        value: '1',
        label: i18n.t('ApComponents.bank'), // 'NOT PAID',
    },
    {
        value: '2',
        label: i18n.t('ApComponents.cashpaid'), //'CASH PAID',
    },
    // {
    //     value: '3',
    //     label: 'FUNDING TRANSFER',
    // },
    // {
    //     value: '4',
    //     label: 'INTERCOM',
    // },
])
const purposeOptions = reactive([
    {
        value: 'STANDARD',
        label: i18n.t('bkAp.purposeStandard'), // 'Standard',
    },
    {
        value: 'PROFORMA',
        label: i18n.t('bkAp.purposeProforma'), //'Proforma'
    },
    {
        value: 'CREDIT_MEMO',
        label: i18n.t('bkAp.purposeCreditMemo'), //'Credit Memo'
    },
    // {
    //     value: 'SUBSEQUENT_CREDIT_MEMO',
    //     label: i18n.t('bkAp.purposeSubsequentCreditMemo'), //'Subsequent Credit Memo'
    // },
])
const payMethodApIntegrationOptions = reactive([
    {
        value: '1',
        label: i18n.t('ApComponents.bank'), // 'BANK',
    },
    {
        value: '3',
        label: i18n.t('ApComponents.check'), //'CHECK'
    },
    {
        value: '2',
        label: i18n.t('ApComponents.credit'), //'CREDIT'
    },
])
const typeOptions = reactive([
    {
        value: '1',
        label: i18n.t('ApComponents.wbs'), // 'WBS',
    },
    {
        value: '2',
        label: i18n.t('ApComponents.costCenter'), //'Cost Center',
    },
])
const downloadFileloading = ref(false)
const countryCodeCheck = ref(false)
const spotRateCheck = ref(false)
const taxRateCheck = ref(false)
const isDisable = computed(() => {
    if (form.value.id === null || form.value.id === undefined) {
        return !(
            countryCodeCheck.value &&
            spotRateCheck.value &&
            taxRateCheck.value &&
            satCertification.value !== 'PENDING'
        )
    } else {
        return false
    }
})
const isWeekend = ref(false)
const postingDate = ref(dayjs().format('YYYY-MM-DD'))
const fixedDate = ref(0)
const formLoading = ref(false)
const currencyLoading = ref(false)
const referenceNoLoading = ref(false)
const current = reactive({
    contact_name: '',
})
const glCurrent = ref({})
const show = ref(false)
const autoCalTaxFlag = ref('1') // '1': change value from "Total"; '0': change value from 'item list';'2': change value from bill province selected
const autoCalculateState = ref(true)
const enableTaxExempt = ref(false)
// const additional = ref(['','','','','','','','','',''])
const spot = ref({
    rate: '',
    rate_date: '',
})

const total_diff = ref(0)
const prev_total = ref(-1)

const contactPageSize = ref(10)
const contactLoading = ref(false)
const contactKeyword = ref('')
const satCertification = ref('') // '' - false; 'PENDING' - processing; 'SUCCESS' - success; 'FAILURE' - fail
const isContactKeywordEmpty = computed(() => contactKeyword.value == null || contactKeyword.value.length === 0)
const poLoading = ref(false)
const poKeyword = ref('')
const isPoKeywordEmpty = computed(() => poKeyword.value == null || poKeyword.value.length === 0)
const sapMasterDataTopNew = ref({
    gl_account_code: '',
    wbs_code: '',
    cost_center_code: '',
    internal_order_code: '',
    profit_center_code: '',
})

const showReverseLoading = ref(false)
const showConvertLoading = ref(false)
const realizeLoading = ref(false)

const showConfirm = ref(false)
const showConvert = ref(false)
const showUploadConfirm = ref(false)
const showGlDialog = ref(false)
const confirmationWrap = ref<HTMLElement | null>(null)
const confirmationWrap2 = ref<HTMLElement | null>(null)
const convertWrap = ref<HTMLElement | null>(null)

const confirmText = i18n.t('ApComponents.confirm') //'Please confirm to reverse'
const confirmText2 = i18n.t('ApComponents.confirm2')
const confirmConvert = i18n.t('ArComponents.confirmConvert') //'Please confirm to reverse'

const fileTypes = ['.png', '.jpg', '.jpeg', '.xlsx', '.xls', '.pdf']
const fileList = ref([] as any[])
const uploadFileList = ref([] as any[])
const uploadState = ref(false)
const uploadDisabled = ref(false)
const glReadonlyMode = ref(true)
const glOperationMode = ref('apDetail')

const discount_saved = ref(false)
const enableDiscountModal = ref(false)
const itemEditModal = ref(false)
const payment_discounts_label = ref('')
const payment_discounts_label_format = ref('')

const newIssuerName = ref('')

const poNumberLoading = ref(false)
const popConfirmModal = () => {
    if (apIntegration === 1 && (form.value.br_flag === 1 || form.value.br_flag === 2)) {
        message.error(i18n.t('ApComponents.confirm3'))
        return
    }
    showConfirm.value = true
}
const popConvertModal = () => {
    showConvert.value = true
}
const popUploadConfirmModal = () => {
    if (!form.value.file_url) {
        showUploadModall.value = true
    } else {
        showUploadConfirm.value = true
    }
}
const delay = (callback: () => void, ms: number) => {
    setTimeout(callback, ms)
}
const confirmationConfirm = async () => {
    showReverseLoading.value = true
    await reversePurchase(false)
}

const convertConfirm = async () => {
    showConvertLoading.value = true
    await convertPurchase()
}

const confirmationCancel = () => {
    showConfirm.value = false
    return
}
const convertCancel = () => {
    showConvert.value = false
    return
}

const uploadConfirmationConfirm = async () => {
    showUploadConfirm.value = false
    showUploadModall.value = true
}

const uploadConfirmationCancel = () => {
    showUploadConfirm.value = false
}

const state = reactive({
    supplierList: apIntegration === 1 ? [] : computed(() => store.state.CommonDropDownStore.supplierOptions),
    localSupplierList: computed(() => store.state.CommonDropDownStore.supplierOptions),
    masterData: computed(() => store.state.ApStore.sapMasterData),
    poTopListData: computed(() => store.state.ApStore.poTopList),
    poOptionList: [] as string[],
    invoiceFlowData: computed(() => store.state.ApStore.invoiceFlow),
    paymentFlowData: computed(() => store.state.ApStore.paymentFlow),
})

const updatePdfInfo = (data: any) => store.commit('ArApBrStore/updatePdfInfo', data)
const updateApTopCoa = (data: any) => store.commit('ApStore/updateApTopCoa', data)
const updateTaxRates = (data: any) => store.commit('TaxCalculationStore/updateTaxRatesList', data)
const getFileBlobById = (data: any) => store.dispatch('ArApBrStore/getFileBlobByIdV1', data)
const reversePurchaseRequest = (payload?: any) => store.dispatch('ApStore/reversePurchaseRequestV1', payload)
const reverseIntegrationPurchaseRequest = (payload?: any) =>
    store.dispatch('ApStore/reverseIntegrationPurchaseRequestV1', payload)
const reverseIntegrationPurchaseWithPoRequest = (payload?: any) =>
    store.dispatch('ApStore/reverseIntegrationPurchaseWithPoRequestV1', payload)
const convertPurchaseRequest = (payload?: any) => store.dispatch('ApStore/convertPurchaseRequestV1', payload)
const fetchApIntegrationPoTop = (payload?: any) => store.dispatch('ApStore/fetchApIntegrationPoTop', payload)
const fetchTaxCodeList = (payload: any) => store.dispatch('ApStore/fetchTaxCodeDropdown', payload)

const updateIntegrationSapStatus = (data: any) => store.dispatch('ApStore/updateIntegrationSapStatusV1', data)
const sendIntegrationReverseDataToSap = (data: any) => store.dispatch('ApStore/sendApReverseDataToSap', data)
const sendIntegrationReverseDataToSapWithCompanyCode = (data: any) =>
    store.dispatch('ApStore/sendApReverseDataToSapWithCompanyCode', data)

const fetchSupplierDropDown = (query?: any) => store.dispatch('CommonDropDownStore/fetchContactDropDown', query)
const fetchApSapContactDropDown = (query?: any) => store.dispatch('ApStore/fetchApSapContactData', query)
const fetchInvoiceFlowData = (query?: any) => store.dispatch('ApStore/fetchInvoiceFlowData', query)
const fetchPaymentFlowData = (query?: any) => store.dispatch('ApStore/fetchPaymentFlowData', query)
const fetchSapMasterData = (query?: any) => store.dispatch('ApStore/fetchApSapMasterData', query)
const fetchSapPoInfo = (query?: any) => store.dispatch('ApStore/fetchSapPoInfoV1', query)
const fetchSapProfomaPoInfo = (query?: any) => store.dispatch('ApStore/fetchSapProfomaPoInfoV1', query)
const fetchSapCmPoInfo = (query?: any) => store.dispatch('ApStore/fetchSapCmPoInfoV1', query)
const fetchAccountDescDropdown = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)
const fetchAllBankList = (query?: any) => store.dispatch('BankInfoStore/fetchAllBankListV1', query)
const checkReferenceNoRepetition = (query?: any) => store.dispatch('ApStore/checkReferenceNoRepetitionV1', query)
const fetchApTopCoa = (query?: any) => store.dispatch('ApStore/fetchApTopCoaV1', query)
const fetchTaxRates = (query?: any) => store.dispatch('TaxCalculationStore/fetchTaxRates2', query)
const fetchCompanyTaxInfo = (query?: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
const getSpot = (query?: any) => store.dispatch('Utils/getSpotv1', query)
const uploadApInvoicePdf = (data: FormData) => store.dispatch('ApStore/uploadApInvoicePdfV1', data)
const uploadApInvoicePdfMX = (data: FormData) => store.dispatch('ApStore/uploadApInvoicePdfV1MX', data)
const fetchApInvoiceDetail = (query: any) => store.dispatch('ApStore/fetchApInvoiceDetailV1', query)
const fetchGlList = (payload: any) => store.dispatch('GlStore/fetchGlListV1', payload)
const deleteInvoiceWithId = (Id: string) => store.dispatch('ApStore/deleteInvoiceWithoutSapIdV1', Id)
const fetchSapMasterDataTop = (query: any) => store.dispatch('ApStore/fetchSapMasterTopV1', query)
const postSapMasterDataTop = (data: any) => store.dispatch('ApStore/postSapMasterTopV1', data)

const fetchPONumberData = (payload: any) => store.dispatch('ApStore/fetchPoData', payload)

// vuex mutations
const fetchOcrInvoiceItem = (data: {file_id: string}) => {
    return store.dispatch('ApStore/fetchApOcrResultByPdfIdV1', data)
}

const invoiceDetail = computed(() => store.state.ApStore.invoiceDetail)

// let supplierList: any = apIntegration === 1 ? [] : computed(() => store.state.CommonDropDownStore.supplierOptions)
const accountDescList: any = computed(() => store.state.CommonDropDownStore.accountDescList)
const accountCurrencyOptions: any = computed(() => store.state.CommonDropDownStore.bankCurrencyOptions)
const currentPdfInfo: any = computed(() => store.state.ArApBrStore.pdfInfo)
const apTopCoaList: any = computed(() => store.state.ApStore.apTopCoaList)
const taxRatesList: any = computed(() => {
    return _.cloneDeep(store.state.TaxCalculationStore.taxRatesList).map((i: any) => {
        return {...i, value: new Decimal(i.value).div(new Decimal(100)).toNumber()}
    })
})
const companyTaxInfo: any = computed(() => store.state.TaxInfoStore.companyTaxInfo)
const ocrInvoiceItem = computed(() => store.state.ApStore.ocrInvoiceItemByFile)
const addItem = () => {
    // console.log('sssssssssssssssssssssssssssss', form.value)
    if (!form.value.items) {
        form.value.items = []
    }

    let credit_coa_id = null
    let credit_coa_code = ''
    let credit_coa_name = ''

    if (apTopCoaList.value.length > 0) {
        const id = apTopCoaList.value[0].credit_coa_id
        const code = apTopCoaList.value[0].credit_coa_code

        const coaItem = accountDescList.value.find((x: any) => x.account_code == code)

        credit_coa_id = id
        credit_coa_code = coaItem?.account_code
        credit_coa_name = coaItem?.name
    }

    form.value.items.push({
        item_no: form.value.items.length + 1,
        model: '',
        description: '',
        costObject: '',
        qty: null,
        unit_price: null,
        total: null,
        type: '',
        dr_cr: 'dr',
        // expenseAccount: apTopCoaList.value.length > 0 ? String(apTopCoaList.value[0]) : undefined,
        // expenseAccountId: apTopCoaList.value.length > 0 ? apTopCoaList.value[0] : undefined,
        // debit_coa_id: apTopCoaList.value.length > 0 ? String(apTopCoaList.value[0]) : undefined,
        // debit_coa_code: apTopCoaList.value.length > 0 ? apTopCoaList.value[0] : undefined,
        credit_coa_id: credit_coa_id,
        credit_coa_code: credit_coa_code,
        credit_coa_name: credit_coa_name,
        sap_gl_account: sapMasterDataTopNew.value.gl_account_code,
        sap_wbs: sapMasterDataTopNew.value.wbs_code,
        sap_cost_center: sapMasterDataTopNew.value.cost_center_code,
        sap_internal_order: sapMasterDataTopNew.value.internal_order_code,
        sap_profit_center: sapMasterDataTopNew.value.profit_center_code,
    })
    // console.log('ttttttttttttttttttttttttttttttt', form.value)
}

const remove = (index: number) => {
    form.value.items.splice(index, 1)
    form.value.items.forEach((item: any, index: number) => {
        item.item_no = index + 1
    })
    // this.form.value.discount = null
}

const edit = (row: any) => {
    currentEditItem.value.id = row.id
    currentEditItem.value.item_no = row.item_no
    currentEditItem.value.model = row.model
    currentEditItem.value.description = row.description
    currentEditItem.value.costObject = row.costObject
    currentEditItem.value.qty = row.qty
    currentEditItem.value.unit_price = row.unit_price
    currentEditItem.value.total = row.total
    currentEditItem.value.type = row.type
    currentEditItem.value.dr_cr = row.dr_cr
    currentEditItem.value.credit_coa_id = row.credit_coa_id
    currentEditItem.value.credit_coa_code = row.credit_coa_code
    currentEditItem.value.credit_coa_name = row.credit_coa_name
    currentEditItem.value.internalOrder = row.internalOrder
    currentEditItem.value.profileCenter = row.profileCenter
    currentEditItem.value.sap_gl_account = row.sap_gl_account
    currentEditItem.value.sap_wbs = row.sap_wbs
    currentEditItem.value.sap_cost_center = row.sap_cost_center
    currentEditItem.value.sap_internal_order = row.sap_internal_order
    currentEditItem.value.sap_profit_center = row.sap_profit_center

    // currentEditItem.value.type = row.type ?? '1'
    // console.log('---------- sapMasterData', state.masterData.COST_CENTER)
    showItemEditModal()
    // form.value.items.splice(index, 1)
    // form.value.items.forEach((item: any, index: number) => {
    //     item.item_no = index + 1
    // })
    // this.form.value.discount = null
}

const realize = async () => {
    if (form.value.purpose !== 'PROFORMA') {
        message.error({
            content: 'Wrong purpose and cannot realize this invoice',
            duration: 3,
        })
        return
    }

    if (await formRef.value?.validateFields()) {
        calculateInvoiceTotal()
        const {...queryForm} = {..._.cloneDeep(form.value)}

        if (apIntegration === 1) {
            queryForm.items = form.value.items
        } else {
            queryForm.items = queryForm.items.filter((i: any) => i.credit_coa_id !== null)
        }
        queryForm.purpose = 'STANDARD'

        // realize
        let response: any = {}
        try {
            realizeLoading.value = true
            if (apIntegration === 1 && queryForm.po != null && queryForm.po.length > 0) {
                console.log('ap form with po ============= ', form)
                if (INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && queryForm.purpose === 'CREDIT_MEMO') {
                    response = await store.dispatch('ApStore/createIntegrationInvoiceWithCmPoV1', queryForm)
                } else if (INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && queryForm.purpose === 'SUBSEQUENT_CREDIT_MEMO') {
                    response = await store.dispatch('ApStore/createIntegrationInvoiceWithCmOPoV1', queryForm)
                } else {
                    response = await store.dispatch('ApStore/createIntegrationInvoiceWithPoV1', queryForm)
                }
            } else if (apIntegration === 1) {
                response = await store.dispatch('ApStore/createIntegrationInvoiceWithoutPoV1', queryForm)
            }
            if (response.data.statusCode === 200) {
                console.log('realize response data========', response.data)
                // get ap invoice br history
                const apRealizeBankReconcile = {
                    new_invoice_id: queryForm.id,
                    old_invoice_id: queryForm.id,
                    company_code: userCompany[0].code,
                }
                const apRealizeBrRes = await store.dispatch('ApStore/postApRealizeBankReconcile', apRealizeBankReconcile)
                console.log('ap realize br response.data========', apRealizeBrRes.data)
                if (response.data.statusCode !== 200) {
                    message.error({
                        content: response.data.message,
                        duration: 3,
                    })
                    return
                } else {
                    message.success({
                        content: i18n.t('update.realizeSuccess'), // 'Realize Success',
                        duration: 3,
                    })
                }
            } else {
                // message.error({
                //     content: response.data.message,
                // })
            }
            emits('dismiss', true)
        } catch (error: any) {
            console.log(error)
            // message.error(error.response.data.message)
        } finally {
            realizeLoading.value = false
        }
    }
}

const save = async () => {
    console.log('form.value', form.value)
    if (!form.value.items || (form.value.items && form.value.items.length === 0)) {
        message.error({
            content: i18n.t('ApComponents.atleast'), //'Invoice must contain at least one [ Item ]',
            duration: 6,
        })
        return
    }
    if (apIntegration === 1) {
        const referenceNoValidation = await referenceNoValid()
        if (!referenceNoValidation) return
        form.value.reference_no = form.value.reference_no.trim()
    }
    // if (form.value.items.length > 1 && form.value.pay_method === '2') {
    //     message.error({
    //         content: 'Invoice must be only one [ Item ]',
    //         duration: 6,
    //     })
    //     return
    // }
    if (!form.value.net_amount) {
        message.error({
            content: i18n.t('ApComponents.lackNetAmount'), //"Can't create invoice without [ Net Amount ]",
            duration: 6,
        })
        return
    }
    if (await formRef.value?.validateFields()) {
        // if (form.value.purpose !== 'PROFORMA' && (!form.value.file_url || !form.value.xml_url)) {
        //     message.error({
        //         content: i18n.t('UploadFileComp.error_two_files'),
        //         duration: 6,
        //     })
        //     return
        // }
        if (form.value.purpose === 'CREDIT_MEMO' && form.value.total_fee > 0) {
            message.error({
                content: i18n.t('bkAp.msgCreditMemoInvoiceBeNegative'),
                duration: 6,
            })
            return
        }
        calculateInvoiceTotal()
        const {
            engine_document_id = '',
            engine_reverse_document_id = '',
            NotExisted = false,
            ...queryForm
        } = {..._.cloneDeep(form.value)}

        // queryForm.items.forEach(
        //     (item: {credit_coa_code: any; credit_coa_id: any; debit_coa_code: any; debit_coa_id: any}) => {
        //         item.credit_coa_code = accountDescList.value.filter(
        //             (i: {id: any}) => i.id === item.credit_coa_id,
        //         )[0].fieldCode
        //         item.debit_coa_code = item.credit_coa_code
        //     },
        // )

        // when line item total is negative, assign 'dr_cr' to opposite
        // for example ap's dr_cr default is 'cr'
        // when line item amount is negative
        // dr_cr change to 'dr'

        //0314 change ap default to dr
        if (apIntegration === 1) {
            queryForm.items = form.value.items
        } else {
            queryForm.items = queryForm.items.filter((i: any) => i.credit_coa_id !== null)
        }
        queryForm.items.forEach((x: any) => {
            if (+x.total < 0) {
                x.dr_cr = 'cr'
            } else {
                if (!x.dr_cr) {
                    x.dr_cr = 'dr'
                }
            }
        })
        queryForm.creator = userInfo?.id
        queryForm.creator_name = userInfo?.account
        queryForm.company_id = userCompany[0].id
        queryForm.company_code = userCompany[0].code
        queryForm.file_id = currentPdfInfo.value.id || 0
        queryForm.file_name = currentPdfInfo.value.file_name || ''
        queryForm.file_url = currentPdfInfo.value.file_url || ''
        queryForm.xml_url = currentPdfInfo.value.xml_url || ''
        queryForm.company_name = queryForm.company_name?.toUpperCase()
        if (apIntegration === 1) {
            queryForm.send_sap_status = 1
        }
        if (fixedDate.value) {
            queryForm.invoice_due_date = moment(queryForm.invoice_due_date, 'YYYY-MM-DD')
                .add(fixedDate.value, 'days')
                .format('YYYY-MM-DD')
        }
        // queryForm.billToCustomerId = this.billToCustomerId
        if (
            form.value.total_fee != undefined &&
            form.value.total_tax != undefined &&
            form.value.total_fee.toFixed(2) !==
                (Number(form.value.net_amount) + Number(form.value.total_tax) - Number(form.value.mx_discount) - Number(form.value.mx_isr) - Number(form.value.mx_iva)).toFixed(2)
        ) {
            message.error({
                content: i18n.t('ApComponents.notEqual'), //'Net amount plus tax does not equal total',
                duration: 8,
            })
            return
        }

        if (queryForm.pay_method === '2') {
            //AR/AP 创建cash paid发票的功能已经好了，创建发票的时候会自动完成对账。需要注意的是，入参中pay_method = ‘2’ (cash paid)，br_type = ‘9’ (cash pay invoice)
            queryForm.br_type =
                apIntegration === 1 ? (queryForm.net_amount && queryForm.net_amount < 0 ? '2' : '1') : '9'
        } else {
            queryForm.br_type = queryForm.net_amount && queryForm.net_amount < 0 ? '2' : '1'
        }
        if (queryForm.purpose === 'PROFORMA') {
            queryForm.br_type = '12'
        }

        // some patch
        if (queryForm.exchange_rate === '') queryForm.exchange_rate = null
        emits('save', queryForm)
    }
}

const reversePurchase = async (isCopied: boolean) => {
    if (props.readonlyMode) {
        try {
            if (
                apIntegration === 1 &&
                (form.value.po == null || form.value.po.length == 0) &&
                reverseSapDocNo.value != null &&
                reverseSapDocNo.value.length > 0
            ) {
                console.log('reverse integration without po start...')
                const response = await reverseIntegrationPurchaseRequest({
                    id: form.value.id,
                    creator: userInfo?.id,
                    creator_name: userInfo?.account,
                    posting_date: reverseDt?.value,
                })
                if (response.status === 200 && form.value.send_sap_status === 2) {
                    const status = {
                        id: form.value.id,
                        status: 4, // ap reverse data sending
                        creator: userInfo?.id,
                        creator_name: userInfo?.account,
                    }
                    await updateIntegrationSapStatus(status)
                    console.log('userCompany', userCompany)
                    // await sendIntegrationReverseDataToSap({
                    //     sap_document_id: form.value.sap_document_id,
                    //     posting_date: reverseDt?.value,
                    // })
                    await sendIntegrationReverseDataToSapWithCompanyCode({
                        company_code: userCompany[0].code,
                        sap_document_id: form.value.sap_document_id,
                        posting_date: reverseDt?.value,
                    })
                    actionAfterReverse()
                }
            } else if (
                apIntegration === 1 &&
                form.value.po != null &&
                form.value.po.length > 0 &&
                reverseSapDocNo.value != null &&
                reverseSapDocNo.value.length > 0
            ) {
                console.log('reverse integration with po start...')
                const response = await reverseIntegrationPurchaseWithPoRequest({
                    id: form.value.id,
                    creator: userInfo?.id,
                    creator_name: userInfo?.account,
                    posting_date: reverseDt?.value,
                    invoice: form.value,
                })
                if (response.status === 200) {
                    actionAfterReverse()
                } else {
                    throw new Error('Reverse Error')
                }
            } else if (apIntegration === 1 && (reverseSapDocNo.value == null || reverseSapDocNo.value.length == 0)) {
                const response = await reverseIntegrationPurchaseRequest({
                    id: form.value.id,
                    creator: userInfo?.id,
                    creator_name: userInfo?.account,
                    posting_date: reverseDt?.value,
                })
                if (response.status === 200) {
                    actionAfterReverse()
                } else {
                    throw new Error('Reverse Error')
                }
            } else {
                const response = await reversePurchaseRequest({
                    id: form.value.id,
                    creator: userInfo?.id,
                    creator_name: userInfo?.account,
                    posting_date: reverseDt?.value,
                })
                if (response.status === 200) {
                    actionAfterReverse()
                } else {
                    throw new Error('Reverse Error')
                }
            }
            // copy
            if (isCopied) {
                copyInvoice()
            }
        } catch (e: any) {
            console.log(e)
            // message.error(e.response.data.message)
        } finally {
            showReverseLoading.value = false
            showConfirm.value = false
            // formLoading.value = true
        }
    }
}
const actionAfterReverse = () => {
    message.success(i18n.t('ApComponents.success'))
    showConfirm.value = false
    formLoading.value = true
    emits('dismiss', true)
    emits('reverse', true)
}
const convertPurchase = async () => {
    if (props.readonlyMode) {
        try {
            const response = await convertPurchaseRequest({
                id: form.value.id,
                creator: userInfo?.id,
                creator_name: userInfo?.account,
            })
            if (response.status === 200) {
                message.success(i18n.t('ApComponents.success'))
                showConfirm.value = false
                emits('dismiss', true)
                emits('reverse', true)
            }
        } catch (e: any) {
            console.log(e)
            // message.error(e.response.data.message)
        } finally {
            showConvertLoading.value = false
        }
    }
}

const delayUpdate = (value: string) => {
    form.value.invoice_due_date = value
}
const resetFormField = () => {
    formRef.value?.resetFields()
}
const cancel = () => {
    if (apIntegration === 1) {
        emits('dismiss', true)
    } else {
        emits('dismiss')
    }
}

const handleQtyChange = (index: number) => {
    console.log('index: ', index)
    autoCalTaxFlag.value = '0'
    calItemTotal(index)
}
const handleUnitPriceChange = (index: number) => {
    autoCalTaxFlag.value = '0'
    calItemTotal(index)
}
const calItemTotal = (index: number) => {
    if (form.value.items[index].qty && form.value.items[index].unit_price) {
        form.value.items[index].total = Number(
            (form.value.items[index].qty * form.value.items[index].unit_price).toFixed(2),
        )
    } else {
        form.value.items[index].total = 0.0
    }
}
const handleItemTotalChange = (row: {total: any}, index: number) => {
    autoCalTaxFlag.value = '0'
    if (row.total) {
        if (form.value.items[index].qty) {
            form.value.items[index].unit_price = Number((row.total / form.value.items[index].qty).toFixed(2))
        } else {
            form.value.items[index].qty = null
            form.value.items[index].unit_price = null
        }
    }
}
const blurItemTotalChange = (row: any, index: any) => {
    autoCalTaxFlag.value = '0'
    if (!autoCalculateState.value) {
        form.value.items[0].total = form.value.net_amount
    }
    // if (row.total) {
    //     form.value.items[index].qty = null
    //     form.value.items[index].unit_price = null
    // }
}

const addNewContact = () => {
    show.value = true
    current.contact_name = compNameValue.value ? compNameValue.value : ''
}
const showContactCreationModal = (bool = false) => {
    show.value = bool
}

const handlePayMethodChange = (flag: any, value: string) => {
    // if (value === '0') {
    // if (value) {
    //     isDisable.value = false
    // } else {
    //     isDisable.value = true
    // }
    return
}
const updateNewContact = async (response: any) => {
    formRef.value?.clearValidate()
    await fetchSupplierDropDown({...currentUserCompanyQuery})
    // form.value.companyEmail = response.data.data.email || ''
    // form.value.companyName = response.data.data.supplierName
    // form.value.companyPhone = response.data.data.tel || ''
    // form.value.supplierId = response.data.data.supplierId
    form.value.company_code = userCompany[0].code
    form.value.company_id = userCompany[0].id

    form.value.issuer_email = response.data.email || ''
    form.value.issuer_name = response.data.contact_name || ''
    form.value.issuer_tel = response.data.tel || ''
    form.value.issuer_id = response.data.contact_id || ''

    await getTaxRates({
        companyCode: companyTaxInfo.value.code,
        buyerCountryCode: companyTaxInfo.value.country,
        buyerRegionCode: companyTaxInfo.value.province,
        sellerCountryCode: apIntegration === 1 ? companyTaxInfo.value.country : response.data.office_country,
        sellerRegionCode: apIntegration === 1 ? companyTaxInfo.value.province : response.data.office_province,
    })

    await getApTopCoa()

    // calculate taxs by Province
    autoCalTaxFlag.value = '2'
    form.value.tax_content = calculateTaxRates(form.value.net_amount)
    await calculateInvoiceTotal()
}

// const checkFields = (contact: any) => {
//     if (contact[0] && !contact[0].email) {
//         message.error({
//             content: 'Selected Contact Email is Empty!',
//         })
//         return false
//     }
//     return true
// }
const handleIssuerNameKeyup = (event: any) => {
    newIssuerName.value = event.target.value.toUpperCase()
}
const handleCompanyChange = async (flag: string, value: any) => {
    const current = state.supplierList.find((i: any) => i.id === value)
    const found = state.supplierList.filter((item: {id: any}) => item.id === value)

    // get sap master data top
    if (apIntegration === 1) {
        sapMasterDataTopNew.value = await fetchSapMasterDataTop({
            company_code: userCompany[0].code,
            contact_id: found[0].contact_id,
        })
        form.value.items.forEach((item: any) => {
            item.sap_gl_account = sapMasterDataTopNew.value.gl_account_code
            item.sap_wbs = sapMasterDataTopNew.value.wbs_code
            item.sap_cost_center = sapMasterDataTopNew.value.cost_center_code
            item.sap_internal_order = sapMasterDataTopNew.value.internal_order_code
            item.sap_profit_center = sapMasterDataTopNew.value.profit_center_code
        })
    }

    form.value.issuer_address =
        [
            current.office_receiver || '',
            current.office_street || '',
            current.office_city || '',
            current.office_province || '',
            current.office_country || '',
            current.office_postal_code || '',
        ]
            .filter(i => i)
            .join() || ''
    if (flag === 'ship') {
        // const {shippingReceiver, shippingStreet, shippingCity, shippingProvince, shippingPostalCode} = found[0]
        // this.form.value.shipToReceiver = shippingReceiver
        // this.form.value.shipToStreet = shippingStreet
        // this.form.value.shipToCity = shippingCity
        // this.form.value.shipToProvince = shippingProvince
        // this.form.value.shipToPostalCode = shippingPostalCode
        // this.companyProvinceCode = found[0].shippingProvince
        form.value.company_code = userCompany[0].code
        form.value.company_id = userCompany[0].id
        form.value.issuer_email = found[0].email
        form.value.issuer_name = found[0].contact_name
        // this.form.value.companyAddress = found[0].officeStreet + found[0].officeCity + found[0].officeProvince + found[0].officeCountry + found[0].officePostalCode
        form.value.issuer_tel = found[0].tel
        form.value.issuer_id = found[0].contact_id

        await getTaxRates({
            companyCode: companyTaxInfo.value.code,
            buyerCountryCode: companyTaxInfo.value.country,
            buyerRegionCode: companyTaxInfo.value.province,
            sellerCountryCode: apIntegration === 1 ? companyTaxInfo.value.country : found[0].office_country,
            sellerRegionCode: apIntegration === 1 ? companyTaxInfo.value.province : found[0].office_province,
            // buyerCountryCode: 'CA', //companyTaxInfo.value.country,
            // buyerRegionCode: 'QC', //companyTaxInfo.value.province,
            // sellerCountryCode: 'CA', //found[0].office_country,
            // sellerRegionCode: 'QC', //found[0].office_province,
        })

        await getApTopCoa()

        // calculate taxs by Province
        autoCalTaxFlag.value = '2'
        form.value.tax_content = calculateTaxRates(form.value.net_amount)
        await calculateInvoiceTotal()
    }
}
const handlePoChange = async (flag: string, value: any) => {
    // get sap master data top
    poNumberApIntegrationChange()
}
const handleShippingChange = (value: any) => {
    // if (value === null) this.form.value.shipping = 0.0
    if (form.value.net_amount) {
        form.value.shipping = value
        calculateInvoiceTotal()
    }
}
const calculateInvoiceTotal = () => {
    if (!autoCalculateState.value) {
        const totalFee = form.value.total_fee ? form.value.total_fee : 0.0
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(totalFee)
            .sub(new Decimal(form.value.total_tax))
            .add(new Decimal(form.value.mx_discount))
            .add(new Decimal(form.value.mx_isr))
            .add(new Decimal(form.value.mx_iva))
            .toDP(2)
            .toNumber()
        form.value.items[0].total = form.value.net_amount
    } else {
        const shipFee = form.value.shipping || 0.0
        const discountFee = form.value.mx_discount || 0.0
        const withholdingIsr = form.value.mx_isr || 0.0
        const withholdingIva = form.value.mx_iva || 0.0
        // const disCountFee = this.form.value.discount || 0.0
        const amountFee = form.value.net_amount ? form.value.net_amount : 0.0
        const totalTaxableFee = amountFee + shipFee - discountFee
        // const totalTaxableFee = amountFee + shipFee - disCountFee > 0 ? amountFee + shipFee - disCountFee : 0.0
        const totalTaxFee = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        const totalFee = new Decimal(totalTaxableFee).add(new Decimal(totalTaxFee)).sub(new Decimal(withholdingIsr)).sub(new Decimal(withholdingIva)).toDP(2).toNumber()
        // form.value.amountFee = amountFee
        // form.value.totalTaxable = totalTaxableFee

        form.value.total_tax = totalTaxFee
        form.value.total_fee = totalFee
        updateSpot()
        // form.value.total_fee_local =
        //     form.value.invoice_currency == '2'
        //         ? form.value.total_fee
        //         : form.value.total_fee * parseFloat(spot.value.rate)
    }
}
const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}
const initFormData = () => {
    formRef.value?.resetFields()
    form.value.tax_content = []
    form.value.total_tax = 0
    form.value.total_fee = 0
    form.value.total_fee_local = 0
    form.value.net_amount = 0
    form.value.mx_discount = 0
    form.value.mx_isr = 0
    form.value.mx_iva = 0

    form.value.items = []
    addItem()
}

const handleTaxChange = (current: any, item: any) => {
    if (current === null) {
        item.value = 0
    } else {
        item.value = current
    }

    calculateInvoiceTotal()
}

const referenceNoChange = async () => {
    if (form.value.reference_no && apIntegration !== 1) {
        await checkRefNoRepetition(form.value.reference_no)
    }
}

const poNumberChange = async () => {
    if (form.value.po) {
        poNumberLoading.value = true
        try {
            const res = await fetchPONumberData({po: form.value.po})
            form.value.posting_date = moment(res.data.data?.budat, 'YYYYMMDD').format('YYYY-MM-DD')
            form.value.invoice_currency = res.data.data?.waers
        } catch (e) {
            // message.error('fetch PO error')
        } finally {
            poNumberLoading.value = false
        }
    }
}
const handleFocus = async () => {
    // if (contactKeyword.value.trim() === '') {
    if (apIntegration === 1) {
        const queryObj: any = {}
        queryObj['$limit'] = contactPageSize.value
        queryObj['$skip'] = 0
        await fetchSupplierDropDown({...queryObj, ...currentUserCompanyQuery})
        state.supplierList = state.localSupplierList
    }
}
const handlePoFocus = async () => {
    if (apIntegration === 1) {
        state.poOptionList = state.poTopListData
    }
}
const poNumberApIntegrationChange = async () => {
    if (form.value.po) {
        poLoading.value = true
        formLoading.value = true
        try {
            // clear items and isser name
            form.value.items = []
            form.value.issuer_name = null
            // fetch sap po info
            const res =
                form.value.purpose === 'CREDIT_MEMO'
                    ? await fetchSapCmPoInfo({
                          company_code: userCompany[0].code,
                          po_no: form.value.po,
                      })
                    : form.value.purpose === 'PROFORMA'
                    ? await fetchSapProfomaPoInfo({
                          company_code: userCompany[0].code,
                          po_no: form.value.po,
                      })
                    : await fetchSapPoInfo({
                          company_code: userCompany[0].code,
                          po_no: form.value.po,
                      })
            if (res.status !== 200) {
                // clear po
                form.value.po = null
                message.error(i18n.t('update.poNumberNotFound')) // 'PO Number Not Found'
            } else {
                //
                const poInfo = res.data
                if (
                    poInfo.ET_PO_SUPPLIER_DATA.length === 0 ||
                    !poInfo.ET_PO_SUPPLIER_DATA[0] ||
                    poInfo.ET_PO_SUPPLIER_DATA[0].BP_NAME == null ||
                    poInfo.ET_PO_SUPPLIER_DATA[0].BP_NAME == ''
                ) {
                    // clear po
                    // form.value.po = null
                    message.error(i18n.t('update.poNumberNotFound')) // 'PO Number Not Found'
                } else {
                    await handlePoInfoChange(poInfo)
                    if (poInfo.ET_PO_ITEM && poInfo.ET_PO_ITEM.length === 0) {
                        message.success(i18n.t('update.purchaseOrderFoundButNoOpenItem'))
                    } else {
                        message.success(i18n.t('update.purchaseOrderFound')) // Purchase order found
                    }
                }
            }
        } catch (e) {
            // message.error('fetch PO error')
        } finally {
            poLoading.value = false
            formLoading.value = false
        }
    }
}
const handlePoInfoChange = async (poInfo: any) => {
    // currency change
    form.value.invoice_currency = poInfo.ET_PO_HEADER[0].CURRENCY
    // issuer info change
    contactKeyword.value = poInfo.ET_PO_SUPPLIER_DATA[0].BP_NAME
    state.supplierList = []
    contactPageSize.value = 10
    console.log('contactKeyword.value', contactKeyword.value)
    const queryObjSap: any = {
        company_code: companyTaxInfo.value.code,
        keyword: contactKeyword.value,
    }
    // const sapSupplierListRes = await fetchApSapContactDropDown({...queryObjSap})
    const sapSupplierList = poInfo.ET_PO_SUPPLIER_DATA.map((item: any) => {
        return {
            ...item,
            isSapReturned: true,
            id: item.BP_NUMBER,
            contact_name: item.BP_NAME,
            contact_id: item.BP_NUMBER,
            company_code: companyTaxInfo.value.code,
            billing_city: item.CITY,
            billing_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
            billing_province: item.REGION !== '' ? item.REGION : 'QC',
            billing_postal_code: item.POSTAL_CODE,
            billing_street: item.STREET,
            office_city: item.CITY,
            office_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
            office_province: item.REGION !== '' ? item.REGION : 'QC',
            office_postal_code: item.POSTAL_CODE,
            office_street: item.STREET,
            shipping_city: item.CITY,
            shipping_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
            shipping_province: item.REGION !== '' ? item.REGION : 'QC',
            shipping_postal_code: item.POSTAL_CODE,
            shipping_street: item.STREET,
            email: item.EMAIL,
            tel: item.TELEPHONE,
        }
    })
    state.supplierList = sapSupplierList
    handleCompanyChange('ship', sapSupplierList[0].id)

    // ap item info change
    remove(0)
    poInfo.ET_PO_ITEM.forEach((item: any, index: any) => {
        form.value.items.push({
            item_no: index + 1,
            po_item_number: item.PO_ITEM_NUMBER,
            material: item.MATERIAL,
            description: item.DESCRIPTION,
            qty: item.QTY,
            unit_price: item.UNIT_PRICE,
            total: item.NET_AMOUNT,
            fiscal_year: item.FISCAL_YEAR,
            goods_receipt_item_number: item.GOODS_RECEIPT_ITEM_NUMBER,
            goods_receipt_number: item.GOODS_RECEIPT_NUMBER,
            tax_code: item.TAX_CODE,
        })
    })
}

const checkRefNoRepetition = async (value: string) => {
    const query = {
        company_code: userCompany[0].code,
        reference_no: value,
        br_flag: {$ne: 3},
        page_index: 1,
        page_size: 10,
    }
    try {
        referenceNoLoading.value = true
        const response = await checkReferenceNoRepetition(query)
        if (response.data.statusCode === 200) {
            if (response.data.data.length > 0) {
                message.warning({
                    content: i18n.t('bkAp.msgReferenceNoExisted'),
                    duration: 8,
                })
            }
        } else {
            // message.error({
            //     content: i18n.t('bkAp.msgReferenceCheckFail'),
            //     duration: 5,
            // })
        }
    } catch (error) {
        console.log(error)
    } finally {
        referenceNoLoading.value = false
    }
}
const changeItemListRowExpenseAccount = (value: any, index: number, fieldCode: any) => {
    // form.value.items[index].debit_coa_id = value
    // form.value.items[index].debit_coa_code = fieldCode
    // const selected = accountDescList.value.find((x: any) => x.id === value)
    // console.log(accountDescList)
    form.value.items[index].credit_coa_id = value
    const coaItem = accountDescList.value.find((x: any) => x.id == value)
    form.value.items[index].credit_coa_code = coaItem?.account_code
    form.value.items[index].credit_coa_name = coaItem?.name
    // form.value.items[index].bank_account = selected.bank_account
}

const changeItemListRowType = (value: any, index: number, fieldCode: any) => {
    form.value.items[index].type = value
}

const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const getApTopCoa = async () => {
    try {
        await fetchApTopCoa({company_code: form.value.company_code, issuer_id: form.value.issuer_id})

        if (form.value.items.length === 1 && apTopCoaList.value.length > 0) {
            const credit_coa_code = apTopCoaList.value[0].credit_coa_code
            const coaItem = accountDescList.value.find((x: any) => x.account_code == credit_coa_code)

            form.value.items[0].credit_coa_id = coaItem?.id
            form.value.items[0].credit_coa_code = coaItem?.account_code
            form.value.items[0].credit_coa_name = coaItem?.name
        } else {
            form.value.items[0].credit_coa_id = null
            form.value.items[0].credit_coa_code = ''
            form.value.items[0].credit_coa_name = ''
        }
    } catch (error) {
        console.log(error)
    }
}

const getTaxRates = async (query: any) => {
    const {buyerCountryCode, buyerRegionCode, sellerCountryCode, sellerRegionCode} = query

    if (!buyerCountryCode || !buyerRegionCode || !sellerCountryCode || !sellerRegionCode) {
        countryCodeCheck.value = false
        message.error({
            content: i18n.t('taxRates.countryNotSet'),
            duration: 8,
        })

        updateTaxRates([])
        return
    } else {
        countryCodeCheck.value = true
    }
    formLoading.value = true
    await fetchTaxRates({...query, invoiceDate: moment().valueOf(), action: 'Purchase'})
    formLoading.value = false
}

const handleDiscountChange = (value: any) => {
    calculateInvoiceTotal()
}

const changeTotalAmount = (value: any, reverse: boolean) => {
    autoCalTaxFlag.value = '1'
    if (value && form.value.items.length === 1) {
        if (form.value.invoice_currency !== localCurrency && reverse) {
            updateSpotReverse()
            value = form.value.total_fee ?? value
        }

        form.value.tax_content = calculateTaxRates(value)
        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
        form.value.net_amount = new Decimal(form.value.total_fee || 0)
            .sub(new Decimal(form.value.total_tax))
            .add(new Decimal(form.value.mx_discount))
            .add(new Decimal(form.value.mx_isr))
            .add(new Decimal(form.value.mx_iva))
            .toDP(2)
            .toNumber()
        form.value.items[0].total = form.value.net_amount
        form.value.items[0].qty = 0
        form.value.items[0].unit_price = 0
    } else if (!value && form.value.items.length === 1) {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0

        form.value.items[0].total = 0
        form.value.items[0].qty = 0
        form.value.items[0].unit_price = 0
    }

    if (form.value.invoice_currency !== localCurrency && !reverse) {
        updateSpot()
    }
}

const revertTaxCal = (amount: number | null = null) => {
    if (amount) {
        _.forEach(taxRatesList.value, (item, index) => {
            form.value.tax_content[index].value = new Decimal(amount).mul(item.value).toDP(2).toNumber()
        })

        // 计算基础total_fee（amount + 税费）
        const baseTotalFee = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            amount,
        )

        // 从total_fee中减去mx_discount, mx_isr, mx_iva
        form.value.total_fee = new Decimal(baseTotalFee)
            .sub(new Decimal(form.value.mx_discount || 0))
            .sub(new Decimal(form.value.mx_isr || 0))
            .sub(new Decimal(form.value.mx_iva || 0))
            .toDP(2)
            .toNumber()

        form.value.total_tax = _.reduce(
            form.value.tax_content,
            (sum, n) => {
                return new Decimal(sum)
                    .add(new Decimal(n.value || 0))
                    .toDP(2)
                    .toNumber()
            },
            0,
        )
    } else {
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_fee = 0
        form.value.total_tax = 0
    }
}
const handleEnterEvent = (event: any) => {
    event.target.blur()
}
const selectInputValues = (e: FocusEvent) => {
    const target = e.target as HTMLInputElement // 使用类型断言告诉TypeScript不为空
    if (target) {
        target.select()
    }
}
const referenceNoValid = async () => {
    if (form.value.reference_no == null) form.value.reference_no = ''
    const query = {
        company_code: userCompany[0].code,
        reference_no: form.value.reference_no.trim(),
        br_flag: {$ne: 3},
    }
    if (form.value.reference_no !== null && form.value.reference_no.length > 0 && apIntegration === 1) {
        const resReferenceValid = await store.dispatch('ApStore/checkReferenceNoRepetitionV1', query)
        if (resReferenceValid.data.data.length > 0) {
            message.error({content: i18n.t('ApComponents.referenceError')})
            return false
        }
    }
    return true
}
const calculateTaxRates = (amount: number | null = null) => {
    if (enableTaxExempt.value) {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: 0}
        })
    }
    // change from item List ( row net amount)
    if (autoCalTaxFlag.value === '0' || autoCalTaxFlag.value === '2') {
        return _.map(taxRatesList.value, (item: any) => {
            return {...item, value: amount ? new Decimal(amount).mul(item.value).toDP(2).toNumber() : 0}
        })
    }

    // change from TOTAL
    const taxTotal = _.reduce(
        taxRatesList.value,
        (sum, item) => {
            return new Decimal(sum).add(new Decimal(item.value || 0)).toNumber()
        },
        0,
    )
    const netTotal = amount ? new Decimal(amount).div(new Decimal(1 + taxTotal)).toNumber() : 0
    return _.map(taxRatesList.value, (item: any) => {
        return {...item, value: netTotal ? new Decimal(netTotal).mul(item.value).toDP(2).toNumber() : 0}
    })
}
const changeAutoCalculateMode = (state: any) => {
    if (state) {
        const sum = form.value.items.reduce((prev: any, curr: {total: any}) => {
            return prev + curr.total
        }, 0)

        form.value.net_amount = sum
        form.value.tax_content = calculateTaxRates(sum)
        calculateInvoiceTotal()
    }
}

const expenseAccountAlias = (
    // row: {expenseAccountId: any; expenseAccount: string},
    row: {credit_coa_code: string; credit_coa_id: string},
    accountList: any[],
) => {
    // let alias = ''
    // accountDescList.forEach((item: {account_code: string; name: string}) => {
    //     if (String(item.account_code) === String(row.credit_coa_code)) {
    //         alias = item.name
    //     }
    // })
    const item = accountList.find((item: any) => item.account_code === row.credit_coa_code)
    return `${row.credit_coa_code.substring(0, 4)} | ${item?.name || ''}`
}

const isEffectiveDate = (date: string) => {
    const year = moment(date).year()
    return (
        moment() <=
        moment()
            .year(year)
            .month(financialYear! + 13)
            .endOf('month')
    )
}

const postingDateChange = (date: string) => {
    if (financialYear !== null && !isEffectiveDate(date)) {
        Modal.info({
            title: 'Note',
            content: 'The deadline for input has passed.',
        })
    }
    updateSpot()
}

const updateSpot = async () => {
    if (userType.value === 'jushi') return
    if (form.value.invoice_currency?.toString() === localCurrency) {
        form.value.total_fee_local = form.value.total_fee
        spotRateCheck.value = true
        return
    }

    currencyLoading.value = true
    const baseCurrency = form.value.invoice_currency
    const quoteCurrency = localCurrency

    const weekOfDayDiff = getSpotInputDateStatus(form.value.posting_date)
    isWeekend.value = weekOfDayDiff < 0
    if (!isWeekend.value) {
        postingDate.value = form.value.posting_date
    } else {
        postingDate.value = moment(form.value.posting_date, 'YYYY-MM-DD')
            .add(weekOfDayDiff, 'days')
            .format('YYYY-MM-DD')
    }

    // try {
    //     spot.value = await getSpot({baseCurrency, quoteCurrency, date: postingDate.value})
    //     form.value.exchange_rate = spot.value.rate
    //     spotRateCheck.value = spot.value.rate !== ''
    // } finally {
    //     currencyLoading.value = false
    // }

    // comments for test
    // postingDate.value = form.value.posting_date
    // isWeekend.value = getSpotInputDateStatus(form.value.posting_date)
    //
    // form.value.total_fee_local = form.value.total_fee != null ? form.value.total_fee * parseFloat(spot.value.rate) : null

    // form.value.total_fee_local =
    //     form.value.total_fee != undefined
    //         ? Number((form.value.total_fee * parseFloat(spot.value.rate ?? '')).toFixed(2))
    //         : null
    form.value.total_fee_local = form.value.total_fee
}
const updateSpotReverse = async () => {
    if (form.value.invoice_currency?.toString() === localCurrency) {
        form.value.total_fee = form.value.total_fee_local
        return
    }

    form.value.total_fee =
        form.value.total_fee_local != undefined
            ? Number((form.value.total_fee_local / parseFloat(spot.value.rate ?? '')).toFixed(2))
            : null
}

const getSpotInputDateStatus = (date: moment.MomentInput) => {
    const weekOfday = moment(date, 'YYYY-MM-DD').format('E')
    return 5 - +weekOfday
}

//  compNameSearch
const contactSearch = _.debounce(async (value: any) => {
    console.log('contactSearch', contactKeyword.value)
    //reset volume
    contactKeyword.value = value
    state.supplierList = []
    contactPageSize.value = 10
    await fetchContactList()
}, 500)

//  po search
const poSearch = _.debounce(async (value: any) => {
    poLoading.value = true
    console.log('poSearch', poKeyword.value)
    //reset value
    poKeyword.value = value
    if (contactKeyword.value.trim() === '') {
        state.poOptionList = state.poTopListData
    }
    const res = await fetchSapPoInfo({
        company_code: userCompany[0].code,
        po_no: value,
    })
    if (res.status == 200) {
        //
        const poInfo = res.data
        if (poInfo.ET_PO_SUPPLIER_DATA[0].BP_NAME != null && poInfo.ET_PO_SUPPLIER_DATA[0].BP_NAME != '') {
            state.poOptionList = [value]
            poKeyword.value = ''
        }
    }
    poLoading.value = false
}, 500)

const fetchContactList = async () => {
    try {
        contactLoading.value = true
        // state.supplierList = []
        const queryObj: any = {}
        if (contactKeyword.value) {
            queryObj['contact_name[$like]'] = `%${contactKeyword.value.trim()}%`
        }
        queryObj['$limit'] = contactPageSize.value
        queryObj['$skip'] = 0
        await fetchSupplierDropDown({...queryObj, ...currentUserCompanyQuery})
        if (apIntegration === 1 && contactKeyword.value) {
            console.log('contactKeyword.value', contactKeyword.value)
            if (contactKeyword.value.trim() === '') {
                state.supplierList = state.localSupplierList
            } else {
                const queryObjSap: any = {
                    company_code: companyTaxInfo.value.code,
                    keyword: contactKeyword.value,
                }
                const sapSupplierListRes = await fetchApSapContactDropDown({...queryObjSap})
                const sapSupplierList = sapSupplierListRes.map((item: any) => {
                    return {
                        ...item,
                        isSapReturned: true,
                        id: item.BP_NUMBER,
                        contact_name: item.BP_NAME,
                        contact_id: item.BP_NUMBER,
                        company_code: companyTaxInfo.value.code,
                        billing_city: item.CITY,
                        billing_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
                        billing_province: item.REGION !== '' ? item.REGION : 'QC',
                        billing_postal_code: item.POSTAL_CODE,
                        billing_street: item.STREET,
                        office_city: item.CITY,
                        office_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
                        office_province: item.REGION !== '' ? item.REGION : 'QC',
                        office_postal_code: item.POSTAL_CODE,
                        office_street: item.STREET,
                        shipping_city: item.CITY,
                        shipping_country: item.COUNTRY !== '' ? item.COUNTRY : 'CA',
                        shipping_province: item.REGION !== '' ? item.REGION : 'QC',
                        shipping_postal_code: item.POSTAL_CODE,
                        shipping_street: item.STREET,
                        email: item.EMAIL,
                        tel: item.TELEPHONE,
                    }
                })
                state.supplierList = sapSupplierList
            }
        } else if (apIntegration === 1 && !contactKeyword.value) {
            state.supplierList = state.localSupplierList
        }
    } catch (e) {
        console.log(e)
    } finally {
        contactLoading.value = false
    }
    return void 0
}

const changeTaxExempt = () => {
    if (enableTaxExempt.value) {
        form.value.total_fee = new Decimal(form.value.net_amount ?? 0)
            .sub(new Decimal(form.value.mx_discount))
            .sub(new Decimal(form.value.mx_isr))
            .sub(new Decimal(form.value.mx_iva))
            .toDP(2)
            .toNumber()
        _.forEach(form.value.tax_content, item => {
            item.value = 0
        })
        form.value.total_tax = 0
    } else {
        revertTaxCal(form.value.net_amount)
    }

    updateSpot()
}

const contactScroll = async (e: any) => {
    const {target} = e
    // when user scoll near bottom
    if (Math.abs(target.scrollTop + target.offsetHeight - target.scrollHeight) < 2 && !contactLoading.value) {
        contactPageSize.value += 10
        await fetchContactList()
    }
}

const displayFileViewerModal = (url: string) => {
    selectedFileUrl.value = url
    showFileViewer.value = true
}

const stopPolling = (intervalId: number) => {
    clearInterval(intervalId)
}
const startPolling = (fileId: number) => {
    const startTime = Date.now()
    const intervalId = setInterval(async () => {
        try {
            const result = await store.dispatch('ArApBrStore/getFileObjById', {id: fileId})
            if (result.data.statusCode == '200' && result.data.data.xml_status === 'SUCCESS') {
                satCertification.value = 'SUCCESS'
                stopPolling(intervalId)
                return
            } else if (result.data.statusCode == '200' && result.data.data.xml_status === 'FAILURE') {
                satCertification.value = 'FAILURE'
                stopPolling(intervalId)
                return
            }
        } catch (error: any) {
            console.log(error)
        }

        const elapsedTime = (Date.now() - startTime) / 1000 // 计算已运行时间
        if (elapsedTime >= 30) {
            satCertification.value = 'FAILURE'
            stopPolling(intervalId)
        }
    }, 5000)
}
const uploadFile = async ({fileList: newFileList}: UploadChangeParam) => {
    satCertification.value = 'PENDING'
    // 检查文件数量是否为2
    if (newFileList.length !== 2) {
        fileList.value = []
        message.error(i18n.t('UploadFileComp.error_two_files'))
        return
    }

    if (uploadDisabled.value) return
    uploadDisabled.value = true
    const formData = new FormData()
    const jsonStr: any[] = []
    newFileList.forEach(file => {
        const file_type = form.value.pay_method === '1' ? '1' : '3'
        const baseObj = {
            company_id: userCompany[0].id,
            company_code: userCompany[0].code,
            file_type: file_type,
            file_name: file.name,
            file_currency: '',
            bank_account: '',
            bank_month: '',
            creator: userInfo?.id,
        }
        if (file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase() !== 'xml' && jsonStr.length === 0) {
            jsonStr.push(baseObj)
        }
        formData.append('file', file.originFileObj as any)
    })
    formData.append('jsonStr', JSON.stringify(jsonStr))

    try {
        const response = await uploadApInvoicePdfMX(formData)
        if (response.data.statusCode === 200) {
            form.value.file_id = response.data.data.id
            form.value.file_url = response.data.data.file_url
            form.value.xml_url = response.data.data.xml_url
            // form.value.file_name = response.data.data.file_name
            const fileObj = {
                file_name: response.data.data.file_name,
                file_url: form.value.file_url,
                xml_url: form.value.xml_url,
                id: form.value.file_id,
            }
            uploadFileList.value.push(fileObj)
            updatePdfInfo(fileObj)
            uploadState.value = true
            // polling xml state
            startPolling(form.value.file_id)
        } else {
            // message.error('failed')
        }
    } catch (error: any) {
        fileList.value = []
        satCertification.value = ''
        console.log(error)
        // message.error(error.response.data.message)
    }
    uploadDisabled.value = false
}

const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}

const viewerStyle = {
    height: '500px',
}

const requireGLAccount = (rule: any, value: number | null) => {
    const index = Number(rule.field.match(/\.(\S*)\./)[1])
    const item = form.value.items[index]

    if (value === null && item.qty && item.total && item.unit_price) {
        return Promise.reject()
    } else {
        return Promise.resolve()
    }
}

const rules = reactive({
    issuer_name: [...requireRule(i18n.t('bkAp.companyName'))],
    purpose: [...requireRule(i18n.t('bkAp.purpose'))],
    reference_no: [...requireRule(i18n.t('bkAp.referenceNo'))],
    //companyAddress: [...requireRule(i18n.t('bkAp.companyAddr'))],
    //companyPhone: [...requireRule(i18n.t('bkAp.companyTel'))],
    //companyEmail: [
    //  ...requireRule(i18n.t('bkAp.companyEmail')),
    //  {type: 'email', message: i18n.t('bkCommonTag.msgEmailRule'), trigger: 'blur'},
    //],
    // companyGstNo: [...requireRule(i18n.t('bkAp.companyGst'))],
    // companyPstNo: [...requireRule(i18n.t('bkAp.companyQst'))],
    // gst: [...requireRule(i18n.t('bkAp.companyGstHst'))],
    // pst: [...requireRule(i18n.t('bkAp.companyPst'))],
    invoice_currency: [...requireRule(i18n.t('bkAp.currency'))],
    // invoiceCreateDate: [...requireRule(i18n.t('bkAp.date'))],
    invoice_due_date: [...requireRule(i18n.t('bkAp.dueDate'))],
    // po: [...requireRule('po')],
    // billing
    //billToCompany: [...requireRule(i18n.t('bkAp.billToCompany'))],
    // billToReceiver: [...requireRule(i18n.t('bkAp.billToReceiver'))],
    // // billToAddress: [...requireRule(i18n.t('bkAp.billToAddress'))],
    // billToCity: [...requireRule(i18n.t('bkAp.billToCity'))],
    // billToProvince: [...requireRule(i18n.t('bkAp.billToProvince'))],
    // // billToPostalCode: [...requireRule(i18n.t('bkAp.billToPostalCode'))],
    // billToTel: [...requireRule(i18n.t('bkAp.billToTel'))],
    // billToEmail: [
    //     ...requireRule(i18n.t('bkAp.billToEmail')),
    //     {type: 'email', message: i18n.t('bkCommonTag.msgEmailRule'), trigger: 'blur'},
    // ],
    // shipping
    // shipToCompany: [...requireRule(i18n.t('bkAp.shipToCompany'))],
    // shipToReceiver: [...requireRule(i18n.t('bkAp.shipToReceiver'))],
    // shipToAddress: [...requireRule(i18n.t('bkAp.shipToAddress'))],
    // shipToCity: [...requireRule(i18n.t('bkAp.shipToCity'))],
    // shipToProvince: [...requireRule(i18n.t('bkAp.shipToProvince'))],
    // shipToPostalCode: [...requireRule(i18n.t('bkAp.shipToPostalCode'))],
    // shipToTel: [...requireRule(i18n.t('bkAp.shipToTel'))],
    // shipToEmail: [
    //   ...requireRule(i18n.t('bkAp.shipToEmail')),
    //   {type: 'email', message: i18n.t('bkCommonTag.msgEmailRule'), trigger: 'blur'},
    // ],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur', 'change'],
        },
    ],
    requireGLAccount: [
        {
            message: 'Selection required',
            validator: requireGLAccount,
            trigger: ['blur', 'change'],
        },
    ],
    // may be later
    // amount: [...requireRule(i18n.t('bkAp.amount'))],
    // shipping: [...requireRule(i18n.t('bkAp.shipping'))],
    // totalTaxable: [...requireRule(i18n.t('bkAp.totalTaxable'))],
    // totalTax: [...requireRule(i18n.t('bkAp.totalTax'))],
    // tps: [...requireRule(i18n.t('bkAp.tps'))],
    // tvq: [...requireRule(i18n.t('bkAp.tvq'))],
    // totalFee: [...requireRule(i18n.t('bkAp.totalFee'))],
    //bankAccount: [...requireRule(i18n.t('bkAp.bankAccount'))],
    //trevenueAccount: [...requireRule(i18n.t('bkAp.trevenueAccount'))],
    posting_date: [...requireRule(i18n.t('bkAp.postingDate'))],
    invoice_comments: [...lengthLimitRule(1, 200000)],
    pay_method: [...requireRule(i18n.t('bkAp.payMethod'))],
})

//查看gl详情
const seeDetail = async (val: any) => {
    const query = {
        company: userCompany[0].code,
        document_no: val,
    }

    const response = await fetchGlList(query)
    glCurrent.value = _.cloneDeep(response.data)
    showGlDialog.value = true
}
const glDismiss = () => {
    showDialog(false)
}
const showDialog = (bool: boolean) => {
    showGlDialog.value = bool
    if (!bool) {
        glCurrent.value = {}
    }
}

const showDiscountModal = () => {
    console.log('show discount modal')
    enableDiscountModal.value = true
}

const closeDiscountModal = () => {
    enableDiscountModal.value = false
}

const showItemEditModal = () => {
    console.log('show item edit modal')
    itemEditModal.value = true
}

const closeItemEditModal = () => {
    itemEditModal.value = false
}

const confirmItemEditModal = (discounts: any) => {
    const currentItem = form.value.items.find((item: any) => item.item_no == currentEditItem.value.item_no)
    currentItem.sap_gl_account = currentEditItem.value.sap_gl_account
    currentItem.sap_wbs = currentEditItem.value.sap_wbs
    currentItem.sap_cost_center = currentEditItem.value.sap_cost_center
    currentItem.sap_internal_order = currentEditItem.value.sap_internal_order
    currentItem.sap_profit_center = currentEditItem.value.sap_profit_center
    currentItem.credit_coa_id = -1 // 14071
    currentItem.credit_coa_code = `1002-${userCompany[0].code}-01`
    currentItem.credit_coa_name = 'Deposits in Canadian banks and institutions - Canadian currency'
    // payment_discounts_label.value = ''
    // payment_discounts_label_format.value = ''
    // for (const x in discounts) {
    //     payment_discounts_label.value += discounts[x]
    // }
    // const {
    //     payment_terms_day_1,
    //     payment_terms_day_2,
    //     payment_terms_day_3,
    //     payment_terms_discount_1,
    //     payment_terms_discount_2,
    //     payment_terms_discount_3,
    // } = discounts
    // if (payment_discounts_label.value) {
    //     payment_discounts_label_format.value = `${payment_terms_day_1}-${payment_terms_discount_1}%;${payment_terms_day_2}-${payment_terms_discount_2}%;${payment_terms_day_3}-${payment_terms_discount_3}%`
    // }
    // discount_saved.value = true // user need open modal at least once
    // form.value = {...form.value, ...discounts}
    // console.log('check after assignment', form.value)
    itemEditModal.value = false
}

const confirmDiscountModal = (discounts: any) => {
    console.log('check before assignment', form.value)
    payment_discounts_label.value = ''
    payment_discounts_label_format.value = ''
    for (const x in discounts) {
        payment_discounts_label.value += discounts[x]
    }
    const {
        payment_terms_day_1,
        payment_terms_day_2,
        payment_terms_day_3,
        payment_terms_discount_1,
        payment_terms_discount_2,
        payment_terms_discount_3,
    } = discounts
    if (payment_discounts_label.value) {
        payment_discounts_label_format.value = `${payment_terms_day_1}-${payment_terms_discount_1}%;${payment_terms_day_2}-${payment_terms_discount_2}%;${payment_terms_day_3}-${payment_terms_discount_3}%`
    }
    discount_saved.value = true // user need open modal at least once
    form.value = {...form.value, ...discounts}
    console.log('check after assignment', form.value)
    enableDiscountModal.value = false
}

const closeFileViewerModal = () => {
    showFileViewer.value = false
    selectedFileUrl.value = ''
}
const downloadFile = async () => {
    if (form.value.file_url) {
        downloadFileloading.value = true
        const response = await getFileBlobById(form.value.file_url)
        try {
            if (response.status === 200 && response.data.size > 0) {
                const filename = form.value.file_url?.split('/').pop()
                // const blob = new Blob([response.data], {type: 'application/pdf;charset=utf-8'})
                const blob = new Blob([response.data], {
                    type: form.value.file_url.includes('.pdf')
                        ? 'application/pdf;charset=utf-8'
                        : 'image/jpg;charset=utf-8',
                })
                FileSaver.saveAs(blob, filename)
            } else {
                // message.error({content: 'Invoice file is not available.'})
            }
        } catch (e) {
            console.log(e)
        } finally {
            downloadFileloading.value = false
        }
    }
}
const deleteFile = (form: any) => {
    form.file_url = ''
    form.xml_url = ''
    form.file_id = false
    form.file_name = ''

    updatePdfInfo({id: '', file_url: '', file_name: '', file_type: '', xml_url: ''})
    uploadState.value = false
}

const assignFile = () => {
    // readonly is true, form will have all file info from api, don't need assign.
    if (!props.readonlyMode) {
        form.value.file_id = !form.value.file_id ? currentPdfInfo.value.id || 0 : form.value.file_id
        form.value.file_name = !form.value.file_name
            ? currentPdfInfo.value.file_url?.split('/').pop() || ''
            : form.value.file_name
        form.value.file_url = !form.value.file_url ? currentPdfInfo.value.file_url || '' : form.value.file_url
        form.value.xml_url = !form.value.xml_url ? currentPdfInfo.value.xml_url || '' : form.value.xml_url
    }

    updatePdfInfo({
        id: form.value.file_id,
        file_name: form.value.file_name,
        file_url: form.value.file_url,
        xml_url: form.value.xml_url,
    })
}

const getPopupContainer = (node: any) => {
    return tableWrapRef.value
}

const closeUploadModal = () => {
    showUploadModall.value = false
    uploadProcessing.value = false
}

const uploadInvoiceFiles = async () => {
    try {
        uploadProcessing.value = true
        const response = (await uploadComp.value?.uploadFiles()) as any
        if (response !== null) {
            if (response.data.statusCode === 200) {
                message.success(i18n.t('ApComponents.success'))
                form.value.file_url = response.data.data.file_url
                form.value.xml_url = response.data.data.xml_url
                emits('update', true)
            }
            showUploadModall.value = false
        }
    } catch (error: any) {
        console.log(error)
    } finally {
        uploadProcessing.value = false
    }
}
const activeKey = ref([])
const handleMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
        case '1':
            // delete
            console.log('click -- delete')
            deleteInvoice(form.value)
            break
        case '2':
            // resend
            console.log('click -- resend', e)
            resendFlow(form.value)
            break
        case '3':
            // reverse
            console.log('click -- reverse')
            popConfirmModal()
            break
    }
}
const approveFlow = async (record: any) => {
    console.log('approveFlow', record)
    formLoading.value = true
    try {
        await store.dispatch('ApStore/approveFlow', {
            uuid: record.uuid,
            email: userInfo?.email,
            invoice_id: record.invoice_id,
        })
    } catch (e) {
        console.log('approve flow error', e)
    } finally {
        // refresh approval data
        fetchApprovalFlowData()
        formLoading.value = false
    }
}
const rejectFlow = async (record: any) => {
    console.log('rejectFlow', record)
    formLoading.value = true
    try {
        await store.dispatch('ApStore/rejectFlow', {
            uuid: record.uuid,
            email: userInfo?.email,
            invoice_id: record.invoice_id,
        })
    } catch (e) {
        console.log('reject flow error', e)
    } finally {
        // refresh approval data
        fetchApprovalFlowData()
        formLoading.value = false
    }
}
const resendFlow = async (record: any) => {
    console.log('resendFlow', record)
    formLoading.value = true
    try {
        await store.dispatch('ApStore/resendApprovalFlow', record)
    } catch (e) {
        console.log('resend flow error', e)
    } finally {
        // refresh approval data
        fetchApprovalFlowData()
        formLoading.value = false
    }
}
const deleteInvoice = async (record: any) => {
    console.log('deleteInvoice', record)
    formLoading.value = true
    try {
        await deleteInvoiceWithId(record.id)
        message.success('delete success')
    } catch (e) {
        console.log('delete invoice error', e)
    } finally {
        formLoading.value = false
        emits('dismiss', 'delete')
    }
}
const isShowAction = (record: any) => {
    let res = true
    // 非审批用户不可见
    if (
        !record.email_list.toLowerCase().includes(userInfo?.email.toLowerCase()) &&
        userInfo?.account !== 'kycadmin' &&
        userInfo?.account !== '8888'
    )
        return false
    if (record.type === 'INVOICE') {
        console.log('isShowAction -- invoice', record)
        // 第一个判断
        if (record.level === 1 && record.status !== 0) return false
        // 非第一个和最后一个的数据判断
        for (let i = 0; i < record.level - 1; i++) {
            const itemFlow = state.invoiceFlowData[i]
            if (itemFlow.status === 0 || itemFlow.status === 2) {
                res = false
                break
            }
        }
        // 对自己的判断
        if (record.status !== 0) return false
        // 最后一个判断
        if (record.level === state.invoiceFlowData.length && record.status !== 0) return false
    } else if (record.type === 'PAYMENT') {
        console.log('isShowAction -- payment', record)
        for (let i = 0; i < state.invoiceFlowData.length; i++) {
            const itemFlow = state.invoiceFlowData[i]
            if (itemFlow.status === 0 || itemFlow.status === 2) {
                res = false
                break
            }
        }
        // 第一个判断
        if (record.level === 1 && record.status !== 0) return false
        // 非第一个和最后一个的数据判断
        for (let i = 0; i < record.level - 1; i++) {
            const itemFlow = state.paymentFlowData[i]
            if (itemFlow.status === 0 || itemFlow.status === 2) {
                res = false
                break
            }
        }
        // 对自己的判断
        if (record.status !== 0) return false
        // 最后一个判断
        if (record.level === state.paymentFlowData.length && record.status !== 0) return false
    }
    return res
}

const handleReverseMenuClick: MenuProps['onClick'] = e => {
    switch (e.key) {
        case '1':
            // reverse only
            console.log('click -- reverse')
            confirmationConfirm()
            break
        case '2':
            // reverse and redo
            console.log('click -- reverse & redo', e)
            handleReverseAndRedoClick()
            break
    }
}

const handleReverseAndRedoClick = async () => {
    showReverseLoading.value = true
    await reversePurchase(true)
}

const copyInvoice = () => {
    const {...record} = {..._.cloneDeep(form.value)}
    record.id = null
    record.br_flag = null
    record.sap_document_id = null
    record.sap_reverse_document_id = null
    record.sap_message = null
    record.engine_document_id = ''
    record.engine_reverse_document_id = null
    record.xml_status = 'PENDING'
    record.xml_message = null
    record.invoice_no = null
    record.reference_no = ''
    delete record.create_time
    delete record.update_time
    record.creator = null
    record.creator_name = null
    emits('copy', record)
}

watch(
    () => taxRatesList.value,
    list => {
        if (props.readonlyMode) return
        form.value.tax_content = []
        if (list.length) {
            list.forEach((i: any) => {
                form.value.tax_content = [...form.value.tax_content, {...i, value: 0}]
            })
            taxRateCheck.value = true
        } else {
            taxRateCheck.value = false
        }
    },
)

watch(
    () => form.value.reference_no,
    (count, prevCount) => {
        if (count && !props.readonlyMode) {
            form.value.reference_no = count.toUpperCase()
        }
    },
)

// watch(
//     () => form.value.total_fee_local,
//     _.debounce((count, prevCount) => {
//         if ((prev_total.value || 0) < 0) {
//             total_diff.value = 0
//             prev_total.value = 0
//             return
//         }
//         console.log('previous, current', prev_total.value, count, form.value.total_fee_local)
//         total_diff.value = Math.abs(+(prev_total.value || 0) - +(count || 0))
//         prev_total.value = +(form.value.total_fee_local || 0)
//     }, 1000),
// )

watch(
    () => form.value.issuer_name,
    (count, prevCount) => {
        if (autoCalTaxFlag.value === '0') {
            form.value.tax_content = calculateTaxRates(form.value.net_amount)
            calculateInvoiceTotal()
        }
    },
)

watch(
    () => form.value.items,
    (count, prevCount) => {
        if (!autoCalculateState.value) return
        if (props.readonlyMode || !count) return
        const sum = new Decimal(
            count.reduce((prev: any, curr: any) => {
                return prev + curr.total
            }, 0),
        )
            .toDP(2)
            .toNumber()
        if (form.value.net_amount === sum) return
        form.value.net_amount = sum
        if (autoCalTaxFlag.value === '0' || autoCalTaxFlag.value === '2') {
            form.value.tax_content = calculateTaxRates(form.value.net_amount)
            calculateInvoiceTotal()
        }
    },
    {deep: true},
)

watch(
    () => ocrInvoiceItem.value,
    (count, prevCount) => {
        if (Object.keys(ocrInvoiceItem.value).length > 1) {
            formLoading.value = false
        }
        if (Object.keys(ocrInvoiceItem.value).length === 1 && ocrInvoiceItem.value.NotExisted) {
            formLoading.value = false
        }
    },
    {deep: true},
)

watch([x, y], () => {
    if (!startedDrag.value) {
        startX.value = x.value
        startY.value = y.value
        const bodyRect = document.body.getBoundingClientRect()
        const titleRect = modalTitleRef.value?.getBoundingClientRect()

        dragRect.value.left = -(bodyRect.width + titleRect!.width)
        dragRect.value.right = bodyRect.width + titleRect!.width

        dragRect.value.bottom = bodyRect.height + titleRect!.height
        preTransformX.value = transformX.value
        preTransformY.value = transformY.value
    }
    startedDrag.value = true
})
watch(isDragging, () => {
    if (!isDragging) {
        startedDrag.value = false
    }
})

watchEffect(() => {
    if (startedDrag.value) {
        transformX.value =
            preTransformX.value + Math.min(Math.max(dragRect.value.left, x.value), dragRect.value.right) - startX.value
        transformY.value =
            preTransformY.value + Math.min(Math.max(dragRect.value.top, y.value), dragRect.value.bottom) - startY.value
    }
})

const transformStyle = computed<CSSProperties>(() => {
    return {
        transform: `translate(${transformX.value}px, ${transformY.value}px)`,
    }
})
const fetchApprovalFlowData = async () => {
    await fetchInvoiceFlowData({invoice_id: props.currentInvoice.id})
    await fetchPaymentFlowData({invoice_id: props.currentInvoice.id})
}

onBeforeMount(async () => {
    console.log('!!!!======!!!!!')
    formLoading.value = true
    userType.value = userInfo.roles === '9996' ? 'jushi' : 'common'
    console.log('operationMode', props.operationMode)
    updateTaxRates([])

    if (props.currentInvoice.id) {
        fetchApprovalFlowData()
    }
    if (currentPdfInfo.value.id) {
        const file_filter = {file_id: currentPdfInfo.value.id}
        await fetchOcrInvoiceItem(file_filter)
    }
    if (props.operationMode === 'editing' || props.readonlyMode) {
        let invoiceObj = props.currentInvoice.id ? props.currentInvoice : ocrInvoiceItem.value

        if (props.invoiceId) {
            const res = await fetchApInvoiceDetail({id: props.invoiceId})
            invoiceObj = invoiceDetail.value
            // form.value = _.cloneDeep(invoiceObj)
        } else if (props.invoiceNo) {
            const res = await fetchApInvoiceDetail({invoice_no: props.invoiceNo, company_code: userCompany[0].code})
            invoiceObj = invoiceDetail.value
            // form.value = _.cloneDeep(invoiceObj)
        }
        if (props.from === 'massive_process_created') {
            const res = await fetchApInvoiceDetail({file_id: currentPdfInfo.value.id, 'br_flag[$not]': '3'})
            invoiceObj = invoiceDetail.value
        }

        form.value = _.cloneDeep({...invoiceObj, invoice_currency: invoiceObj.invoice_currency || localCurrency})
        reverseDt.value = invoiceObj.posting_date
        reverseSapDocNo.value = invoiceObj.sap_document_id

        const {max_reverse_number, reverse_number} = props.currentInvoice // number compare only used by BR
        if (max_reverse_number !== undefined && reverse_number !== undefined) {
            enableReverse.value = Boolean(max_reverse_number > reverse_number)
        } else {
            enableReverse.value = form.value.br_flag !== 3
        }
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee, balance
        // const {amount, totalTaxable, totalTax, totalFee, balance} =  _.cloneDeep(this.currentInvoice)
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee
        const {net_amount, total_tax, total_fee} = props.currentInvoice.id
            ? _.cloneDeep(props.currentInvoice)
            : _.cloneDeep(invoiceObj)

        // form.value.pay_method =
        //     props.currentInvoice.br_flag === '2' && !props.currentInvoice.sap_document_id ? '2' : '1'

        await nextTick(() => {
            // form.value.net_amount = net_amount ? net_amount : 0.0 //TODO Remove ???
            // form.value.totalTaxable = totalTaxable ? totalTaxable : 0.0
            form.value.total_tax = total_tax ? total_tax : 0.0
            form.value.total_fee = total_fee ? total_fee : 0.0
            // form.value.balance = balance ? balance : 0.0
        })
    }
    if (
        !props.readonlyMode &&
        (props.operationMode === 'creating' || props.operationMode === 'editing') &&
        !form.value.items?.length
    ) {
        if (props.from === 'copy') {
            //如果是copy 发票清空以下三个属性
            form.value.file_id = 0
            form.value.file_page_index = null
            form.value.file_url = null
            form.value.xml_url = null
        }
        addItem()
    }
    if (!props.readonlyMode) {
        if (apIntegration === 1 && INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)) {
            form.value.pay_method = '1'
        } else if (apIntegration === 1 && !INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)) {
            form.value.pay_method = '2'
        } else if (currentPdfInfo.value.file_type === '1') {
            form.value.pay_method = '1'
        } else if (currentPdfInfo.value.file_type === '3') {
            form.value.pay_method = '2'
        }
        form.value.purpose = 'STANDARD'
        form.value.invoice_create_date = getToday()
        form.value.invoice_due_date = getToday()
        form.value.posting_date = getToday()
        form.value.mx_discount = 0
        form.value.mx_isr = 0
        form.value.mx_iva = 0
    }
    // form.value.file_id = currentPdfInfo.value.id || ''
    // form.value.file_name = currentPdfInfo.value.file_name || ''
    // form.value.file_url = currentPdfInfo.value.file_url || ''
    assignFile()
    try {
        if (apIntegration === 1 && !props.readonlyMode) {
            await Promise.all([
                fetchAllBankList({company_code: userCompany[0].code}),
                fetchSupplierDropDown({...currentUserCompanyQuery}),
                fetchAccountDescDropdown({...accountQuery}),
                fetchCompanyTaxInfo({code: userCompany[0].code}),
                fetchApIntegrationPoTop({company_code: userCompany[0].code}),
                fetchSapMasterData({...currentUserCompanyQuery}),
                console.log('fetchApIntegrationPoTop ----------------', state.poTopListData),
            ])
        } else {
            await Promise.all([
                fetchAllBankList({company_code: userCompany[0].code}),
                fetchSupplierDropDown({...currentUserCompanyQuery}),
                fetchAccountDescDropdown({...accountQuery}),
                fetchCompanyTaxInfo({code: userCompany[0].code}),
                fetchSapMasterData({...currentUserCompanyQuery}),
            ])
        }
        // add operationMode condition state, so editing can keep previous refNo
        if (form.value.reference_no && !props.readonlyMode && props.operationMode !== 'editing' && apIntegration !== 1) {
            await checkRefNoRepetition(form.value.reference_no)
        } else if (form.value.reference_no && !props.readonlyMode && props.operationMode !== 'editing' && apIntegration === 1) {
            await referenceNoValid()
        }
        await updateSpot()
    } catch (e) {
        console.log(e)
    } finally {
        formLoading.value = false
        console.log('page before mounted', form.value, enableReverse.value)
        if (userType.value === 'jushi') {
            enableTaxExempt.value = true
            changeTaxExempt()
        }
    }
    console.log('---------------current invoice form', form)
})

onMounted(async () => {
    console.log('!!!!====!!!!')
    if (props.from === 'copy') {
        form.value = _.cloneDeep(props.currentInvoice)
        if (form.value.file_url && form.value.file_url !== '') {
            const fileObj = {
                file_name: form.value.file_name,
                file_url: form.value.file_url,
                xml_url: form.value.xml_url,
                id: form.value.file_id,
            }
            uploadFileList.value.push(fileObj)
            updatePdfInfo(fileObj)
            uploadState.value = true
        } else {
            form.value.file_id = 0
            form.value.file_page_index = null
            form.value.file_url = null
            form.value.xml_url = null
        }
        countryCodeCheck.value = true
        spotRateCheck.value = true
        taxRateCheck.value = true
        satCertification.value = form.value.xml_url !== null && form.value.xml_url !== '' ? 'SUCCESS' : ''
    }
    // console.log(accountCurrencyOptions.value)
    console.log('payment: ', payment_discounts_label.value, payment_discounts_label_format.value, discount_saved.value)
    console.log('payment input: ', Boolean(payment_discounts_label.value && discount_saved.value))
    formRef.value?.clearValidate()
    console.log(tableWrapRef)
})

onUnmounted(() => {
    uploadFileList.value = [] as any[]
    updatePdfInfo({id: '', file_url: '', file_name: '', file_type: '', xml_url: ''})
    updateApTopCoa([])
})

defineExpose({resetFormField, initFormData})
</script>

<template>
    <div class="ap-invoice-page-wrap">
        <a-spin :tip="apIntegration === 1 ? i18n.t('commonTag.sapTip') : ''" :spinning="realizeLoading"
            wrapperClassName="custom-spin">
            <a-form
                ref="formRef"
                :model="form"
                :layout="'vertical'"
                :rules="readonlyMode ? {} : rules"
                class="invoice-form"
                autocomplete="off"
            >
                <div class="ap-invoice-block">
                    <a-row :gutter="24">
                        <a-col :span="8">
                            <a-form-item name="purpose" :label="i18n.t('bkAp.purpose')">
                                <a-select
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="form.purpose"
                                    :disabled="readonlyMode"
                                    @change="handlePayMethodChange('purpose', $event)"
                                >
                                    <a-select-option
                                        v-for="item in purposeOptions"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.label }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="apIntegration === 1" :span="8">
                            <a-form-item name="po">
                                <template v-slot:label>
                                    {{ i18n.t('bkAp.purchaseOrder') }}
                                </template>
                                <a-spin :spinning="poLoading" wrapperClassName="input-spin">
                                    <a-input
                                        v-model:value="form.po"
                                        ref="po"
                                        @blur="poNumberApIntegrationChange"
                                        :placeholder="readonlyMode ? '' : i18n.t('bkAp.poPlaceholder')"
                                        :disabled="readonlyMode || form.purpose === null || form.purpose === undefined"
                                    >
                                        <template #suffix>
                                            <svg-icon
                                                style="cursor: pointer"
                                                name="icon_fetch_gery"
                                                @click="poNumberApIntegrationChange"
                                            ></svg-icon>
                                        </template>
                                    </a-input>
                                </a-spin>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="apIntegration === 1" :span="8">
                            <!-- <a-form-item required name="po"> -->
                            <a-form-item name="reference_no">
                                <template v-slot:label>
                                    {{ i18n.t('bkAp.referenceNo') }}
                                </template>
                                <a-spin :spinning="poNumberLoading" wrapperClassName="input-spin">
                                    <a-input
                                        v-model:value="form.reference_no"
                                        @blur="referenceNoValid()"
                                        @keypress.enter="handleEnterEvent"
                                        ref="referenceNo"
                                        @focus="selectInputValues"
                                        :disabled="readonlyMode && !(form.purpose === 'PROFORMA' && form.br_flag === 2)"
                                    >
                                    </a-input>
                                </a-spin>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item name="issuer_name" :label="i18n.t('bkAp.companyName')">
                                <a-input
                                    v-if="userType === 'jushi'"
                                    v-model:value="form.issuer_name"
                                    placeholder="Please input"
                                ></a-input>
                                <a-config-provider v-if="apIntegration === 1">
                                    <template
                                        v-if="(isContactKeywordEmpty && !contactLoading) || contactLoading"
                                        #renderEmpty
                                    >
                                        <div v-if="isContactKeywordEmpty && !contactLoading" style="text-align: center">
                                            <smile-outlined style="font-size: 20px" />
                                            <p>{{ i18n.t('bkApInvoice.issueNameEmpty') }}</p>
                                        </div>
                                        <div v-if="contactLoading" style="text-align: center">
                                            <a-spin
                                                :tip="apIntegration === 1 ? i18n.t('commonTag.sapTip') : ''"
                                                :spinning="contactLoading"
                                                size="small"
                                                wrapperClassName="custom-spin"
                                            />
                                        </div>
                                    </template>
                                    <a-select
                                        v-if="apIntegration === 1"
                                        :placeholder="i18n.t('commonTag.msgSelect')"
                                        v-model:value="form.issuer_name"
                                        :disabled="readonlyMode"
                                        show-search
                                        :filter-option="false"
                                        @keyup="handleIssuerNameKeyup($event)"
                                        @change="handleCompanyChange('ship', $event)"
                                        @search="contactSearch"
                                        @popupScroll="contactScroll"
                                        @focus="handleFocus"
                                    >
                                        <a-select-option
                                            v-for="item in state.supplierList"
                                            :key="item.id"
                                            :value="item.id"
                                            >{{ item.contact_name }}</a-select-option
                                        >
                                        <template v-if="apIntegration !== 1" #dropdownRender="{menuNode: menu}">
                                            <div
                                                style="padding: 4px 12px; cursor: pointer; color: #004fc1"
                                                @click="addNewContact"
                                            >
                                                <plus-circle-outlined />
                                                {{ i18n.t('ApComponents.NewBP') }}
                                                <a-divider style="margin: 4px 0" />
                                            </div>
                                            <v-nodes :vnodes="menu" />
                                        </template>
                                    </a-select>
                                </a-config-provider>
                                <a-select
                                    v-if="apIntegration !== 1"
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="form.issuer_name"
                                    :disabled="readonlyMode"
                                    show-search
                                    :filter-option="false"
                                    @keyup="handleIssuerNameKeyup($event)"
                                    @change="handleCompanyChange('ship', $event)"
                                    @search="contactSearch"
                                    @popupScroll="contactScroll"
                                    :loading="contactLoading"
                                >
                                    <a-select-option v-for="item in state.supplierList" :key="item.id" :value="item.id">{{
                                        item.contact_name
                                    }}</a-select-option>
                                    <template v-if="apIntegration !== 1" #dropdownRender="{menuNode: menu}">
                                        <div
                                            style="padding: 4px 12px; cursor: pointer; color: #004fc1"
                                            @click="addNewContact"
                                        >
                                            <plus-circle-outlined />
                                            {{ i18n.t('ApComponents.NewBP') }}
                                            <a-divider style="margin: 4px 0" />
                                        </div>
                                        <v-nodes :vnodes="menu" />
                                    </template>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="apIntegration !== 1" :span="8">
                            <a-form-item name="referenceNo">
                                <template v-slot:label>
                                    {{ i18n.t('bkAp.referenceNo') }}
                                    <a-tooltip>
                                        <!-- <template #title>Reference No. in the receipts.</template> -->
                                        <template #title>{{ i18n.t('ApComponents.referenceNo') }}</template>
                                        <exclamation-circle-outlined class="icon-exclamation" />
                                    </a-tooltip>
                                </template>
                                <a-spin :spinning="referenceNoLoading" wrapperClassName="input-spin">
                                    <a-input
                                        v-model:value="form.reference_no"
                                        @blur="referenceNoChange"
                                        :disabled="readonlyMode"
                                    ></a-input>
                                </a-spin>
                            </a-form-item>
                            <div style="padding: 2px 5px">&nbsp;</div>
                        </a-col>
                        <a-col v-if="apIntegration === 1" :span="8">
                            <a-form-item
                                :name="!readonlyMode ? 'postingDate' : ''"
                                :label="i18n.t('bkAp.apIntegrationPostingDate')"
                            >
                                <a-date-picker
                                    v-model:value="form.posting_date"
                                    :disabled="readonlyMode"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :placeholder="$t('bkAp.postingDate')"
                                    style="width: 100%"
                                    clearable
                                    @change="postingDateChange"
                                >
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                v-if="userType !== 'jushi'"
                                name="invoice_currency"
                                :label="i18n.t('bkAp.currency')"
                            >
                                <a-select
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="form.invoice_currency"
                                    :disabled="readonlyMode"
                                    @change="updateSpot"
                                >
                                    <a-select-option
                                        v-for="item in accountCurrencyOptions"
                                        :key="item.key"
                                        :value="item.key"
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                                <div v-show="form.invoice_currency === localCurrency" style="padding: 2px 5px">&nbsp;</div>
                                <!-- <div v-show="form.invoice_currency !== localCurrency" style="color: red; padding: 2px 5px">
                                    <span>
                                        {{
                                            i18n.t('ApComponents.spotCurrency', {
                                                rate: spot.rate || 'null',
                                                date: postingDate,
                                            })
                                        }}
                                        {{ !spot.rate ? ', ' + i18n.t('ApComponents.contactAdmin') : '' }}
                                    </span>
                                </div> -->
                            </a-form-item>
                            <a-form-item v-else name="invoice_currency" :label="i18n.t('bkAp.currency')">
                                <a-select
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="form.invoice_currency"
                                    :disabled="readonlyMode"
                                >
                                    <a-select-option
                                        v-for="item in accountCurrencyOptions"
                                        :key="item.key"
                                        :value="item.key"
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8" v-if="apIntegration === 1">
                            <a-form-item name="invoiceCreateDate" :label="i18n.t('bkAp.postingDate')">
                                <a-date-picker
                                    v-model:value="form.invoice_create_date"
                                    :disabled="readonlyMode"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    placeholder="Create Date"
                                    style="width: 100%"
                                    clearable
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8" v-show="readonlyMode && apIntegration !== 1">
                            <a-form-item name="invoiceFixedDate" :label="i18n.t('bkAp.dueDate')">
                                <a-date-picker
                                    v-model:value="form.invoice_due_date"
                                    :disabled="readonlyMode"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    placeholder="Fixed Date"
                                    style="width: 100%"
                                    clearable
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8" v-if="apIntegration !== 1">
                            <a-form-item :name="!readonlyMode ? 'postingDate' : ''" :label="i18n.t('bkAp.postingDate')">
                                <a-date-picker
                                    v-model:value="form.posting_date"
                                    :disabled="readonlyMode"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :placeholder="$t('bkAp.postingDate')"
                                    style="width: 100%"
                                    clearable
                                    @change="postingDateChange"
                                >
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="userType !== 'jushi'" :span="8">
                            <div style="width: 100%">
                                <a-form-item name="payMethod" :label="i18n.t('bkAp.payMethod')">
                                    <a-select
                                        :placeholder="i18n.t('commonTag.msgSelect')"
                                        v-model:value="form.pay_method"
                                        :disabled="readonlyMode"
                                        @change="handlePayMethodChange('payMethod', $event)"
                                    >
                                        <a-select-option
                                            v-for="item in apIntegration === 1
                                                ? payMethodApIntegrationOptions
                                                : payMethodOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            >{{ item.label }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>

                                <a-button
                                    style="top: -92px; left: 90px; height: 30px"
                                    v-if="
                                        form.pay_method === '1' &&
                                        form.br_flag === 0 &&
                                        apIntegration !== 1 &&
                                        form.pay_method === '1' &&
                                        form.br_flag === 0 &&
                                        paymentIntegration !== true
                                    "
                                    class="cancel-button"
                                    shape="round"
                                    @click="popConvertModal()"
                                >
                                    {{ i18n.t('ArComponents.convert') }}
                                </a-button>
                            </div>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item
                                name=""
                                label=""
                                v-if="
                                    readonlyMode &&
                                    form.file_url !== undefined &&
                                    form.file_url !== null &&
                                    form.file_url !== '' &&
                                    form.xml_url !== undefined &&
                                    form.xml_url !== null &&
                                    form.file_url !== ''
                                "
                            >
                                <br />
                                <span
                                    style="
                                        margin-left: 60px;
                                        margin-right: 5px;
                                        color: green;
                                        font-size: 18px;
                                        font-weight: bold;
                                        vertical-align: middle;
                                    "
                                >
                                    SAT Certified
                                </span>
                                <svg-icon
                                    name="mx_checkedbox"
                                    style="vertical-align: middle; width: 20px; height: 20px"
                                ></svg-icon>
                            </a-form-item>
                            <a-form-item name="" label="" v-if="!readonlyMode">
                                <br />
                                <a-spin :spinning="satCertification === 'PENDING'" wrapperClassName="input-spin">
                                    <span
                                        v-if="!readonlyMode && satCertification === 'PENDING'"
                                        style="
                                            margin-left: 60px;
                                            margin-right: 5px;
                                            color: grey;
                                            font-size: 18px;
                                            font-weight: bold;
                                            vertical-align: middle;
                                        "
                                    >
                                        SAT certification in progress...
                                    </span>
                                    <span
                                        v-if="!readonlyMode && satCertification === 'SUCCESS'"
                                        style="
                                            margin-left: 60px;
                                            margin-right: 5px;
                                            color: green;
                                            font-size: 18px;
                                            font-weight: bold;
                                            vertical-align: middle;
                                        "
                                    >
                                        SAT Certified
                                    </span>
                                    <span
                                        v-if="!readonlyMode && satCertification === 'FAILURE'"
                                        style="
                                            margin-left: 60px;
                                            margin-right: 5px;
                                            color: red;
                                            font-size: 18px;
                                            font-weight: bold;
                                            vertical-align: middle;
                                        "
                                    >
                                        SAT Not Certified
                                    </span>
                                    <svg-icon
                                        v-if="!readonlyMode && satCertification === 'SUCCESS'"
                                        name="mx_checkedbox"
                                        style="vertical-align: middle; width: 20px; height: 20px"
                                    ></svg-icon>
                                </a-spin>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
                <div class="ap-invoice-block" ref="tableWrapRef">
                    <a-table :dataSource="form.items" :pagination="false">
                        <a-table-column
                            align="center"
                            :title="i18n.t('bkAp.itemNo')"
                            data-index="item_no"
                            width="5%"
                            :ellipsis="true"
                        />

                        <a-table-column
                            v-if="form.po == null || form.po.length == 0"
                            :title="i18n.t('bkAp.modelNumber')"
                            data-index="model"
                            :ellipsis="true"
                            width="15%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'model']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-input
                                        v-model:value="record.model"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </a-form-item>
                                <template v-else>
                                    {{ record.model }}
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column
                            v-if="form.po != null && form.po.length > 0"
                            :title="i18n.t('bkAp.modelNumber')"
                            data-index="model"
                            :ellipsis="true"
                            width="15%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'material']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-input
                                        v-model:value="record.material"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </a-form-item>
                                <template v-else>
                                    {{ record.material }}
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column
                            :title="i18n.t('bkAp.description')"
                            data-index="description"
                            :ellipsis="true"
                            width="15%"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'description']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-input
                                        v-model:value="record.description"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </a-form-item>
                                <template v-else>
                                    {{ record.description }}
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column :title="'UoM'" data-index="uom" width="10%" v-if="userType === 'jushi'">
                            <template #default="{index, record}">
                                <a-form-item :name="['items', index, 'uom']" v-if="!readonlyMode" class="column-item-input">
                                    <a-input
                                        v-model:value="record.uom"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </a-form-item>
                                <template v-else>
                                    {{ record.uom }}
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column
                            :title="i18n.t('bkAp.accountingCategory')"
                            data-index="bank_account"
                            width="20%"
                            v-if="userType !== 'jushi' && apIntegration !== 1"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'credit_coa_id']"
                                    :rules="rules['requireGLAccount']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-select
                                        :placeholder="i18n.t('commonTag.msgSelect')"
                                        v-model:value="record.credit_coa_id"
                                        show-search
                                        :dropdownMatchSelectWidth="400"
                                        :filter-option="filterOption"
                                        :getPopupContainer="getPopupContainer"
                                        class="table-input"
                                        @change="changeItemListRowExpenseAccount(record.credit_coa_id, index, record)"
                                    >
                                        <a-select-option
                                            v-for="item in accountDescList"
                                            :key="item.account_code + ' | ' + item.name"
                                            :value="item.id"
                                            >{{ item.account_code.substring(0, 4) + ' | ' + item.name }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                                <template v-else>
                                    {{ expenseAccountAlias(record, accountDescList) }}
                                </template>
                            </template>
                        </a-table-column>
                        <!-- <a-table-column
                                :title="i18n.t('bkAp.type')"
                                data-index="type"
                                width="15%"
                                :ellipsis="true"
                                v-if="apIntegration === 1"
                            >
                                <template #default="{index, record}">
                                    <a-form-item :name="['items', index, 'type']" v-if="!readonlyMode">
                                        <a-select
                                            :placeholder="i18n.t('commonTag.msgSelect')"
                                            v-model:value="record.type"
                                            show-search
                                            :dropdownMatchSelectWidth="200"
                                            class="table-input"
                                            @change="changeItemListRowType(record.type, index, record)"
                                        >
                                            <a-select-option
                                                v-for="item in typeOptions"
                                                :key="item.value"
                                                :value="item.value"
                                                >{{ item.label }}</a-select-option
                                            >
                                        </a-select>
                                    </a-form-item>
                                    <template v-else>
                                        {{ record.type }}
                                    </template>
                                </template>
                            </a-table-column> -->
                        <!-- <a-table-column
                                :title="i18n.t('bkAp.costObject')"
                                data-index="costObject"
                                width="15%"
                                :ellipsis="true"
                                v-if="apIntegration === 1"
                            >
                                <template #default="{index, record}">
                                    <a-form-item :name="['items', index, 'costObject']" v-if="!readonlyMode">
                                        <a-input
                                            v-model:value="record.costObject"
                                            :placeholder="i18n.t('commonTag.msgInput')"
                                            class="table-input"
                                        ></a-input>
                                    </a-form-item>
                                    <template v-else>
                                        {{ record.costObject }}
                                    </template>
                                </template>
                            </a-table-column> -->
                        <a-table-column :title="i18n.t('bkAp.qty')" data-index="qty" :ellipsis="true" width="10%" v-if="form.purpose !== 'CREDIT_MEMO'">
                            <template #default="{index, record}">
                                <a-form-item :name="['items', index, 'qty']" v-if="!readonlyMode" class="column-item-input">
                                    <a-input-number
                                        v-model:value="record.qty"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :max="9999999"
                                        :min="0 || null"
                                        class="table-input"
                                        @blur="handleQtyChange(index)"
                                    ></a-input-number>
                                </a-form-item>
                                <template v-else>
                                    {{ record.qty }}
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column
                            :title="i18n.t('bkAp.unitPrice')"
                            data-index="unit_price"
                            :ellipsis="true"
                            width="10%"
                            v-if="form.purpose !== 'CREDIT_MEMO'"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'unit_price']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-input-number
                                        v-model:value="record.unit_price"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :controls="false"
                                        :precision="3"
                                        :min="0 || null"
                                        class="table-input"
                                        @blur="handleUnitPriceChange(index)"
                                    ></a-input-number>
                                </a-form-item>
                                <template v-else>
                                    {{ Number(record.unit_price).toFixed(3) }}
                                </template>
                            </template>
                        </a-table-column>

                        <a-table-column :title="i18n.t('bkAp.total')" data-index="total" :ellipsis="true" width="12%">
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'total']"
                                    :rules="rules['requireItem']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <!-- @change="handleItemTotalChange(record, index)"-->
                                    <!-- <a-input-number v-model:value="record.total"
                                            :placeholder="i18n.t('commonTag.msgInput')" :controls="false" :precision="2"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            class="table-input" :disabled="!autoCalculateState"
                                            @change="handleItemTotalChange(record, index)"
                                            @blur="blurItemTotalChange(record, index)"></a-input-number> -->
                                    <InputNumber
                                        class="amount-input-prime item-amount"
                                        v-model="record.total"
                                        :controls="false"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        :disabled="!autoCalculateState"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        :locale="decimalFormat()"
                                        fluid
                                        @value-change="handleItemTotalChange(record, index)"
                                        @blur="blurItemTotalChange(record, index)"
                                    />
                                </a-form-item>
                                <template v-else>
                                    <!-- {{ Number(record.total).toFixed(2) }} -->
                                    {{ $formatNumber(Number(record.total)) }}
                                </template>
                            </template>
                        </a-table-column>
                        <!-- <a-table-column title="ASSIGMENT" v-if="!readonlyMode" key="operation" width="10%"> -->
                        <a-table-column
                            :title="i18n.t('update.assigment')"
                            key="operation"
                            width="12%"
                            v-if="apIntegration === 1"
                        >
                            <template #default="{index, record}">
                                <span>
                                    <a-button
                                        type="link"
                                        @click="edit(record)"
                                        v-if="apIntegration === 1"
                                        :disabled="
                                            sapPayableGl !== 1 &&
                                            sapPayableProfitCenter !== 1 &&
                                            sapPayableWbs !== 1 &&
                                            sapPayableCostCenter !== 1 &&
                                            sapPayableInternalOrder !== 1
                                        "
                                    >
                                        <svg-icon name="icon_edit2"></svg-icon>
                                    </a-button>
                                </span>
                            </template>
                        </a-table-column>
                        <a-table-column
                            :title="i18n.t('bkAp.taxCode')"
                            key="tax_code"
                            width="13%"
                            v-if="apIntegration === 1"
                        >
                            <template #default="{index, record}">
                                <a-form-item
                                    :name="['items', index, 'tax_code']"
                                    v-if="!readonlyMode"
                                    class="column-item-input"
                                >
                                    <a-select
                                        v-model:value="record.tax_code"
                                        :placeholder="i18n.t('commonTag.msgSelect')"
                                        show-search
                                        allowClear
                                        :dropdownMatchSelectWidth="400"
                                        :filter-option="filterOption"
                                        class="table-input"
                                        :getPopupContainer="getPopupContainer"
                                    >
                                        <a-select-option
                                            v-for="item in state.masterData.ET_TAX_CODE"
                                            :key="item.TAXCODE + ', ' + item.DESCRIPTION"
                                            :value="item.TAXCODE"
                                            >{{ item.TAXCODE }}, {{ item.DESCRIPTION }}
                                        </a-select-option>
                                    </a-select>
                                </a-form-item>
                                <template v-else>
                                    {{ getTaxCodeLabel(record.tax_code) }}
                                </template>
                            </template>
                        </a-table-column>
                        <a-table-column :title="''" v-if="!readonlyMode" key="operation" width="10%">
                            <template #default="{index, record}">
                                <span>
                                    <a-button :disabled="form.items.length === 1" type="link" danger @click="remove(index)">
                                        <svg-icon name="icon_delete"></svg-icon>
                                    </a-button>
                                </span>
                            </template>
                        </a-table-column>
                    </a-table>
                    <a-button v-show="!readonlyMode" class="invoice-add" type="primary" ghost @click="addItem">
                        <template #icon><plus-outlined /></template>
                        {{ i18n.t('bkAp.addItemBtnTxt') }}
                    </a-button>
                    <div class="ap-invoice-amount-block">
                        <div class="ap-invoice-amount-block-left">
                            <a-collapse
                                v-model:activeKey="activeKey"
                                accordion
                                v-show="
                                    ((state.invoiceFlowData != null && state.invoiceFlowData.length > 0) ||
                                        (state.paymentFlowData != null && state.paymentFlowData.length > 0)) &&
                                    readonlyMode
                                "
                            >
                                <a-collapse-panel
                                    key="1"
                                    :header="i18n.t('connectivity.payable_approval_procedure')"
                                    v-show="state.invoiceFlowData != null && state.invoiceFlowData.length > 0"
                                >
                                    <a-table :dataSource="state.invoiceFlowData" :pagination="false">
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('bkApInvoice.level')"
                                            data-index="level"
                                            width="8%"
                                        />
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('bkApInvoice.emailList')"
                                            data-index="email_list"
                                            width="20%"
                                        />
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('bkApInvoice.approvalStatus')"
                                            data-index="status"
                                            width="10%"
                                            :ellipsis="true"
                                        >
                                            <template #default="{record}">
                                                <span v-if="record.status === 0">
                                                    <a-tag class="tag-gray">{{
                                                        i18n.t('bkApInvoice.statusPending')
                                                    }}</a-tag>
                                                </span>
                                                <span v-if="record.status === 1">
                                                    <a-tag color="success">{{
                                                        i18n.t('bkApInvoice.statusApproved')
                                                    }}</a-tag>
                                                </span>
                                                <span v-else-if="record.status === 2">
                                                    <a-tag class="tag-red">{{
                                                        i18n.t('bkApInvoice.statusRejected')
                                                    }}</a-tag>
                                                </span>
                                            </template>
                                        </a-table-column>
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('commonTag.action')"
                                            key="operation"
                                            width="10%"
                                        >
                                            <template #default="{index, record}">
                                                <span>
                                                    <a-button
                                                        v-show="isShowAction(record)"
                                                        type="link"
                                                        danger
                                                        @click="approveFlow(record)"
                                                    >
                                                        <CheckOutlined style="font-size: 14px; color: green" />
                                                    </a-button>
                                                </span>
                                                <span>
                                                    <a-button
                                                        v-show="isShowAction(record)"
                                                        type="link"
                                                        danger
                                                        @click="rejectFlow(record)"
                                                    >
                                                        <CloseOutlined style="font-size: 14px; color: red" />
                                                    </a-button>
                                                </span>
                                                <!-- <span>
                                                        <a-button v-show="isShowAction(record)" type="link" danger
                                                            @click="resendFlow(record)">
                                                            <SendOutlined style="font-size: 14px; color: black"/>
                                                        </a-button>
                                                    </span>-->
                                            </template>
                                        </a-table-column>
                                    </a-table>
                                </a-collapse-panel>
                                <a-collapse-panel
                                    key="2"
                                    :header="i18n.t('connectivity.payment_approval_procedure')"
                                    v-show="state.paymentFlowData != null && state.paymentFlowData.length > 0"
                                >
                                    <a-table :dataSource="state.paymentFlowData" :pagination="false">
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('bkApInvoice.level')"
                                            data-index="level"
                                            width="5%"
                                        />
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('bkApInvoice.emailList')"
                                            data-index="email_list"
                                            width="20%"
                                        />
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('bkApInvoice.approvalStatus')"
                                            data-index="status"
                                            width="8%"
                                            :ellipsis="true"
                                        >
                                            <template #default="{record}">
                                                <span v-if="record.status === 0">
                                                    <a-tag class="tag-gray">{{
                                                        i18n.t('bkApInvoice.statusPending')
                                                    }}</a-tag>
                                                </span>
                                                <span v-if="record.status === 1">
                                                    <a-tag color="success">{{
                                                        i18n.t('bkApInvoice.statusApproved')
                                                    }}</a-tag>
                                                </span>
                                                <span v-else-if="record.status === 2">
                                                    <a-tag class="tag-red">{{
                                                        i18n.t('bkApInvoice.statusRejected')
                                                    }}</a-tag>
                                                </span>
                                            </template>
                                        </a-table-column>
                                        <a-table-column
                                            align="center"
                                            :title="i18n.t('commonTag.action')"
                                            key="operation"
                                            width="15%"
                                        >
                                            <template #default="{index, record}">
                                                <span>
                                                    <a-button
                                                        v-show="isShowAction(record)"
                                                        type="link"
                                                        danger
                                                        @click="approveFlow(record)"
                                                    >
                                                        <CheckOutlined style="font-size: 14px; color: green" />
                                                    </a-button>
                                                </span>
                                                <span>
                                                    <a-button
                                                        v-show="isShowAction(record)"
                                                        type="link"
                                                        danger
                                                        @click="rejectFlow(record)"
                                                    >
                                                        <CloseOutlined style="font-size: 14px; color: red" />
                                                    </a-button>
                                                </span>
                                                <!-- <span>
                                                        <a-button v-show="isShowAction(record)" type="link" danger
                                                            @click="resendFlow(record)">
                                                            <SendOutlined style="font-size: 14px; color: black"/>
                                                        </a-button>
                                                    </span> -->
                                            </template>
                                        </a-table-column>
                                    </a-table>
                                </a-collapse-panel>
                            </a-collapse>
                            <date-delay v-if="!readonlyMode" @update="delayUpdate"></date-delay>
                        </div>
                        <div class="ap-invoice-amount-block-right">
                            <div class="amount-block">
                                <div v-show="!readonlyMode" v-if="userType !== 'jushi'" class="title">
                                    <a-switch
                                        class="switch-wrap"
                                        v-model:checked="autoCalculateState"
                                        size="small"
                                        @change="changeAutoCalculateMode"
                                    />{{ i18n.t('ApComponents.auto') }}
                                </div>
                                <div v-show="!readonlyMode" v-if="userType !== 'jushi'" class="title">
                                    <a-switch
                                        class="switch-wrap"
                                        size="small"
                                        v-model:checked="enableTaxExempt"
                                        @change="changeTaxExempt"
                                    />{{ i18n.t('ApComponents.exempt') }}
                                </div>
                                <div v-if="userType === 'jushi'" class="amount-item-wrap">
                                    <div class="amount-item">
                                        <div class="amount-lable bold">{{ i18n.t('ApComponents.amount') }}</div>
                                        <a-input-number
                                            v-model:value="form.total_fee"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                </div>
                                <div v-else class="amount-item-wrap">
                                    <div class="amount-item">
                                        <div class="amount-lable">{{ i18n.t('ApComponents.netAmount') }}</div>
                                        <a-input-number
                                            v-model:value="form.net_amount"
                                            :disabled="true"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <!-- taxRatesList item-block  -->
                                    <div class="amount-item" v-for="item in form.tax_content || []" :key="item.fieldName">
                                        <div class="amount-lable">{{ item.alias }}</div>
                                        <!-- <a-input-number v-model:value="item.value" :disabled="readonlyMode"
                                                :controls="false" :precision="2" class="amount-input"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                @change="handleTaxChange($event, item)">
                                            </a-input-number> -->
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="item.value"
                                            :controls="false"
                                            :disabled="props.readonlyMode"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="handleTaxChange($event, item)"
                                        />
                                    </div>
                                    <div class="amount-item" key="discount">
                                        <div class="amount-lable">{{ i18n.t('bkAp.mxDiscount') }}</div>
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="form.mx_discount"
                                            :controls="false"
                                            :disabled="props.readonlyMode"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="handleDiscountChange($event)"
                                        />
                                    </div>
                                    <div class="amount-item" key="isr">
                                        <div class="amount-lable">{{ i18n.t('bkAp.mxIsr') }}</div>
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="form.mx_isr"
                                            :controls="false"
                                            :disabled="props.readonlyMode"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="handleDiscountChange($event)"
                                        />
                                    </div>
                                    <div class="amount-item" key="isr">
                                        <div class="amount-lable">{{ i18n.t('bkAp.mxIva') }}</div>
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="form.mx_iva"
                                            :controls="false"
                                            :disabled="props.readonlyMode"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="handleDiscountChange($event)"
                                        />
                                    </div>
                                    <div class="amount-item" v-show="false">
                                        <div class="amount-lable">{{ i18n.t('bkAp.shipping') }}</div>
                                        <a-input-number
                                            v-model:value="form.shipping"
                                            :disabled="readonlyMode"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            @change="handleShippingChange"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div class="amount-item">
                                        <div class="amount-lable">{{ i18n.t('ApComponents.subtotal') }}</div>
                                        <a-input-number
                                            v-model:value="form.total_tax"
                                            :disabled="true"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div v-show="false" class="amount-item">
                                        <div class="amount-lable">{{ i18n.t('ApComponents.taxableSubtotal') }}</div>
                                        <a-input-number
                                            v-model:value="form.net_amount"
                                            :disabled="true"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div class="amount-item">
                                        <div class="amount-lable bold">
                                            {{ i18n.t('ApComponents.total') }} {{ form.invoice_currency }}
                                        </div>
                                        <!-- <a-input-number v-model:value="form.total_fee" :disabled="(form.items && form.items.length > 1 && autoCalculateState) ||
                                                props.readonlyMode
                                                " :controls="false" :precision="2" class="amount-input"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                @change="changeTotalAmount($event, false)">
                                            </a-input-number> -->
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="form.total_fee"
                                            :controls="false"
                                            :disabled="
                                                (form.items && form.items.length > 1 && autoCalculateState) ||
                                                props.readonlyMode
                                            "
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                            @value-change="changeTotalAmount($event, false)"
                                        />
                                    </div>
                                    <!--         @change="changeTotalAmount($event, true)"-->
                                    <div class="amount-item" v-if="form.invoice_currency !== localCurrency && false">
                                        <div class="amount-lable bold">
                                            {{ i18n.t('ApComponents.total') }} {{ localCurrency }}
                                        </div>
                                        <!-- <a-input-number v-model:value="form.total_fee_local"
                                                :disabled="props.readonlyMode" :controls="false" :precision="2"
                                                :formatter="formatNumber"
                                                :parser="parseNumber"
                                                class="amount-input">
                                            </a-input-number> -->
                                        <InputNumber
                                            class="amount-input-prime"
                                            v-model="form.total_fee_local"
                                            :controls="false"
                                            :disabled="props.readonlyMode"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="2"
                                            :locale="decimalFormat()"
                                            fluid
                                        />
                                    </div>
                                    <div
                                        class="amount-item"
                                        v-if="form.invoice_currency !== localCurrency && !props.readonlyMode && false"
                                    >
                                        <div class="amount-lable bold">{{ i18n.t('ApComponents.difference') }}</div>
                                        <a-input-number
                                            v-model:value="form_diff"
                                            :disabled="true"
                                            :controls="false"
                                            :formatter="formatNumber"
                                            :parser="parseNumber"
                                            :precision="2"
                                            class="amount-display-alert"
                                        >
                                        </a-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ap-invoice-block" style="display: flex">
                    <a-form-item
                        name="invoiceComments"
                        :style="{
                            width: props.operationMode === 'editing' || props.from === 'massive_process' ? '200%' : '98%',
                        }"
                        :label="i18n.t('bkAp.invoiceComment')"
                    >
                        <a-textarea
                            class="textarea-wrap"
                            v-model:value="form.invoice_comments"
                            :placeholder="i18n.t('commonTag.msgInput')"
                            :disabled="readonlyMode"
                        />
                    </a-form-item>
                    <a-form-item
                        name="originalDocument"
                        :label="i18n.t('bkAp.originalDocument')"
                        v-if="
                            !props.readonlyMode &&
                            props.operationMode !== 'editing' &&
                            props.from !== 'massive_process' &&
                            !uploadState &&
                            !currentPdfInfo.file_url &&
                            !readonlyMode
                        "
                    >
                        <div class="ap-upload-block" v-if="!uploadState && !currentPdfInfo.file_url && !readonlyMode">
                            <a-upload-dragger
                                v-model:file-list="fileList"
                                name="upload_file"
                                :multiple="true"
                                :openFileDialogOnClick="true"
                                :customRequest="() => {}"
                                @change="uploadFile"
                                :show-upload-list="false"
                                :max-count="2"
                                :min-count="2"
                                :accept="fileTypes.join(',')"
                            >
                                <p class="ant-upload-drag-icon">
                                    <!-- <inbox-outlined></inbox-outlined> -->
                                    <svg-icon name="icon_uploadnew"></svg-icon>
                                </p>
                                <p class="ant-upload-text">{{ i18n.t('ApComponents.drag') }}</p>
                                <div v-if="uploadDisabled" class="process-bar-wrapper">
                                    <!--                                        <a-icon type="sync" spin :style="{color: '#1a94d0', 'font-size': '16px'}" />-->
                                    <span class="bar-txt">{{ i18n.t('ApComponents.uploading') }} ...</span>
                                </div>
                                <p class="ant-upload-hint">{{ i18n.t('ApComponents.sizeExceed') }}</p>
                            </a-upload-dragger>
                        </div>

                        <div class="meta-wrap" v-if="false">
                            <div class="title">{{ i18n.t('ApComponents.MEAT') }}</div>
                            <a-row :gutter="[24, 24]">
                                <a-col :span="6" v-for="item in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]" :key="item">
                                    <div class="meta-lable">{{ 'additional' + item }}</div>
                                    <a-input></a-input>
                                </a-col>
                            </a-row>
                        </div>
                    </a-form-item>
                    <a-form-item
                        name="displayBlock"
                        v-if="(uploadState || form.file_url || readonlyMode) && props.from !== 'massive_process'"
                    >
                        <div style="font-size: 16px; font-weight: bold" v-if="readonlyMode && apIntegration != 1">
                            {{ i18n.t('ArComponents.JE') }}
                            <div style="background-color: #f5f5f5">
                                <a class="document-item-link" @click="seeDetail(form.engine_document_id)">{{
                                    form.engine_document_id
                                }}</a>
                                <a class="document-item-link" @click="seeDetail(form.cash_engine_payment_no)">{{
                                    form.cash_engine_payment_no || ''
                                }}</a>
                            </div>
                            <div style="background-color: #f5f5f5; margin-top: 3px">
                                <a
                                    class="document-item-link"
                                    v-for="id in form.engine_reverse_document_id?.split(',')"
                                    :key="id"
                                    @click="seeDetail(id)"
                                    >{{ id }}</a
                                >
                            </div>
                        </div>
                        <div style="font-size: 16px; font-weight: bold" v-if="readonlyMode && apIntegration === 1">
                            {{ i18n.t('ArComponents.SAP') }}
                            <div style="background-color: #f5f5f5">
                                <a>{{ form.sap_document_id }}</a>
                            </div>
                            <div style="background-color: #f5f5f5; margin-top: 3px" v-if="apIntegration != 1">
                                <a
                                    class="document-item-link"
                                    v-for="id in form.engine_reverse_document_id?.split(',')"
                                    :key="id"
                                    @click="seeDetail(id)"
                                    >{{ id }}</a
                                >
                            </div>
                            <div style="background-color: #f5f5f5; margin-top: 3px" v-if="apIntegration === 1">
                                <a>{{ form.sap_reverse_document_id }}</a>
                            </div>
                        </div>
                        <div
                            v-if="/*(uploadState || form.file_url) && */ props.from !== 'massive_process'"
                            style="font-size: 16px; font-weight: bold"
                        >
                            <div>
                                {{ i18n.t('ApComponents.oriDoc') }}
                                <svg-icon name="icon_upload" @click="popUploadConfirmModal"></svg-icon>
                            </div>

                            <div style="background-color: #f5f5f5">
                                <svg-icon
                                    name="icon_delete1"
                                    v-if="form.file_url && !readonlyMode"
                                    @click="deleteFile(form)"
                                ></svg-icon>
                                <br />
                                <a @click="displayFileViewerModal">{{ form.file_url?.split('/').pop() }}</a>
                                <br />
                                <a @click="displayFileViewerModal">{{ form.xml_url?.split('/').pop() }}</a>
                            </div>
                        </div>
                    </a-form-item>
                </div>
                <div class="ap-invoice-footer">
                    <!-- <a-button
                            v-show="readonlyMode"
                            :disabled="!enableReverse"
                            type="primary"
                            @click="popUploadConfirmModal"
                            shape="round"
                            >{{ i18n.t('bkApInvoice.upload') }}</a-button
                        >
                        <a-button class="cancel-button" shape="round" @click="cancel">{{
                            i18n.t('commonTag.cancel')
                        }}</a-button> -->

                    <!-- <a-button
                            v-if="form.pay_method === '1' && form.br_flag === 0"
                            class="cancel-button"
                            shape="round"
                            @click="popConvertModal()"
                            >{{ i18n.t('ArComponents.convert') }}</a-button
                        > -->
                    <a-button class="cancel-button" shape="round" @click="cancel">{{
                        ((state.invoiceFlowData != null && state.invoiceFlowData.length > 0) ||
                            (state.paymentFlowData != null && state.paymentFlowData.length > 0)) &&
                        readonlyMode
                            ? i18n.t('commonTag.close')
                            : i18n.t('commonTag.cancel')
                    }}</a-button>
                    <a-dropdown v-show="!readonlyMode" :disabled="!enableReverse">
                        <template #overlay>
                            <a-menu @click="handleMenuClick">
                                <a-menu-item v-if="form.sap_document_id === null" key="1">{{
                                    i18n.t('commonTag.delete')
                                }}</a-menu-item>
                                <a-menu-item key="2">{{ i18n.t('commonTag.resend') }}</a-menu-item>
                                <a-menu-item v-if="(form.sap_document_id !== null || (form.sap_document_id === null && form.purpose === 'PROFORMA'))" key="3">{{
                                    i18n.t('commonTag.reverse')
                                }}</a-menu-item>
                            </a-menu>
                        </template>
                        <a-button
                            type="primary"
                            shape="round"
                            v-show="
                                readonlyMode &&
                                ((state.invoiceFlowData != null && state.invoiceFlowData.length > 0) ||
                                    (state.paymentFlowData != null && state.paymentFlowData.length > 0))
                            "
                        >
                            Actions
                            <DownOutlined />
                        </a-button>
                    </a-dropdown>
                    <a-button
                        v-show="
                            readonlyMode &&
                            !(state.invoiceFlowData != null && state.invoiceFlowData.length > 0) &&
                            !(state.paymentFlowData != null && state.paymentFlowData.length > 0)
                        "
                        :disabled="!enableReverse"
                        type="primary"
                        @click="popConfirmModal"
                        shape="round"
                        >{{ i18n.t('commonTag.reverse') }}</a-button
                    >
                    <a-button
                        v-show="readonlyMode && form.purpose === 'PROFORMA' && form.br_flag === 2"
                        type="primary"
                        @click="realize"
                        shape="round"
                        :loading="formLoading"
                        >{{ i18n.t('commonTag.realize') }}</a-button
                    >
                    <a-button
                        v-if="userType !== 'jushi'"
                        v-show="!readonlyMode"
                        shape="round"
                        type="primary"
                        @click="save"
                        :loading="formLoading"
                        :disabled="isDisable"
                    >
                        {{ props.operationMode === 'creating' ? i18n.t('bkAp.create') : 'Save' }}
                    </a-button>
                    <a-button
                        v-else
                        v-show="!readonlyMode"
                        shape="round"
                        type="primary"
                        @click="save"
                        :loading="formLoading"
                    >
                        {{ props.operationMode === 'creating' ? i18n.t('bkAp.create') : 'Save' }}
                    </a-button>
                </div>
            </a-form>
        </a-spin>
        <a-modal
            title="Create New Supplier"
            v-model:visible="show"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="820"
            :wrapClassName="'modal-wrap'"
        >
            <supplier-form
                :current-customer="current"
                :edit-mode="false"
                :origin="'outside'"
                client-type="SUPPLIER"
                :contact-name="newIssuerName"
                @updateNewContact="updateNewContact"
                @dismiss="showContactCreationModal(false)"
            ></supplier-form>
        </a-modal>
        <a-modal
            :title="i18n.t('gl.readonly')"
            v-model:visible="showGlDialog"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="1000"
            style="z-index: 999"
            :dialogStyle="{top: '10px'}"
            :bodyStyle="{padding: '10px 24px 24px'}"
        >
            <gl-component
                :current-invoice="glCurrent"
                :readonly-mode="true"
                :operation-mode="glOperationMode"
                @dismiss="glDismiss"
            ></gl-component>
        </a-modal>
        <div ref="confirmationWrap">
            <a-modal
                v-model:visible="showConfirm"
                centered
                destroyOnClose
                :get-container="confirmationWrap"
                :width="480"
                :closable="true"
                :confirm-loading="showReverseLoading"
                :wrapClassName="'modal-wrap'"
                :ok-text="i18n.t('commonTag.confirm')"
                :ok-type="'primary'"
                :ok-button-props="{shape: 'round'}"
                :cancel-button-props="{hidden: true}"
                @ok="confirmationConfirm"
            >
                <!-- <template #title>
                    <div class="confirm-title-wrap">
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('bkCommonTag.confirmation') }}
                        </div>
                    </div>
                </template> -->
                <div class="confirmation-content-text">{{ confirmText }}</div>
                <div v-if="form.br_flag === 1 || form.br_flag === 2" class="confirmation-content-text2">
                    {{ confirmText2 }}
                </div>
                <div class="date-select">
                    <a-date-picker
                        v-model:value="reverseDt"
                        :allowClear="false"
                        :inputReadOnly="true"
                        format="YYYY-MM-DD"
                        valueFormat="YYYY-MM-DD"
                    >
                        <template #suffixIcon>
                            <svg-icon name="icon_date"></svg-icon>
                        </template>
                    </a-date-picker>
                </div>
                <a-spin :spinning="showReverseLoading" :tip="i18n.t('update.reverseProcessing')">
                    <div style="height: 40px"></div>
                </a-spin>
                <template #footer>
                    <div class="modal-footer">
                        <a-dropdown placement="bottomCenter">
                            <a-button type="primary" shape="round" @click="confirmationConfirm">
                                {{ i18n.t('commonTag.reverse') }}
                                <DownOutlined />
                            </a-button>
                            <template #overlay>
                                <a-menu @click="handleReverseMenuClick" class="reverse-button-centered-menu">
                                    <a-menu-item key="2">{{ i18n.t('commonTag.reverseAndRedo') }}</a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </div>
                </template>
            </a-modal>
        </div>
        <div ref="confirmationWrap2">
            <a-modal
                v-model:visible="showUploadConfirm"
                centered
                destroyOnClose
                :get-container="confirmationWrap2"
                :width="480"
                :closable="false"
                :wrapClassName="'modal-wrap'"
                :ok-text="i18n.t('commonTag.confirm')"
                :ok-type="'primary'"
                :ok-button-props="{shape: 'round'}"
                :cancel-text="i18n.t('commonTag.cancel')"
                :cancel-button-props="{shape: 'round', ghost: true, type: 'primary'}"
                @ok="uploadConfirmationConfirm"
                @cancel="uploadConfirmationCancel"
            >
                <template #title>
                    <div class="confirm-title-wrap">
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('bkCommonTag.confirmation') }}
                        </div>
                    </div>
                </template>
                <div class="confirmation-content-text">{{ i18n.t('ApComponents.uploadConfirm') }}</div>
            </a-modal>
        </div>

        <div ref="convertWrap">
            <a-modal
                v-model:visible="showConvert"
                centered
                destroyOnClose
                :get-container="convertWrap"
                :width="480"
                :closable="false"
                :confirm-loading="showConvertLoading"
                :wrapClassName="'modal-wrap'"
                :ok-text="i18n.t('commonTag.confirm')"
                :ok-type="'primary'"
                :ok-button-props="{shape: 'round'}"
                :cancel-text="i18n.t('commonTag.cancel')"
                :cancel-button-props="{shape: 'round', ghost: true, type: 'primary'}"
                @ok="convertConfirm"
                @cancel="convertCancel"
            >
                <template #title>
                    <div class="confirm-title-wrap">
                        <div class="confirm-title-text">
                            <ExclamationCircleFilled class="confirm-title-icon" />
                            {{ i18n.t('bkCommonTag.confirmation') }}
                        </div>
                    </div>
                </template>
                <div class="confirmation-content-text">{{ confirmConvert }}</div>
            </a-modal>
        </div>

        <a-modal
            :body-style="viewerStyle"
            v-model:visible="showFileViewer"
            destroyOnClose
            :closeable="true"
            :mask="false"
            :maskClosable="false"
            :width="820"
            :wrapClassName="'modal-wrap'"
            ><pdf-viewer :url="selectedFileUrl" />
            <template #title>
                <div ref="modalTitleRef" class="ant-modal-title-detail">
                    <span>View File</span>
                </div>
            </template>
            <template #modalRender="{originVNode}">
                <div :style="transformStyle">
                    <component :is="originVNode" />
                </div>
            </template>
            <template #footer>
                <a-button key="back" @click="closeFileViewerModal" shape="round" class="cancel-button">
                    Cancel
                </a-button>
                <a-button
                    key="submit"
                    type="primary"
                    shape="round"
                    :loading="downloadFileloading"
                    @click="downloadFile"
                >
                    {{ i18n.t('ApComponents.download') }}
                </a-button>
            </template></a-modal
        >
        <a-modal
            title="Payment Term Discount"
            v-model:visible="enableDiscountModal"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="820"
            :wrapClassName="'modal-wrap'"
        >
            <payment-discount-term
                style="padding: 24px 24px"
                :discounts="form"
                @confirm="confirmDiscountModal"
                @dismiss="closeDiscountModal"
            ></payment-discount-term>
        </a-modal>
        <!-- 上传部分 -->
        <div class="upload-modal-wrap">
            <a-modal
                :title="i18n.t('ApUploadInvoice.upload')"
                v-model:visible="showUploadModall"
                destroyOnClose
                :closeable="true"
                :width="'34vw'"
                :bodyStyle="{padding: '10px 14px 14px'}"
                :wrapClassName="'modal-wrap'"
            >
                <upload-file-comp
                    v-if="!INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                    ref="uploadComp"
                    :upload-processing="uploadProcessing"
                    :file-limit="1"
                    :fileType="restrictFileType"
                    :is-upload-invoice-original-document="true"
                    :invoice-id="form.id"
                    :selected-type="form.pay_method === '1' ? '1' : '3'"
                    :page-type="'1'"
                />
                <upload-file-comp-mx
                    v-if="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                    ref="uploadComp"
                    :upload-processing="uploadProcessing"
                    :file-limit="2"
                    :is-upload-invoice-original-document="true"
                    :invoice-id="form.id"
                    :selected-type="form.pay_method === '1' ? '1' : '3'"
                    :page-type="'1'"
                />
                <template #footer>
                    <a-button key="cancel" class="btn-cancel" shape="round" @click="closeUploadModal">Cancel</a-button>
                    <a-button
                        key="upload"
                        shape="round"
                        type="primary"
                        :loading="uploadProcessing"
                        @click="uploadInvoiceFiles"
                        >Upload
                    </a-button>
                </template>
            </a-modal>
        </div>
        <a-modal
            v-model:visible="itemEditModal"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="920"
            :wrapClassName="'modal-wrap'"
            :bodyStyle="{padding: '10px', 'margin-left': '30px'}"
        >
            <template #title>
                <div style="text-align: left; padding: 8px 0px; margin-left: -8px">{{ i18n.t('bkAp.assigmentSapCostObject') }}</div>
            </template>
            <a-form :model="currentEditItem" :layout="'vertical'" autocomplete="off" @submit="confirmItemEditModal">
                <div class="import-form-block">
                    <a-row v-if="sapPayableGl === 1" type="flex" :gutter="[24, 32]">
                        <a-col :span="7">
                            <div>{{ i18n.t('bkAp.assigmentGlAccount') }}</div>
                        </a-col>
                        <a-col :span="17">
                            <a-form-item name="sap_gl_account">
                                <a-select
                                    :disabled="readonlyMode"
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="currentEditItem.sap_gl_account"
                                    show-search
                                    allow-clear
                                    :dropdownMatchSelectWidth="200"
                                    :filter-option="filterOption"
                                    class="table-input"
                                >
                                    <a-select-option
                                        v-for="item in state.masterData.ET_GL_ACCOUNT"
                                        :key="item.GL_ACCOUNT + ' | ' + item.DESCRIPTION"
                                        :value="item.GL_ACCOUNT"
                                        >{{ item.GL_ACCOUNT }} | {{ item.DESCRIPTION }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <div>&nbsp;</div>
                    <a-row v-if="sapPayableWbs === 1" type="flex" :gutter="[24, 32]">
                        <a-col :span="7">
                            <div>{{ i18n.t('bkAp.assigmentWbs') }}</div>
                        </a-col>
                        <a-col :span="17">
                            <a-form-item name="sap_wbs">
                                <a-select
                                    :disabled="readonlyMode"
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="currentEditItem.sap_wbs"
                                    show-search
                                    allow-clear
                                    :dropdownMatchSelectWidth="200"
                                    :filter-option="filterOption"
                                    class="table-input"
                                >
                                    <a-select-option
                                        v-for="item in state.masterData.ET_WBS"
                                        :key="item.WBS + ' | ' + item.DESCRIPTION"
                                        :value="item.WBS"
                                    >
                                        {{ item.WBS }} | {{ item.DESCRIPTION }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <div v-if="sapPayableWbs === 1">&nbsp;</div>
                    <a-row v-if="sapPayableCostCenter === 1" type="flex" :gutter="[24, 32]">
                        <a-col :span="7">
                            <div>{{ i18n.t('bkAp.assigmentCostCenter') }}</div>
                        </a-col>
                        <a-col :span="17">
                            <a-form-item
                                :title="i18n.t('bkAp.costObject')"
                                data-index="costObject"
                                width="15%"
                                :ellipsis="true"
                                v-if="apIntegration === 1"
                            >
                                <a-form-item name="sap_cost_center">
                                    <a-select
                                        :disabled="readonlyMode"
                                        :placeholder="i18n.t('commonTag.msgSelect')"
                                        v-model:value="currentEditItem.sap_cost_center"
                                        show-search
                                        allow-clear
                                        :dropdownMatchSelectWidth="200"
                                        :filter-option="filterOption"
                                        class="table-input"
                                    >
                                        <a-select-option
                                            v-for="item in state.masterData.ET_COST_CENTER"
                                            :key="item.COST_CENTER + ' | ' + item.DESCRIPTION"
                                            :value="item.COST_CENTER"
                                        >
                                            {{ item.COST_CENTER }} | {{ item.DESCRIPTION }}
                                        </a-select-option>
                                    </a-select>
                                </a-form-item>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <!-- <div>&nbsp;</div> -->
                    <a-row v-if="sapPayableInternalOrder === 1" type="flex" :gutter="[24, 32]">
                        <a-col :span="7">
                            <div>{{ i18n.t('bkAp.assigmentInternalOrder') }}</div>
                        </a-col>
                        <a-col :span="17">
                            <a-form-item
                                :title="i18n.t('bkAp.costObject')"
                                data-index="costObject"
                                width="15%"
                                :ellipsis="true"
                                v-if="apIntegration === 1"
                            >
                                <a-form-item name="sap_internal_order">
                                    <a-input
                                        :disabled="readonlyMode"
                                        v-model:value="currentEditItem.sap_internal_order"
                                        :placeholder="i18n.t('commonTag.msgInput')"
                                        class="table-input"
                                    ></a-input>
                                </a-form-item>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row v-if="sapPayableProfitCenter === 1" type="flex" :gutter="[24, 32]">
                        <a-col :span="7">
                            <div>{{ i18n.t('bkAp.assigmentProfitCenter') }}</div>
                        </a-col>
                        <a-col :span="17">
                            <a-form-item
                                :title="i18n.t('bkAp.costObject')"
                                data-index="costObject"
                                width="15%"
                                :ellipsis="true"
                                v-if="apIntegration === 1"
                            >
                                <a-form-item name="sap_profit_center">
                                    <a-select
                                        :disabled="readonlyMode"
                                        :placeholder="i18n.t('commonTag.msgSelect')"
                                        v-model:value="currentEditItem.sap_profit_center"
                                        show-search
                                        :dropdownMatchSelectWidth="200"
                                        :filter-option="filterOption"
                                        allow-clear
                                        class="table-input"
                                    >
                                        <a-select-option
                                            v-for="item in state.masterData.ET_PROFIT_CENTER"
                                            :key="item.PROFIT_CENTER + ' | ' + item.DESCRIPTION"
                                            :value="item.PROFIT_CENTER"
                                        >
                                            {{ item.PROFIT_CENTER }} | {{ item.DESCRIPTION }}
                                        </a-select-option>
                                    </a-select>
                                </a-form-item>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <div>&nbsp;</div>
                </div>
                <div style="text-align: right">
                    <!-- <a-button shape="round" class="cancel-button" @click="closeItemEditModal">Cancel</a-button>
                    <span style="margin: 0 5px"></span>
                    <a-button shape="round" type="primary" html-type="submit">Confirm</a-button> -->
                    <a-button class="custom-btn" shape="round" @click="closeItemEditModal">
                        <CloseOutlined
                            shape="round"
                            style="font-size: 20px; color: #1677ff"
                            @click="closeItemEditModal"
                        />
                    </a-button>
                    <span style="margin: 0 5px"></span>
                    <a-button class="custom-btn" shape="round" @click="confirmItemEditModal">
                        <CheckOutlined
                            shape="round"
                            style="font-size: 20px; color: #1677ff"
                            @click="confirmItemEditModal"
                        />
                    </a-button>
                </div>
            </a-form>
        </a-modal>
    </div>
</template>

<style lang="scss" scoped>
.ap-invoice-block {
    padding: 24px 0;
    border-bottom: 1px solid #e2e2ea;

    > div.ant-form-item {
        width: 50%;
    }

    &:first-child {
        padding-top: 0;
    }
}

.ap-invoice-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn + .ant-btn {
        margin-left: 12px;
    }
}

.table-input {
    background-color: #f5f7f9;
    border-color: #f5f7f9;
    border-radius: 4px;
    padding-left: 4px;
    padding-right: 4px;

    &.ant-input-number {
        padding-left: 0px;
        padding-right: 0px;

        :deep(.ant-input-number-input-wrap input) {
            padding-left: 4px;
            padding-right: 4px;
        }
    }

    &.ant-select {
        padding-left: 0px;
        padding-right: 0px;

        :deep(.ant-select-selector) {
            padding-left: 4px;
            padding-right: 4px;

            .ant-select-selection-search {
                left: 4px;
            }
        }
    }

    &.ant-input:hover,
    &.ant-input-number:hover,
    &.ant-input:focus,
    &.ant-input-focused,
    &.ant-input-number-focused {
        border-color: #216fcf;
    }

    :deep(.ant-select-selector) {
        background-color: #f5f7f9 !important;
        border-color: #f5f7f9;
    }
}

.ap-invoice-block {
    :deep(.ant-row.ant-form-item) {
        margin: 0;

        .ant-col.ant-form-item-control .table-input {
            width: 100%;
        }
    }
}

:deep(.ap-invoice-block .ant-col.ant-form-item-label) {
    height: 31.5px;
}

:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -10px;
    font-size: 12px;
    line-height: 12px;
    min-height: 12px;
    display: none;
}

.icon-exclamation {
    margin: 0;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #aab9cb;
    margin-left: 5px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}

:deep(.ant-form-item-label [title='Comments']) {
    font-size: 16px;
    font-weight: 700;
}

.textarea-wrap {
    min-height: 138px;
    height: 100%;
    margin-bottom: 15px;
}

.meta-wrap {
    .title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 700;
    }

    .meta-lable {
        margin-bottom: 8px;
    }
}

.ap-invoice-amount-block {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 0 0;

    .ap-invoice-amount-block-left {
        width: 700px;
    }

    .ap-invoice-amount-block-right {
        width: 300px;
        min-width: 300px;

        // max-width: calc(100% - 285px);
        .amount-block {
            width: 100%;

            .title {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                text-align: right;
            }

            .switch-wrap {
                margin-right: 8px;
            }

            .amount-item-wrap {
                padding: 20px 16px;
                width: 100%;
                background-color: #f5f7f9;
                border-radius: 8px;

                .amount-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0px;
                    }

                    .amount-lable {
                        width: calc(100% - 151px - 8px);
                        min-width: 70px;
                        text-align: right;

                        &.bold {
                            font-weight: 700;
                        }
                    }

                    .amount-input {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: #fff;
                        }
                    }

                    .amount-display-alert {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: rgba(255, 0, 0, 0.2);
                            color: red;
                        }
                    }
                }
            }
        }
    }
}

.poAdd-icon {
    width: 20px;
}

.confirm-title-wrap {
    padding-top: 15px;
    padding-bottom: 5px;

    .confirm-title-icon {
        color: #faad14 !important;
        font-size: 21px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .confirm-title-text {
        //  font-family: Calibri;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        font-weight: 400;
    }
}

.confirmation-content-text {
    // font-family: Calibri;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    font-weight: 400;
    padding-left: 64px;
    padding-top: 5px;
    // height: 75px;
}

.confirmation-content-text2 {
    // font-family: Calibri;
    font-size: 14px;
    color: #ff0000;
    line-height: 20px;
    font-weight: 400;
    padding-left: 64px;
    padding-top: 5px;
    // height: 75px;
}

.date-select {
    color: rgba(0, 0, 0, 0.65);
    min-width: 145px;
    width: 260px;
    max-width: 300px;
    padding-top: 5px;
    padding-left: 64px;
    flex: 1;
}

.document-item-link {
    margin-right: 10px;
}

:deep(.ant-form-item-label [title='Original Document']) {
    font-size: 16px;
    font-weight: 700;
}

:deep(.ant-modal-body) {
    height: 50px;
}

:deep(.ant-modal-header) {
    padding: 0;

    .ant-modal-title {
        .ant-modal-title-detail {
            padding: 17px 24px;
            width: 100%;
            cursor: move;
            user-select: none;
        }
    }
}

:deep(.ant-modal-footer) {
    border-top: none;
}

.column-item-input {
    margin-top: 12px;
    margin-bottom: 12px;
}

:deep(.custom-spin > div > .ant-spin .ant-spin-text) {
    top: calc(50% + 36px);
}

.custom-btn {
    border-color: #1677ff;
}

.amount-input-prime {
    width: 151px;
    height: 33px;
    border-radius: 6px;
    color: #333;
    background-color: #fff;

    &.item-amount {
        max-width: 100%;
        background-color: #f5f7f9;
    }
}

:deep(.p-inputnumber input) {
    font-size: 14px;
    padding-left: 10px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border: 1px solid #4096ff;
        box-shadow: 0 0 0 2px #0591ff1a;
    }
}

:deep(.p-inputnumber.item-amount input) {
    border-color: #f5f7f9;

    &.p-inputtext:enabled:hover,
    &.p-inputtext:enabled:focus {
        border-color: #216fcf;
    }
}

:deep(.p-inputnumber.item-amount input::placeholder) {
    color: #bbb;
}

.reverse-button-centered-menu {
    min-width: 160px; /* 增加菜单宽度 */

    :deep(.ant-dropdown-menu-item) {
        text-align: center;
        white-space: nowrap; /* 防止文本换行 */
        padding: 5px 12px; /* 增加内边距 */
    }

    :deep(.ant-dropdown-menu-item:hover) {
        background-color: #f0f0f0; /* 鼠标悬停时的背景色 */
    }
}

:deep(.ant-dropdown) {
    max-width: calc(100% - 48px); /* 48px 是左右各 24px 的 padding */
}
</style>
