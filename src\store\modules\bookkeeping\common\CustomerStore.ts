/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'

const http = service

const CustomerStore = {
    namespaced: true,
    state: {
        customersList: [],
        customerDetail: {},
        totalNumber: 0,
    },
    mutations: {
        updateCustomersList(state: {customersList: any[]}, list: any) {
            state.customersList = [...list]
        },
        updateCustomerDetail(state: {customerDetail: any}, detail: any) {
            state.customerDetail = {...detail}
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
    },
    actions: {
        async fetchCustomers(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/customer/list', payload)
            const list = response.data.data.list.map((item: any) => {
                return {
                    ...item,
                    accountType: 'Customer',
                }
            })
            store.commit('updateCustomersList', list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async createCustomer(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.post('/bk/customer', payload)
        },
        async updateCustomer(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.put('/bk/customer', payload)
        },
        async deleteUser(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/customer/${payload}`)
        },
        async createClient(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.post('/bk/client', payload)
        },
        async changePwd(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {loginAccount: string | Blob; newpassword: string | Blob},
        ) {
            const formData = new FormData()
            formData.append('loginAccount', payload.loginAccount)
            formData.append('loginToken', payload.newpassword)
            return http.post('/management/account/password/reset/', formData)
        },
    },
}

export default CustomerStore
