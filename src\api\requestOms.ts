/** @format */

import axios from 'axios'
import router from '../router'
import {message} from 'ant-design-vue'

// create an axios instance
const serviceoms = axios.create({
    baseURL: import.meta.env.VITE_BASIC_API_OMS, // url = base url + request url
    withCredentials: true, // send cookies when cross-domain requests
    timeout: 90000, // request timeout
})

serviceoms.interceptors.response.use(
    response => {
        if (response.data.code !== 200) {
            message.error({
                content: `OMS - ${response.data.msg}`,
                duration: 5,
            })
        }
        return response
    },
    error => {
        if (error.code === 'ECONNABORTED' || error.message === 'Network Error' || error.message.includes('timeout')) {
            message.error({
                content: 'Time Out Error.',
                duration: 6,
            })
        } else if (error?.response?.status === 401) {
            router.replace({name: 'Login'})
        } else {
            message.error({
                content: error.response.data.message || 'failed',
                duration: 5,
            })
        }
        return Promise.reject(error)
    },
)

export default serviceoms
