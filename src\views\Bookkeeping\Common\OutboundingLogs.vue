<!-- @format -->

<script lang="ts" setup>
import { ref, onMounted, reactive, computed, onActivated } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, CopyOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import http from '@/api/requestNew'
import { UserCompany, UserInfo } from '@/lib/storage'
import type { Composer } from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import FileSaver from 'file-saver'

const i18n: Composer = i18nInstance.global
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}

// 只有kycadmin用户可见
const isKycAdmin = computed(() => ['kycadmin', 'ntadmin'].includes(userInfo.account))

// 设置活动标签页
const activeKey = ref('logs')

interface LogItem {
  id: number
  company_code: string
  operation: string | null
  header: any
  url: string
  send_data: any
  response: any
  create_time: string
  execution_time: number
}

const logs = ref<LogItem[]>([])
const loading = ref(false)
const modalVisible = ref(false)
const currentLog = ref<LogItem | null>(null)
const copyLoading = ref(false)

// 搜索条件
const searchOperation = ref('')
const searchContent = ref('')

const parseToken = (header: any) => {
  if (!header) return null

  let tokenStr = ''
  try {
    const headerObj = typeof header === 'string' ? JSON.parse(header) : header
    tokenStr = headerObj.token
    if (!tokenStr) return null

    const payloadBase64 = tokenStr.split('.')[1]
    const base64 = payloadBase64.replace(/-/g, '+').replace(/_/g, '/')
    const padded = base64.padEnd(base64.length + (4 - (base64.length % 4)) % 4, '=')
    const decoded = atob(padded)
    const payload = JSON.parse(decoded)

    // 转换 exp/iat/nbf 等时间戳字段为本地时间字符串
    const timeFields = ['exp', 'iat', 'nbf']
    timeFields.forEach(key => {
      if (payload[key]) payload[key] = formatTimestamp(payload[key])
    })

    return payload
  } catch (e) {
    console.error('Error parsing token:', e, 'token:', tokenStr)
    return null
  }
}



// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total: number) => `共 ${total} 条记录`
})

const fetchLogs = async () => {
  loading.value = true
  try {
    // 构建查询参数
    let url = `/prefectClient/outbounding_log?company_code=${userCompany[0].code}`
    
    // 添加operation查询条件（如果有）
    if (searchOperation.value.trim()) {
      url += `&operation=${encodeURIComponent(searchOperation.value.trim())}`
    }
    
    // 添加content查询条件（如果有）
    if (searchContent.value.trim()) {
      url += `&content=${encodeURIComponent(searchContent.value.trim())}`
    }
    
    const res = await http.get(url)
    if (res.status === 200) {
      logs.value = res.data
      pagination.total = res.data.length
    } else {
      message.error('Failed to fetch logs')
    }
  } catch (error) {
    console.error('Error fetching logs:', error)
    message.error('Failed to fetch logs')
  } finally {
    loading.value = false
  }
}

const showLogDetails = async (record: LogItem) => {
  loading.value = true
  try {
    // 调用详情接口获取日志详情
    const res = await http.get(`/prefectClient/outbounding_log_detail?log_id=${record.id}`)
    if (res.status === 200) {
      currentLog.value = res.data
    } else {
      currentLog.value = record
      message.error('Failed to fetch log details')
    }
  } catch (error) {
    console.error('Error fetching log details:', error)
    currentLog.value = record
    message.error('Failed to fetch log details')
  } finally {
    loading.value = false
    modalVisible.value = true
  }
}

const formatJson = (json: any) => {
  if (!json) return ''
  try {
    if (typeof json === 'string') {
      return JSON.stringify(JSON.parse(json), null, 2)
    }
    return JSON.stringify(json, null, 2)
  } catch (e) {
    return json
  }
}

// 复制页面内容到剪贴板
const copyPageContent = async () => {
  copyLoading.value = true
  try {
    // 准备要复制的内容
    let content = ''
    
    // 如果当前有打开的日志详情，则复制详情内容
    if (modalVisible.value && currentLog.value) {
      content = `Log ID: ${currentLog.value.id}\n`
      content += `Operation: ${currentLog.value.operation || '-'}\n`
      content += `URL: ${currentLog.value.url?.trim()}\n`
      content += `Create Time: ${formatDateTime(currentLog.value.create_time)}\n`
      content += `Execution Time: ${currentLog.value.execution_time ? `${currentLog.value.execution_time}s` : '-'}\n\n`
      
      content += `Header:\n${formatJson(currentLog.value.header)}\n\n`
      content += `Token Payload:\n${formatJson(parseToken(currentLog.value.header))}\n\n`
      content += `Send Data:\n${formatJson(currentLog.value.send_data)}\n\n`
      content += `Response:\n${formatJson(currentLog.value.response)}`
    } else {
      // 否则复制表格内容
      content = 'Logs:\n'
      logs.value.forEach(log => {
        content += `ID: ${log.id}, `
        content += `Company Code: ${log.company_code}, `
        content += `Operation: ${log.operation || '-'}, `
        content += `URL: ${log.url?.trim()}, `
        content += `Create Time: ${formatDateTime(log.create_time)}, `
        content += `Execution Time: ${log.execution_time ? `${log.execution_time}s` : '-'}`
        content += '\n'
      })
    }
    
    // 处理大文件：使用Blob和ClipboardItem API处理大型内容
    // 这种方法可以处理更大的数据量
    try {
      // 检查浏览器是否支持 ClipboardItem API
      if (typeof window !== 'undefined' && window.ClipboardItem && navigator.clipboard && navigator.clipboard.write) {
        const blob = new Blob([content], { type: 'text/plain' })
        const clipboardItem = new window.ClipboardItem({ 'text/plain': blob })
        await navigator.clipboard.write([clipboardItem])
        message.success('Content has been copied to the clipboard')
      } else {
        // 如果不支持 ClipboardItem API，直接使用回退方法
        throw new Error('ClipboardItem API not supported')
      }
    } catch (clipError) {
      // 如果高级API不可用，回退到传统方法
      const textArea = document.createElement('textarea')
      textArea.value = content
      document.body.appendChild(textArea)
      textArea.select()
      const successful = document.execCommand('copy')
      document.body.removeChild(textArea)
      
      if (successful) {
        message.success('内容已复制到剪贴板')
      } else {
        throw new Error('复制失败')
      }
    }
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请重试')
  } finally {
    copyLoading.value = false
  }
}

// 将UTC时间转换为浏览器所在时区的时间
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return ''
  try {
    // 确保将日期字符串解析为UTC时间，然后转换为本地时区
    // 数据库存储的是UTC时间，需要明确指定为UTC
    const utcDate = new Date(dateTimeStr + 'Z')
    // 转换为浏览器所在时区的时间（如加拿大东部时区）
    return utcDate.toLocaleString()
  } catch (e) {
    console.error('Error formatting date:', e)
    return dateTimeStr
  }
}
const formatTimestamp = (ts: number | string) => {
  if (!ts) return ''
  try {
    // 有些 JWT 是秒级时间戳
    const t = typeof ts === 'string' ? parseInt(ts) : ts
    // 转成毫秒
    const date = new Date(t * 1000)
    return date.toLocaleString()
  } catch (e) {
    console.error('Error formatting timestamp:', e)
    return ts
  }
}

// 导出日志数据
const exportLogs = async () => {
  loading.value = true
  try {
    // 构建查询参数，与fetchLogs保持一致
    let url = `/prefectClient/export_outbounding_log_excel?company_code=${userCompany[0].code}`
    
    // 添加operation查询条件（如果有）
    if (searchOperation.value.trim()) {
      url += `&operation=${encodeURIComponent(searchOperation.value.trim())}`
    }
    
    // 添加content查询条件（如果有）
    if (searchContent.value.trim()) {
      url += `&content=${encodeURIComponent(searchContent.value.trim())}`
    }
    
    const res = await http.get(url, {
      responseType: 'blob'
    })
    
    if (res.status === 200) {
      // 获取文件名
      const filename = res.headers['content-disposition'] 
        ? res.headers['content-disposition'].split('filename=')[1].replace(/"/g, '')
        : `integration_logs_${new Date().toISOString().split('T')[0]}.xlsx`
      
      // 创建blob对象并下载
      const blob = new Blob([res.data], {
        type: res.headers['content-type'] || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      
      FileSaver.saveAs(blob, filename)
      message.success('Export successful')
    } else {
      message.error('Failed to export logs')
    }
  } catch (error) {
    console.error('Error exporting logs:', error)
    message.error('Failed to export logs')
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

onMounted(() => {
  if (isKycAdmin.value) {
    fetchLogs()
  }
})

// 每次激活组件时重新获取数据
onActivated(() => {
  if (isKycAdmin.value) {
    fetchLogs()
  }
})
</script>

<template>
  <div class="page-container-gl_list">
    <div class="content-box-import-form">
      <div v-if="isKycAdmin">
        <!-- <a-tabs v-model:activeKey="activeKey" :destroyInactiveTabPane="true">
          <a-tab-pane key="logs" :tab="Logs"> -->
            <div class="logs-container">
              <div class="table-header">
                <div class="search-inputs">
                  <a-input
                    v-model:value="searchOperation"
                    placeholder="Operation"
                    allowClear
                    style="width: 200px; margin-right: 10px;"
                  />
                  <a-input
                    v-model:value="searchContent"
                    placeholder="Content"
                    allowClear
                    style="width: 200px; margin-right: 10px;"
                  />
                </div>
                <div class="action-buttons">
                  <a-button type="primary" @click="exportLogs" :loading="loading" style="margin-right: 10px;">
                    <template #icon><download-outlined /></template>
                    Export Template
                  </a-button>
                  <a-button type="primary" shape="circle" @click="fetchLogs" :loading="loading">
                    <template #icon><reload-outlined /></template>
                  </a-button>
                </div>
              </div>
              <a-table
                :dataSource="logs"
                :loading="loading"
                rowKey="id"
                :pagination="pagination"
                @change="handleTableChange"
                :customRow="(record: LogItem) => {
                  return {
                    onClick: () => {
                      showLogDetails(record)
                    }
                  }
                }"
              >
                <a-table-column title="ID" dataIndex="id" :width="80" />
                <a-table-column title="Company Code" dataIndex="company_code" :width="120" />
                <a-table-column title="Operation" dataIndex="operation" :width="120">
                  <template #default="{text}">
                    <span>{{text || '-'}}</span>
                  </template>
                </a-table-column>
                <a-table-column title="URL" dataIndex="url" :ellipsis="true" :width="300">
                  <template #default="{text}">
                    <span>{{text?.trim()}}</span>
                  </template>
                </a-table-column>
                <a-table-column title="Create Time" dataIndex="create_time" :width="180">
                  <template #default="{text}">
                    <span>{{formatDateTime(text)}}</span>
                  </template>
                </a-table-column>
                <a-table-column title="Execution Time" dataIndex="execution_time" :width="120">
                  <template #default="{text}">
                    <span :style="{color: text > 2 ? '#ff4d4f' : ''}">
                      {{text ? `${text}s` : '-'}}
                    </span>
                  </template>
                </a-table-column>
              </a-table>

              <a-modal v-model:visible="modalVisible" width="80%" :footer="null">
                <template #title>
                  <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                    <span>Outbounding Log Details</span>
                    <div>
                      <a-button type="primary" @click="copyPageContent" :loading="copyLoading" style="margin-right: 28px;">
                        <template #icon><copy-outlined /></template>
                      </a-button>
                    </div>
                  </div>
                </template>
                <div v-if="currentLog" class="log-details">
                  <div class="detail-item">
                    <div class="detail-label">ID:</div>
                    <div class="detail-value">{{ currentLog.id }}</div>
                  </div>

                  <div class="detail-item">
                    <div class="detail-label">Operation:</div>
                    <div class="detail-value">{{ currentLog.operation || '-' }}</div>
                  </div>
                  
                  <div class="detail-item">
                    <div class="detail-label">URL:</div>
                    <div class="detail-value">{{ currentLog.url?.trim() }}</div>
                  </div>
                  
                  <div class="detail-item">
                    <div class="detail-label">Create Time:</div>
                    <div class="detail-value">{{ formatDateTime(currentLog.create_time) }}</div>
                  </div>
                  
                  <div class="detail-item">
                    <div class="detail-label">Execution Time:</div>
                    <div class="detail-value" :style="{color: currentLog.execution_time > 2 ? '#ff4d4f' : ''}">
                      {{ currentLog.execution_time ? `${currentLog.execution_time}s` : '-' }}
                    </div>
                  </div>
                  
                  <div class="detail-item">
                    <div class="detail-label">Header:</div>
                    <div class="detail-value">
                      <a-card>
                        <a-typography-paragraph>
                          <pre class="json-viewer">{{ formatJson(currentLog.header) }}</pre>
                        </a-typography-paragraph>
                        <!-- 解析 token -->
                        <div style="margin-top:10px;">
                          <div class="detail-label">Token Payload:</div>
                          <pre class="json-viewer">{{ formatJson(parseToken(currentLog.header)) }}</pre>
                        </div>
                      </a-card>
                    </div>
                  </div>
                  
                  <div class="detail-item">
                    <div class="detail-label">Send Data:</div>
                    <div class="detail-value">
                      <a-card>
                        <a-typography-paragraph>
                          <pre class="json-viewer">{{ formatJson(currentLog.send_data) }}</pre>
                        </a-typography-paragraph>
                      </a-card>
                    </div>
                  </div>
                  
                  <div class="detail-item">
                    <div class="detail-label">Response:</div>
                    <div class="detail-value">
                      <a-card>
                        <a-typography-paragraph>
                          <pre class="json-viewer">{{ formatJson(currentLog.response) }}</pre>
                        </a-typography-paragraph>
                      </a-card>
                    </div>
                  </div>
                </div>
              </a-modal>
            </div>
          <!-- </a-tab-pane> -->
        <!-- </a-tabs> -->
      </div>
      <div v-else class="no-access">
        <p>You do not have permission to view this page.</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container-gl_list {
  padding: 20px;
}

.content-box-import-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-access {
  text-align: center;
  padding: 40px;
  color: #999;
  font-size: 16px;
}

.logs-container {
  padding: 20px 0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .search-inputs {
    display: flex;
  }
  
  .action-buttons {
    display: flex;
    align-items: center;
  }
}

.log-details {
  .detail-item {
    margin-bottom: 16px;
    
    .detail-label {
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .detail-value {
      word-break: break-all;
    }
  }
}

.json-viewer {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.ant-table-tbody > tr) {
  cursor: pointer;
  
  &:hover {
    background-color: #f5f5f5;
  }
}
</style>