<!-- @format -->

<script lang="ts" setup>
import {ref} from 'vue'
import {useStore} from 'vuex'

import {notification, message} from 'ant-design-vue'
import {useRouter} from 'vue-router'

import ApInvoiceComponent from '../../../components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'

const props = withDefaults(
    defineProps<{
        from: string
        currentInvoice: any
    }>(),
    {
        currentInvoice: {},
        from: '',
    },
)

interface ApInvoiceFrom {
    create_time: any
    id: any
    pay_method: string
    company_name: string
    reference_no: string
    invoice_due_date: string
    posting_date: string
    invoice_currency: string
    net_amount: number
    items: any[]
    invoice_comments: number
    gst: number
    pst: number
    qst: number
    total_fee: number
}

const emits = defineEmits(['custom-cancel'])

const store = useStore()
const router = useRouter()

const tableLoading = ref(false)
const current = ref({})

const invoiceForm: any = ref(null)

const save = async (form: ApInvoiceFrom) => {
    let response: any = {}
    try {
        tableLoading.value = true

        //TODO: test pay_method === 2 scenario
        // if (form.pay_method === '2') {
        //     const payload = {
        //         company_name: form.company_name,
        //         statement_id: '-1',
        //         invoice_type: '2',
        //         reference_no: form.reference_no,
        //         invoice_due_date: form.invoice_due_date, //2022-01-24
        //         posting_date: form.posting_date, //2022-01-25
        //         bankAccount: '',
        //         invoice_currency: form.invoice_currency,
        //         net_amount: form.net_amount,
        //         expense_account_id: form.items[0].expense_account_id,
        //         invoice_comments: form.invoice_comments,
        //         items: form.items,
        //         gst: form.gst,
        //         pst: form.pst,
        //         qst: form.qst,
        //         expense_account: form.items[0].expense_account, //coa in item
        //         esAmount: form.total_fee, //total
        //         total_fee: form.total_fee, //total
        //         pay_method: form.pay_method,
        //     }
        //     // response = await store.dispatch('ApStore/noestatementBR', payload)
        //
        // } else {
        //     response = await store.dispatch('ApStore/createInvoiceV1', form)
        // }
        console.log('fadsdfasfafasfdasf', form)
        if (form.id) {
            form.id = null
            delete form.create_time
        }
        form.items.forEach(item => {
            delete item.id
            delete item.create_time
        })
        response = await store.dispatch('ApStore/createInvoiceV1', form)
        if (response.data.statusCode === 200) {
            message.success('success')
            // notification.success({
            //     message: 'Success',
            //     duration: 6,
            //     description: response.data.message,
            // })
            invoiceForm.value.initFormData()
            const content_id = document.getElementById('content_id')
            if (content_id) {
                content_id.scrollIntoView({behavior: 'smooth'})
            }
            await router.push({path: '/bookkeeping/ap/invoiceHistory'})
        } else {
            // message.error({
            //     content: response.data.message,
            // })
        }
        emits('custom-cancel')
    } catch (error: any) {
        console.log(error)
        // message.error(error.response.data.message)
    } finally {
        tableLoading.value = false
    }
}
const dismiss = () => {
    // router.go(-1)
    emits('custom-cancel')
}
</script>
<template>
    <div class="page-container-invoice" id="content_id">
        <a-spin :spinning="tableLoading">
            <ap-invoice-component
                ref="invoiceForm"
                :current-invoice="props.currentInvoice"
                :from="props.from"
                :readonly-mode="false"
                :operationMode="'creating'"
                @save="save"
                @dismiss="dismiss"
            ></ap-invoice-component>
        </a-spin>
    </div>
</template>

<style lang="scss" scoped>
.page-container-invoice {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .ap-invoice-page-wrap {
        :deep(.ap-invoice-block) {
            background-color: #fff;
            padding: 32px 20px;
            border-radius: 12px;
            margin-bottom: 16px;
            border-bottom: 0;
        }
        :deep(.ap-invoice-amount-block .ap-invoice-amount-block-left) {
            width: 510px;
        }
        :deep(.ap-invoice-amount-block) {
            padding-bottom: 0;
        }

        :deep(.textarea-wrap) {
            margin-bottom: 32px;
        }

        :deep(.ap-invoice-footer) {
            margin-bottom: 40px;
        }
    }
}
</style>
