/** @format */

import type {ActionContext} from 'vuex'
import service from '@/api/request'
import serviceV1 from '@/api/requestNew'

const http = service
// v1 is new api.
const httpv1 = serviceV1

const PyStore = {
    namespaced: true,
    state: {
        invoicesList: [],
        invoicesListByPdf: [],
        ocrInvoicesListByPdf: [],
        ocrInvoiceItemByFile: {},
        invoicesHistoryList: [],
        invoiceDetail: {},
        totalNumber: 0,
        apTopCoaList: [],
        activeTabKey: '',
    },
    mutations: {
        updateInvoicesList(state: {invoicesList: any[]}, list: any) {
            state.invoicesList = [...list]
        },
        updateInvoicesHistoryList(state: {invoicesHistoryList: any[]}, list: any) {
            state.invoicesHistoryList = [...list]
        },
        updateInvoiceDetail(state: {invoiceDetail: any}, detail: any) {
            state.invoiceDetail = {...detail}
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateInvoicesListByPdf(state: {invoicesListByPdf: any[]}, list: any) {
            state.invoicesListByPdf = [...list]
        },
        updateOcrInvoicesList(state: {ocrInvoicesListByPdf: any[]}, list: any) {
            state.ocrInvoicesListByPdf = [...list]
        },
        updateOcrInvoiceItem(state: {ocrInvoiceItemByFile: any}, list: any) {
            if (list && list.length > 0) {
                state.ocrInvoiceItemByFile = {...list[0]}
            } else {
                state.ocrInvoiceItemByFile = {NotExisted: true}
            }
        },
        updateApTopCoa(state: {apTopCoaList: any[]}, list: any) {
            state.apTopCoaList = [...list]
        },
        updateActiveTabKey(state: {activeTabKey: string}, keyName: string) {
            state.activeTabKey = keyName ?? 'pending'
        },
    },
    actions: {
        async fetchInvoicesList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/file/list', payload)
            store.commit('updateInvoicesList', response.data.data.list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchInvoicesListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.get('invoice-statement/api/v1/file', {params: payload})
            const totalNumber = response.data.paginated ? response.data.paginated.total : response.data.data.length
            store.commit('updateInvoicesList', response.data.data)
            store.commit('updateTotalFoundNumber', totalNumber)
            return response
        },
        async fetchInvoicesHistoryList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            // const response = await http.post('/bk/ap/invoice/list', payload)
            // store.commit('updateInvoicesHistoryList', response.data.data.list)
            // store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            // // used for creating item callback to get the item just create.
            // if (response.data.data.list && response.data.data.totalCount === 1) {
            //     store.commit('updateOcrInvoiceItem', response.data.data.list)
            // }
            //  delete payload.page_size
            const response = await httpv1.get('/invoice-statement/api/v1/ap', {params: payload})

            const list = response.data.data ?? []
            const totalNumber = response.data.paginated ? response.data.paginated.total : list.length
            store.commit('updateInvoicesHistoryList', list)
            store.commit('updateTotalFoundNumber', totalNumber)
            // used for creating item callback to get the item just create.
            if (list && list.length === 1) {
                store.commit('updateOcrInvoiceItem', list)
            }

            return response
        },
        async checkReferenceNoRepetition(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await http.post('/bk/ap/invoice/list', payload)
            return response
        },
        async checkReferenceNoRepetitionV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.get('/invoice-statement/api/v1/ap', {params: payload})
            return response
        },
        async fetchApInvoiceDetailWithId(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any},
        ) {
            const response = await http.get(`/bk/ap/invoice/${payload.id}`)
            store.commit('updateInvoiceDetail', response.data.data)
            return response
        },
        async fetchApInvoiceDetailWithIdv1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any},
        ) {
            const response = await httpv1.get(`/invoice-statement/api/v1/ap/${payload.id}`)
            store.commit('updateInvoiceDetail', response.data.data)
            store.commit('updateOcrInvoiceItem', response.data.data)
            return response
        },
        async deletePdfWithId(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/file/${payload}`)
        },
        async deletePdfWithIdV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpv1.delete(`invoice-statement/api/v1/file/${payload}`)
        },
        async uploadApInvoicePdf(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const formData = payload
            const response = await http.post('/bk/file/upload', formData)
            return response
        },
        async uploadApInvoicePdfV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const formData = payload
            const response = await httpv1.post('/invoice-statement/api/v1/file/upload', formData)
            return response
        },
        async fetchApInvoiceListWithPdf(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {fileId: any},
        ) {
            const response = await http.post(`/bk/ap/pdf/${payload.fileId}/invoice/list`)
            store.commit('updateInvoicesListByPdf', response.data.data)
            return response
        },
        async fetchApOcrResultByPdfId(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {fileId: any},
        ) {
            const response = await http.post(`/bk/ocr/pdf/${payload.fileId}/invoice/list`)
            store.commit('updateOcrInvoicesList', response.data.data)
            // always use first item as 'current' in apInvoiceFromPdf component.
            store.commit('updateOcrInvoiceItem', response.data.data)
            return response
        },
        async fetchApOcrResultByPdfIdV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {file_id: any},
        ) {
            // ocr will not enable
            // const response = await http.post(`/bk/ocr/pdf/${payload.fileId}/invoice/list`)
            const response = await httpv1.get('/invoice-statement/api/v1/ap', {params: payload})
            store.commit('updateOcrInvoicesList', response.data.data)
            // always use first item as 'current' in apInvoiceFromPdf component.
            store.commit('updateOcrInvoiceItem', response.data.data)
            return response
        },
        async createInvoice(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/ap/invoice', payload)
            return response
        },
        async createInvoiceV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post('/invoice-statement/api/v1/ap', payload)
            return response
        },
        async updateInvoiceV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.put(`/invoice-statement/api/v1/ap/${payload.id}`, payload)
            return response
        },
        async reversePurchaseRequestV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.patch(`/invoice-statement/api/v1/ap/${payload.id}/reverse`, {
                creator: payload?.creator,
                creator_name: payload?.creator_name,
            })
            return response
        },
        async noestatementBR(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/br/reconciliation/noestatement/submit', payload)
            return response
        },
        async fetchApTopCoa(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {clientId: any},
        ) {
            const response = await http.get(`/bk/ap/coa/top?clientId=${payload.clientId}`)
            store.commit('updateApTopCoa', response.data)
            return response
        },
        async fetchApTopCoaV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {clientId: any},
        ) {
            const response = await httpv1.get(`/system-preferences/api/v1/coa?company_id=${payload.clientId}`)
            store.commit('updateApTopCoa', response.data.data)
            return response
        },
    },
}

export default PyStore
