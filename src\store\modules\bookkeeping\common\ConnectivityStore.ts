/** @format */

import type {ActionContext} from 'vuex'
// import service from '../../../../api/request'
import service from '@/api/requestNew'
import stripeLogo from '@/assets/image/logo/stripe-logo.png'
import amazonLogo from '@/assets/image/logo/amazon-logo.png'
import shopifyLogo from '@/assets/image/logo/shopify-logo.png'
import squareLogo from '@/assets/image/logo/square-logo.png'
import cloverLogo from '@/assets/image/logo/clover-logo.png'

const http = service

const ConnectivityStore = {
    namespaced: true,
    getters: {
        importSources: () => {
            return [
                {id: 0, key: '0', value: 'stripe', name: 'Stripe', label: 'Stripe', image: stripeLogo},
                {id: 1, key: '1', value: 'amazon', name: 'Amazon', label: 'Amazon', image: amazonLogo},
                {id: 1, key: '1', value: 'shopify', name: 'Shopify', label: 'Shopify', image: shopify<PERSON>ogo},
                {id: 1, key: '1', value: 'square', name: 'Square', label: 'Square', image: squareLogo},
                {id: 1, key: '1', value: 'clover', name: 'Clover', label: 'Clover', image: cloverLogo},
            ]
        },
        //Stripe, Amazon, Shopify, Square, Clover
    },
    state: {
        sourceItem: {},
        smtpItems: [],
        netDiskItem: {},
    },
    mutations: {
        updateSourceItem(state: {sourceItem: any}, source: any) {
            state.sourceItem = {...source}
        },
        updateSMTPItems(state: {smtpItems: any}, smtp: any) {
            state.smtpItems = [...smtp]
        },
        updateNetDiskItem(state: {netDiskItem: any}, netdisk: any) {
            state.netDiskItem = {...netdisk}
        },
    },
    actions: {
        async fetchSource(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get(`/mail-attachment/api/v2/mail-attachment`, {
                params: {...payload, $limit: 1, $skip: 0},
            })
            const item = response.data.data.length > 0 ? response.data.data[0] : {}
            store.commit('updateSourceItem', item)

            return response
        },
        async fetchSMTP(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get(`/mail-attachment/api/v2/mail-attachment`, {
                params: {...payload},
            })
            const items = response.data.data.length > 0 ? response.data.data : []
            store.commit('updateSMTPItems', items)

            return response
        },
        async fetchNetDisk(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get(`/mail-attachment/api/v2/mail-attachment?$limit=1&$skip=0`)
            const item = response.data.data.length > 0 ? response.data.data[0] : {}
            store.commit('updateNetDiskItem', item)

            return response
        },
        async createSMTP(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post(`/mail-attachment/api/v2/mail-attachment`, payload)
            const item = response.data.data ? response.data.data : {}

            return response
        },
        async updateSMTP(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.patch(`/mail-attachment/api/v2/mail-attachment/${payload.id}`, payload)
            const item = response.data.data ? response.data.data : {}
            // store.commit('updateSMTPItems', item)

            return response
        },
    },
}

export default ConnectivityStore
