<!-- @format -->

<script setup lang="ts">
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {
    computed,
    nextTick,
    onBeforeMount,
    onMounted,
    onUnmounted,
    reactive,
    ref,
    watch,
    watchEffect,
    type CSSProperties,
} from 'vue'
import {useStore} from 'vuex'
import {ExclamationCircleOutlined} from '@ant-design/icons-vue'
import {message, type FormInstance} from 'ant-design-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import PdfViewer from '@/components/bookkeepingComponents/PdfViewer.vue'
import moment from 'moment'
import * as _ from 'lodash'
import {UserCompany, LocalCurrency} from '@/lib/storage'
import {useDraggable} from '@vueuse/core'
import dayjs from 'dayjs'
import GlComponent from '@/components/bookkeepingComponents/GlComponents/GlComponent.vue'
import FileSaver from 'file-saver'

const modalTitleRef = ref<HTMLElement | null>(null)
const {x, y, isDragging} = useDraggable(modalTitleRef)
const startedDrag = ref(false)
const startX = ref<number>(0)
const startY = ref<number>(0)
const transformX = ref(0)
const transformY = ref(0)
const preTransformX = ref(0)
const preTransformY = ref(0)
const dragRect = ref({left: 0, right: 0, top: 0, bottom: 0})

const userCompany: any = UserCompany.get() || []
const i18n: Composer = i18nInstance.global
const store = useStore()

const props = withDefaults(
    defineProps<{
        currentInvoice: any
        readonlyMode: boolean
        operationMode: string
        invoiceId: string
    }>(),
    {
        readonlyMode: false,
        operationMode: 'creating',
        invoiceId: '',
    },
)

const emits = defineEmits(['save', 'dismiss'])
const accountQuery = {bk_type: 2, company_code: userCompany[0].code, $limit: -1, del_flag: 0}
const formRef = ref<FormInstance>()
const showFileViewer = ref(false)

const form_diff = computed(() => Number(form.value.total_fee_local) - Number(form.value.total_fee))
const localCurrency = LocalCurrency.get() || 'CAD'

const form = ref({
    id: null,
    pay_method: '1',
    company_name: undefined as string | undefined,
    company_code: '',
    company_id: '',
    company_email: '',
    company_phone: '',
    issuer_name: '',
    issuer_tel: '',
    issuer_email: '',
    reference_no: '',
    invoice_currency: localCurrency,
    invoice_create_date: '',
    invoice_due_date: '',
    posting_date: '',
    items: [] as any[],
    amount: null as number | null,
    net_amount: null as number | null,
    file_id: '',
    file_url: '',
    file_name: '',
    creator: '',
    engine_document_id: '' || [] || null,
    cash_engine_payment_no: '' || null,
    engine_reverse_document_id: '' || (null as any),
    total_tax: null as number | null,
    total_fee: null as number | null,
    total_fee_local: null as number | null,
    tax_content: [] as any[],
    shipping: null,
    invoice_comments: '',
    issuer_id: '',
    issuer_address: null,
    invoice_no: '',
    br_type: '',
    exchange_rate: null as string | null,
})
const payMethodOptions = reactive([
    {
        value: '1',
        label: i18n.t('ApInvoiceFormPdf.notPaid'), //'NOT PAID',
    },
    {
        value: '2',
        label: i18n.t('ApComponents.cashpaid'), //'CASH PAID',
    },
    // {
    //     value: '3',
    //     label: 'FUNDING TRANSFER',
    // },
    // {
    //     value: '4',
    //     label: 'INTERCOM',
    // },
])
const downloadFileloading = ref(false)
const isWeekend = ref(false)
const postingDate = ref(dayjs().format('YYYY-MM-DD'))
const formLoading = ref(false)
const currencyLoading = ref(false)
const referenceNoLoading = ref(false)
const glCurrent = ref({})
const spot = ref({
    rate: '',
    rate_date: '',
})

const contactLoading = ref(false)

const showGlDialog = ref(false)
const uploadFileList = ref([] as any[])
const uploadState = ref(false)
const glOperationMode = ref('apDetail')

const getFileBlobById = (data: any) => store.dispatch('ArApBrStore/getFileBlobByIdV1', data)
const fetchAccountDescDropdown = (query?: any) =>
    store.dispatch('CommonDropDownStore/fetchAccountDescDropdownV2', query)
const checkReferenceNoRepetition = (query?: any) => store.dispatch('ApStore/checkReferenceNoRepetitionV1', query)
const getSpot = (query?: any) => store.dispatch('Utils/getSpotv1', query)
const fetchApInvoiceDetail = (query: any) => store.dispatch('ApStore/fetchApInvoiceDetailWithIdv1', query)
const fetchGlList = (payload: any) => store.dispatch('GlStore/fetchGlListV1', payload)

const fetchInvoiceItem = (data: {file_id: string}) => {
    return store.dispatch('ApStore/fetchApOcrResultByPdfIdV1', data)
}
const supplierList: any = computed(() => store.state.CommonDropDownStore.supplierOptions)
const accountDescList: any = computed(() => store.state.CommonDropDownStore.accountDescList)
const accountCurrencyOptions: any = computed(() => store.state.CommonDropDownStore.bankCurrencyOptions)
const currentPdfInfo: any = computed(() => store.state.ArApBrStore.pdfInfo)
const invoiceItem = computed(() => store.state.ApStore.ocrInvoiceItemByFile)

const resetFormField = () => {
    formRef.value?.resetFields()
}
const cancel = () => {
    emits('dismiss')
}

const referenceNoChange = async () => {
    if (form.value.reference_no) {
        await checkRefNoRepetition(form.value.reference_no)
    }
}
const checkRefNoRepetition = async (value: string) => {
    const query = {
        reference_no: value,
        page_index: 1,
        page_size: 10,
    }
    try {
        referenceNoLoading.value = true
        const response = await checkReferenceNoRepetition(query)
        if (response.data.statusCode === 200) {
            if (response.data.data.length > 0) {
                message.warning({
                    content: i18n.t('bkAp.msgReferenceNoExisted'),
                    duration: 8,
                })
            }
        }
    } catch (error) {
        console.log(error)
    } finally {
        referenceNoLoading.value = false
    }
}

const expenseAccountAlias = (
    // row: {expenseAccountId: any; expenseAccount: string},
    row: {credit_coa_code: string; credit_coa_id: string},
    accountList: any[],
) => {
    // let alias = ''
    // accountDescList.forEach((item: {account_code: string; name: string}) => {
    //     if (String(item.account_code) === String(row.credit_coa_code)) {
    //         alias = item.name
    //     }
    // })
    const item = accountList.find((item: any) => item.account_code === row.credit_coa_code)
    return `${row.credit_coa_code.substring(0, 4)} | ${item?.name || ''}`
}

const updateSpot = async () => {
    if (form.value.invoice_currency.toString() === localCurrency) {
        form.value.total_fee_local = form.value.total_fee // TODO
        return
    }
    currencyLoading.value = true
    const baseCurrency = form.value.invoice_currency
    const quoteCurrency = localCurrency

    const weekOfDayDiff = getSpotInputDateStatus(form.value.posting_date)
    isWeekend.value = weekOfDayDiff < 0
    if (!isWeekend.value) {
        postingDate.value = form.value.posting_date
    } else {
        postingDate.value = moment(form.value.posting_date, 'YYYY-MM-DD')
            .add(weekOfDayDiff, 'days')
            .format('YYYY-MM-DD')
    }
    try {
        spot.value = await getSpot({baseCurrency, quoteCurrency, date: postingDate.value})
        form.value.exchange_rate = spot.value.rate
    } finally {
        currencyLoading.value = false
    }

    // comments for test
    // postingDate.value = form.value.posting_date
    // isWeekend.value = getSpotInputDateStatus(form.value.posting_date)
    //
    // form.value.total_fee_local = form.value.total_fee != null ? form.value.total_fee * parseFloat(spot.value.rate) : null

    form.value.total_fee_local =
        form.value.total_fee != undefined
            ? Number((form.value.total_fee * parseFloat(spot.value.rate ?? '')).toFixed(2))
            : null
}

const getSpotInputDateStatus = (date: moment.MomentInput) => {
    const weekOfday = moment(date, 'YYYY-MM-DD').format('E')
    return 5 - +weekOfday
}

const displayFileViewerModal = () => {
    showFileViewer.value = true
}

const viewerStyle = {
    height: '500px',
}

//查看gl详情
const seeDetail = async (val: any) => {
    const query = {
        company: userCompany[0].code,
        document_no: val,
    }

    const response = await fetchGlList(query)
    glCurrent.value = _.cloneDeep(response.data)
    showGlDialog.value = true
}
const glDismiss = () => {
    showDialog(false)
}
const showDialog = (bool: boolean) => {
    showGlDialog.value = bool
    if (!bool) {
        glCurrent.value = {}
    }
}
const closeFileViewerModal = () => {
    showFileViewer.value = false
}
const downloadFile = async () => {
    if (form.value.file_url) {
        downloadFileloading.value = true
        const response = await getFileBlobById(form.value.file_url)
        try {
            if (response.status === 200 && response.data.size > 0) {
                const filename = form.value.file_url.split('/').pop()
                // const blob = new Blob([response.data], {type: 'application/pdf;charset=utf-8'})
                const blob = new Blob([response.data], {
                    type: form.value.file_url.includes('.pdf')
                        ? 'application/pdf;charset=utf-8'
                        : 'image/jpg;charset=utf-8',
                })
                FileSaver.saveAs(blob, filename)
            }
            // else {
            //      message.error({content: 'Invoice file is not available.'})
            // }
        } catch (e) {
            console.log(e)
        } finally {
            downloadFileloading.value = false
        }
    }
}

watch(
    () => invoiceItem.value,
    (count, prevCount) => {
        if (Object.keys(invoiceItem.value).length > 1) {
            formLoading.value = false
        }
        if (Object.keys(invoiceItem.value).length === 1 && invoiceItem.value.NotExisted) {
            formLoading.value = false
        }
    },
    {deep: true},
)

watch([x, y], () => {
    if (!startedDrag.value) {
        startX.value = x.value
        startY.value = y.value
        const bodyRect = document.body.getBoundingClientRect()
        const titleRect = modalTitleRef.value?.getBoundingClientRect()

        dragRect.value.left = -(bodyRect.width + titleRect!.width)
        dragRect.value.right = bodyRect.width + titleRect!.width

        dragRect.value.bottom = bodyRect.height + titleRect!.height
        preTransformX.value = transformX.value
        preTransformY.value = transformY.value
    }
    startedDrag.value = true
})

watch(isDragging, () => {
    if (!isDragging) {
        startedDrag.value = false
    }
})

watchEffect(() => {
    if (startedDrag.value) {
        transformX.value =
            preTransformX.value + Math.min(Math.max(dragRect.value.left, x.value), dragRect.value.right) - startX.value
        transformY.value =
            preTransformY.value + Math.min(Math.max(dragRect.value.top, y.value), dragRect.value.bottom) - startY.value
    }
})

const transformStyle = computed<CSSProperties>(() => {
    return {
        transform: `translate(${transformX.value}px, ${transformY.value}px)`,
    }
})

onBeforeMount(async () => {
    formLoading.value = true
    if (currentPdfInfo.value.id) await fetchInvoiceItem({file_id: currentPdfInfo.value.id})
    if (props.operationMode === 'editing' || props.readonlyMode) {
        let invoiceObj = props.currentInvoice.id ? props.currentInvoice : invoiceItem.value

        if (props.invoiceId) {
            const res = await fetchApInvoiceDetail({id: props.invoiceId})
            invoiceObj = res.data.data
        }
        form.value = _.cloneDeep(invoiceObj)
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee, balance
        // const {amount, totalTaxable, totalTax, totalFee, balance} =  _.cloneDeep(this.currentInvoice)
        // keep original ocr scan results for calculated vars: amount, totalTaxable, totalTax, totalFee
        const {net_amount, total_tax, total_fee} = props.currentInvoice.id
            ? _.cloneDeep(props.currentInvoice)
            : _.cloneDeep(invoiceItem.value)

        form.value.pay_method =
            props.currentInvoice.br_flag === '2' && !props.currentInvoice.sap_document_id ? '2' : '1'

        await nextTick(() => {
            form.value.net_amount = net_amount ? net_amount : 0.0
            //form.value.totalTaxable = totalTaxable ? totalTaxable : 0.0
            form.value.total_tax = total_tax ? total_tax : 0.0
            form.value.total_fee = total_fee ? total_fee : 0.0
            // form.value.balance = balance ? balance : 0.0
        })
    }

    try {
        await fetchAccountDescDropdown({...accountQuery})
        await updateSpot()
    } catch (e) {
        console.log(e)
    } finally {
        formLoading.value = false
        console.log('page before mounted')
    }
})

onMounted(async () => {
    console.log(props.currentInvoice)
    console.log(form.value)
    console.log(accountCurrencyOptions.value)
    formRef.value?.clearValidate()
})

onUnmounted(() => {
    uploadFileList.value = [] as any[]
})

defineExpose({resetFormField})
</script>

<template>
    <div class="ap-invoice-page-wrap">
        <a-spin :spinning="formLoading">
            <a-form
                ref="formRef"
                :model="form"
                :layout="'vertical'"
                :rules="{}"
                class="invoice-form"
                autocomplete="off"
            >
                <div class="ap-invoice-block">
                    <a-row :gutter="24">
                        <a-col :span="8">
                            <a-form-item name="issuer_name" :label="i18n.t('bkAp.companyName')">
                                <a-select
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="form.issuer_name"
                                    :disabled="true"
                                    show-search
                                    :filter-option="false"
                                    :loading="contactLoading"
                                >
                                    <a-select-option v-for="item in supplierList" :key="item.id" :value="item.id">{{
                                        item.contact_name
                                    }}</a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item name="referenceNo">
                                <template v-slot:label>
                                    {{ i18n.t('bkAp.referenceNo') }}
                                    <a-tooltip>
                                        <template #title>Reference No. in the receipts.</template>
                                        <exclamation-circle-outlined class="icon-exclamation" />
                                    </a-tooltip>
                                </template>
                                <a-spin :spinning="referenceNoLoading" wrapperClassName="input-spin">
                                    <a-input
                                        v-model:value="form.reference_no"
                                        @blur="referenceNoChange"
                                        :disabled="true"
                                    ></a-input>
                                </a-spin>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item name="invoice_currency" :label="i18n.t('bkAp.currency')">
                                <a-select
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="form.invoice_currency"
                                    :disabled="true"
                                    @change="updateSpot"
                                >
                                    <a-select-option
                                        v-for="item in accountCurrencyOptions"
                                        :key="item.key"
                                        :value="item.key"
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                                <div v-show="form.invoice_currency === localCurrency" style="padding: 2px 5px">
                                    &nbsp;
                                </div>
                                <div
                                    v-show="form.invoice_currency !== localCurrency"
                                    style="color: red; padding: 2px 5px"
                                >
                                    <!--  <span v-if="!isWeekend && !spot.rate">-->
                                    <!--      Spot currency is null on {{ form.posting_date }}, pls contact administrator-->
                                    <!--  </span>-->
                                    <!--  <span v-else>-->
                                    <!--      Spot currency is {{ spot.rate || 'null' }} on {{ postingDate }}-->
                                    <!--  </span>-->
                                    <span>
                                        {{
                                            i18n.t('ApComponents.spotCurrency', {
                                                rate: spot.rate || 'null',
                                                date: postingDate,
                                            })
                                        }}
                                        {{ !spot.rate ? ', ' + i18n.t('ApComponents.contactAdmin') : '' }}
                                    </span>
                                </div>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8" v-show="false">
                            <a-form-item name="invoiceCreateDate" :label="i18n.t('bkAp.date')">
                                <a-date-picker
                                    v-model:value="form.invoice_create_date"
                                    :disabled="true"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    placeholder="Create Date"
                                    style="width: 100%"
                                    clearable
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item name="invoiceFixedDate" :label="i18n.t('bkAp.dueDate')">
                                <a-date-picker
                                    v-model:value="form.invoice_due_date"
                                    :disabled="true"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    placeholder="Fixed Date"
                                    style="width: 100%"
                                    clearable
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item :name="''" :label="i18n.t('bkAp.postingDate')">
                                <a-date-picker
                                    v-model:value="form.posting_date"
                                    :disabled="true"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :placeholder="$t('bkAp.postingDate')"
                                    style="width: 100%"
                                    clearable
                                    @change="updateSpot"
                                >
                                    <template #suffixIcon>
                                        <svg-icon name="icon_date"></svg-icon>
                                    </template>
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <a-form-item name="payMethod" :label="i18n.t('bkAp.payMethod')">
                                <a-select
                                    :placeholder="i18n.t('commonTag.msgSelect')"
                                    v-model:value="form.pay_method"
                                    :disabled="true"
                                >
                                    <a-select-option
                                        v-for="item in payMethodOptions"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.label }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>
                <div class="ap-invoice-block">
                    <a-table :dataSource="form.items" :pagination="false">
                        <a-table-column
                            align="center"
                            :title="i18n.t('bkAp.itemNo')"
                            data-index="item_no"
                            width="8%"
                            :ellipsis="true"
                        />

                        <a-table-column
                            :title="i18n.t('bkAp.modelNumber')"
                            data-index="model"
                            :ellipsis="true"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                {{ record.model }}
                            </template>
                        </a-table-column>
                        <a-table-column
                            :title="i18n.t('bkAp.description')"
                            data-index="description"
                            :ellipsis="true"
                            width="15%"
                        >
                            <template #default="{index, record}">
                                {{ record.description }}
                            </template>
                        </a-table-column>
                        <a-table-column
                            :title="i18n.t('bkAp.accountingCategory')"
                            data-index="bank_account"
                            width="20%"
                        >
                            <template #default="{index, record}">
                                {{ expenseAccountAlias(record, accountDescList) }}
                            </template>
                        </a-table-column>
                        <a-table-column :title="i18n.t('bkAp.qty')" data-index="qty" :ellipsis="true" width="10%">
                            <template #default="{index, record}">
                                {{ record.qty }}
                            </template>
                        </a-table-column>
                        <a-table-column
                            :title="i18n.t('bkAp.unitPrice')"
                            data-index="unit_price"
                            :ellipsis="true"
                            width="10%"
                        >
                            <template #default="{index, record}">
                                {{ Number(record.unit_price).toFixed(2) }}
                            </template>
                        </a-table-column>

                        <a-table-column :title="i18n.t('bkAp.total')" data-index="total" :ellipsis="true" width="12%">
                            <template #default="{index, record}">
                                <!-- {{ Number(record.total).toFixed(2) }} -->
                                {{ $formatNumber(Number(record.total)) }}
                            </template>
                        </a-table-column>
                    </a-table>
                    <div class="ap-invoice-amount-block">
                        <div class="ap-invoice-amount-block-left"></div>
                        <div class="ap-invoice-amount-block-right">
                            <div class="amount-block">
                                <div class="amount-item-wrap">
                                    <div class="amount-item">
                                        <div class="amount-lable">Net Amount</div>
                                        <a-input-number
                                            v-model:value="form.net_amount"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div
                                        class="amount-item"
                                        v-for="item in form.tax_content || []"
                                        :key="item.fieldName"
                                    >
                                        <div class="amount-lable">{{ item.alias }}</div>
                                        <a-input-number
                                            :value="item.value"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div class="amount-item">
                                        <div class="amount-lable">Tax Subtotal</div>
                                        <a-input-number
                                            v-model:value="form.total_tax"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div v-show="false" class="amount-item">
                                        <div class="amount-lable">Taxable Subtotal</div>
                                        <a-input-number
                                            v-model:value="form.net_amount"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div class="amount-item">
                                        <div class="amount-lable bold">TOTAL {{ form.invoice_currency }}</div>
                                        <a-input-number
                                            v-model:value="form.total_fee"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <div class="amount-item" v-if="form.invoice_currency !== localCurrency">
                                        <div class="amount-lable bold">TOTAL {{ localCurrency }}</div>
                                        <a-input-number
                                            v-model:value="form.total_fee_local"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-input"
                                        >
                                        </a-input-number>
                                    </div>
                                    <!-- <div
                                        class="amount-item"
                                        v-if="form.invoice_currency !== localCurrency && !props.readonlyMode"
                                    >
                                        <div class="amount-lable bold">Difference</div>
                                        <a-input-number
                                            v-model:value="form_diff"
                                            :disabled="true"
                                            :controls="false"
                                            :precision="2"
                                            class="amount-display-alert"
                                        >
                                        </a-input-number>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ap-invoice-block" style="display: flex; justify-content: space-between">
                    <a-form-item
                        name="invoiceComments"
                        :style="{width: props.operationMode === 'editing' ? '100%' : '49%'}"
                        :label="i18n.t('bkAp.invoiceComment')"
                    >
                        <a-textarea
                            class="textarea-wrap"
                            v-model:value="form.invoice_comments"
                            :placeholder="i18n.t('commonTag.msgInput')"
                            :rows="4"
                            :disabled="true"
                        />
                    </a-form-item>
                    <a-form-item
                        name="displayBlock"
                        style="width: 50%"
                        v-if="uploadState || form.file_url || readonlyMode"
                    >
                        <div style="font-size: 16px; font-weight: bold" v-if="readonlyMode">
                            {{ i18n.t('ArComponents.JE') }}
                            <div class="document-item-link" style="background-color: #f5f5f5">
                                <a @click="seeDetail(form.engine_document_id)">{{ form.engine_document_id }}</a>
                                <a class="document-item-link" @click="seeDetail(form.cash_engine_payment_no)">{{
                                    form.cash_engine_payment_no || ''
                                }}</a>
                            </div>
                            <div>
                                <a
                                    class="document-item-link"
                                    v-for="id in form.engine_reverse_document_id?.split(',')"
                                    :key="id"
                                    @click="seeDetail(id)"
                                    >{{ id }}</a
                                >
                            </div>
                        </div>
                        <div v-if="uploadState || form.file_url" style="font-size: 16px; font-weight: bold">
                            <div>Original Document</div>
                            <div style="background-color: #f5f5f5">
                                <a @click="displayFileViewerModal">{{ form.file_url?.split('/').pop() }}</a>
                            </div>
                            <!-- <a @click="displayFileViewerModal"> 1232323.pdf</a> -->
                        </div>
                    </a-form-item>
                </div>
                <div class="ap-invoice-footer">
                    <a-button class="cancel-button" shape="round" @click="cancel">{{
                        i18n.t('commonTag.cancel')
                    }}</a-button>
                    <a-button disabled type="primary" shape="round">{{ i18n.t('commonTag.reverse') }}</a-button>
                </div>
            </a-form>
        </a-spin>
        <a-modal
            :title="i18n.t('gl.readonly')"
            v-model:visible="showGlDialog"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="1000"
            style="z-index: 999"
            :dialogStyle="{top: '10px'}"
            :bodyStyle="{padding: '10px 24px 24px'}"
        >
            <gl-component
                :current-invoice="glCurrent"
                :readonly-mode="true"
                :operation-mode="glOperationMode"
                @dismiss="glDismiss"
            ></gl-component>
        </a-modal>
        <a-modal
            :body-style="viewerStyle"
            v-model:visible="showFileViewer"
            destroyOnClose
            :closeable="true"
            :mask="false"
            :maskClosable="false"
            :width="820"
            :wrapClassName="'modal-wrap'"
            ><pdf-viewer :url="''" />
            <template #title>
                <div ref="modalTitleRef" class="ant-modal-title-detail">
                    <span>View File</span>
                </div>
            </template>
            <template #modalRender="{originVNode}">
                <div :style="transformStyle">
                    <component :is="originVNode" />
                </div>
            </template>
            <template #footer>
                <a-button key="back" @click="closeFileViewerModal" shape="round" class="cancel-button">
                    Cancel
                </a-button>
                <a-button
                    key="submit"
                    type="primary"
                    shape="round"
                    :loading="downloadFileloading"
                    @click="downloadFile"
                >
                    Download
                </a-button>
            </template></a-modal
        >
    </div>
</template>

<style lang="scss" scoped>
.ap-invoice-block {
    padding: 24px 0;
    border-bottom: 1px solid #e2e2ea;

    &:first-child {
        padding-top: 0;
    }
}

.ap-invoice-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }

    .ant-btn + .ant-btn {
        margin-left: 12px;
    }
}

.table-input {
    background-color: #f5f7f9;
    border-color: #f5f7f9;
    border-radius: 4px;
    padding-left: 4px;
    padding-right: 4px;

    &.ant-input-number {
        padding-left: 0px;
        padding-right: 0px;

        :deep(.ant-input-number-input-wrap input) {
            padding-left: 4px;
            padding-right: 4px;
        }
    }

    &.ant-select {
        padding-left: 0px;
        padding-right: 0px;

        :deep(.ant-select-selector) {
            padding-left: 4px;
            padding-right: 4px;

            .ant-select-selection-search {
                left: 4px;
            }
        }
    }

    &.ant-input:hover,
    &.ant-input-number:hover,
    &.ant-input:focus,
    &.ant-input-focused,
    &.ant-input-number-focused {
        border-color: #216fcf;
    }

    :deep(.ant-select-selector) {
        background-color: #f5f7f9 !important;
        border-color: #f5f7f9;
    }
}

.ap-invoice-block {
    :deep(.ant-row.ant-form-item) {
        margin: 0;

        .ant-col.ant-form-item-control .table-input {
            width: 100%;
        }
    }
}

:deep(.ap-invoice-block .ant-col.ant-form-item-label) {
    height: 31.5px;
}

:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -15px;
    font-size: 14px;
    line-height: 14px;
    min-height: 14px;
    display: none;
}

.icon-exclamation {
    margin: 0;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #aab9cb;
    margin-left: 5px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}

:deep(.ant-form-item-label [title='Comments']) {
    font-size: 16px;
    font-weight: 700;
}

.textarea-wrap {
    min-height: 125px;
    height: 125px;
    max-height: 96px;
    margin-bottom: 15px;
}

.meta-wrap {
    .title {
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 700;
    }

    .meta-lable {
        margin-bottom: 8px;
    }
}

.ap-invoice-amount-block {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 0 0;

    .ap-invoice-amount-block-left {
        width: 300px;
    }

    .ap-invoice-amount-block-right {
        width: 285px;
        min-width: 285px;

        // max-width: calc(100% - 305px);
        .amount-block {
            width: 100%;

            .title {
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                text-align: right;
            }

            .switch-wrap {
                margin-right: 8px;
            }

            .amount-item-wrap {
                padding: 20px 16px;
                width: 100%;
                background-color: #f5f7f9;
                border-radius: 8px;

                .amount-item {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0px;
                    }

                    .amount-lable {
                        width: calc(100% - 151px - 8px);
                        min-width: 70px;
                        text-align: right;

                        &.bold {
                            font-weight: 700;
                        }
                    }

                    .amount-input {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: #fff;
                        }
                    }

                    .amount-display-alert {
                        width: 151px;
                        max-width: calc(100% - 75px);

                        &.ant-input-number-disabled {
                            background-color: rgba(255, 0, 0, 0.2);
                            color: red;
                        }
                    }
                }
            }
        }
    }
}

.confirm-title-wrap {
    padding-top: 15px;
    padding-bottom: 5px;
    .confirm-title-icon {
        color: #faad14 !important;
        font-size: 21px;
        padding-left: 8px;
        padding-right: 8px;
    }

    .confirm-title-text {
        //  font-family: Calibri;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 24px;
        font-weight: 400;
    }
}

.confirmation-content-text {
    // font-family: Calibri;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 20px;
    font-weight: 400;
    padding-left: 64px;
    padding-top: 5px;
    // height: 75px;
}

.document-item-link {
    margin-right: 10px;
}

:deep(.ant-form-item-label [title='Original Document']) {
    font-size: 16px;
    font-weight: 700;
}

:deep(.ant-modal-body) {
    height: 50px;
}

:deep(.ant-modal-header) {
    padding: 0;
    .ant-modal-title {
        .ant-modal-title-detail {
            padding: 17px 24px;
            width: 100%;
            cursor: move;
            user-select: none;
        }
    }
}

:deep(.ant-modal-footer) {
    border-top: none;
}
</style>
