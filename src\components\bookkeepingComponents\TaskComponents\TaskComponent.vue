<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import moment from 'moment'
import {computed, onBeforeMount, onMounted, reactive, ref, watch} from 'vue'
import {message, type FormInstance} from 'ant-design-vue'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {PlusOutlined, QuestionCircleOutlined} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import {v4 as uuidv4} from 'uuid'

import * as _ from 'lodash'
import {UserCompany, UserInfo, LocalCurrency} from '@/lib/storage'

const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const localAdmin = 'kycadmin'
const localStatus = 'todo'
const readonlyMode = ref(false)
const i18n: Composer = i18nInstance.global
const formRef = ref<FormInstance>()
const getToday = () => {
    const today = moment().format('yyyy-MM-DD')
    return today
}
const form = ref<any>({
    name: '',
    company: `${userCompany[0].code} - ${userCompany[0].name}`,
    company_code: `${userCompany[0].code}`,
    company_name: `${userCompany[0].name}`,
    assign_to: '',
    due_date: '',
    status: localStatus,
    estimated_hour: null,
    actual_hour: null,
    tag: null,
    email: null,
    last: [],
    period_start: '',
    period_end: '',
    priority: null,
})
const formLoading = ref(false)
const companyOptions: any = ref([])
companyOptions.value = _.cloneDeep(_.sortBy(userCompany, 'id'))
const adminOptions = reactive([
    {
        value: 'kycadmin',
        label: 'kycadmin',
    },
    {
        value: '<EMAIL>',
        label: '<EMAIL>',
    },
    {
        value: '<EMAIL>',
        label: '<EMAIL>',
    },
    {
        value: '<EMAIL>',
        label: '<EMAIL>',
    },
])
const statusOptions = reactive([
    {
        value: 'todo',
        label: i18n.t('task.statusToDo'),
    },
    {
        value: 'doing',
        label: i18n.t('task.statusDoing'),
    },
    {
        value: 'done',
        label: i18n.t('task.statusDone'),
    },
])
const emit = defineEmits(['save', 'dismiss', 'post', 'reverse', 'saveDraft'])
const post = async () => {
    if (form.value.company) {
        form.value.company_code = form.value.company.substring(0, 4)
        form.value.company_name = form.value.company.substring(7)
    }
    if (form.value.last) {
        form.value.period_start = form.value.last[0]
        form.value.period_end = form.value.last[1]
    }
    // validate task name
    if (form.value.name == '') {
        message.error({
            content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('task.name')}`,
            duration: 6,
        })
        return
    }
    // validate company
    if (form.value.company_code == '' || form.value.company_name == '') {
        message.error({
            content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('task.companyName')}`,
            duration: 6,
        })
        return
    }
    // validate assign to
    if (form.value.assign_to == '') {
        message.error({
            content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('task.assignedTo')}`,
            duration: 6,
        })
        return
    }
    // validate due date
    if (form.value.due_date == '') {
        message.error({
            content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('task.dueDate')}`,
            duration: 6,
        })
        return
    }
    // validate status
    if (form.value.status == '') {
        message.error({
            content: `${i18n.t('bkCommonTag.msgRequireRule')} ${i18n.t('task.status')}`,
            duration: 6,
        })
        return
    }
    // // validate estimated hour
    // if (form.value.estimated_hour == '') {
    //     message.error({
    //         content: 'Estimated hour is required',
    //         duration: 6,
    //     })
    //     return
    // }
    // // validate actual hour
    // if (form.value.actual_hour == '') {
    //     message.error({
    //         content: 'Actual hour is required',
    //         duration: 6,
    //     })
    //     return
    // }
    // // validate tag
    // if (form.value.tag == '') {
    //     message.error({
    //         content: 'Tag is required',
    //         duration: 6,
    //     })
    //     return
    // }
    // // validate email
    // if (form.value.email == '') {
    //     message.error({
    //         content: 'Email is required',
    //         duration: 6,
    //     })
    //     return
    // }
    // // validate last
    // if (form.value.period_start == '' || form.value.period_end == '') {
    //     message.error({
    //         content: 'Last is required',
    //         duration: 6,
    //     })
    //     return
    // }
    // // validate priority
    // if (form.value.priority == '') {
    //     message.error({
    //         content: 'Priority is required',
    //         duration: 6,
    //     })
    //     return
    // }
    emit('post', form)
    cancel()
}
const cancel = () => {
    emit('dismiss')
}

// currency change回调
const updateSpot = async () => {
    if (localAdmin === form.value.currency) return
    // const baseCurrency = form.value.currency
    // const quoteCurrency = localCurrency
    // spot.value = await getSpot({baseCurrency, quoteCurrency, date: form.value.posting_date})
    // if (form.value.currency !== localCurrency) {
    // form.value.line_items.map((i: any) => {
    //     i.amount_tc_1 = +(i.amount_tc_1 * parseFloat(spot.value?.rate)).toFixed(2)
    //     i.amount_tc_2 = +(i.amount_tc_2 * parseFloat(spot.value?.rate)).toFixed(2)
    // })
    // }
}
const requireRule = (propName: any) => [
    {
        required: true,
        message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
        trigger: ['blur', 'change'],
    },
]

const requireGLAccount = (rule: any, value: string | undefined) => {
    const index = Number(rule.field.match(/\.(\S*)\./)[1])
    const item = form.value.line_items[index]

    if (!value && (item.amount_tc_1 || item.amount_tc_2)) {
        return Promise.reject()
    } else {
        return Promise.resolve()
    }
}
const optionalEmailRule = (rule: any, value: string) => {
    // 当值不存在时，返回 true，不触发任何验证
    if (!value) {
        return Promise.resolve()
    }

    // 当值存在时，应用邮箱验证规则
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i
    if (emailRegex.test(value)) {
        return Promise.resolve()
    }
    return Promise.reject()
}
const rules = reactive({
    name: [...requireRule(i18n.t('task.name'))],
    email: [{validator: optionalEmailRule, message: i18n.t('bkCommonTag.msgEmailRule'), trigger: ['blur', 'change']}],
    requireItem: [
        {
            required: true,
            message: 'field required',
            trigger: ['blur'],
        },
    ],
    requireItemTypeSelect: [
        {
            required: true,
            message: 'field required',
            validator: requireGLAccount,
            trigger: ['blur', 'change'],
        },
    ],
})
onBeforeMount(async () => {
    updateSpot()
})
onMounted(() => {
    ;(formRef.value as any).clearValidate()
})
</script>
<template>
    <div class="page-container-ap-invoice-form" id="scroll-box">
        <a-spin :spinning="formLoading">
            <a-form
                ref="formRef"
                :model="form"
                :rules="readonlyMode ? {} : rules"
                :layout="'vertical'"
                label-width="auto"
                label-position="top"
                class="form-box"
            >
                <div class="ap-invoice-block">
                    <a-row :gutter="24">
                        <a-col :span="12">
                            <!-- 第一行第一列输入框值 - task name -->
                            <a-form-item
                                :label="$t('task.name')"
                                name="name"
                                required
                                class="form-box-item_reference_no"
                            >
                                <template v-slot:label>
                                    {{ i18n.t('task.name') }}
                                    <a-tooltip placement="top">
                                        <template #title>
                                            <div>{{ i18n.t('gl.descriptionxxx') }}</div>
                                        </template>
                                        <question-circle-outlined class="el-icon-question" />
                                    </a-tooltip>
                                </template>
                                <a-input
                                    v-model:value="form.name"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    :disabled="readonlyMode"
                                >
                                </a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <!-- 第一行第二列输入框值 - company -->
                            <a-form-item :label="i18n.t('task.companyName')" class="form-box-item_currency" required>
                                <a-select
                                    mode="single"
                                    show-search
                                    style="width: 100%"
                                    :controls="false"
                                    v-model:value="form.company"
                                >
                                    <a-select-option
                                        v-for="item in companyOptions"
                                        :key="item.name + uuidv4()"
                                        :company_name="item.name"
                                        :value="item.code + ' - ' + item.name"
                                    >
                                        {{ item.code + ' - ' + item.name }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="24">
                        <a-col :span="8">
                            <!-- 第二行第一列输入框值 - assign to -->
                            <a-form-item
                                :label="i18n.t('task.assignedTo')"
                                class="form-box-item_currency"
                                name="assign_to"
                                required
                            >
                                <a-select
                                    :placeholder="i18n.t('workTimeManager.msgInput')"
                                    v-model:value="form.assign_to"
                                    style="width: 100%"
                                    :disabled="readonlyMode"
                                >
                                    <a-select-option
                                        style="width: 100%"
                                        v-for="item in adminOptions"
                                        :key="item.label"
                                        :value="item.value"
                                        clearable
                                        >{{ item.label }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第二行第二列输入框 - due date -->
                            <a-form-item
                                :label="i18n.t('task.dueDate')"
                                name="due_date"
                                class="form-box-item_posting_date"
                                required
                            >
                                <a-date-picker
                                    v-model:value="form.due_date"
                                    type="date"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :disabled="readonlyMode"
                                    :placeholder="i18n.t('task.dueDate')"
                                    style="width: 100%"
                                    clearable
                                    @change="updateSpot"
                                >
                                </a-date-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第二行第三列输入框值 - status -->
                            <a-form-item :label="i18n.t('task.status')" class="form-box-item_currency" required>
                                <a-select
                                    :placeholder="i18n.t('workTimeManager.msgInput')"
                                    v-model:value="form.status"
                                    style="width: 100%"
                                    :disabled="readonlyMode"
                                >
                                    <a-select-option
                                        style="width: 100%"
                                        v-for="item in statusOptions"
                                        :key="item.label"
                                        :value="item.value"
                                        clearable
                                        >{{ item.label }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="24">
                        <a-col :span="8">
                            <!-- 第三行第一列输入框值 - estimated hour -->
                            <a-form-item
                                :label="$t('task.estimatedHour')"
                                name="estimated_hour"
                                class="form-box-item_reference_no"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    v-model:value="form.estimated_hour"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    :disabled="readonlyMode"
                                    :precision="1"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第三行第二列输入框 - actual hour -->
                            <a-form-item
                                :label="i18n.t('task.actualHour')"
                                name="actual_hour"
                                class="form-box-item_posting_date"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    v-model:value="form.actual_hour"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    :disabled="readonlyMode"
                                    :precision="1"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第三行第三列输入框值 - tag -->
                            <a-form-item :label="$t('task.tag')" name="tag" class="form-box-item_reference_no">
                                <a-input
                                    v-model:value="form.tag"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    :disabled="readonlyMode"
                                >
                                </a-input>
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="24">
                        <a-col :span="8">
                            <!-- 第四行第一列输入框值 - email -->
                            <a-form-item
                                :label="$t('task.email')"
                                name="email"
                                class="form-box-item_reference_no"
                                :rules="rules['email']"
                            >
                                <a-input
                                    v-model:value="form.email"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    :disabled="readonlyMode"
                                >
                                </a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第四行第二列输入框 - last date -->
                            <a-form-item :label="i18n.t('task.last')" name="last" class="form-box-item_posting_date">
                                <a-range-picker
                                    v-model:value="form.last"
                                    format="YYYY-MM-DD"
                                    valueFormat="YYYY-MM-DD"
                                    :disabled="readonlyMode"
                                    :placeholder="['Start Time', 'End Time']"
                                    style="width: 100%"
                                    clearable
                                    @change="updateSpot"
                                >
                                </a-range-picker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            <!-- 第四行第三列输入框值 - priority -->
                            <a-form-item
                                :label="$t('task.priority')"
                                name="priority"
                                class="form-box-item_reference_no"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    v-model:value="form.priority"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    :disabled="readonlyMode"
                                    :precision="0"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </div>

                <div class="amount-block"></div>
                <div class="amount-block"></div>
            </a-form>
        </a-spin>

        <div class="ap-invoice-footer">
            <div class="divClass">
                <a-button v-show="!readonlyMode" shape="round" type="primary" @click="post" :loading="formLoading">
                    {{ i18n.t('commonTag.save') }}
                </a-button>

                <a-button v-show="!readonlyMode" shape="round" type="primary" @click="cancel" :loading="formLoading">
                    {{ i18n.t('commonTag.cancel') }}
                </a-button>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.ap-invoice-block {
    padding: 24px 0;
    border-bottom: 1px solid #e2e2ea;

    &:first-child {
        padding-top: 0;
    }

    .total-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 20px;
        margin-bottom: 10px;

        .total-item {
            display: flex;
            align-items: center;
            justify-content: flex-end;

            .totalCad,
            .amount {
                font-weight: bold;
            }
        }
    }
}

:deep(.ap-invoice-block .ant-col.ant-form-item-label) {
    height: 31.5px;
}

.difference_message {
    color: #ff0000;
}

.el-icon-question {
    margin: auto;
    position: relative;
    padding-bottom: 0px;
    font-size: 20px;
    color: #2ead2b;
    margin-left: 5px;
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}

.ap-invoice-footer {
    width: 100%;
    display: flex;
    margin-top: 20px;

    .aClass {
        text-align: left;
        width: 500px;
        line-height: 30px;
        justify-content: center;
        color: #004fc1;
        cursor: pointer;
    }

    .divClass {
        justify-content: flex-end;
        display: flex;
        width: 100%; //calc(100% - 200px);

        .cancel-button {
            border-color: #004fc1;
            color: #004fc1;
        }

        .reverse-button {
            background-color: #004fc1;
            color: #fff;
        }

        .reverse-button-disable {
            background-color: #f5f5f5;
            color: rgba(0, 0, 0, 0.25);
            border-color: #c3c7d4;
        }

        .ant-btn + .ant-btn {
            margin-left: 12px;
        }
    }
}

.total_box {
    display: flex;
    align-items: center;
    margin: 0px 10px;
}

.inputWidth_f_o {
    width: 200px;
}

.inputWidth_f_t {
    width: 272px;
}

:deep(.ant-table .ant-form-item-explain.ant-form-item-explain-connected) {
    position: absolute;
    bottom: -10px;
    font-size: 12px;
    line-height: 12px;
    min-height: 12px;
    display: none;
}
</style>
