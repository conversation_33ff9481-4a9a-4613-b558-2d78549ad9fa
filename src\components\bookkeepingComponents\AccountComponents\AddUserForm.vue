<!-- @format -->

<script lang="ts" setup>
import {computed, onMounted, reactive, ref} from 'vue'
import {useStore} from 'vuex'
import i18nInstance from '@/locales/i18n'
import type {Composer} from 'vue-i18n'
import {message, type FormInstance} from 'ant-design-vue'
import {UserCompany, UserInfo} from '@/lib/storage'
import dayjs from 'dayjs'

const i18n: Composer = i18nInstance.global
const store = useStore()
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const emits = defineEmits(['updateData', 'dismiss'])

const formRef = ref<FormInstance>()
const roleSelectDisabled = ref(false)
const form = ref({} as any)

const account = ref('')
const formLoading = ref(false)

const createAccountUser = (query: any) => store.dispatch('UserStore/addUser', query)

const role_permission_mapping = computed(() => store.getters.rolePermissionMapping)
const web_role_list = computed(() => {
    const init_list = store.getters['UserStore/web_roles']
    const inherits = role_permission_mapping.value.find((x: any) => x.role_id === userInfo.roles)?.inherit_roles || []
    return init_list.filter((x: any) => inherits.includes(x.key))
})
const user_category = computed(() => store.getters['UserStore/user_category'])

const save = async () => {
    if (await formRef.value?.validateFields()) {
        try {
            form.value.email = ''
            form.value.company_code = '0' // new user use 0 as placeholder
            form.value.product = 'BK'
            formLoading.value = true
            const response = await createAccountUser({...form.value, category: form.value.category.join()})
            if (response.status === 200 || response.status === 201) {
                message.success({
                    content: 'Success',
                    duration: 3,
                })
                emits('updateData')
                cancel()
            } else {
                // message.error({
                //     content: response.data.errors[0].message,
                //     duration: 5,
                // })
            }
        } catch (error: any) {
            console.log(error)
            // message.error({
            //     content: error.response.data.errors[0].message,
            //     duration: 5,
            // })
        } finally {
            formLoading.value = false
            form.value = {}
            formRef.value?.resetFields()
        }
    }
}
const cancel = () => {
    form.value = {}
    emits('dismiss')
}

const selectRole = (value: any, option: any) => {
    console.log('role selected, ', value, form.value.roles, option)
}

const selectCategory = (value: string[], option: any) => {
    if (value.length === 1 && value[0] === 'app') {
        form.value.roles = '9996'
        roleSelectDisabled.value = true
    } else {
        roleSelectDisabled.value = false
    }
}

const requireInputRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: 'blur',
        },
    ]
}
const requireSelectRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('commonTag.msgSelect') + ` ${propName}`,
            trigger: 'blur',
        },
    ]
}
const rules = reactive({
    account: requireInputRule(i18n.t('account.userId')),
    password: requireInputRule(i18n.t('account.password')),
    roles: requireSelectRule(i18n.t('account.role')),
    category: requireSelectRule(i18n.t('account.cate')),
})

onMounted(() => {
    form.value = {}
})
</script>

<template>
    <div class="account-desc-form-wrap">
        <a-form
            :layout="'vertical'"
            :model="form"
            :rules="rules"
            ref="formRef"
            autocomplete="off"
            class="account-desc-form"
            :loading="formLoading"
        >
            <a-form-item :label="$t('account.userId')" name="account" class="form-item">
                <a-input v-model:value="form.account" :placeholder="i18n.t('commonTag.msgInput')" />
            </a-form-item>

            <a-form-item :label="$t('account.password')" name="password" class="form-item">
                <a-input v-model:value="form.password" type="password" :placeholder="i18n.t('commonTag.msgInput')" />
            </a-form-item>
            <a-form-item :label="$t('account.cate')" name="category" class="form-item">
                <a-select
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="form.category"
                    mode="multiple"
                    :options="user_category"
                    @change="selectCategory"
                >
                </a-select>
            </a-form-item>
            <div style="width: 50%"></div>
            <a-form-item :label="$t('account.role')" name="roles" class="form-item">
                <a-select
                    :disabled="roleSelectDisabled"
                    :placeholder="i18n.t('commonTag.msgSelect')"
                    v-model:value="form.roles"
                    :options="web_role_list"
                    @change="selectRole"
                >
                </a-select>
            </a-form-item>
        </a-form>
        <a-divider class="footer-divider"></a-divider>

        <footer>
            <a-button shape="round" class="cancel-button" @click="cancel" size="small">
                {{ i18n.t('commonTag.cancel') }}
            </a-button>
            <a-button type="primary" :loading="formLoading" size="small" shape="round" @click="save"> submit </a-button>
        </footer>
    </div>
</template>

<style lang="scss" scoped>
.account-desc-form-wrap {
    .account-desc-form {
        padding: 20px 24px 60px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .form-item {
            width: 49%;
        }
    }
}
.footer-divider {
    margin-top: 0px;
    margin-bottom: 0px;
}
footer {
    padding: 12px 24px;
    text-align: right;
    .cancel-button {
        border-color: #004fc1;
        color: #004fc1;
    }
    .ant-btn {
        min-width: 65px;
    }
    .ant-btn + .ant-btn {
        margin-left: 8px;
    }
}
</style>
