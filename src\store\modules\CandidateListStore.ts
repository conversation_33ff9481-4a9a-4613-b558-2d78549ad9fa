/** @format */

import type {ActionContext} from 'vuex'
import service from '../../api/request'

const json2Form = (payload: {[s: string]: any}) => {
    const form = new FormData()
    Object.entries(payload).forEach(([k, v]) => {
        form.append(k, v)
    })
    return form
}

const state = () => ({
    candidates: [],
})

const mutations = {
    setCandidates(state: {candidates: any[]}, candidates: any) {
        state.candidates = [...candidates]
    },
}

const actions = {
    async fetchCandidates(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        const res = await service.post('/recruitment/candidates', json2Form(payload))
        ctx.commit('setCandidates', res.data.data.itemList)
        return res
    },
    async fetchCandidateInfo(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: {id: any}) {
        const res = await service.get(`/emp/detail/infoNew?id=${payload.id}`)
        return res
    },
    async confirmCandidate(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        const res = await service.post('/recruitment/confirmInduct', json2Form(payload))
        return res.data
    },
    async sendQRCode(ctx: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        return service.post('/recruitment/quickInduct', json2Form(payload))
    },
}

export default {actions, state, mutations}
