﻿{
    "login": {
        "title": "开始",
        "logIn": "登录",
        "logOut": "退出",
        "english": "英文",
        "chinese": "中文",
        "language": "语言",
        "username": "用户",
        "password": "密码",
        "msgInputUsername": "请输入用户",
        "msgInputPassword": "请输入密码",
        "msgInputAccount": "请输入帐号",
        "serverError": "服务器故障",
        "changePwd": "重置密码",
        "newpassword": "密码",
        "repassword": "重新输入密码",
        "gQr": "谷歌身份验证器",
        "pwdRule": "必须至少包括1个大写字母、1个小写字母、1个数字、1个特殊字符",
        "pwdRule1": "请输入不含空格的字符",
        "inconsistentPassword": "密码不一致",
        "repasswordInput": "请重新输入密码"
    },
    "mainPage": {
        "noLogin": "未登录"
    },
    "homepage": {
        "times1": "数字化薪资管理'",
        "times2": "管理员版本",
        "title": "与我们一起轻松地开始支付雇员，在线支付。",
        "info": "NT数字化薪资应用程序提供了一个从组织到薪资的全面覆盖，一个超自动化的过程使核心商业运作最大化你的人力资本价值。",
        "titleBk1": "基本操作",
        "titleBk2": "",
        "titleBk": "超自动化记账",
        "infoBk": "我们的财务解决方案是为北美小型企业而设的，无论你是小企业主还是自营职业者，我们都能使你的财务状况更有条理。因此，你可以做出更好的商业决策，专注于增长，并从管理和合规时间中减去压力。",
        "link": "了解更多"
    },
    "router": {
        "uploadInvoice": "大规模的过程",
        "woBillsInvoice": "手动预订",
        "invoiceFromPdf": "发票来自于PDF",
        "invoiceHistory": "发票历史",
        "accountReceivable": "应收",
        "fullInvoice": "预订",
        "bankReconciliation": "银行对账",
        "uploadStatement": "连接",
        "EStatementFromPdf": "来自CSV的电子报表",
        "main": "调和",
        "common": "设置",
        "customer": "联系",
        "taxInformation": "资料",
        "bankInformation": "银行信息",
        "accountDescription": "会计科目",
        "taxCalculation": "税表",
        "spotCurrency": "即时汇率",
        "exchangeRate": "汇率",
        "supplier": "供应商",
        "payStubs": "工资单",
        "history": "历史",
        "commonCompany": "公司",
        "commonAccount": "帐户",
        "gl": "总账",
        "glListing": "列表",
        "glEntry": "手动输入",
        "fy": "财年",
        "localCurrency": "本位币"
    },
    "commonTag": {
        "tip": "提示",
        "confirm": "确认",
        "cancel": "取消",
        "search": "搜索",
        "new": "新建",
        "save": "保存",
        "edit": "编辑",
        "delete": "删除",
        "action": "操作",
        "actions": "操作集",
        "view": "查看",
        "remark": "备注",
        "back": "返回",
        "close": "关闭",
        "serial": "序列号",
        "status": "状态",
        "submit": "提交",
        "send": "发送",
        "resend": "重新发送",
        "download": "下载",
        "sendEmail": "发送邮件",
        "columns": "列定制",
        "filter": "筛选",
        "msgInput": "请输入",
        "msgSelect": "请选择",
        "reverse": "冲销",
        "reverseAndRedo": "Reverse & Redo",
        "realize": "实现",
        "print": "打印",
        "wihtreverse": "Without reverse",
        "enable": "开启",
        "disable": "关闭"
    },
    "columns": {
        "modalTitle": "列定制",
        "title": "标题",
        "operation": "运作"
    },
    "bkApInvoice": {
        "to": " ",
        "readonly": "发票详情",
        "create": "创建发票",
        "edit": "发票编辑",
        "date": "日期:",
        "totalCol": "总计:",
        "minFee": "最低金额",
        "maxFee": "最高金额",
        "invoiceNo": "发票编号",
        "invoiceComment": "备注",
        "creator": "创建人",
        "createTime": "创建时间",
        "createDate": "创建日期",
        "issuerCol": "供应商",
        "invoiceType": "发票类型",
        "issuer": "供应商",
        "total": "总计",
        "type": "类型",
        "br": "对账",
        "status": "状态",
        "payMethod": "付款方式",
        "sapStatus": "SAP 状态",
        "sapNotSent": "未发送",
        "sapPending": "已暂停",
        "sapSending": "发送中",
        "sapSentSuccess": "发送成功",
        "sapSentFail": "发送失败",
        "sapReversing": "冲销中",
        "sapReverseSuccess": "冲销成功",
        "sapReverseFail": "冲销失败",
        "referenceNo": "参考信息",
        "dueDate": "交付日期",
        "postingDate": "过账日期",
        "balance": "余额",
        "operation": "操作",
        "checkNo": "支票号码",
        "checkPrintTime": "执行时间",
        "printStatus": "Print Status",
        "printStatus0": "Not Printed",
        "printStatus1": "Printed",
        "brStatus0": "未支付",
        "brStatus": "所有",
        "brStatus2": "未支付",
        "parked": "预制凭证",
        "posted": "已过账",
        "notPaid": "未付款",
        "pmntApproved": "支付已核准",
        "pmntExecuted": "支付已執行",
        "paid": "已付款",
        "partialPaid": "部分付款",
        "reversed": "已沖銷",
        "captured": "已扫描",
        "created": "已创建",
        "scan": "扫描",
        "fetch": "获取",
        "upload": "上传",
        "del": "删除",
        "level": "级别",
        "emailList": "审批人",
        "approvalStatus": "审批状态",
        "statusPending": "等待审批",
        "statusApproved": "审批通过",
        "statusRejected": "审批拒绝",
        "printApCheckWarning": "您的付款正在处理，请确认。",
        "issueNameEmpty": "请输入供应商名字"
    },
    "bkApUpload": {
        "fileName": "文件名称",
        "updateTime": "更新时间",
        "createTime": "创建时间",
        "creator": "创建人",
        "xmlStatus": "XML 状态",
        "payMethod": "付款方式",
        "comment": "备注",
        "ocrStatus": "OCR 状态",
        "scanned": "扫描",
        "nonScanned": "未扫描",
        "pending": "未扫描",
        "delFile": "删除",
        "edit": "编辑",
        "createFile": "创建发票",
        "analyzeFile": "OCR识别",
        "viewDetail": "详情",
        "downloadInvoice": "下载",
        "editComment":"编辑评论",
        "editCommentPlaceholder": "请输入评论"
    },
    "bkCustomer": {
        "company": "公司",
        "tel": "电话",
        "email": "电子邮箱",
        "receiver": "收件方",
        "receiver01": "收件方",
        "address": "地址",
        "address01": "地址",
        "officeStreet": "街道",
        "officeCity": "城市",
        "officeProvince": "省份",
        "officeCountry": "国家",
        "officePostalCode": "邮政编码",
        "expenseAccount": "账户",
        "operation": "操作",
        "createReceiverTitle": "创建客户",
        "createAllReceiverTitle": "创建商家",
        "accountType": "类型",
        "editReceiverTitle": "编辑联系人详细信息",
        "msgPhrSelect": "请选择",
        "officeAddress": "公司地址",
        "shippingAddress": "发货地址",
        "billingAddress": "账单地址",
        "general": "一般",
        "sameAsOffice": "与公司地址相同",
        "itemNo": "#",
        "category": "类别",
        "businessKey": "业务ID",
        "debitReceipt": "借方",
        "creditReceipt": "贷方",
        "save": "保存",
        "caterogyRule": "请选择类别",
        "coaJson": "低代码"
    },
    "bkAp": {
        "invoiceHeader": "发票抬头",
        "companyName": "供应商",
        "companyAddr": "地址",
        "companyTel": "电话",
        "companyEmail": "电子邮件",
        "companyGst": "GST No.",
        "companyQst": "QST No.",
        "companyGstHst": "GST/HST",
        "companyPst": "PST (QST/--)",
        "invoiceNo": "发票编号",
        "referenceNo": "参考信息",
        "purpose": "目的",
        "purposeStandard": "标准",
        "purposeProforma": "形式发票",
        "purposeCreditMemo": "信用备忘录",
        "purposeSubsequentCreditMemo": "后续信用备忘录",
        "poPlaceholder": "首先选择目的",
        "purchaseOrder": "采购订单",
        "currency": "货币",
        "date": "创建日期",
        "dueDate": "交付日期",
        "fixedDate": "固定日期",
        "afterDays": "延后天数",
        "customerInfo": "客户信息",
        "billToCompany": "公司名称",
        "billTo": "客户",
        "billToReceiver": "收件方",
        "billingStreet": "街道",
        "billToCity": "城市",
        "billToProvince": "省份",
        "billToZip": "邮编",
        "billToTel": "电话",
        "billToEmail": "电子邮箱",
        "shipToSameAddr": "寄到同一地址",
        "shipToCompany": "公司",
        "shipToReceiver": "收件方",
        "shipToStreet": "街道",
        "shipToCity": "城市",
        "shipToProvince": "省份",
        "shipToZip": "邮编",
        "shipToTel": "电话",
        "shipToEmail": "电子邮件",
        "itemsDetail": "详情",
        "itemNo": "#",
        "modelNumber": "产品/服务",
        "description": "描述",
        "payMethod": "付款方式",
        "addPayMethod": "增加付款方式",
        "qty": "数量",
        "unitPrice": "单位价格",
        "total": "金额",
        "type": "类型",
        "bankAccount": "银行账户",
        "trevenueAccount": "TRevenue账户",
        "accountingCategory": "会计科目",
        "amountAndTax": "金额和税收",
        "amount": "金额",
        "amountRule": "金额",
        "shipping": "运送",
        "discount": "折扣",
        "totalTaxable": "应税总额",
        "tps": "GST/HST",
        "tvq": "QST",
        "tvp": "PST",
        "mxDiscount": "折扣",
        "mxIsr": "所得税预提",
        "mxIva": "增值税预提",
        "totalTax": "税金",
        "totalCad": "总额",
        "deposit": "押金",
        "balance": "结余",
        "accountAndType": "账户和类型",
        "invoiceComment": "备注",
        "createArInvoice": "创建发票",
        "viewInvoice": "查看发票",
        "preview": "预览",
        "postingDate": "凭证日期",
        "apIntegrationPostingDate": "过账日期",
        "create": "保存",
        "totalFee": "总计",
        "msgReferenceNoExisted": "参考号已经存在!",
        "msgReferenceCheckFail": "参考号重复检查失败。请再试一次!",
        "from": "从",
        "addItemBtnTxt": "新建",
        "addItemBtnTxtInvoice": "新建发票审批人",
        "addItemBtnTxtPayment": "新建付款审批人",
        "paymentDelayDate": "付款日期",
        "msgCreditMemoInvoiceBeNegative": "Credit Memo 类型的发票金额必须为负数。",
        "paymentDelaySelect": "选择支付期限",
        "originalDocument": "原始文件",
        "costObject": "成本对象",
        "taxCode":"税码",
        "assigmentSapCostObject": "SAP 成本对象",
        "assigmentGlAccount": "总帐科目",
        "assigmentWbs": "WBS",
        "assigmentCostCenter": "成本中心",
        "assigmentInternalOrder": "Internal Order",
        "assigmentProfitCenter": "利润中心",
        "paymentExportColumnOperation": "OPERATION",
        "paymentExportColumnIssuerName": "ALIAS / RECIPIENT",
        "paymentExportColumnIsserId": "DESTINATION ACCOUNT",
        "paymentExportColumnImport": "IMPORT",
        "paymentExportColumnReferenceNo": "NUMERICAL REFERENCE",
        "paymentExportColumnInvoiceComments": "REFERENCE / CONCEPT",
        "paymentExportColumnReference": "REFERENCE",
        "paymentExportColumnInvoiceCurrency": "DIVIDE",
        "paymentExportColumnIva": "IVA",
        "paymentExportColumnDestinationRfc": "RFC DESTINATION",
        "paymentExportColumnBankAccount": "ORIGIN ACCOUNT"
    },
    "bkCommonTag": {
        "sapTip": "Sychronizing with SAP",
        "confirmation": "确认",
        "msgDeleteConfirm": "数据将被删除!",
        "msgRequireRule": "请输入",
        "msgLengthRule": "长度应该为",
        "msgEmailRule": "请输入正确的邮箱",
        "msgSelectRule": "请选择",
        "msgDeleteSelectConfirm": "所选的 PDF 文件将被删除!"
    },
    "bkArInvoice": {
        "to": " ",
        "readonly": "查看发票详情",
        "create": "制作发票",
        "date": "日期:",
        "totalCol": "共计:",
        "minFee": "最低费用",
        "maxFee": "最高费用",
        "billingType": "发票类型",
        "billingTypeInvoice": "标准发票",
        "billingTypeCreditNote": "信用备忘录",
        "billingTypeDebitMemo": "借项通知单",
        "invoiceNo": "发票号",
        "invoiceComment": "备注",
        "creator": "创建人",
        "createTime": "创建时间",
        "createDate": "创建日期",
        "issuerCol": "客户",
        "issuer": "客户",
        "total": "总计",
        "type": "类型",
        "br": "状态",
        "referenceNo": "参考信息",
        "dueDate": "交付日期",
        "postingDate": "过账日期",
        "balance": "结余",
        "operation": "操作",
        "payerPayee": "付款方/收款方",
        "brStatus0": "未对账",
        "brStatus1": "部分对账",
        "brStatus2": "已对账",
        "brStatus3": "全部",
        "sapStatus": "SAP状态",
        "search": "搜索客户名",
        "billing": "账单",
        "bill2Customer": "客户账单",
        "pb": "定期账单",
        "billingNumber": "发票编号",
        "referenceNumber": "参考信息编号",
        "currency": "币种",
        "paymentDue": "付款截止",
        "postDate": "发布日期",
        "billingDate": "帐单日期",
        "originalBillingNumber": "原始发票号码",
        "uuid": "UUID"
    },
    "workTimeManager": {
        "projectName": "项目名称: ",
        "msgInput": "请输入",
        "applicant": "雇员: ",
        "applicationDate": "工作日期: ",
        "approvalDate": "审批日期: ",
        "status": "状态: ",
        "memberNature": "人事性质: ",
        "company": "公司: ",
        "export": "导出",
        "workingHourCode": "工时订单编号",
        "applicantName": "申请人姓名",
        "applicantPhone": "报告人的手机号码",
        "consultantType": "顾问类型",
        "projectNameIntern": "项目名称",
        "wbs": "WBS",
        "whMonths": "每月工作时数",
        "totalWHPerDay": "总工时/天",
        "preTotalIncome": "估计总收入",
        "applicationTime": "工作时数",
        "approvalTime": "处理时间",
        "emailApprover": "审批人",
        "approvalStatus": "审批状态",
        "passed": "通过",
        "toBeSubmitted": "要提交",
        "toBeApproved": "待定",
        "failed": "未能通过",
        "abandoned": "无效",
        "canceled": "无效",
        "msgCancelReimburseInfo1": "被废止后，时间报告表将变得无效，系统将自动发送一封无效通知邮件到",
        "msgCancelReimburseInfo2": "和负责人的批准。该行动是不可逆转的，请确认继续?",
        "cancelReimburse": "无效时间报告表",
        "msgCancelSuccess": "报废成功!"
    },
    "bkAr": {
        "invoiceHeader": "发票抬头",
        "companyName": "收件人为",
        "companyAddr": "地址",
        "companyTel": "电话",
        "companyEmail": "电子邮箱",
        "companyGst": "GST No.",
        "companyQst": "QST No.",
        "companyGstHst": "GST/HST",
        "companyPst": "PST (QST/--)",
        "invoiceNo": "发票编号.",
        "currency": "货币",
        "date": "创建日期",
        "dueDate": "交付日期",
        "fixedDate": "固定日期",
        "afterDays": "到期后日",
        "customerInfo": "客户资料",
        "billToCompany": "公司",
        "billToReceiver": "收票方",
        "billingStreet": "街道",
        "billToCity": "城市",
        "billToProvince": "省份",
        "billToZip": "邮编",
        "billToTel": "电话",
        "billToEmail": "电子邮箱",
        "shipToSameAddr": "寄到同一地址",
        "shipToCompany": "公司",
        "shipToReceiver": "收件方",
        "shipToStreet": "街道",
        "shipToCity": "城市",
        "shipToProvince": "省份",
        "shipToZip": "邮编",
        "shipToTel": "电话",
        "shipToEmail": "电子邮件",
        "itemsDetail": "详情",
        "itemNo": "#",
        "modelNumber": "产品/服务",
        "to": " ",
        "description": "描述",
        "accountingCategory": "会计类别",
        "payMethod": "付款方式",
        "qty": "数量",
        "unitPrice": "单价",
        "total": "金额",
        "type": "类型",
        "bankAccount": "银行账户",
        "amountAndTax": "金额",
        "amount": "金额",
        "amountRule": "数量",
        "shipping": "运送",
        "discount": "折扣",
        "totalTaxable": "应税总额",
        "tps": "GST/HST",
        "tvq": "QST",
        "tvp": "PST",
        "totalTax": "税金",
        "totalCad": "总计",
        "deposit": "押金",
        "balance": "结余",
        "accountAndType": "账户和类型",
        "invoiceComment": "备注",
        "createArInvoice": "创建发票",
        "viewInvoice": "查看",
        "preview": "预览",
        "create": "保存",
        "postingDate": "过账日期",
        "totalFee": "总计",
        "msgReferenceNoExisted": "参考号已经存在!",
        "msgReferenceCheckFail": "参考号重复检查失败。请再试一次!",
        "from": "来自于",
        "addItemBtnTxt": "添加",
        "billingDoc": "Billing Doc",
        "referenceNo": "参考号"
    },
    "esUpload": {
        "bankAccount": "银行账户",
        "bankMonth": "账单月份",
        "buttonSubmited": "已提交",
        "buttonEdit": "编辑"
    },
    "bankInfo": {
        "bankName": "银行",
        "bankAccount": "银行账户",
        "accountType": "银行类型",
        "currency": "货币",
        "coa": "CoA",
        "operation": "操作",
        "createBankTitle": "创建",
        "editBankTitle": "编辑",
        "msgPhrSelect": "请选择",
        "authorizaiton": "授权",
        "fetchFrom": "起始日期"
    },
    "esMain": {
        "date ": "日期",
        "description": "描述",
        "inOutType": "输入/输出",
        "amount": "数量",
        "bankAccount": "银行账户",
        "statementPeriod": "账期",
        "cashIn": "现金进账",
        "cashOut": "提取现金",
        "createCashStatementTitle": "创建",
        "editCashStatementTitle": "编辑",
        "cashStatement": "现金报表",
        "withdrawal": "出账",
        "withdraw": "提取",
        "deposit": "入账",
        "postingDate": "过账日期",
        "reference": "参考信息",
        "payerPayee": "付款方/收款方",
        "debit": "存款",
        "debitManu": "借方",
        "credit": "提款",
        "creditManu": "贷方",
        "sendSapStatus": "SAP 状态",
        "viewInvoice": "查看",
        "invoiceDetail": "发票详情",
        "balance": "结余",
        "chargeFee": "手续费用",
        "chargeCoa": "手续费CoA",
        "reasonMsg": "原因代码",
        "currency": "货币",
        "brType": "类型",
        "transactionDate": "交易日期",
        "delWarn": "删除后将无法恢复。",
        "reconcileGL": "Reconcile G/L",
        "autoReconcile": "自动对账",
        "autoReconcileOn": "自动对账开启",
        "autoReconcileOff": "自动对账关闭"
    },
    "chartOfAccount": {
        "coa": "CoA",
        "btnLoadCoa": "加载",
        "btnReloadCoa": "关闭",
        "fieldCode": "编码",
        "account": "帐户",
        "accountDes": "描述",
        "alias": "别名",
        "operation": "操作",
        "fullCoa": "CoA列表",
        "createCoaTitle": "创建",
        "editCoaTitle": "编辑",
        "msgCoaDelete01": "账户与描述: ",
        "msgCoaDelete02": " 将被删除!",
        "msgCoaPause01": "账户与描述: ",
        "msgCoaPause02": " 将被停用!",
        "msgCoaActive01": "账户与描述: ",
        "msgCoaActive02": " 将被重新启用!",
        "yes": "是",
        "no": "否",
        "groupName": "组名"
    },
    "taxCalculation": {
        "msgWarning": "请确认汇率信息",
        "provinceName": "省份名称",
        "provinceCode": "省份编码",
        "applicableSalesTax": "适用的销售税",
        "total": "总计"
    },
    "spotCurrency": {
        "msgWarning": "银行发布的汇率。",
        "date": "日期",
        "currency": "货币（美元/加元)",
        "operation": "操作",
        "createTitle": "创建",
        "editTitle": "编辑兑换率",
        "selectTime": "请选择日期",
        "msgInputRate": "请输入汇率"
    },
    "connectivity": {
        "sourceHolder": "请输入",
        "source": "来自于",
        "username": "帐号",
        "password": "密码",
        "startDate": "开始日期",
        "endDate": "截止日期",
        "powerAutomate": "功能自动化",
        "server": "服务器",
        "email": "电子邮箱",
        "emailHost": "邮件主机",
        "type": "类型",
        "disk_secret": "公钥",
        "disk_app_id": "应用ID",
        "disk_key": "私钥",
        "application_number": "申请编号",
        "instance_number": "实例编号",
        "userId": "用户ID",
        "version": "版本",
        "receivable_integration": "应收集成",
        "payable_integration": "应付集成",
        "payment_integration":"付款执行",
        "reconciliation_integration": "对账集成",
        "fts_integration": "FTS集成",
        "sap_instance": "实例",
        "sap_general": "General",
        "sap_setting": "Setting",
        "payable_approval_procedure": "发票审批流程",
        "payment_approval_procedure": "支付审批流程"
    },
    "gl": {
        "search": "搜索描述/凭证号",
        "glNo": "#",
        "narration": "描述",
        "module": "模块",
        "journalEntry": "凭证号",
        "createDate": "创建日期",
        "total": "总计",
        "totalDebit": "借方",
        "totalCredit": "贷方",
        "to": " ",
        "readonly": "只读",
        "edit": "编辑",
        "create": "创建",
        "descriptionxxx": "描述,xxx",
        "post": "提交",
        "date": "日期:",
        "totalCol": "总计:",
        "minFee": "最低金额",
        "maxFee": "最高金额",
        "operation": "操作",
        "postingDate": "过账日期",
        "draftDate": "日期",
        "currency": "货币",
        "itemNo": "#",
        "description": "描述",
        "debit": "借",
        "credit": "贷",
        "saveDraft": "保存草稿",
        "glAccount": "会计科目",
        "createStartDate": "开始",
        "createEndDate": "结束",
        "status": "状态",
        "sapStatus": "SAP 状态",
        "editGl": "编辑",
        "delGl": "删除",
        "viewGl": "查看",
        "draft": "草稿",
        "posted": "提交",
        "failure ": "失败",
        "msgTotalDebitCannotNull": "借贷总额不能为空，请再次检查",
        "msgTotalCreditCannotNull": "信贷总额不能为空，请再次检查",
        "msgNotMatch": "借贷总额与信贷总额不一致，请再次检查",
        "msgNumberLimited": "输入的数字范围限制在0-13位",
        "msgGlDelete": "该数据将被删除！",
        "transactionCurrency": "当地货币 - ",
        "status0": "草稿",
        "status1": "已提交",
        "status2": "已冲销",
        "format": "格式错误",
        "payerAndPayee": "供应商"
    },
    "glEntry": {
        "totalDebit": "借方总额",
        "totalCredit": "贷方总额",
        "msgAtLeastOne": "至少添加一行信息",
        "transactionCurrency": "当地货币 - "
    },
    "bkSupplier": {
        "company": "公司",
        "tel": "电话",
        "email": "电子邮箱",
        "receiver": "供应商",
        "receiver01": "收件方",
        "supplierAddr": "联络人",
        "address": "地址",
        "address01": "地址",
        "street": "街道",
        "city": "城市",
        "province": "省份",
        "country": "国家",
        "postalCode": "邮编",
        "expenseAccount": "支出账户",
        "operation": "操作",
        "createReceiverTitle": "创建",
        "editReceiverTitle": "编辑",
        "msgPhrSelect": "请选择",
        "sameAsOffice": "与公司地址相同"
    },
    "taxInfo": {
        "companyLogo": "公司Logo",
        "companyName": "名称",
        "companyAddress": "地址",
        "companyEmail": "电子邮箱",
        "companyPhone": "电话",
        "gstNo": "GST / HST No.",
        "qstNo": "QST / PST No.",
        "limit": "注意：只能上传jpg/png文件，不超过2M。",
        "editLogoBtn": "编辑",
        "rmLogoBtn": "删除"
    },
    "account": {
        "userId": "帐户",
        "password": "密码",
        "cate": "类型",
        "role": "角色",
        "add": "添加用户"
    },
    "taxRates": {
        "countryNotSet": "请设置供应商（客户）公司地址所在国家和省份"
    },
    "ApComponents": {
        "notpaid": "非现金",
        "bank": "银行借记账户",
        "credit":"信用卡",
        "cashpaid": "现金",
        "check": "支票",
        "confirm": "确认冲销日期",
        "confirm2": "此操作也会冲销对帐。",
        "confirm3": "请先撤销银行对帐",
        "referenceError": "重复的参考编号",
        "contactFirstUsing": "新商家需要维护银行信息。",
        "atleast": "请添加行项目",
        "mustone": "请添加行项目",
        "lackNetAmount": "发票金额为空",
        "notEqual": "金额不等",
        "success": "成功",
        "referenceNo": "参考编号。",
        "spotCurrency": "{date}汇率是{rate}。",
        "contactAdmin": "请联系管理员",
        "NewBP": "新建",
        "subtotal": "税金",
        "taxableSubtotal": "应税金额",
        "total": "总额",
        "difference": "差额",
        "drag": "将文件拖到这里，或点击上传",
        "uploading": "上传文件",
        "sizeExceed": "注意：每个文件的大小限制为10Mb。",
        "MEAT": "主题字段",
        "oriDoc": "原始文件",
        "download": "下载",
        "auto": "自动模式",
        "exempt": "免税",
        "amount": "总金额",
        "netAmount": "金额",
        "GST": "GST/HST",
        "QST": "QST",
        "PST": "PST",
        "search": "搜索供应商",
        "pay": "支付",
        "uploadConfirm": "原始文件将会被新文件覆盖！",
        "wbs": "WBS",
        "costCenter": "成本中心",
        "costObject": "成本对象",
        "internalOrder": "内部订单",
        "profitCenter": "利润中心",
        "payableGL": "应付总帐",
        "testGetPgoTokenSuccess": "获取Pgo Token成功",
        "testGetPgoTokenFail": "获取Pgo Token失败"
    },
    "ArComponents": {
        "inavailable": "发票文件不可用。",
        "notExist": "客户不存在，想添加新客户吗？",
        "postingDt": "发布日期",
        "billTo": "发票到",
        "shipTo": "发货至",
        "netAmount": "金额",
        "shipping": "运费",
        "discount": "折扣",
        "GST": "GST/HST",
        "QST": "QST",
        "PST": "PST",
        "USD": "合计美元",
        "CAD": "总共加元",
        "deposit": "存款",
        "balance": "余额",
        "JE": "日记账目",
        "SAP": "SAP Document",
        "file": "发票文件",
        "download": "下载",
        "cancel": "取消",
        "convert": "使用现金支付",
        "confirmConvert": "Original invoice will be removed and cannot be recovered."
    },
    "PeriodicalBooking": {
        "notFound": "找不到 DUMMY!"
    },
    "EstatementTable": {
        "selectBank": "请选择银行",
        "required": "银行账户字段需要编号。",
        "inovice": "发票",
        "fetch": "获取",
        "upload": "上传",
        "acceptAll": "接受所有",
        "autoOn": "自动对账是开启的。点击它来接受对账。",
        "autoOff": "自动调节是关闭的。点击它启用自动对账。",
        "reject": "点击它拒绝推荐的自动对账，并导航到手动对账页",
        "RP": "RP [采购]",
        "RS": "RS [销售］",
        "PR": "PR [采购退货］",
        "SR": "SR [销售退款］",
        "FT": "FT [转账］",
        "FX": "FX [外币转账］",
        "PY": "PY [发薪]",
        "BC": "EE [Express Entry]",
        "SP": "SP [Stripe]",
        "PP": "PP [Prepayment]",
        "BCMessage": "Invoice will be created and reconciled, please select vendor (or customer) and G/L account.",
        "GLMessage": "Please choose Type and Accounting Category.",
        "integrationEEbrTitle": "Express Entry",
        "account": "银行账户",
        "cancel": "取消",
        "OK": "确定",
        "uploadES": "上传对账单",
        "reconcileAll": "全部对账",
        "payoutIdMsgInput": "Please enter payoutId",
        "confirm": "确认",
        "updateChargeFee": "手续费"
    },
    "RcInfoTable": {
        "account": "银行账户",
        "date": "付款日期",
        "desc": "说明",
        "debit": "借方",
        "credit": "贷方",
        "balance": "帐户余额",
        "invoiceNo": "发票号码",
        "comments": "备注",
        "paymentDt": "付款日期",
        "fee": "费用总额",
        "amount": "金额",
        "brDoc": "付款文件"
    },
    "CustomerForm": {
        "USA": "美国邮政编码： 12345或12345-1234",
        "CA": "加拿大邮政编码： H1H 2H2"
    },
    "PyComponents": {
        "total": "总计"
    },
    "FileUploadResult": {
        "failed": "文件上传失败。请再试一次。"
    },
    "UploadFileComp": {
        "notPaid": "非现金",
        "cash": "现金",
        "reconcile": "对账",
        "uploadLimit": "上传文件限制：",
        "select": "请选择文件",
        "perfileLimit": "每个文件的大小限制：",
        "uploadFile": "请上传{type}文件",
        "success": "上传成功",
        "error_two_files": "需要同时上传两个文件, 且包括一个xml文件",
        "retry": "上传错误[{err}]。请重试",
        "selectBtn": "选择",
        "type": "类型",
        "note": "注意：单个文件不能超过：{fileSize}Mb。 每次上传不超过{fileLimit}个文件。"
    },
    "mainLayout": {
        "setFY": "设置货币和财政年度",
        "createBP": "创建",
        "createChart": "创建",
        "fillin": "期初余额",
        "careteBank": "创建",
        "setup": "向导"
    },
    "userGuide": {
        "setFY": "设置货币和财政年度",
        "createBP": "创建",
        "createChart": "创建",
        "fillin": "期初余额",
        "careteBank": "创建"
    },
    "userPage": {
        "selectUser": "必须选择一个用户",
        "success": "成功",
        "error": "错误",
        "companyWithDesc": "公司描述",
        "companyCd": "公司代码",
        "companyNm": "公司名称",
        "AddAll": "添加所有公司",
        "cdnm": "公司代码/公司名"
    },
    "ApInvoiceFormPdf": {
        "invoice": "发票文件",
        "fetch": "获取",
        "notPaid": "未支付",
        "partialPaid": "部分支付",
        "paid": "已支付",
        "reversed": "冲销"
    },
    "ApUploadInvoice": {
        "upload": "上传发票文件",
        "OCR": "OCR识别",
        "file": "选择了文件：",
        "fillError": "发票文件不可用。",
        "analyzing": "正在识别（通过{type}）...",
        "error": "错误",
        "confirm": "确认"
    },
    "ArInvoiceHistory": {
        "invoice": "发票文件不可用",
        "Send Invoice": "发送发票"
    },
    "ReconciliationDetails": {
        "title": "Bank Reconciliation Detail",
        "invoiceNo": "发票编号",
        "select": "请选择",
        "search": "搜索、",
        "totals": "总金额 ${amt}, 选择金额 ${amt2}, 差额 ${amt3}。",
        "reconcile": "对账",
        "manual": "手动对账",
        "manualIntegration": "手动对账 for Integration",
        "payerAndPayee": "搜索Payer/Payee"
    },
    "AccountDescription": {
        "importAll": "你想导入所选组的所有CoA吗？",
        "add": "你想添加一个新的子类别吗？",
        "primary": "初级",
        "create": "请创建会计科目表。",
        "step1": "第一步：输入信息",
        "next": "下一步",
        "step2": "第2步：点击按钮",
        "step3": "第3步：点击选择链接",
        "got": "知道了",
        "importGroup": "导入",
        "pause": "暂停",
        "active": "激活",
        "copy": "复制",
        "cdnm": "账户代码或账户名"
    },
    "bankInformation": {
        "BMO": "满地可银行",
        "CIBC": "加拿大帝国商业银行",
        "NBC": "加拿大国家银行",
        "RBC": "加拿大皇家银行",
        "Desjardins": "加鼎银行",
        "TDB": "多伦多道明银行",
        "JPCB": "摩根大通银行",
        "FB": "弗里蒙特银行",
        "SEB": "东南银行",
        "EWB": "华美银行",
        "CITI": "花旗银行",
        "BOC": "中国银行",
        "CMB": "招商银行",
        "ASPIRE": "渴望银行",
        "ICBC": "中国工商银行",
        "BONJ": "南京银行",
        "SANTANDER": "桑坦德銀行",
        "BASEINET": "BASEinet银行",
        "MONEX": "Monex 银行",
        "BBVA": "BBVA 银行",
        "email": "设置电子邮件。",
        "create": "请创建银行。"
    },
    "ContactCustomer": {
        "create": "请创建。",
        "got": "知道了"
    },
    "SettingConnectivities": {
        "importBilling": "导入",
        "import": "导入",
        "save": "保存",
        "cancel": "取消",
        "pass": "邮箱密码",
        "channel": "全渠道集成",
        "SMTP": "邮件服务器",
        "Netdist": "网盘",
        "approvers": "审批人",
        "lowCode": "低代码",
        "pgoPlusSetting": "Pgo Plus 设置"
    },
    "TaxInformation": {
        "removed": "成功删除",
        "error": "上传错误",
        "recorrrect": "请输入正确的`{msg}`，例如：{desc}。",
        "phoneNo": "请输入正确的电话号码。例如：**********",
        "FY": "请设置货币和财政年度。 "
    },
    "reports": {
        "balanceSheet": "资产负债表",
        "trialBalance": "试算平衡表",
        "cashFlowStatement": "现金流量表",
        "purchaseReport": "采购报告",
        "incomeStatement": "损益表",
        "salesReport": "销售报告",
        "apReport": "应付账款报告",
        "arReport": "应收账款报告",
        "mxElectronicAccountingCoaReport": "电子会计 — 会计科目表",
        "mxElectronicAccountingTrialBalanceReport": "电子会计-试算平衡表",
        "mxInformativeDeclarationReport": "与第三方交易信息申报表 (DIOT)",
        "mxValueAddedTaxReport": "增值税",
        "mxColumnSeqNo": "Seq. No.",
        "mxColumnGlAccountDetails": "G/L Account Details",
        "mxColumnGlAccount": "G/L Account",
        "mxColumnStartingBalanceAmount": "Starting Balance Amount",
        "mxColumnDebitAmount": "Debit Amount",
        "mxColumnCreditAmount": "Credit Amount",
        "mxColumnEndingBalanceAmount": "Ending Balance Amount",
        "mxColumnSatCode": "SAT Code",
        "mxColumnGlAccountDescription": "G/L Account Description",
        "mxColumnLevel": "Level",
        "mxColumnNatureOfAccount": "Nature Of Account",
        "mxDiotColumn1": "Third party type",
        "mxDiotColumn2": "Tipo de operación",
        "mxDiotColumn3": "RFC",
        "mxDiotColumn4": "Tax identification number",
        "mxDiotColumn5": "Foreigner's name",
        "mxDiotColumn6": "Country or jurisdiction of tax residence",
        "mxDiotColumn7": "Specify place of tax jurisdiction",
        "mxDiotColumn8": "Total value of paid events or activities / Paid events or activities in the northern border region",
        "mxDiotColumn9": "Refunds, discounts and bonuses / Paid events or activities in the northern border region",
        "mxDiotColumn10": "Total value of paid events or activities / Paid events or activities in the southern border region",
        "mxDiotColumn11": "Refunds, discounts and bonuses / Paid events or activities in the southern border region",
        "mxDiotColumn12": "Total value of paid events or activities / Total events or activities paid at the 16% VAT rate",
        "mxDiotColumn13": "Refunds, discounts and bonuses / Total events or activities paid at the rate of 16% VAT",
        "mxDiotColumn14": "Total value of acts or activities paid / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn15": "Refunds, discounts and bonuses / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn16": "Total value of acts or activities paid / Acts or activities paid in the importation of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn17": "Refunds, discounts and bonuses / Acts or activities paid for the import of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn18": "Exclusively for taxed activities / Acts or activities paid in the northern border region",
        "mxDiotColumn19": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region",
        "mxDiotColumn20": "Exclusively for taxed activities / Acts or activities paid in the southern border region",
        "mxDiotColumn21": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region",
        "mxDiotColumn22": "Exclusively from taxable activities / Total acts or activities paid at the 16% VAT rate",
        "mxDiotColumn23": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate",
        "mxDiotColumn24": "Exclusively for taxed activities / Acts or activities paid for in the importation by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn25": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn26": "Exclusively for taxed activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn27": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn28": "Associated with activities for which a proportion was applied / Acts or activities paid in the northern border region",
        "mxDiotColumn29": "Associated with activities that do not meet requirements / Paid events or activities in the northern border region",
        "mxDiotColumn30": "Associated with exempt activities / Paid acts or activities in the northern border region",
        "mxDiotColumn31": "Associated with non-object activities / Paid acts or activities in the northern border region",
        "mxDiotColumn32": "Associated with activities for which a proportion was applied / Acts or activities paid in the southern border region",
        "mxDiotColumn33": "Associated with activities that do not meet requirements / Paid events or activities in the southern border region",
        "mxDiotColumn34": "Associated with exempt activities / Paid events or activities in the southern border region",
        "mxDiotColumn35": "Associated with non-object activities / Paid acts or activities in the southern border region",
        "mxDiotColumn36": "Associated with activities for which a proportion was applied / Total acts or activities paid at the 16% VAT rate",
        "mxDiotColumn37": "Associated with activities that do not meet requirements / Total acts or activities paid at the 16% VAT rate",
        "mxDiotColumn38": "Associated with exempt activities / Total acts or activities paid at the 16% VAT rate",
        "mxDiotColumn39": "Associated with non-object activities / Total acts or activities paid at the 16% VAT rate",
        "mxDiotColumn40": "Associated with activities for which a proportion was applied / Acts or activities paid for the importation by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn41": "Associated with activities that do not comply with requirements / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn42": "Associated with exempt activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn43": "Associated with non-object activities / Acts or activities paid for in the import by customs of tangible goods at the rate of 16% VAT",
        "mxDiotColumn44": "Associated with activities for which a proportion was applied / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn45": "Associated with activities that do not comply with requirements / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn46": "Associated with exempt activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn47": "Associated with non-object activities / Acts or activities paid for in the importation of intangible goods and services at the rate of 16% VAT",
        "mxDiotColumn48": "VAT withheld by the taxpayer",
        "mxDiotColumn49": "Acts or activities paid for in the importation of goods and services for which VAT is not paid (Exempt)",
        "mxDiotColumn50": "Paid acts or activities for which VAT will not be paid (Exempt)",
        "mxDiotColumn51": "Other acts or activities paid at the 0% VAT rate",
        "mxDiotColumn52": "Acts or activities not subject to VAT carried out in national territory",
        "mxDiotColumn53": "Acts or activities not subject to VAT due to not having an establishment in national territory",
        "mxDiotColumn54": "I declare that fiscal effects were given to the receipts that support the operations carried out with the supplier",
        "closingBalance": "期末余额",
        "itemsDetail": "项目明细",
        "startDate": "开始日期",
        "experationDate": "截止日期",
        "company": "公司",
        "targetCurrency": "目标货币",
        "transactionCurrency": "交易货币",
        "COAAccount": "总账",
        "refresh": "刷新",
        "category": "类别",
        "accountCode": "账户代码",
        "nameGLCategory": "账户名称",
        "endDebit": "借方",
        "endCredit": "贷方",
        "all": "全部",
        "endingAt": "截止日期",
        "subTotal": "小计",
        "updatedOn": "更新时间",
        "postingDate": "过账日期",
        "documentNo": "单据编号",
        "invoiceNo": "发票号码",
        "supplier": "供应商",
        "customer": "客户",
        "netAmount": "净金额",
        "currency": "货币",
        "PST": "省销售税",
        "GST": "商品及服务税",
        "QST": "魁北克销售税",
        "VAT": "增值税",
        "Discount": "折扣",
        "IsrWithholding": "所得税预提",
        "VatWithholding": "增值税预提",
        "totalTaxAmount": "总税额",
        "totalAmount": "总金额",
        "invoiceAmount": "发票金额",
        "payment": "付款",
        "totalDue": "总计应付",
        "businessPartner": "业务伙伴",
        "invoiceReference": "发票参考",
        "transactionType": "交易类型",
        "current": "当前",
        "31To60": "31到60天",
        "61To90": "61到90天",
        "91+": "超过91天",
        "contactName": "联系人姓名",
        "narration": "说明",
        "cashFlowName": "现金流量项目",
        "cashFlowMonth": "本月",
        "cashFlowYear": "本年",
        "vatChargeable": "VAT chargeable",
        "valueOfActsOrActivitiesTaxedAtTheRateOf16": "Value of acts or activities taxed at the rate of 16%",
        "valueOfActsOrActivitiesTaxedAtThe0ExportRate": "Value of acts or activities taxed at the 0% export rate",
        "valueOfActsOrActivitiesTaxedAtTheRateOf0Others": "Value of acts or activities taxed at the rate of 0% others",
        "sumOfTheTaxedActsOrActivities": "Sum of the taxed acts or activities",
        "valueOfActsOrActivitiesForWhichTaxIsNotPayable": "Value of acts or activities for which tax is not payable (exempt)",
        "valueOfActsOrActivitiesNotSubjectToTax": "Value of acts or activities not subject to tax",
        "vatChargeableAtTheRateOf16": "VAT chargeable at the rate of 16%",
        "vatCharged": "VAT charged",
        "updatedAmountToBeReimbursedDerivedFromTheAdjustment": "Updated amount to be reimbursed derived from the adjustment",
        "totalVatDue": "Total VAT due",
        "btCapture": "Capture",
        "vatCreditable": "VAT creditable",
        "amountOfPaidEventsOrActivities": "Amount of paid events or activities",
        "totalActsPaid16Percent": "Total of acts or activities paid at the 16% VAT rate",
        "totalActsPaidImport16Percent": "Total of acts or activities paid for in the import of goods and services at the 16% VAT rate",
        "totalActsPaid0Percent": "Total of other acts or activities paid at the 0% VAT rate",
        "totalPaidActsExempt": "Total of paid acts or activities for which VAT will not be paid (exempt)",
        "determinationCreditableVAT": "Determination of the creditable Value Added Tax",
        "vatOnActsPaid16Percent": "VAT on acts or activities paid at the rate of 16%",
        "vatOnImportPaid16Percent": "VAT on acts or activities paid on the import of goods and services at the rate of 16%",
        "totalVATTransferred": "Total VAT transferred to the taxpayer (Effectively paid)",
        "updatedCreditableAmount": "Updated creditable amount to increase derived from the adjustment",
        "totalCreditableVAT": "Total creditable VAT",
        "determination": "Determination",
        "vatWithheld": "VAT withheld",
        "totalCreditableVat": "Total creditable VAT",
        "otherAmountsPayableByTheTaxpayer": "Other amounts payable by the taxpayer",
        "otherAmountsInFavorOfTheTaxpayer": "Other amounts in favor of the taxpayer",
        "amountDue": "Amount due",
        "creditingOfTheBalanceInFavorOfPreviousPeriods": "Crediting of the balance in favor of previous periods (Without exceeding the amount due)",
        "taxDue": "Tax due",
        "amountToBeDetailed1": "Amount to be detailed",
        "interestChargedAtRate16": "Interest charged at a rate of 16%",
        "royaltiesBetweenRelatedPartiesAtRate16": "Royalties between related parties at the rate of 16%",
        "otherActsOrActivitiesTaxedAtRate16": "Other acts or activities taxed at the rate of 16%",
        "amountToBeDetailed2": "Amount to be detailed",
        "agriculturalLivestockForestryFishingActivitiesTaxedAtRate0": "Agricultural, livestock, forestry or fishing activities taxed at a rate of 0%",
        "otherActsOrActivitiesTaxedAtRate0": "Other acts or activities taxed at a rate of 0%",
        "amountToBeDetailed3": "Amount to be detailed",
        "alienationOfLandAndBuildingsForResidentialHousing": "Alienation of land and buildings attached to the land, intended or used for residential housing",
        "saleOfBooksNewspapersMagazinesNotByTaxpayer": "Sale of books, newspapers and magazines (not published by the taxpayer)",
        "royaltiesChargedByAuthors": "Royalties charged by authors",
        "disposalOfUsedMovablePropertyExceptByCompanies": "Disposal of used movable property, except those disposed of by companies",
        "alienationOfLotteryTicketsAndReceipts": "Alienation of tickets and other receipts from lotteries, raffles, drawings or games with bets and contests of all kinds",
        "teachingServices": "Teaching services",
        "publicLandTransportationServiceForPeople": "Public land transportation service for people",
        "derivativeFinancialTransactions": "Derivative financial transactions",
        "ticketSalesForPublicShows": "Ticket sales for public shows",
        "professionalMedicalServices": "Professional medical services",
        "temporaryUseOfRealEstateForResidentialOrFarming": "Temporary use or enjoyment of real estate for residential purposes and for farms for agricultural or livestock purposes",
        "otherIncomeExemptFromVat": "Other income exempt from VAT"
    },
    "menu": {
        "task": "任务",
        "purchase": "采购",
        "massiveProcess": "批处理",
        "invoices": "发票",
        "sales": "销售",
        "billing": "账单",
        "payroll": "发薪",
        "payrollRecord": "发薪记录",
        "bankReconciliation": "银行对账",
        "reconcile": "对账",
        "history": "历史记录",
        "generalLedger": "总账",
        "journalEntries": "记账",
        "setting": "设置",
        "contact": "商家信息",
        "profile": "公司信息",
        "coA": "科目总账",
        "coAMapping": "科目总账映射",
        "connectivities": "连接配置",
        "reporting": "报表",
        "reports": "商业分析",
        "exportTrialBalance": "报表",
        "help": "帮助",
        "userGuide": "用户指南",
        "FAQ": "常见问题",
        "account": "账户",
        "user": "用户",
        "dashboard": "工作台",
        "payment":"支付"
    },
    "fileTypeList": {
        "salesNotPaid": "非现金",
        "notPaid": "非现金",
        "salesCash": "现金",
        "cashPaid": "现金",
        "ES": "银行对账单",
        "yearEnd": "年终报告"
    },
    "task": {
        "name": "任务",
        "companyCode": "公司代码",
        "companyName": "公司名称",
        "last": "最近",
        "dueDate": "到期日",
        "estimatedHour": "预计 (小时)",
        "actualHour": "实际 (小时)",
        "status": "状态",
        "assignedTo": "指派",
        "email": "电子邮件",
        "tag": "标签",
        "priority": "优先事项",
        "createTime": "创建时间",
        "updateTime": "更新时间",
        "action": "操作",
        "placeholderSearch": "搜索任务名称",
        "placeholderStarting": "开始日期",
        "placeholderEnding": "结束日期",
        "placeholderMinHour": "最小小时数",
        "placeholderMaxHour": "最大小时数",
        "searchTitleEstimated": "预计",
        "statusToDo": "待处理",
        "statusDoing": "正在处理",
        "statusDone": "完成处理",
        "statusDeleted": "已删除"
    },
    "update": {
        "items": "条",
        "item": "条",
        "assigment": "科目分配",
        "paymentDueLater": "稍后付款",
        "withinDay7":"7天内",
        "withinDay15":"15天内",
        "withinDay30":"30天内",
        "withinDayOther":"固定日期",
        "other": "Other",
        "searchByBankAccount": "Search by bank account",
        "searchByFaq": "Search",
        "sapInstance": "Instance",
        "sapApplicationNumber": "Application Number",
        "reverseProcessing": "冲销处理中...",
        "poNumberNotFound": "未找到采购订单编号",
        "purchaseOrderFound": "已找到采购订单",
        "realizeSuccess": "正式发票生成成功",
        "purchaseOrderFoundButNoOpenItem": "找到采购订单，但没有未清项进行发票验证。",
        "excelGenerated": "Excel文件已生成并下载",
        "excelGenerateFailed": "生成Excel文件失败",
        "noPdfContent": "没有可用的 PDF 内容",
        "noXmlContent": "没有可用的 XML 内容",
        "excelOpenFailed": "无法打开 PDF 内容",
        "excelDownloadFailed": "无法下载 XML 内容"
    }
}
