/** @format */

import type {ActionContext} from 'vuex'
// import service from '../../../../api/request'
import service from '../../../../api/requestNew'
import serviceNew from '@/api/requestNewGL'
import serviceOMS from '@/api/requestOms'

import axios from 'axios'

const http = service
const httpV1 = serviceNew
const httpOMS = serviceOMS

const CoaMappingStore = {
    namespaced: true,
    state: {
        coaMappingList: [],
        totalNumber: 0,
        statciList: [],
        businessKeyList: [],
        orderTypeList: [],
        movementTypeList: [],
        debOrCreList: [],
    },
    getters: {
        coa_mapping_category: () => {
            return [
                {id: '1', title: 'Pay Item'},
                {id: '2', title: 'Movement Type'},
                {id: '3', title: 'Order Type'},
            ]
        },
    },
    mutations: {
        updateList(state: {coaMappingList: any[]}, list: any) {
            state.coaMappingList = [...list]
        },
        updateTotalNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateStaticList(state: {statciList: any}, list: any) {
            state.statciList = [...list]
        },
        updateBusinessKeyList(state: {businessKeyList: any}, list: any) {
            state.businessKeyList = [...list]
        },
        updateOrderTypeList(state: {orderTypeList: any}, list: any) {
            state.orderTypeList = [...list]
        },
        updateMovementTypeList(state: {movementTypeList: any}, list: any) {
            state.movementTypeList = [...list]
        },
        updateDebOrcreList(state: {debOrCreList: any}, list: any) {
            state.debOrCreList = [...list]
        },
    },
    actions: {
        // 请求coamappingList数据
        async fetchCoaMappingList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            // const response = await http.get(
            //     `/system-preferences/api/v1/coa-rel?$limit=${payload.limit}&$skip=${payload.skip}`,
            // )
            const response = await http.get(`/system-preferences/api/v1/coa-rel`, {params: payload})
            if (response) {
                const list = response.data.data.map((item: any) => {
                    return {
                        ...item,
                    }
                })
                store.commit('updateList', list)
                store.commit('updateTotalNumber', response.data.total)
                return response
            }
        },
        // 请求本地json数据
        async fetchBusinessKeyList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.get(`/users/api/v1/company-payitem?company_code=${payload}`)

            store.commit('updateBusinessKeyList', response.data.data)
            return response
        },
        async fetchMovementTypeList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpOMS.get(`/oms/svc/movementType/list?companyCode=${payload}`)
            store.commit(
                'updateOrderTypeList',
                response.data.data.filter((i: any) => i.type === '3'),
            )
            store.commit(
                'updateMovementTypeList',
                response.data.data.filter((i: any) => i.type === '2'),
            )

            return response
        },
        //请求deb和cre数据
        async fetchDebOrCreditList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const queryParams = new URLSearchParams()
            queryParams.append('company', payload?.company)
            // queryParams.append('is_archive', 'false')
            const response = await httpV1.get(`/bk/coa`, {params: queryParams})
            const list = response.data.data.rows
            store.commit('updateDebOrcreList', list)
            return response
        },
        //新增
        async AddCoaMappingItem(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/system-preferences/api/v1/coa-rel', payload)
            return response
        },
        // 编辑
        async updateCoaMappingItem(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.put(`/system-preferences/api/v1/coa-rel/${payload.id}`, payload)
            return response
        },
        async fetchDelItemAction(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.delete(`/system-preferences/api/v1/coa-rel/${payload}`)
            return response
        },
    },
}

export default CoaMappingStore
