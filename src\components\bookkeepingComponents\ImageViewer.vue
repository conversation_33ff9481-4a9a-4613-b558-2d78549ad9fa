<!-- @format -->

<template>
    <viewer :images="[url]" class="images clearfix" :options="options">
        <template #default>
            <img :src="url" :key="url" class="image" />
        </template>
    </viewer>
</template>

<script setup lang="ts">
import 'viewerjs/dist/viewer.css'
import {component as Viewer} from 'v-viewer'

const props = defineProps({
    url: {
        type: String,
        required: false,
    },
})

const options = {
    inline: true,
    navbar: false,
    backdrop: false,
    title: false,
    button: false,
}
</script>

<style lang="scss" scoped>
.images {
    display: none;
}

//:deep(.viewer-footer) {
//    margin-bottom: 100px;
//    background: red;
//}
//
//.viewer-footer {
//    margin-bottom: 100px;
//    background: red;
//}
.viewer-one-to-one,
.viewer-reset,
.viewer-prev,
.viewer-play,
.viewer-next {
    display: none;
}
</style>
