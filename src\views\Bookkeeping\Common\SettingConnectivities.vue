<!-- @format -->
<script lang="ts" setup>
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {computed, onBeforeMount, onMounted, reactive, ref, watch} from 'vue'
import dayjs from 'dayjs'
import type {Dayjs} from 'dayjs'
import {EyeOutlined, EyeInvisibleOutlined} from '@ant-design/icons-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import {useStore} from 'vuex'
import {UserCompany} from '@/lib/storage'
import {type FormInstance, message} from 'ant-design-vue'
const store = useStore()
const i18n: Composer = i18nInstance.global
const userCompany: any = UserCompany.get() || []
interface ImportForm {
    source: string
    username: string
    password: string
    startDate: Dayjs | string
    endDate: Dayjs | string
}

interface SMTPForm {
    id?: number
    mail_host: string
    mail_user: string
    mail_pass: string
    start_date: Dayjs | string
    company_id: number
    company_code: string
    // mail_port: number
    // mail_tls: number
    mail_type: number
}

interface NetdiskForm {
    id?: number
    secret: string
    app_id: string
    key: string
    disk_type: string
}

const fetchSMTP = (payload: any) => store.dispatch('ConnectivityStore/fetchSMTP', payload)
const createSMTP = (payload: any) => store.dispatch('ConnectivityStore/createSMTP', payload)
const updateSMTP = (payload: any) => store.dispatch('ConnectivityStore/updateSMTP', payload)

//const fetchSMTP = (payload: any) => store.dispatch('ConnectivityStore/fetchSMTP')

const importFromOptions = computed(() => store.getters['ConnectivityStore/importSources'])

const smtpStoreList = computed<SMTPForm[]>(() => store.state.ConnectivityStore.smtpItems)

const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const rules = reactive({
    source: [...requireRule(i18n.t('connectivity.source'))],
    // password: {},
    mail_host: [{required: true, message: 'Please input email host.', trigger: ['blur', 'change']}],
    mail_user: [{required: true, type: 'email', trigger: ['blur', 'change']}],
    mail_pass: [{required: true, message: 'Please input email password.', trigger: ['blur', 'change']}],
    // start_date: {},
})

const state = reactive({
    activeKey: 'import',
    formLoading: true,
})
const passwordInput = reactive({
    enableDisplay: false,
    inputType: 'password',
})
// const submitLoading = ref(false)
const isEnableSubmit = ref(false)

const passwordDisplaySwitch = () => {
    passwordInput.enableDisplay = !passwordInput.enableDisplay
    passwordInput.inputType = passwordInput.enableDisplay ? 'text' : 'password'
}

// const formRef = ref<FormInstance>()
const sourceFormState = ref<ImportForm>({
    source: 'shopify',
    username: '',
    password: '',
    startDate: dayjs(),
    endDate: dayjs(),
})

const initSMTPState: SMTPForm = {
    mail_host: '',
    mail_user: '',
    mail_pass: '',
    start_date: dayjs(),
    company_id: 0,
    company_code: '',
    // mail_port: 0,
    // mail_tls: 0,
    mail_type: 1,
}

const initNetdiskState: NetdiskForm = {
    secret: '',
    app_id: '',
    key: '',
    disk_type: '',
}

const smtpFormState = ref<SMTPForm>({...initSMTPState})
const smtpFormRef = ref<FormInstance>()
const netDiskFormState = ref<NetdiskForm>({...initNetdiskState})
const getImageUrl = (name: any) => {
    return new URL(`@/assets/image/${name}`, import.meta.url).href
}
const serverTypeOptions = ref([
    {
        key: 1,
        value: 1,
        label: 'Hotmail',
        img: 'server/hotmail.png',
    },
    {
        key: 2,
        value: 2,
        label: 'Outlook.com',
        img: 'server/outlook.png',
    },
    {
        key: 3,
        value: 3,
        label: 'Office 365',
        img: 'server/office365.png',
    },
    {
        key: 4,
        value: 4,
        label: 'Other POP, IMAP',
        img: 'server/other.png',
    },
    {
        key: 5,
        value: 5,
        label: 'Sap',
        img: 'server/sap.png',
    },
])

const netdiskTypeOptions = ref([
    {
        key: 1,
        value: 1,
        label: 'Google Doc',
        img: 'netdisk/googledoc.png',
    },
    {
        key: 2,
        value: 2,
        label: 'Drop Box',
        img: 'netdisk/dropbox.png',
    },
])

const validateSourceEnableSubmit = () => {
    isEnableSubmit.value = !!(
        sourceFormState.value.source &&
        sourceFormState.value.username &&
        sourceFormState.value.password
    )
}
const validateSMTPEnableSubmit = async () => {
    try {
        const result = await smtpFormRef.value?.validateFields()
        if (result) {
            await submitSMTP()
        }
    } catch (e) {
        return
    }
    // isEnableSubmit.value = !!(
    //     smtpFormState.value.mail_type &&
    //     smtpFormState.value.mail_user &&
    //     smtpFormState.value.mail_pass
    // )
}
const validateNetDiskEnableSubmit = () => {
    isEnableSubmit.value = !!(
        sourceFormState.value.source &&
        sourceFormState.value.username &&
        sourceFormState.value.password
    )
}

const submitImport = () => {
    console.log(sourceFormState)
}
const submitSMTP = async () => {
    state.formLoading = true
    const payload = {...smtpFormState.value}
    payload.company_code = userCompany[0].code
    payload.company_id = userCompany[0].id
    try {
        const response = payload.id ? await updateSMTP(payload) : await createSMTP(payload)
        if ([200, 201].includes(response.status)) {
            message.success(i18n.t('ApComponents.success'))
            await fetchSMTP({company_code: userCompany[0].code})
        } else {
            // message.error({content: response.data.message ?? 'failed'})
        }
    } catch (e: any) {
        console.log(e)
    } finally {
        state.formLoading = false
    }
}
const submitNetdisk = async () => {
    console.log('save netdisk')
}

const handleSourceChange = (action: string, e: any) => {
    // TODO
}

const handleSMTPServerChange = (action: string, e: any) => {
    smtpFormState.value.mail_type = e
    smtpFormRef.value?.clearValidate()
    console.log('select', smtpFormState.value)
}
const handleNetdiskServerChange = (action: string, e: any) => {
    // netDiskFormState.value.disk_type = e
    console.log('select', netDiskFormState.value)
}

watch(
    () => smtpStoreList.value,
    (count, preCount) => {
        if (smtpStoreList.value.length > 0) {
            smtpFormState.value = {...smtpStoreList.value[0]}
        }
    },
)

watch(
    () => smtpFormState.value.mail_type,
    (count, prevCount) => {
        console.log(count, prevCount)
        const selected = smtpStoreList.value.find((x: SMTPForm) => x.mail_type === smtpFormState.value.mail_type)
        if (selected) {
            smtpFormState.value = {...selected}
        } else {
            // Object.assign(smtpFormState.value, initSMTPState)
            smtpFormState.value = {...initSMTPState}
            smtpFormState.value.mail_type = count
        }
    },
)

onBeforeMount(async () => {
    await Promise.all([fetchSMTP({company_code: userCompany[0].code})])
    state.formLoading = false
})

onMounted(async () => {
    console.log(smtpStoreList.value)
    state.formLoading = false
})
</script>
<template>
    <div class="page-container-connectivities" >
        <a-spin :spinning="state.formLoading" >
            <a-tabs
                v-model:activeKey="state.activeKey"
                :tab-bar-style="{width: '100%'}"
                size="default"
                class="tabs-contents"
            >
                <a-tab-pane
                    key="import"
                    v-if="!['8888', '8001'].includes(userCompany[0].code)"
                    :tab="$t('SettingConnectivities.channel')"
                >
                    <div class="content-box-import-form">
                        <div class="import-form-title">{{ i18n.t('SettingConnectivities.importBilling') }}</div>
                        <div class="import-form-wrap">
                            <a-form
                                :model="sourceFormState"
                                :layout="'vertical'"
                                autocomplete="off"
                                @submit="submitImport"
                                @validate="validateSourceEnableSubmit"
                            >
                                <div class="import-form-block">
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item name="source" :label="i18n.t('connectivity.source')">
                                                <a-select
                                                    :options="importFromOptions"
                                                    :placeholder="i18n.t('connectivity.sourceHolder')"
                                                    v-model:value="sourceFormState.source"
                                                    @change="handleSourceChange('ship', $event)"
                                                >
                                                </a-select>
                                                <div
                                                    class="img-wrap"
                                                    v-if="
                                                        sourceFormState.source &&
                                                        importFromOptions.find((i:any) => i.value == sourceFormState.source)
                                                    "
                                                >
                                                    <img
                                                        style="height: 36px; padding: 5px 10px"
                                                        :src="importFromOptions.find((i:any) => i.value == sourceFormState.source).image"
                                                    />
                                                </div>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12"
                                            ><div>
                                                <img
                                                    v-if="
                                                        sourceFormState.source &&
                                                        importFromOptions.find((i:any) => i.value == sourceFormState.source)
                                                    "
                                                    class="shopify-logo-wrap"
                                                    :src="importFromOptions.find((i:any) => i.value == sourceFormState.source).image"
                                                />
                                            </div>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                required
                                                name="username"
                                                :label="i18n.t('connectivity.username')"
                                            >
                                                <a-input v-model:value="sourceFormState.username" />
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item
                                                required
                                                name="password"
                                                :label="i18n.t('connectivity.password')"
                                            >
                                                <a-input
                                                    v-model:value="sourceFormState.password"
                                                    :type="passwordInput.inputType"
                                                >
                                                    <template #suffix>
                                                        <div @click="passwordDisplaySwitch">
                                                            <eye-invisible-outlined
                                                                v-if="passwordInput.enableDisplay"
                                                            />
                                                            <eye-outlined v-else />
                                                        </div>
                                                    </template>
                                                </a-input>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                required
                                                name="startDate"
                                                :label="i18n.t('connectivity.startDate')"
                                            >
                                                <a-date-picker
                                                    v-model:value="sourceFormState.startDate"
                                                    value-format="YYYY-MM-DD"
                                                    style="width: 100%"
                                                >
                                                    <!--                                                <template #suffixIcon> </template>-->
                                                </a-date-picker>
                                            </a-form-item>
                                        </a-col>
                                        <a-col :span="12">
                                            <a-form-item
                                                required
                                                name="endDate"
                                                :label="i18n.t('connectivity.endDate')"
                                            >
                                                <a-date-picker
                                                    v-model:value="sourceFormState.endDate"
                                                    value-format="YYYY-MM-DD"
                                                    style="width: 100%"
                                                >
                                                    <!--                                            <template #suffixIcon> </template>-->
                                                </a-date-picker>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                </div>
                                <div style="text-align: right">
                                    <a-button
                                        shape="round"
                                        :disabled="!isEnableSubmit"
                                        type="primary"
                                        html-type="submit"
                                        >{{ i18n.t('SettingConnectivities.import') }}</a-button
                                    >
                                </div>
                            </a-form>
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane key="smtp" :tab="$t('SettingConnectivities.SMTP')">
                    <div class="content-box-smtp">
                        <div class="content-box-smtp-wrap">
                            <a-form
                                ref="smtpFormRef"
                                :rules="rules"
                                :model="smtpFormState"
                                :layout="'vertical'"
                                autocomplete="off"
                            >
                                <div class="import-form-block">
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item name="mail_type" :label="i18n.t('connectivity.server')">
                                                <a-select
                                                    :placeholder="i18n.t('connectivity.sourceHolder')"
                                                    v-model:value="smtpFormState.mail_type"
                                                    @change="handleSMTPServerChange('ship', $event)"
                                                    :options="serverTypeOptions"
                                                >
                                                </a-select>
                                                <div
                                                    class="img-wrap"
                                                    v-if="
                                                        smtpFormState.mail_type &&
                                                        serverTypeOptions.find(i => i.value == +smtpFormState.mail_type)
                                                    "
                                                >
                                                    <img
                                                        v-if="smtpFormState.mail_type == 1"
                                                        style="height: 36px; padding: 5px 10px"
                                                        src="@/assets/image/server/hotmail.png"
                                                    />
                                                    <img
                                                        v-if="smtpFormState.mail_type == 2"
                                                        style="height: 36px; padding: 5px 10px"
                                                        src="@/assets/image/server/outlook.png"
                                                    />
                                                    <img
                                                        v-if="smtpFormState.mail_type == 3"
                                                        style="height: 36px; padding: 5px 10px"
                                                        src="@/assets/image/server/office365.png"
                                                    />
                                                    <img
                                                        v-if="smtpFormState.mail_type == 4"
                                                        style="height: 36px; padding: 5px 10px"
                                                        src="@/assets/image/server/other.png"
                                                    />
                                                    <img
                                                        v-if="smtpFormState.mail_type == 5"
                                                        style="height: 36px; padding: 5px 10px"
                                                        src="@/assets/image/server/sap.png"
                                                    />
                                                </div>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                name="mail_host"
                                                v-if="smtpFormState.mail_type === 4"
                                                :label="i18n.t('connectivity.emailHost')"
                                            >
                                                <a-input v-model:value="smtpFormState.mail_host" />
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item name="mail_user" :label="i18n.t('connectivity.email')">
                                                <a-input v-model:value="smtpFormState.mail_user" />
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item name="mail_pass" :label="$t('SettingConnectivities.pass')">
                                                <a-input
                                                    v-model:value="smtpFormState.mail_pass"
                                                    :type="passwordInput.inputType"
                                                >
                                                    <template #suffix>
                                                        <div @click="passwordDisplaySwitch">
                                                            <eye-invisible-outlined
                                                                v-if="passwordInput.enableDisplay"
                                                            />
                                                            <eye-outlined v-else />
                                                        </div>
                                                    </template>
                                                </a-input>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                name="start_date"
                                                :disabled="Number(smtpFormState.id) > 0"
                                                :label="i18n.t('connectivity.startDate')"
                                            >
                                                <a-date-picker
                                                    v-model:value="smtpFormState.start_date"
                                                    value-format="YYYY-MM-DD"
                                                    style="width: 100%"
                                                >
                                                    <!--   <template #suffixIcon> </template>-->
                                                </a-date-picker>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                </div>
                                <div style="text-align: left">
                                    <a-button shape="round" type="primary" @click="validateSMTPEnableSubmit">{{
                                        i18n.t('SettingConnectivities.save')
                                    }}</a-button>
                                    <a-button style="margin-left: 10px" shape="round">{{
                                        i18n.t('commonTag.cancel')
                                    }}</a-button>
                                </div>
                            </a-form>
                        </div>
                    </div>
                </a-tab-pane>
                <a-tab-pane style="display: none" key="server" :tab="$t('SettingConnectivities.Netdist')" >
                    <div class="content-box-smtp">
                        <div class="content-box-smtp-wrap">
                            <a-form
                                :model="netDiskFormState"
                                :layout="'vertical'"
                                autocomplete="off"
                                @submit="submitNetdisk"
                                @validate="validateNetDiskEnableSubmit"
                            >
                                <div class="import-form-block">
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                name="netdisk_type"
                                                required
                                                :label="i18n.t('connectivity.type')"
                                            >
                                                <a-select
                                                    :placeholder="i18n.t('connectivity.sourceHolder')"
                                                    v-model:value="netDiskFormState.disk_type"
                                                    @change="handleNetdiskServerChange('ship', $event)"
                                                    :options="netdiskTypeOptions"
                                                >
                                                </a-select>
                                                <div
                                                    class="img-wrap"
                                                    v-if="
                                                        netDiskFormState.disk_type &&
                                                        netdiskTypeOptions.find(
                                                            i => i.value == +netDiskFormState.disk_type,
                                                        )
                                                    "
                                                >
                                                    <img
                                                        v-if="netDiskFormState.disk_type == '1'"
                                                        style="height: 36px; padding: 5px 10px"
                                                        src="@/assets/image/netdisk/googledoc.png"
                                                    />
                                                    <img
                                                        v-if="netDiskFormState.disk_type == '2'"
                                                        style="height: 36px; padding: 5px 10px"
                                                        src="@/assets/image/netdisk/dropbox.png"
                                                    />
                                                </div>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                required
                                                name="secret"
                                                :label="i18n.t('connectivity.disk_secret')"
                                            >
                                                <a-input
                                                    v-model:value="netDiskFormState.secret"
                                                    :type="passwordInput.inputType"
                                                >
                                                    <template #suffix>
                                                        <div @click="passwordDisplaySwitch">
                                                            <eye-invisible-outlined
                                                                v-if="passwordInput.enableDisplay"
                                                            />
                                                            <eye-outlined v-else />
                                                        </div>
                                                    </template>
                                                </a-input>
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                required
                                                name="disk_app_id"
                                                :label="i18n.t('connectivity.disk_app_id')"
                                            >
                                                <a-input v-model:value="netDiskFormState.app_id" />
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                    <a-row :gutter="48">
                                        <a-col :span="12">
                                            <a-form-item
                                                required
                                                name="disk_key"
                                                :label="i18n.t('connectivity.disk_key')"
                                            >
                                                <a-input v-model:value="netDiskFormState.key" />
                                            </a-form-item>
                                        </a-col>
                                    </a-row>
                                </div>
                                <div style="text-align: left">
                                    <a-button shape="round" :disabled="true" type="primary" html-type="submit">{{
                                        i18n.t('commonTag.save')
                                    }}</a-button>
                                    <a-button style="margin-left: 10px" shape="round">{{
                                        i18n.t('SettingConnectivities.cancel')
                                    }}</a-button>
                                </div>
                            </a-form>
                        </div>
                    </div>
                </a-tab-pane>
            </a-tabs>
        </a-spin>
    </div>
</template>

<style lang="scss" scoped>
.page-container-connectivities {
    height: 100%;
    .tabs-contents {
        border-radius: 12px;
        flex: 1;
    }
}
.content-box-import-form,
.content-box-smtp {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    padding: 24px 20px 20px;
    background-color: #fff;
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    //.content-box-smtp-wrap {
    //    width: 40%;
    //    .btn-auth {
    //        margin-top: 20px;
    //        width: 120px;
    //        height: 36px;
    //    }
    //}
}

.import-form-wrap {
}
.import-form-title {
    //  font-family: Calibri;
    font-size: 44px;
    color: #262626;
    line-height: 54px;
    font-weight: 400;
}

.import-form-block {
    padding: 24px 0;
}

.shopify-logo-wrap {
    height: 71px;
    // width: 237px;
}
.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover {
    //@extend .ant-btn-primary;
    opacity: 50%;
    color: #fff;
    border-color: #004fc1;
    background: #004fc1;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}
.img-wrap {
    position: absolute;
    right: 30px;
    top: 0px;
}
</style>
