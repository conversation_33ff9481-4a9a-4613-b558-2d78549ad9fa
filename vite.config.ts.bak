/** @format */

import {fileURLToPath, URL} from 'node:url'

import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueI18n from '@intlify/vite-plugin-vue-i18n'
import {createSvgIconsPlugin} from 'vite-plugin-svg-icons'

// https://vitejs.dev/config/
export default defineConfig({
    base: './',
    plugins: [
        vue(),
        vueJsx(),
        vueI18n({
            runtimeOnly: false,
            // if you want to use Vue I18n Legacy API, you need to set `compositionOnly: false`
            // compositionOnly: false,

            // you need to set i18n resource including paths !
            include: fileURLToPath(new URL('./src/locales/lang', import.meta.url)),
        }),
        createSvgIconsPlugin({
            iconDirs: [fileURLToPath(new URL('./src/assets/icons', import.meta.url))],
            symbolId: 'icon-[name]',
        }),
    ],
    resolve: {
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url)),
        },
    },
    css: {
        preprocessorOptions: {
            less: {
                modifyVars: {
                    'primary-color': '#004FC1',
                    'font-size-base': '14px',
                    'heading-color': '#262626',
                    'font-family': 'Lato',
                    'text-color': '#262626',
                    'border-radius-base': '4px',
                    'btn-height-base': '36px',
                    'btn-height-sm': '32px',
                    'btn-font-size-sm': '14px',
                    'btn-padding-horizontal-sm': '10px',
                    'border-color-base': '#C3C7D4',
                    'btn-default-color': '#676D7C',
                    'input-height-base': '36px',
                    'menu-item-height': '48px',
                    'menu-item-active-bg': '#f5f7f9',
                    'menu-item-color': '#262626',
                    'menu-inline-submenu-bg': '#fff',
                    'menu-item-active-border-width': '0px',
                    'menu-highlight-color': '#004FC1',
                    'menu-item-group-title-color': '#262626',
                    'menu-item-group-title-font-size': '18px',
                    'table-bg': '#fff',
                    'table-header-bg': '#f7f9fa',
                    'table-header-color': '#262626',
                    'table-selected-row-bg': '#f5f7f9',
                    'table-selected-row-hover-bg': '#f5f7f9',
                    'table-row-hover-bg': '#f5f7f9',
                    'table-padding-vertical': '12px',
                    'tabs-card-height': '54px',
                    'tabs-title-font-size': '16px',
                    'tabs-active-color': '#004FC1',
                    'tabs-horizontal-margin': '0px',
                    'tabs-horizontal-padding': '16px 20px',
                    'modal-header-title-font-size': '16px',
                    'modal-header-padding': '15px 20px 19px',
                    'select-dropdown-height': '36px',
                    'select-dropdown-line-height': '34px',
                    'layout-sider-background': '#fff',
                    'layout-body-background': '#f5f7f9',
                    'layout-header-background': '#004FC1',
                    'layout-header-height': '82px',
                    'input-placeholder-color': '#AAB9CB',
                },
                javascriptEnabled: true,
            },
        },
    },
    server: {
        proxy: {
            '/api': {
                target: 'https://eo-gateway.inossemtimes.com', //测试接口
                //target:'http://infinite-bk.canadacentral.cloudapp.azure.com/web',//NT生产接口
                changeOrigin: true,
                rewrite: path => path.replace(/^\/api/, ''),
            },
            '/newapi': {
                // target: 'https://eo-gateway-test.inossemtimes.com', //测试接口
                target: 'https://eo-gateway.inossemtimes.com',// NT生产接口
                changeOrigin: true,
                rewrite: path => path.replace(/^\/newapi/, ''),
            },
            '/newgl': {
                // target: 'https://eo-gateway-test.inossemtimes.com/bkp-engine', //测试接口
                target: 'https://eo-gateway.inossemtimes.com/bkp-engine', //NT生产接口
                changeOrigin: true,
                rewrite: path => path.replace(/^\/newgl/, ''),
            },
            '/kyc': {
                // target: 'http://kyc.inossemtimes.com/api-test', //测试接口
                target: 'https://eo-gateway.inossemtimes.com/kyc', //NT生产接口
                // target: 'http://eo-gateway-test.inossemtimes.com/kyc',
                changeOrigin: true,
                rewrite: path => path.replace(/^\/kyc/, ''),
            },
            '/oms': {
                // target: 'https://eo-gateway-test.inossemtimes.com/oms', //测试接口
                target: 'https://oms.inossemtimes.com/oms', //NT生产接口
                changeOrigin: true,
                rewrite: path => path.replace(/^\/oms/, ''),
            },
            '/pgo': {
                target: 'https://dev-pgo-backend.inossemcanada.com', //测试接口
                // target: 'http://dev-pgo-backend.inossemcanada.com', //NT生产接口
                changeOrigin: true,
                rewrite: path => path.replace(/^\/pgo/, ''),
            },
        },
    },
})
