<!-- @format -->

<template>
    <div class="history-page-wrap">
        <div class="history-page-content">
            <div style="display: flex" class="main-head">
                <a-tabs
                    :tab-bar-style="{width: '100%'}"
                    v-model:activeKey="state.activeName"
                    size="default"
                    @tab-click="changeTab"
                >
                    <a-tab-pane
                        key="pending"
                        :tab="i18n.t('bkApInvoice.captured')"
                        name="pending"
                        class="tab-panel-class"
                    >
                        <div class="invoice-table-wrap">
                            <!-- <div class="content-box-btn_upload"> -->
                            <!--  :disabled="disableOcrButton"-->
                            <!-- </div> -->
                            <a-spin :spinning="state.pendingTableLoading">
                                <template v-slot:indicator>
                                    <LoadingOutlined />
                                </template>
                                <ap-invoice-table
                                    ref="childTable"
                                    v-if="state.activeName === 'pending'"
                                    :active-name="state.activeName"
                                    @pageChange="pageChange"
                                    @edit="editInvoice"
                                    @delete="deletePdf"
                                    @updateComment="updateComment"
                                    @ocrFiles="ocrFiles"
                                    @updateSelected="updateSelectedOcrFiles"
                                ></ap-invoice-table>
                            </a-spin>
                        </div>
                    </a-tab-pane>
                    <a-tab-pane
                        key="finished"
                        :tab="i18n.t('bkApInvoice.created')"
                        name="finished"
                        class="tab-panel-class"
                    >
                        <div class="invoice-table-wrap">
                            <a-spin :spinning="state.finishingTableLoading">
                                <template v-slot:indicator>
                                    <LoadingOutlined />
                                </template>
                                <ap-invoice-table
                                    ref="childTable"
                                    v-if="state.activeName === 'finished'"
                                    :active-name="state.activeName"
                                    @view="viewFile"
                                    @updateComment="updateComment"
                                    @download="downloadFile"
                                ></ap-invoice-table>
                            </a-spin>
                        </div>
                    </a-tab-pane>
                    <template #tabBarExtraContent>
                      <!-- <a-tooltip :title="i18n.t('bkApInvoice.scan')">
                          <a-button
                              :disabled="disableOcrButton"
                              :ghost="true"
                              shape="round"
                              type="primary"
                              @click="ocrFiles(state.selectedOcrFiles)"
                              v-if="state.currentTabIndex !== 'finished'"
                              class="btn-scan-all"
                          >
                              <scan-outlined />
                          </a-button>
                      </a-tooltip> -->
                      <a-tooltip :title="i18n.t('bkApInvoice.fetch')">
                        <a-button
                            shape="round"
                            type="primary"
                            v-if="state.currentTabIndex !== 'finished' && !excludedCodes.includes(userCompany[0].code)"
                            class="btn-scan-all"
                            :disabled="mailFetching"
                            @click="fetchMailBox"
                        >
                            <!--  <profile-outlined />-->
                            <svg-icon2x name="icon_fetch"></svg-icon2x>
<!--                             {{ i18n.t('bkApInvoice.fetch') }}-->
                        </a-button>
                      </a-tooltip>
                      <a-tooltip :title="i18n.t('bkApInvoice.upload')">
                        <a-button
                            shape="round"
                            type="primary"
                            @click="showUploadDiag(true)"
                            v-if="state.currentTabIndex !== 'finished' && !excludedUploadCodes.includes(userCompany[0].code)"
                            class="btn-scan-all"
                        >
                            <cloud-upload-outlined />
<!--                             {{ i18n.t('bkApInvoice.upload') }}-->
                        </a-button>
                      </a-tooltip>
                        <!-- <a-button danger :ghost="true" shape="round" type="primary"
                            @click="deleteSelectPdf(state.selectedOcrFiles)" v-if="state.currentTabIndex !== 'finished'"
                            class="btn-scan-all">
                            <delete-outlined />
                            {{ i18n.t('bkApInvoice.del') }}
                        </a-button> -->
                    </template>
                </a-tabs>
            </div>
        </div>

        <!-- 上传部分 -->
        <div class="upload-modal-wrap" ref="modalUpload">
            <a-modal
                :title="i18n.t('ApUploadInvoice.upload')"
                v-model:visible="state.show"
                :get-container="modalUpload"
                destroyOnClose
                :closeable="true"
                :width="'34vw'"
                :bodyStyle="{padding: '10px 14px 14px'}"
                :wrapClassName="'modal-wrap'"
            >
                <upload-file-comp
                    v-if="!INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                    ref="uploadComp"
                    :upload-processing="state.uploadProcessing"
                    :file-limit="8"
                    :fileType="restrictFileType"
                    :page-type="'1'"
                />
                <upload-file-comp-mx
                    v-if="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                    ref="uploadComp"
                    :upload-processing="state.uploadProcessing"
                    :file-limit="2"
                    :page-type="'1'"
                />
                <template #footer>
                    <a-button key="cancel" class="btn-cancel" shape="round" @click="closeUploadModal">{{ i18n.t('commonTag.cancel') }}</a-button>
                    <a-button
                        key="upload"
                        shape="round"
                        type="primary"
                        :loading="state.uploadProcessing"
                        @click="uploadInvoiceFiles"
                        >{{ i18n.t('bkApInvoice.upload') }}
                    </a-button>
                </template>
            </a-modal>
        </div>

        <a-modal
            title="Invoice List"
            v-model:visible="state.showInvoiceDetail"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="800"
            :bodyStyle="{padding: '10px 14px 14px'}"
            wrap-class-name="modal-wrap"
        >
            <ocr-invoice-table @view="viewCompletedInvoiceDetail" :readonly-mode="true" />
        </a-modal>
        <a-modal
            :title="$t('bkApInvoice.readonly')"
            v-model:visible="state.showCreateInvoice"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="'1110px'"
            :bodyStyle="{padding: '10px 14px 14px'}"
        >
            <a-spin
                :tip="apIntegration === 1 ? i18n.t('commonTag.sapTip') : ''"
                :spinning="createLoading"
                wrapperClassName="custom-spin"
            >
                <ap-invoice-component-integration-mx
                    v-if="apIntegration === 1 && INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                    ref="invoiceForm"
                    :current-invoice="currentInvoice"
                    :from="from"
                    :readonly-mode="false"
                    :operationMode="'creating'"
                    @save="save"
                    @dismiss="dismiss"
                >
                </ap-invoice-component-integration-mx>
                <ap-invoice-component-integration
                    v-if="apIntegration === 1 && !INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                    ref="invoiceForm"
                    :current-invoice="currentInvoice"
                    :from="from"
                    :readonly-mode="false"
                    :operationMode="'creating'"
                    @save="save"
                    @dismiss="dismiss"
                >
                </ap-invoice-component-integration>
            </a-spin>
        </a-modal>

        <a-modal
            :title="$t('bkApInvoice.readonly')"
            v-model:visible="state.showCompletedInvoice"
            :footer="null"
            destroyOnClose
            :closeable="true"
            :width="'1110px'"
            :bodyStyle="{padding: '10px 14px 14px'}"
        >
            <ap-invoice-component
                v-if="apIntegration !== 1"
                :invoice-id="state.current.id"
                :current-invoice="{}"
                :readonly-mode="true"
                :from="'massive_process_created'"
                @reverse="apReverse"
                @dismiss="showCompletedInvoiceDialog(false)"
            ></ap-invoice-component>
            <ap-invoice-component-integration
                v-if="apIntegration === 1 && !INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                :invoice-id="state.current.id"
                :current-invoice="{}"
                :readonly-mode="true"
                :from="'massive_process_created'"
                @reverse="apReverse"
                @dismiss="showCompletedInvoiceDialog(false)"
            ></ap-invoice-component-integration>
            <ap-invoice-component-integration-mx
                v-if="apIntegration === 1 && INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country)"
                :invoice-id="state.current.id"
                :current-invoice="{}"
                :readonly-mode="true"
                :from="'massive_process_created'"
                @reverse="apReverse"
                @copy="copyInvoiceAfterReverse"
                @dismiss="showCompletedInvoiceDialog(false)"
            ></ap-invoice-component-integration-mx>
        </a-modal>

        <a-modal
            v-model:visible="state.dialogVisible"
            :modal="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :afterClose="handleClose"
            custom-class="notification-class"
            width="30%"
            top="42vh"
        >
            <template #title>
                <div>{{ i18n.t('ApUploadInvoice.OCR') }}</div>
            </template>
            <div class="ocr-notification-content">
                <div class="ocr-notification-title">
                    {{ i18n.t('ApUploadInvoice.file') }} <br /><span style="font-weight: bold">
                        {{ state.selectedOcrFiles.map(x => x.file_name).join('\n') }}
                    </span>
                </div>
                <div class="ocr-notification-graph">
                    <!-- temporary -->
                    <!--                    <a-icon-->
                    <!--                        type="sync"-->
                    <!--                        spin-->
                    <!--                        style="font-size: 16px"-->
                    <!--                        :style="state.currentOcrType === 'ABBYY' ? {color: '#1a94d0'} : {color: '#FF6600'}"-->
                    <!--                    />-->
                    <sync-outlined
                        spin
                        style="font-size: 16px"
                        :style="state.currentOcrType === 'ABBYY' ? {color: '#1a94d0'} : {color: '#FF6600'}"
                    />
                    <span :class="state.currentOcrType === 'ABBYY' ? 'bar-txt' : 'bar-txt-rossum'">{{
                        i18n.t('ApUploadInvoice.analyzing', {type: state.currentOcrType})
                    }}</span>
                </div>
            </div>
        </a-modal>

        <a-modal
            v-model="state.showUploadErrorDiag"
            destroyOnClose
            :closeable="true"
            :width="560"
            :bodyStyle="{padding: '10px 14px 14px'}"
            :afterClose="resetStateForCancel"
            :zIndex="2000"
        >
            <FileUploadResult :result-list="state.fileUploadResult" :result-title="state.fileUploadResultTitle" />
            <template v-slot:title>
                <!--                <a-icon type="close-circle" style="color: red; font-size: 22px" />-->
                <close-circle-outlined style="color: red; font-size: 22px" />
                <span style="display: inline-block; margin-left: 10px"> {{ i18n.t('ApUploadInvoice.error') }} </span>
            </template>
            <template v-slot:footer>
                <a-button type="warning" size="middle" @click="resetStateForCancel">{{
                    i18n.t('ApUploadInvoice.confirm')
                }}</a-button>
            </template>
        </a-modal>

        <a-modal
            :title="i18n.t('bkApInvoice.edit')"
            v-model:visible="state.editRecordShow"
            :footer="null"
            destroyOnClose
            :closeable="true"
            width="100%"
            :bodyStyle="{padding: '10px 14px 14px'}"
            :afterClose="handleClose"
            wrap-class-name="modal-wrap"
        >
            <EditReocrd :pageType="state.pageType" @close-modal="handleClose" />
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import {useStore} from 'vuex'
import ApInvoiceTable from '@/components/bookkeepingComponents/ApComponents/ApInvoiceTable.vue'
import UploadFileComp from '@/components/bookkeepingComponents/UploadFileComp.vue'
import UploadFileCompMx from '@/components/bookkeepingComponents/UploadFileCompMx.vue'
import OcrInvoiceTable from '@/components/bookkeepingComponents/ApComponents/OcrInvoiceTable.vue'
import ApInvoiceComponent from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponent.vue'
import ApInvoiceComponentIntegration from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegration.vue'
import ApInvoiceComponentIntegrationMx from '@/components/bookkeepingComponents/ApComponents/ApInvoiceComponentIntegrationMx.vue'
import FileUploadResult from '@/components/bookkeepingComponents/FileUploadResult.vue'
import EditReocrd from '@/views/Bookkeeping/Ap/ApInvoiceFromPdf.vue'
import {Modal, message} from 'ant-design-vue'
import FileSaver from 'file-saver'
import {reactive, computed, ref, onBeforeMount, onUnmounted, onBeforeUnmount, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {Ap_Integration} from '@/lib/storage'
import {
    LoadingOutlined,
    SyncOutlined,
    CloseCircleOutlined,
    CloudUploadOutlined,
    ScanOutlined,
    DeleteOutlined,
} from '@ant-design/icons-vue'
import SvgIcon2x from '@/components/SvgIcon2x.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import {UserCompany, ApIntegration} from '@/lib/storage'
import {INTEGRATION_COUNTRY_CODES} from '@/constants/integrationCountryCode'

// type definition
interface ListPayload {
    fileType: string
    invoiceCreatedStatus: string
    pageIndex: number
    pageSize: number
}

interface PdfFile {
    fileId: string
    ocrType?: string
}

interface FileRecord {
    id: string
    file_name?: string
    file_url?: string
    xml_url?: string
    is_identified?: string
    file_type?: string
}

interface FileComment {
    id: string
    file_name?: string
    comment?: string
}

interface FileBaseObj {
    file_type: string
    file_name: string
    file_currency: string
    bank_account: string
    bank_month: string
}

interface FailOcrPdf extends FileRecord {
    errorMessage: string
}

// some init
const apIntegration: any = Ap_Integration.get() ?? 0
const store = useStore()
const router = useRouter()
const route = useRoute()
const i18n: Composer = i18nInstance.global
const userCompany: any = UserCompany.get() || []
const state = reactive({
    itemList: [] as string[],
    tableLoading: false,
    activeName: 'pending',
    pageType: '',
    invoiceQuery: {
        fileType: '2',
        invoiceCreatedStatus: '0',
    },
    finishingTableLoading: false,
    pendingTableLoading: false,
    uploadProcessing: false,
    pageQuery: {
        pageIndex: 1,
        pageSize: 10,
    },
    show: false,
    currentTabIndex: 'pending',
    showInvoiceDetail: false,
    showCompletedInvoice: false,
    showCreateInvoice: false,
    editRecordShow: false,
    current: {} as any,
    dialogVisible: false,
    selectedOcrFiles: [] as FileRecord[],
    currentOcrType: '', // temporary
    fileUploadResult: [] as any[],
    fileUploadResultTitle: '',
    showUploadErrorDiag: false,
    currentFileId: '',
})

const interval = ref<number>(0)

const restrictFileType: string[] = ['pdf', 'jpg', 'jpeg', 'png']

//  在这里添加 不显示 Massive Process 里 fetch 按钮的列表
const excludedCodes: string[] = ['38LR', '2603','XLIJ']
//  在这里添加 不显示 Massive Process 里 upload 按钮
const excludedUploadCodes: string[] = []

const currentUserCompanyQuery = {
    company_code: userCompany[0].code,
}
const childTable = ref<{fetchTableData: (index: string) => void}>()
const uploadComp = ref<{uploadFiles: () => void}>()
const modalUpload = ref<HTMLElement | null>(null)
const mailFetching = ref(false)

// vuex actions
// const fetchInvoicesList = async (data: ListPayload) => await store.dispatch('ApStore/fetchInvoicesList', data)
const fetchRemoteMailBox = (payload: any) => store.dispatch('ArApBrStore/fetchRemoteMailBox', payload)
const deletePdfWithId = (Id: string) => store.dispatch('ApStore/deletePdfWithIdV1', Id)
const updateCommentWithId = (payload: any) => store.dispatch('ApStore/updateUploadFileComment', payload)
const uploadApInvoicePdf = (data: FormData) => store.dispatch('ApStore/uploadApInvoicePdf', data)
// const fetchApInvoiceListWithPdf = (data: PdfFile) => store.dispatch('ApStore/fetchApInvoiceListWithPdf', data)
const ocrUploadedFileById = (data: any) => store.dispatch('ArApBrStore/ocrUploadedFileById', data)
const getFileBlobById = (data: any) => store.dispatch('ArApBrStore/getFileBlobByIdV1', data)
const updateActiveKey = (key: string) => store.commit('ApStore/updateActiveTabKey', key)

const handleClose = async () => {
    console.log('refresh table')
    state.editRecordShow = false
    await updateTable()
}
// // just in case
// const fetchInvoiceItem = (data: {fileId: string}) => {
//     return store.dispatch('ApStore/fetchApOcrResultByPdfId', data)
// }
// vuex mutations
const updatePdfInfo = (data: FileRecord) => store.commit('ArApBrStore/updatePdfInfo', data)

// vuex states
const invoicesList = computed(() => store.state.ApStore.invoicesList)
// const invoicesListByPdf = computed(() => store.state.ApStore.invoicesListByPdf)
// const ocrInvoicesListByPdf = computed(() => store.state.ApStore.ocrInvoicesListByPdf)

// common method

// keep disable the button if selected file are OCRed.
// const disableOcrButton = computed(() => !state.selectedOcrFiles.some(x => x.is_identified !== '1'))
const disableOcrButton = computed(() => true)
const ActiveKeyStore = computed(() => store.state.ApStore.activeTabKey)
const updateTable = async () => {
    try {
        state.activeName === 'pending' ? (state.pendingTableLoading = true) : (state.finishingTableLoading = true)
        const query = {
            ...state.invoiceQuery,
            ...state.pageQuery,
        }
        childTable.value?.fetchTableData(state.activeName)
    } catch (err) {
        console.log(err)
    } finally {
        state.activeName === 'pending' ? (state.pendingTableLoading = false) : (state.finishingTableLoading = false)
    }
}

// update table in silent for background
const updateTableNoLoading = async () => {
    try {
        const query = {
            ...state.invoiceQuery,
            ...state.pageQuery,
        }
        childTable.value?.fetchTableData(state.activeName)
    } catch (err) {
        console.log(err)
    }
}
//handleClick
const changeTab = (index: string) => {
    state.currentTabIndex = index
}

const pageChange = async (page: {pageIndex: number; pageSize: number}) => {
    state.pageQuery = {...page}
    // await updateTable()
}

// get the selected file list for ocr.
const updateSelectedOcrFiles = (records: FileRecord[]) => {
    state.selectedOcrFiles = [...records]
}

// uploadFiles() {
//     this.show = true
// },
// // duplicate with uploadFiles
const showUploadDiag = (bool = false) => (state.show = bool)

const closeUploadModal = () => {
    showUploadDiag(false)
    state.uploadProcessing = false
}

const showInvoiceByPdfDiag = (bool = false) => (state.showInvoiceDetail = bool)
const showCompletedInvoiceDialog = (bool: boolean) => (state.showCompletedInvoice = bool)
const apReverse = async () => {
    await updateTable()
}
const editInvoice = async (record: FileRecord) => {
    state.currentFileId = record.id
    if (record && record.file_url) {
        updatePdfInfo({
            id: record.id,
            file_url: record.file_url,
            xml_url: record.xml_url,
            file_name: record.file_name,
            file_type: record.file_type,
        })
        // await fetchInvoiceItem({fileId: record.id})

        state.pageType = 'create'
        state.editRecordShow = true
        // await router.push({
        //     path: '/bookkeeping/ap/invoiceFromPdf',
        //     query: {
        //         fileId: record.id,
        //         pageType: 'create',
        //         pageIndex: state.pageQuery.pageIndex,
        //         pageSize: state.pageQuery.pageSize,
        //     },
        // })
    }
}
const deleteSelectPdf = async (recordList: FileRecord[]) => {
    // 设置按钮为禁用状态
    // disableOcrButton.value = true

    try {
        Modal.confirm({
            title: i18n.t('bkCommonTag.confirmation'),
            content: i18n.t('bkCommonTag.msgDeleteSelectConfirm'),

            // 在onOk回调函数中进行状态更新
            async onOk() {
                try {
                    for (const record of recordList) {
                        const response = await deletePdfWithId(record.id)
                        if (response.data.statusCode === 200) {
                            message.success(i18n.t('ApComponents.success'))
                        }
                    }
                    await updateTable()
                } catch (error) {
                    console.log(error)
                } finally {
                    // 将按钮重新设置为可用状态
                    // disableOcrButton.value = false
                    state.selectedOcrFiles = []
                }
            },
        })
    } catch (error) {
        console.log(error)
        // 如果发生错误，也要确保按钮处于可用状态
        state.selectedOcrFiles = []
        // disableOcrButton.value = false
    }
}

const deletePdf = (record: FileRecord) => {
    Modal.confirm({
        title: i18n.t('bkCommonTag.confirmation'),
        content: `The pdf file [ ${record.file_name} ] will be deleted!`,
        async onOk() {
            try {
                const response = await deletePdfWithId(record.id)
                if (response.data.statusCode === 200) {
                    message.success(i18n.t('ApComponents.success'))
                }
                // else {
                //     message.error({content: response.data.data})
                // }
                await updateTable()
            } catch (error) {
                console.log(error)
            }
        },
        okText: i18n.t('commonTag.confirm'),
        cancelText: i18n.t('commonTag.cancel'),
        okType: 'danger',
        onCancel() {
            return
        },
    })
}

const updateComment = async (record: FileComment) => {
    try {
        const response = await updateCommentWithId(record)
        if (response.data.statusCode === 200) {
            message.success(i18n.t('ApComponents.success'))
        }
        await updateTable()
    } catch (error) {
        console.log(error)
    }
}

const ocrFiles = async (records: FileRecord[], ocrType = 'ABBYY') => {
    records = records.filter(x => x.is_identified !== '1')
    if (records.length === 0) {
        return
    }
    // state.currentOcrFileNames.push(...records.map(x => x.fileName as string))
    state.dialogVisible = true
    state.currentOcrType = ocrType
    const ocrRequests = records.map(x => {
        const pdf = {
            file_id: x.id,
            company_code: userCompany[0].code,
        }
        return ocrUploadedFileById(pdf)
    })
    try {
        const responses = await Promise.all(ocrRequests)
        const failItems: FailOcrPdf[] = []
        responses.forEach((res, index) => {
            if (res.data.statusCode !== 201 && res.data.statusCode !== 200) {
                const failItem: FailOcrPdf = {
                    id: records[index].id,
                    errorMessage: res.data.message,
                    file_name: records[index].file_name,
                }
                failItems.push(failItem)
            }
        })

        if (failItems.length > 0) {
            message.warn({
                content: failItems.map(x => `${x.file_name} ${x.errorMessage}`).join('<br>'),
            })
        } else {
            message.success(i18n.t('ApComponents.success'))
        }
        await updateTable()
    } catch (error: any) {
        // message.error(error.response.data.message)
        console.log(error)
    } finally {
        state.dialogVisible = false
        // state.selectedOcrFiles = []
    }
}

const uploadInvoiceFiles = async () => {
    // const formData = new FormData()
    // const jsonStr: FileBaseObj[] = []
    //
    // uploadFiles.forEach(file => {
    //     const baseObj: FileBaseObj = {
    //         fileType: '2', // 1 for AP
    //         fileName: file.name,
    //         fileCurrency: '',
    //         bankAccount: '',
    //         bankMonth: '',
    //     }
    //     jsonStr.push(baseObj)
    //     formData.append('files', file)
    // })
    // formData.append('jsonStr', JSON.stringify(jsonStr))

    try {
        state.uploadProcessing = true
        const response = (await uploadComp.value?.uploadFiles()) as any
        if (response !== null) {
            if (response.data.statusCode === 200) {
                message.success(i18n.t('ApComponents.success'))
                showUploadDiag(false)
                state.uploadProcessing = false
                if (response.data.data.file_type.toString() === '8') {
                    updateActiveKey('finished')
                    changeTab('finished')
                    // reporting to created
                }
                // TODO: why use timeout
                // } else if (response.data.code === 2000 && response.data.data && response.data.data.length > 0) {
                //     state.fileUploadResult = [...response.data.data]
                //     state.fileUploadResultTitle = response.data.msg
                //     showUploadDiag(false)
                //     // await updateTable()
                //     state.showUploadErrorDiag = true
                //     state.uploadProcessing = false
            } else {
                // message.error({content: response.data.message})
                showUploadDiag(false)
                // await updateTable()
                state.uploadProcessing = false
            }
            // childTable.value?.fetchTableData(state.activeName)
        }
        state.uploadProcessing = false
    } catch (error: any) {
        message.error(error.response.data.message)
        state.uploadProcessing = false
    } finally {
        childTable.value?.fetchTableData(state.activeName)
    }
}

const viewFile = async (record: any) => {
    updatePdfInfo(record)
    console.log(state.current)
    // state.current = record
    showCompletedInvoiceDialog(true)
}

const viewCompletedInvoiceDetail = (record: any) => {
    state.current = {...record}
    showCompletedInvoiceDialog(true)
}

const downloadFile = async (record: any) => {
    if (record.id) {
        try {
            const response = await getFileBlobById(record.file_url)
            if (response.status === 200 && response.data.size > 0) {
                // const filename = record.file_name
                const filename = record.file_url.split('/').pop()
                // const blob = new Blob([response.data], {type: 'application/pdf;charset=utf-8'})
                const blob = new Blob([response.data], {
                    type: record.file_url.includes('.pdf')
                        ? 'application/pdf;charset=utf-8'
                        : 'image/jpeg;charset=utf-8',
                })
                FileSaver.saveAs(blob, filename)
            }
            // else {
            //     message.error({content: 'Invoice file is not available.'})
            // }
        } catch (e) {
            message.error({content: i18n.t('ApUploadInvoice.fillError')})
        }
    }
}

const fetchMailBox = async () => {
    mailFetching.value = true
    try {
        const response = await fetchRemoteMailBox(currentUserCompanyQuery)
        if (response.status === 200 || response.status === 201) {
            message.success(i18n.t('ApComponents.success'))
            if (interval.value > 0) {
                clearInterval(interval.value)
            }
            interval.value = window.setInterval(() => {
                updateTableNoLoading()
                console.log('fetch ap invoices background')
            }, 10000)
        } else {
            // message.error({content: 'Invoice file is not available.'})
        }
    } catch (e: any) {
        console.log(e)
    } finally {
        mailFetching.value = false
    }
}

//handleCancel
const resetStateForCancel = () => {
    state.showUploadErrorDiag = false
    state.fileUploadResult = []
    state.fileUploadResultTitle = ''
}

const createLoading = ref(false)
const from = ref('')
const currentInvoice = ref({})

interface ApInvoiceFrom {
    create_time: any
    id: any
    pay_method: string
    company_name: string
    reference_no: string
    invoice_due_date: string
    posting_date: string
    invoice_currency: string
    net_amount: number
    items: any[]
    invoice_comments: number
    gst: number
    pst: number
    qst: number
    total_fee: number
    po: string
    purpose: string
    company_code: string
}

const copyInvoiceAfterReverse = (record: any, event: any) => {
    currentInvoice.value = {...record}
    from.value = 'copy'
    state.showCreateInvoice = true
}

const save = async (form: ApInvoiceFrom) => {
    let response: any = {}
    try {
        createLoading.value = true
        if (form.id) {
            form.id = null
            delete form.create_time
        }
        form.items.forEach(item => {
            delete item.id
            delete item.create_time
        })
        if (apIntegration === 1 && form.po != null && form.po.length > 0) {
            console.log('ap form with po ============= ', form)
            if (INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && form.purpose === 'CREDIT_MEMO') {
                response = await store.dispatch('ApStore/createIntegrationInvoiceWithCmPoV1', form)
            } else {
                response = await store.dispatch('ApStore/createIntegrationInvoiceWithPoV1', form)
            }
        } else if (apIntegration === 1) {
            response = await store.dispatch('ApStore/createIntegrationInvoiceWithoutPoV1', form)
        } else {
            response = await store.dispatch('ApStore/createInvoiceV1', form)
        }
        if (response.data.statusCode === 200) {
            console.log('response.data========', response.data)
            // get contact if first useing
            const contactFirstUsing = await store.dispatch('ApStore/fetchContactFirstUsingV1', {
                company_code: response.data.data.company_code,
                contact_id: response.data.data.issuer_id,
            })

            if (apIntegration === 1) {
                store.dispatch('ApStore/saveSAPBP', {
                    company_code: response.data.data.company_code,
                    issuer_id: response.data.data.issuer_id,
                })
                await store.dispatch('ApStore/postSapMasterTopV1', {
                    company_code: response.data.data.company_code,
                    contact_id: response.data.data.issuer_id,
                    contact_name: response.data.data.issuer_name,
                    gl_account_code: response.data.data.items[0].sap_gl_account ?? '',
                    wbs_code: response.data.data.items[0].sap_wbs ?? '',
                    cost_center_code: response.data.data.items[0].sap_cost_center ?? '',
                    internal_order_code: response.data.data.items[0].sap_internal_order ?? '',
                    profit_center_code: response.data.data.items[0].sap_profit_center ?? '',
                    gl_account_name: '',
                    wbs_name: '',
                    cost_center_name: '',
                    internal_order_name: '',
                    profit_center_code_name: '',
                })
            }
            // create approval flow
            if (apIntegration === 1 && response.data.data.pay_method !== '2') {
                const resCreateFlow = await store.dispatch('ApStore/createApprovalFlowData', response.data.data)
                if (resCreateFlow.data.statusCode === 200) {
                    const resApproveFlow = await store.dispatch('ApStore/startApprovalFlow', response.data.data)
                    if (resApproveFlow.sap_status !== 2) {
                        message.error({content: resApproveFlow.sap_msg})
                        return
                    }
                }
            }
            // if (apIntegration === 1 && response.data.data.pay_method === '2' && (form.po == null || form.po == '')) {
            //     // send ap data to sap (credit)
            //     const resSap = await store.dispatch('ApStore/sendApDataToSap', response.data.data)
            //     if (resSap.data.sap_status !== 2) {
            //         message.error({content: resSap.data.sap_msg})
            //         return
            //     }
            // }
            if (contactFirstUsing === 0) {
                message.success(`success. ${i18n.t('ApComponents.contactFirstUsing')}`)
            } else {
                message.success(i18n.t('ApComponents.success'))
            }
            // invoiceForm.value.initFormData()
            const content_id = document.getElementById('content_id')
            if (content_id) {
                content_id.scrollIntoView({behavior: 'smooth'})
            }
        } else {
            // message.error({
            //     content: response.data.message,
            // })
        }
    } catch (error: any) {
        console.log(error)
    } finally {
        createLoading.value = false
        state.showCreateInvoice = false
        await updateTable()
    }
}
const dismiss = async () => {
    console.log('refresh table')
    currentInvoice.value = {}
    state.showCreateInvoice = false
    createLoading.value = false
}

onBeforeMount(() => {
    if (ActiveKeyStore.value) {
        state.activeName = ActiveKeyStore.value
    }
    if (router.currentRoute.value.query.activeName === 'finished') {
        state.activeName = 'finished'
        changeTab('finished')
    }
})

onUnmounted(() => {
    if (ActiveKeyStore.value) {
        // reset activekey
        updateActiveKey('pending')
    }
})

onBeforeUnmount(() => {
    if (interval.value > 0) {
        clearInterval(interval.value)
    }
})
</script>

<style lang="scss" scoped>
.no-scroll {
    overflow-y: hidden;
}

.history-page-wrap {
    border-radius: 12px;
    background-color: #fff;
    height: 100%;
    /* 设置容器的高度为父容器高度的80% */
    overflow-y: hidden;
    /* 隐藏纵向滚动条 */

    .history-page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // border-bottom: 1px solid #e2e2ea;
        padding: 0 20px;

        .search-group-wrap {
            display: flex;
            padding: 10px 10px;

            .search-input {
                width: 400px;
                border-radius: 4px;
                color: #676d7c;
            }

            .search-button {
                min-width: 90px;
                margin-left: 8px;
                font-size: 16px;
            }

            .search-button + .search-button {
                min-width: 113px;
            }

            .popover-wrap:deep(.ant-popover-placement-bottom) {
                width: 432px;
            }
        }

        .button-wrap {
            display: flex;
            align-items: center;
        }

        .add-button {
            font-size: 16px;
            padding: 6px 16px;

            &.pay .anticon {
                display: flex;
            }

            & + .add-button {
                margin-left: 8px;
            }
        }
    }

    .history-page-content {
        padding: 12px 20px;
        height: 100%;
        /* 设置容器的高度为父容器高度的80% */
        overflow-y: hidden;

        /* 隐藏纵向滚动条 */
        .main-head {
            display: flex;
            justify-content: space-between;
        }

        :deep(.ant-table-wrapper .ant-table .ant-table-tbody .ant-table-cell) {
            padding-top: 15px;
            padding-bottom: 15px;
            padding-left: 8px;
            padding-right: 8px;
        }

        .pagination-wrap {
            display: flex;
            margin-top: 12px;
            justify-content: flex-end;
            align-items: center;

            span {
                font-size: 12px;
                margin-left: 8px;
                line-height: 16px;
                color: #8c8c8c;
            }
        }
    }
}

.page-container-ap_upload {
    height: 100%;

    .content-box {
        height: 100%;

        .content-box-tables-block {
            // height: 100%;
            // display: flex;
            // flex-direction: column;

            .tabs-contents {
                border-radius: 12px;
                height: 100%;

                :deep(.ant-tabs-content-holder) {
                    .ant-tabs-content {
                        flex-direction: column;
                        height: 100%;
                    }
                }
            }

            .tab-panel-class {
                flex: 1;
                height: 100%;
            }
        }
    }
}

.content-box-btn_upload {
    display: flex;
    justify-content: flex-end;
    padding: 24px 20px 20px;
    background-color: #fff;
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
}

.btn-scan-all {
    margin-right: 20px;
}

// temporary
.bar-txt-rossum {
    color: #ff6600;
    margin-left: 8px;
}

.btn-cancel {
    border: 1px solid rgba(0, 79, 193, 1);
    font-size: 14px;
    color: #004fc1;
    text-align: center;
    line-height: 22px;
    font-weight: 400;
}

.ocr-notification-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 10px;

    .ocr-notification-title {
        span {
            white-space: pre;
            font-weight: bold;
        }
    }
}

.custom-divider {
    margin-top: 8px;
    margin-bottom: 6px;
}

:deep(.a-modal.notification-class) {
    background-color: #fafafa;
    border-radius: 6px;

    .a-modal-header {
        padding: 12px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        background-color: #1a94d0;
        color: white;
    }

    .a-modal-body {
        padding-top: 10px;
    }

    .a-modal-headerbtn {
        top: 10px;

        i {
            color: white;
        }
    }
}

.invoice-table-wrap {
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.02);
    border-radius: 12px;
    display: flex;
    flex-direction: column;

    .ant-spin-nested-loading {
        height: 100%;

        :deep(.ant-spin-container) {
            height: 100%;
        }
    }

    .content-box-btn_upload + .ant-spin-nested-loading {
        :deep(.page-container_ap_table) {
            padding-top: 0;
        }
    }
}

:deep(.ant-modal-body) {
    // height: 20vh;
    max-height: 100px;
}

:deep(.ant-tabs-tab-active) {
    display: flex;
    justify-content: center;
    width: 147px;
}

:deep(.ant-tabs-tab) {
    display: flex;
    justify-content: center;
    width: 147px;
}
.upload-invoice-modal {
    :deep(.ant-modal-footer) {
        margin-top: 80px;
    }
}
:deep(.custom-spin > div > .ant-spin .ant-spin-text) {
    top: calc(50% + 36px);
}
</style>
