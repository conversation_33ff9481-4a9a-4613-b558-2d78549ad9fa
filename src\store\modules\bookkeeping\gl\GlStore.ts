/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'
import serviceGL from '@/api/requestNewGL'
import serviceV1 from '@/api/requestNew'
import FileSaver from 'file-saver'

const http = service
const httpv1 = serviceV1
const httpGL = serviceGL

const GlStore = {
    namespaced: true,
    state: {
        glList: [],
        totalNumber: 0,
        pageType: 1,
        accountDescList: [],
    },
    mutations: {
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateGlList(state: {glList: any[]}, list: any) {
            state.glList = list
        },
        updatePage(state: {pageType: any}, num: any) {
            state.pageType = num
        },

        updateAccountDescList(state: {accountDescList: any[]}, list: any) {
            state.accountDescList = [...list]
        },
    },
    actions: {
        async fetchGlList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/gl/list', payload)
            store.commit('updateGlList', response.data.data.list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchGlListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            if (payload.status == '1') payload.status = '1,2'

            const response = await httpGL.get('/bk/post-journal-entry', {params: payload})
            // store.commit('updateGlList', response.data.rows)
            // store.commit('updateTotalFoundNumber', response.data.total)
            return response
        },
        async fetchGlListByInvoiceHistoryV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            if (payload.status == '1') payload.status = '1,2'

            const response = await httpGL.get('/bk/post-journal-entry', {params: payload})
            if (response.data.rows != null) {
                response.data.rows = response.data.rows.map((record: any) => ({
                    ...record,
                    module:
                        record.document_type === 0
                            ? 'GL'
                            : record.document_type === 1
                            ? 'Reverse'
                            : record.document_type === 2
                            ? 'Reconcile'
                            : record.document_type === 4
                            ? 'Reset'
                            : record.document_type === 5
                            ? 'Cash/Bank'
                            : record.document_type === 6
                            ? 'PartialClear'
                            : record.document_type === 7
                            ? 'Vacation earned and pay'
                            : record.document_type !== 3 ||
                              record.ledger_entry_line[0] === null ||
                              record.ledger_entry_line[0].dr_cr === null ||
                              record.ledger_entry_line[0].dr_cr === ''
                            ? ''
                            : record.ledger_entry_line[0].dr_cr === 'dr'
                            ? 'Sales'
                            : 'Purchase',
                }))
            }

            store.commit('updateGlList', response.data.rows)
            store.commit('updateTotalFoundNumber', response.data.total)
            return response
        },
        async getGlDraftOpening(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpGL.get('/bk/post-journal-entry', {params: payload})
            return response.data
        },
        //获取gl account coa Api
        async getCoaListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpGL.get('/bk/coa', {params: payload})
            const list = response.data.data.rows
            store.commit('updateAccountDescList', list)
            return response
        },
        //新增数据 new Api
        async saveGlAction(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpGL.post('/bk/post-journal-entry', payload)
            return response
        },
        //删除数据 new Api
        async deleteGl(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpGL.delete(`/bk/post-journal-entry?document_no=${payload}`)
        },
        //编辑数据 new Api
        async editGl(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpGL.put('/bk/post-journal-entry', payload)
            return response
        },
        //冲销
        async fetchReverseAction(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpGL.post('/bk/reverse-journal-entry', payload)
            return response
        },
        async postGl(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/gl/post', payload)
            return response
        },
        // async deleteGl(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        //     return http.delete(`/bk/gl/${payload}`)
        // },
        async exportGlInvoiceList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const subPath = `handleExport/gl-export`
            const response = await httpv1.get(subPath, {
                params: payload,
                responseType: 'blob',
            })
            // const filename = (response.headers['content-disposition'] || '')
            //     .split('filename=')[1]
            //     .replace('"', '')
            //     .replace('"', '')
            // console.log(payload)
            const formattedStartDate = payload.start.replace(/-/g, '')
            const formattedEndDate = payload.end.replace(/-/g, '')
            const filename = `${formattedStartDate}-${formattedEndDate} GL ${payload.name.name}.xlsx`

            const blob = new Blob([response.data], {
                type: response.headers['content-type'],
                // type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            })

            FileSaver.saveAs(blob, filename)
        },
    },
}

export default GlStore
