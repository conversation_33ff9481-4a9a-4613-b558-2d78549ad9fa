<!-- @format -->

<script lang="ts" setup>
import {UserCompany, Ap_Integration, UserInfo} from '@/lib/storage'
import i18nInstance from '@/locales/i18n'
import {type FormInstance, message} from 'ant-design-vue'
import {computed, nextTick, onMounted, reactive, ref, watch} from 'vue'
import type {Composer} from 'vue-i18n'
import {useStore} from 'vuex'

import * as _ from 'lodash'
import type {SelectProps} from 'ant-design-vue'
import SvgIcon from '@/components/SvgIcon.vue'
import {PlusOutlined} from '@ant-design/icons-vue'
import {INTEGRATION_COUNTRY_CODES, SPECIAL_USERS} from '@/constants/integrationCountryCode'

interface BankPayMethod {
    title: string
    bank_type: string
    transit_no: string
    swift_no: string
    routing_no: string
    iban_no: string
    account_number: string
    branch_no: string
    receiver_name: string
    is_default: boolean
}

const google = window.google
let googleAutocomplete: google.maps.places.Autocomplete | null = null
let googleAutocomplete2: google.maps.places.Autocomplete | null = null
let googleAutocomplete3: google.maps.places.Autocomplete | null = null

const i18n: Composer = i18nInstance.global
const store = useStore()
const props = defineProps({
    currentCustomer: {
        type: Object,
    },
    editMode: {
        type: Boolean,
        default: false,
    },
    contactName: {
        type: String,
        default: '',
    },
})
const formRef = ref<FormInstance>()
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const form = ref<any>({
    company_id: userCompany[0].id,
    company_code: userCompany[0].code,
    contact_name: props.contactName,
    office_receiver: '',
    office_street: '',
    office_city: '',
    office_province: '',
    office_country: '',
    office_postal_code: '',
    billing_receiver: '',
    billing_street: '',
    billing_city: '',
    billing_province: '',
    billing_country: '',
    billing_postal_code: '',
    billing_status: '0',
    shipping_receiver: '',
    shipping_street: '',
    shipping_city: '',
    shipping_province: '',
    shipping_country: '',
    shipping_postal_code: '',
    shipping_status: '0',
    tel: '',
    email: '',
    intercom: '0',
    expense_account: '0',
    gl_account: '0',
    bank_pay_method: [] as BankPayMethod[],
    income_tax_withholding_rate: 0,
    vat_withholding_rate: 0,
    col_ica_rate: 0,
    income_tax_withholding_threshold: 0,
    vat_withholding_threshold: 0,
    col_ica_threshold: 0,
    // bank_type: '',
    // transit_no: '',
    // branch_no: '',
    // bank_account: '',
    // receiver_name: '',
    // routing_no: '', //ACH
    // iban_no: '', //wire transfer IBAN
    // swift_no: '', //wire transfer swift
})
const inputState = reactive<{[key: string]: boolean}>({
    billing_receiver: false,
    billing_street: false,
    billing_city: false,
    billing_province: false,
    billing_country: false,
    billing_postal_code: false,
    shipping_receiver: false,
    shipping_street: false,
    shipping_city: false,
    shipping_province: false,
    shipping_country: false,
    shipping_postal_code: false,
})
const formLoading = ref(false)
const selectOptionWidth = ref('')
const shippingAsOfficeCheck = ref(true)
const billingAsOfficeCheck = ref(true)
const officeProvinceOptions = ref([] as any[])
const shipProvinceOptions = ref([] as any[])
const billProvinceOptions = ref([] as any[])
const apIntegration: any = Ap_Integration.get() ?? 0
// const activeNames = ref(['1'] as any[])
const activeNames = ref(apIntegration === 1 ? ['4'] : ['1'])
const intercomChk = ref(false)
const bankTypeOption = ref<SelectProps['options']>([
    {
        value: 'EFT',
        label: 'Canadian EFT',
    },
    {
        value: 'ACH',
        label: 'American ACH',
    },
    {
        value: 'IBAN',
        label: 'Wire Transfer IBAN',
    },
    {
        value: 'SWIFT',
        label: 'Wire Transfer SWIFT',
    },
    // {
    //     value: 'CHEQUE',
    //     label: 'Cheque',
    // },
    // {
    //     value: 'CREDIT',
    //     label: 'Credit Card',
    // },
])

// mapActions
const createSupplier = (payload: any) => store.dispatch('ContactStore/createContact', payload)
const updateSupplier = (payload: any) => store.dispatch('ContactStore/updateContact', payload)
const fetchProvinceInfo = (payload: any) => store.dispatch('CommonDropDownStore/fetchProvinceInfo', payload)
const emit = defineEmits(['updateData', 'updateNewContact', 'dismiss'])
const save = async () => {
    form.value.bank_pay_method = form.value.bank_pay_method?.filter((item: any) => !!item.title) ?? []
    if (await formRef.value?.validateFields()) {
        let response
        try {
            formLoading.value = true
            form.value['intercom'] = intercomChk.value ? '1' : '0'
            if (!props.editMode) {
                response = await createSupplier(form.value)
            } else {
                form.value.expense_account = intercomChk.value ? '2622' : '2621'
                response = await updateSupplier(form.value)
            }
            if (response.status === 201 || response.status === 200) {
                message.success(i18n.t('ApComponents.success'))
                emit('updateNewContact', response)
                emit('updateData')
                cancel()
            } else {
                // message.error({
                //     content: response.statusText || 'failed',
                //     duration: 3,
                // })
            }
        } catch (err: any) {
            console.log(err)
            // message.error({
            //     content: err.response.data.message,
            //     duration: 3,
            // })
        } finally {
            formLoading.value = false
        }
    } else {
        return false
    }
}
const cancel = () => {
    emit('dismiss')
}

const googleEvent = () => {
    if (!google) return

    const autocompleteInput: any = document.getElementById('office_street')
    const autocompleteInput2: any = document.getElementById('shipping_street')
    const autocompleteInput3: any = document.getElementById('billing_street')
    // let autocompleteInput = document.getElementById("officeReceiver")

    googleAutocomplete = new google.maps.places.Autocomplete(autocompleteInput, {
        // componentRestrictions: {country: ['ca', 'us']},
        fields: ['address_components', 'geometry'],
        types: ['address'],
    })

    googleAutocomplete2 = new google.maps.places.Autocomplete(autocompleteInput2, {
        // componentRestrictions: {country: ['ca', 'us']},
        fields: ['address_components', 'geometry'],
        types: ['address'],
    })

    googleAutocomplete3 = new google.maps.places.Autocomplete(autocompleteInput3, {
        // componentRestrictions: {country: ['ca', 'us']},
        fields: ['address_components', 'geometry'],
        types: ['address'],
    })
    // use array function instead of normal "function () {}"
    googleAutocomplete.addListener('place_changed', async () => {
        const place = googleAutocomplete!.getPlace()
        const realPlace = {
            address_components: [],
            ...place,
        }
        const mapping: any = {
            locality: 'office_city',
            country: 'office_country',
            administrative_area_level_1: 'office_province',
            postal_code: 'office_postal_code',
        }
        const mappingStreet = {
            street_number: 'office_street',
        }

        for (const item in {...mapping, ...mappingStreet}) {
            form.value[mapping[item]] = ''
        }

        realPlace.address_components.forEach((component: {types: any[]; short_name: any; long_name: any}) => {
            const type = component.types[0]
            if (Object.prototype.hasOwnProperty.call(mapping, type)) {
                form.value[mapping[type]] =
                    type === 'country' || type === 'administrative_area_level_1'
                        ? component.short_name
                        : component.long_name || ''
            }
        })
        await fetchProvinceInfo(form.value.office_country)
        const newProvinceList = _.cloneDeep(provinceOptions.value)
        if (newProvinceList.length !== officeProvinceOptions.value.length) officeProvinceOptions.value = newProvinceList
        const streetMapping = ['street_number', 'route']
        const streetWithNumber = realPlace.address_components
            .filter((component: {types: string[]}) => streetMapping.includes(component.types[0]))
            .map((item: {long_name: any}) => item.long_name)
        const fullStreetAdd = streetWithNumber.join(', ')
        form.value[mappingStreet['street_number']] = fullStreetAdd || ''
    })
    googleAutocomplete2.addListener('place_changed', async () => {
        const place = googleAutocomplete2!.getPlace()
        const realPlace = {
            address_components: [],
            ...place,
        }
        const mapping: any = {
            locality: 'shipping_city',
            country: 'shipping_country',
            administrative_area_level_1: 'shipping_province',
            postal_code: 'shipping_postal_code',
        }
        const mappingStreet = {
            street_number: 'shipping_street',
        }

        for (const item in {...mapping, ...mappingStreet}) {
            form.value[mapping[item]] = ''
            inputState[mapping[item]] = true
        }

        realPlace.address_components.forEach((component: {types: any[]; short_name: any; long_name: any}) => {
            const type = component.types[0]
            if (Object.prototype.hasOwnProperty.call(mapping, type)) {
                form.value[mapping[type]] =
                    type === 'country' || type === 'administrative_area_level_1'
                        ? component.short_name
                        : component.long_name || ''
            }
        })
        await fetchProvinceInfo(form.value.shipping_country)
        const newProvinceList = _.cloneDeep(provinceOptions.value)
        if (newProvinceList.length !== officeProvinceOptions.value.length) officeProvinceOptions.value = newProvinceList
        const streetMapping = ['street_number', 'route']
        const streetWithNumber = realPlace.address_components
            .filter((component: {types: string[]}) => streetMapping.includes(component.types[0]))
            .map((item: {long_name: any}) => item.long_name)
        const fullStreetAdd = streetWithNumber.join(', ')
        form.value[mappingStreet['street_number']] = fullStreetAdd || ''
    })
    googleAutocomplete3.addListener('place_changed', async () => {
        const place = googleAutocomplete3!.getPlace()
        const realPlace = {
            address_components: [],
            ...place,
        }
        const mapping: any = {
            locality: 'billing_city',
            country: 'billing_country',
            administrative_area_level_1: 'billing_province',
            postal_code: 'billing_postal_code',
        }
        const mappingStreet = {
            street_number: 'billing_street',
        }

        for (const item in {...mapping, ...mappingStreet}) {
            form.value[mapping[item]] = ''
            inputState[mapping[item]] = true
        }

        realPlace.address_components.forEach((component: {types: any[]; short_name: any; long_name: any}) => {
            const type = component.types[0]
            if (Object.prototype.hasOwnProperty.call(mapping, type)) {
                form.value[mapping[type]] =
                    type === 'country' || type === 'administrative_area_level_1'
                        ? component.short_name
                        : component.long_name || ''
            }
        })
        await fetchProvinceInfo(form.value.billing_country)
        const newProvinceList = _.cloneDeep(provinceOptions.value)
        if (newProvinceList.length !== officeProvinceOptions.value.length) officeProvinceOptions.value = newProvinceList
        const streetMapping = ['street_number', 'route']
        const streetWithNumber = realPlace.address_components
            .filter((component: {types: string[]}) => streetMapping.includes(component.types[0]))
            .map((item: {long_name: any}) => item.long_name)
        const fullStreetAdd = streetWithNumber.join(', ')
        form.value[mappingStreet['street_number']] = fullStreetAdd || ''
    })
}
const countryChangeHandle = async (countryCode: string, addr: any) => {
    formRef.value?.clearValidate()
    await fetchProvinceInfo(countryCode)
    const newProvince: any = _.cloneDeep(provinceOptions.value)
    console.log(officeProvinceOptions.value.length)
    console.log(newProvince.length)
    if (addr === 'office' && officeProvinceOptions.value.length !== newProvince.length) {
        officeProvinceOptions.value = newProvince
        googleAutocomplete?.setComponentRestrictions({country: [countryCode.toLocaleLowerCase()]})
        form.value.office_province = ''
    } else if (addr === 'ship' && shipProvinceOptions.value.length !== newProvince.length) {
        shipProvinceOptions.value = newProvince
        googleAutocomplete2?.setComponentRestrictions({country: [countryCode.toLocaleLowerCase()]})
        form.value.shipping_province = ''
    } else if (addr === 'bill' && billProvinceOptions.value.length !== newProvince.length) {
        billProvinceOptions.value = newProvince
        form.value.billing_province = ''
        googleAutocomplete3?.setComponentRestrictions({country: [countryCode.toLocaleLowerCase()]})
    }
}
const setOptionWidth = (event: any) => {
    nextTick(() => {
        selectOptionWidth.value = event.srcElement.offsetWidth + 'px'
    })
}
// computed mapState
const AccountDescOptions = computed(() => store.state.CommonDropDownStore.accountDescList)
const countryOptions = computed(() => store.state.CommonDropDownStore.countryOptions)
const provinceOptions = computed(() => store.state.CommonDropDownStore.provinceOptions)

const inputChange = (key: string, event: any) => {
    inputState[key] = _.isString(event.data) || _.isString(event)
}
const requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
const lengthLimitRule = (min = 1, max: number) => {
    return [
        {
            min,
            max,
            message: i18n.t('bkCommonTag.msgLengthRule') + `${min} - ${max}`,
            trigger: 'blur',
        },
    ]
}
const officePostalCodeValidator = async (rule: any, value: any) => {
    if (!value) {
        return Promise.resolve()
    }
    if (form.value.office_country === 'US') {
        if (/(^\d{5}$)|(^\d{5}-\d{4})$/.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(i18n.t('CustomerForm.USA'))
        }
    } else if (form.value.office_country === 'CA') {
        if (/^[A-Za-z]\d[A-Za-z]\s{1}\d[A-Za-z]\d$/.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(i18n.t('CustomerForm.CA'))
        }
    } else if (!form.value.office_country) {
        return Promise.resolve()
    }
}
const shippingPostalCodeValidator = async (rule: any, value: any) => {
    if (!value) {
        return Promise.resolve()
    }
    if (form.value.shipping_country === 'US') {
        if (/(^\d{5}$)|(^\d{5}-\d{4})$/.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(i18n.t('CustomerForm.USA'))
        }
    } else if (form.value.shipping_country === 'CA') {
        if (/^[A-Za-z]\d[A-Za-z]\s{1}\d[A-Za-z]\d$/.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(i18n.t('CustomerForm.CA'))
        }
    } else if (!form.value.shipping_country) {
        return Promise.resolve()
    }
}
const billingPostalCodeValidator = async (rule: any, value: any) => {
    if (!value) {
        return Promise.resolve()
    }
    if (value && form.value.billing_country === 'US') {
        if (/(^\d{5}$)|(^\d{5}-\d{4})$/.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(i18n.t('CustomerForm.USA'))
        }
    } else if (value && form.value.billing_country === 'CA') {
        if (/^[A-Za-z]\d[A-Za-z]\s{1}\d[A-Za-z]\d$/.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(i18n.t('CustomerForm.CA'))
        }
    } else if (!form.value.billing_country) {
        return Promise.resolve()
    }
}
const rules = reactive({
    office_receiver: [...lengthLimitRule(1, 50)],
    shipping_receiver: [...lengthLimitRule(1, 50)],
    billing_receiver: [...lengthLimitRule(1, 50)],
    contact_name: [...requireRule(i18n.t('bkSupplier.company')), ...lengthLimitRule(1, 50)],
    tel: [...lengthLimitRule(1, 18)],
    email: [...lengthLimitRule(1, 50), {type: 'email', message: i18n.t('bkCommonTag.msgEmailRule'), trigger: 'blur'}],
    office_street: [...lengthLimitRule(1, 50)],
    office_city: [...lengthLimitRule(1, 50)],
    office_province: [...requireRule(i18n.t('bkSupplier.province'))],
    office_country: [...requireRule(i18n.t('bkSupplier.country'))],
    office_postal_code: [...lengthLimitRule(1, 10), {validator: officePostalCodeValidator, trigger: 'blur'}],
    shipping_street: [...lengthLimitRule(1, 50)],
    shipping_city: [...lengthLimitRule(1, 50)],
    shipping_postal_code: [...lengthLimitRule(1, 10), {validator: shippingPostalCodeValidator, trigger: 'blur'}],
    billing_street: [...lengthLimitRule(1, 50)],
    billing_city: [...lengthLimitRule(1, 50)],
    billing_postal_code: [...lengthLimitRule(1, 10), {validator: billingPostalCodeValidator, trigger: 'blur'}],
})
watch(
    () => form.value.contact_name,
    current => {
        if (current) {
            form.value.contact_name = current.toUpperCase()
        }
    },
)
watch(
    () => shippingAsOfficeCheck,
    current => {
        if (current) {
            form.value.shipping_receiver = form.value.office_receiver
            form.value.shipping_street = form.value.office_street
            form.value.shipping_city = form.value.office_city
            form.value.shipping_province = form.value.office_province
            form.value.shipping_country = form.value.office_country
            form.value.shipping_postal_code = form.value.office_postal_code
        }
    },
)
watch(
    () => billingAsOfficeCheck,
    current => {
        if (current) {
            form.value.billing_receiver = form.value.office_receiver
            form.value.billing_street = form.value.office_street
            form.value.billing_city = form.value.office_city
            form.value.billing_province = form.value.office_province
            form.value.billing_country = form.value.office_country
            form.value.billing_postal_code = form.value.office_postal_code
        }
    },
)
watch(
    () => form.value.office_receiver,
    current => {
        if (current && shippingAsOfficeCheck.value) {
            form.value.shipping_receiver = current
        }
        if (current && billingAsOfficeCheck.value) {
            form.value.billing_receiver = current
        }
    },
)
watch(
    () => form.value.office_street,
    current => {
        if (current && shippingAsOfficeCheck.value && !inputState.shipping_street) {
            form.value.shipping_street = current
        }
        if (current && billingAsOfficeCheck.value && !inputState.billing_street) {
            form.value.billing_street = current
        }
    },
)
watch(
    () => form.value.office_city,
    current => {
        if (current && shippingAsOfficeCheck.value && !inputState.shipping_city) {
            form.value.shipping_city = current
        }
        if (current && billingAsOfficeCheck.value && !inputState.billing_city) {
            form.value.billing_city = current
        }
    },
)
watch(
    () => form.value.office_province,
    current => {
        if (current && shippingAsOfficeCheck.value && !inputState.shipping_province) {
            form.value.shipping_province = current
            shipProvinceOptions.value = officeProvinceOptions.value
        }
        if (current && billingAsOfficeCheck.value && !inputState.billing_province) {
            form.value.billing_province = current
            billProvinceOptions.value = officeProvinceOptions.value
        }
    },
)
watch(
    () => form.value.office_country,
    current => {
        if (current && shippingAsOfficeCheck.value && !inputState.shipping_country) {
            form.value.shipping_country = current
        }
        if (current && billingAsOfficeCheck.value && !inputState.billing_country) {
            form.value.billing_country = current
        }
    },
)
watch(
    () => form.value.office_postal_code,
    current => {
        if (current && shippingAsOfficeCheck.value && !inputState.shipping_postal_code) {
            form.value.shipping_postal_code = current
        }
        if (current && billingAsOfficeCheck.value && !inputState.billing_postal_code) {
            form.value.billing_postal_code = current
        }
    },
)
onMounted(async () => {
    googleEvent()

    if (props.editMode) {
        form.value = JSON.parse(JSON.stringify(props.currentCustomer))
    }

    const newProvince: any = _.cloneDeep(provinceOptions.value)

    officeProvinceOptions.value = []
    shipProvinceOptions.value = []
    billProvinceOptions.value = []
    if (form.value.office_country) {
        await fetchProvinceInfo(form.value.office_country)
        officeProvinceOptions.value = _.cloneDeep(provinceOptions.value)
    }
    if (form.value.shipping_country) {
        await fetchProvinceInfo(form.value.shipping_country)
        shipProvinceOptions.value = _.cloneDeep(provinceOptions.value)
    }
    if (form.value.billing_country) {
        await fetchProvinceInfo(form.value.billing_country)
        billProvinceOptions.value = _.cloneDeep(provinceOptions.value)
    }

    intercomChk.value = form.value.intercom === '1' ? true : false
})
const filterOption = (input: string, option: any) => {
    return option.key.toString().toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const addPayMethod = () => {
    form.value.bank_pay_method = form.value.bank_pay_method || []
    form.value.bank_pay_method.push({
        title: '',
        bank_type: '',
        transit_no: '',
        swift_no: '',
        routing_no: '',
        iban_no: '',
        account_number: '',
        branch_no: '',
        receiver_name: '',
        is_default: form.value.bank_pay_method.length === 0 ? 1 : 0,
    })
}

const removePayMethod = (index: number) => {
    form.value.bank_pay_method.splice(index, 1)
}

const isDefaultChange = (index: number) => {
    for (let i = 0; i < form.value.bank_pay_method.length; i++) {
        if (i !== index) {
            form.value.bank_pay_method[i].is_default = false
        }
    }
}
const validateNumberInput = (event: Event, field: string) => {
    const target = event.target as HTMLInputElement
    const value = target.value
    // 只允许数字和小数点
    const filteredValue = value.replace(/[^\d.]/g, '')
    // 确保只有一个小数点
    const parts = filteredValue.split('.')
    if (parts.length > 2) {
        form.value[field] = parts[0] + '.' + parts.slice(1).join('')
    } else {
        form.value[field] = filteredValue
    }
}
</script>
<template>
    <div class="supplier-form-wrap">
        <a-form
            ref="formRef"
            :model="form"
            :layout="'vertical'"
            :rules="rules"
            label-width="auto"
            label-position="top"
            class="form-box"
            :loading="formLoading"
        >
            <!-- sap -->
            <a-row v-if="apIntegration === 1" :gutter="[24, 24]" class="customer-form-block">
                <a-col :span="12">
                    <a-form-item name="contact_id" :label="i18n.t('bkAccountingAndBanking.bpNo')">
                        <a-input v-model:value="form.contact_id" :disabled="true"></a-input>
                    </a-form-item>
                </a-col>
            </a-row>
            <a-row :gutter="[24, 24]" class="customer-form-block">
                <a-col :span="12">
                    <a-form-item name="contact_name" :label="i18n.t('bkSupplier.company')">
                        <a-input :disabled="apIntegration === 1" v-model:value="form.contact_name"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item name="tel" :label="i18n.t('bkSupplier.tel')">
                        <a-input :disabled="apIntegration === 1" v-model:value="form.tel"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item name="email" :label="i18n.t('bkSupplier.email')">
                        <a-input :disabled="apIntegration === 1" v-model:value="form.email"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item name="office_receiver" :label="i18n.t('bkSupplier.supplierAddr')">
                        <a-input :disabled="apIntegration === 1" v-model:value="form.office_receiver"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12" v-if="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && SPECIAL_USERS.includes(userInfo?.account)">
                    <a-form-item name="income_tax_withholding_rate" :label="i18n.t('bkAp.mxIsr')">
                        <a-input v-model:value="form.income_tax_withholding_rate" @input="validateNumberInput($event, 'income_tax_withholding_rate')"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12" v-if="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && SPECIAL_USERS.includes(userInfo?.account)">
                    <a-form-item name="income_tax_withholding_threshold" :label="i18n.t('bkAp.mxIsrThreshold')">
                        <a-input v-model:value="form.income_tax_withholding_threshold" @input="validateNumberInput($event, 'income_tax_withholding_threshold')"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12" v-if="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && SPECIAL_USERS.includes(userInfo?.account)">
                    <a-form-item name="vat_withholding_rate" :label="i18n.t('bkAp.mxIva')">
                        <a-input v-model:value="form.vat_withholding_rate" @input="validateNumberInput($event, 'vat_withholding_rate')"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12" v-if="INTEGRATION_COUNTRY_CODES.includes(userCompany[0].country) && SPECIAL_USERS.includes(userInfo?.account)">
                    <a-form-item name="vat_withholding_threshold" :label="i18n.t('bkAp.mxIvaThreshold')">
                        <a-input v-model:value="form.vat_withholding_threshold" @input="validateNumberInput($event, 'vat_withholding_threshold')"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12" v-if="['COL'].includes(userCompany[0].country) && SPECIAL_USERS.includes(userInfo?.account)">
                    <a-form-item name="col_ica_rate" :label="i18n.t('bkAp.colIca')">
                        <a-input v-model:value="form.col_ica_rate" @input="validateNumberInput($event, 'col_ica_rate')"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12" v-if="['COL'].includes(userCompany[0].country) && SPECIAL_USERS.includes(userInfo?.account)">
                    <a-form-item name="col_ica_threshold" :label="i18n.t('bkAp.colIcaThreshold')">
                        <a-input v-model:value="form.col_ica_threshold" @input="validateNumberInput($event, 'col_ica_threshold')"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-checkbox :disabled="apIntegration === 1" v-model:checked="intercomChk">INTERCOMPANY</a-checkbox>
                </a-col>
            </a-row>
            <a-divider class="customer-form-divider" />
            <!-- office address, shipping address and billing address collapse-tabs block  -->
            <a-collapse class="customer-form-block" v-model:activeKey="activeNames" expand-icon-position="right" ghost>
                <a-collapse-panel :forceRender="true" key="1" :header="i18n.t('bkCustomer.officeAddress')">
                    <!-- office address -->
                    <a-row :gutter="[24, 24]">
                        <a-col :span="12">
                            <a-form-item name="office_country" :label="i18n.t('bkSupplier.country')">
                                <a-select
                                    :disabled="apIntegration === 1"
                                    :placeholder="i18n.t('bkSupplier.msgPhrSelect')"
                                    v-model:value="form.office_country"
                                    show-search
                                    :filter-option="filterOption"
                                    @change="countryChangeHandle($event, 'office')"
                                >
                                    <a-select-option v-for="item in countryOptions" :key="item.value" :value="item.key"
                                        >{{ item.value }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="office_province" :label="i18n.t('bkSupplier.province')">
                                <a-select
                                    :disabled="apIntegration === 1"
                                    :placeholder="i18n.t('bkSupplier.msgPhrSelect')"
                                    v-model:value="form.office_province"
                                    show-search
                                    :filter-option="filterOption"
                                >
                                    <a-select-option
                                        v-for="item in officeProvinceOptions"
                                        :key="item.value"
                                        :value="item.key"
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="office_street" :label="i18n.t('bkSupplier.street')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.office_street"
                                    id="office_street"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                ></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="office_city" :label="i18n.t('bkSupplier.city')">
                                <a-input :disabled="apIntegration === 1" v-model:value="form.office_city"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="office_postal_code" :label="i18n.t('bkSupplier.postalCode')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.office_postal_code"
                                    :maxlength="10"
                                >
                                </a-input>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-collapse-panel>
                <a-collapse-panel :forceRender="true" key="2" :header="i18n.t('bkCustomer.shippingAddress')">
                    <a-checkbox
                        :disabled="apIntegration === 1"
                        v-show="false"
                        v-model:checked="shippingAsOfficeCheck"
                        >{{ i18n.t('bkSupplier.sameAsOffice') }}</a-checkbox
                    >
                    <!-- shipping address -->
                    <a-row :gutter="[24, 24]">
                        <a-col :span="12" v-show="false">
                            <a-form-item name="shipping_receiver" :label="i18n.t('bkSupplier.supplierAddr')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.shipping_receiver"
                                ></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="shipping_country" :label="i18n.t('bkSupplier.country')">
                                <a-select
                                    :disabled="apIntegration === 1"
                                    :placeholder="i18n.t('bkSupplier.msgPhrSelect')"
                                    v-model:value="form.shipping_country"
                                    show-search
                                    :filter-option="filterOption"
                                    @change="
                                        countryChangeHandle($event, 'ship'), inputChange('shipping_country', $event)
                                    "
                                >
                                    <a-select-option v-for="item in countryOptions" :key="item.value" :value="item.key"
                                        >{{ item.value }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="shipping_province" :label="i18n.t('bkSupplier.province')">
                                <a-select
                                    :disabled="apIntegration === 1"
                                    :placeholder="i18n.t('bkSupplier.msgPhrSelect')"
                                    v-model:value="form.shipping_province"
                                    show-search
                                    :filter-option="filterOption"
                                    @change="inputChange('shipping_province', $event)"
                                >
                                    <a-select-option
                                        v-for="item in shipProvinceOptions"
                                        :key="item.value"
                                        :value="item.key"
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="shipping_street" :label="i18n.t('bkSupplier.street')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.shipping_street"
                                    id="shipping_street"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    @change="inputChange('shipping_street', $event)"
                                ></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="shipping_city" :label="i18n.t('bkSupplier.city')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.shipping_city"
                                    @change="inputChange('shipping_city', $event)"
                                ></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="shipping_postal_code" :label="i18n.t('bkSupplier.postalCode')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.shipping_postal_code"
                                    :maxlength="10"
                                    @change="inputChange('shipping_postal_code', $event)"
                                >
                                </a-input>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-collapse-panel>
                <a-collapse-panel :forceRender="true" key="3" :header="i18n.t('bkCustomer.billingAddress')">
                    <a-checkbox :disabled="apIntegration === 1" v-show="false" v-model:checked="billingAsOfficeCheck">{{
                        i18n.t('bkSupplier.sameAsOffice')
                    }}</a-checkbox>
                    <!-- billing address -->
                    <a-row :gutter="[24, 24]">
                        <a-col :span="12" v-show="false">
                            <a-form-item name="billing_receiver" :label="i18n.t('bkSupplier.supplierAddr')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.billing_receiver"
                                ></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="billing_country" :label="i18n.t('bkSupplier.country')">
                                <a-select
                                    :disabled="apIntegration === 1"
                                    :placeholder="i18n.t('bkSupplier.msgPhrSelect')"
                                    v-model:value="form.billing_country"
                                    show-search
                                    :filter-option="filterOption"
                                    @change="
                                        countryChangeHandle($event, 'bill'), inputChange('billing_country', $event)
                                    "
                                >
                                    <a-select-option v-for="item in countryOptions" :key="item.value" :value="item.key"
                                        >{{ item.value }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="billing_province" :label="i18n.t('bkSupplier.province')">
                                <a-select
                                    :disabled="apIntegration === 1"
                                    :placeholder="i18n.t('bkSupplier.msgPhrSelect')"
                                    v-model:value="form.billing_province"
                                    show-search
                                    :filter-option="filterOption"
                                    @change="inputChange('billing_province', $event)"
                                >
                                    <a-select-option
                                        v-for="item in billProvinceOptions"
                                        :key="item.value"
                                        :value="item.key"
                                        >{{ item.value }}</a-select-option
                                    >
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="billing_street" :label="i18n.t('bkSupplier.street')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.billing_street"
                                    id="billing_street"
                                    :placeholder="i18n.t('commonTag.msgInput')"
                                    @change="inputChange('billing_street', $event)"
                                ></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="billing_city" :label="i18n.t('bkSupplier.city')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.billing_city"
                                    @change="inputChange('billing_city', $event)"
                                ></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item name="billing_postal_code" :label="i18n.t('bkSupplier.postalCode')">
                                <a-input
                                    :disabled="apIntegration === 1"
                                    v-model:value="form.billing_postal_code"
                                    :maxlength="10"
                                    @change="inputChange('billing_postal_code', $event)"
                                >
                                    <!-- :disabled="!form.billing_country" -->
                                </a-input>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-collapse-panel>

                <!-- counting and banking -->
                <a-collapse-panel
                    v-if="apIntegration === 1"
                    :forceRender="true"
                    key="4"
                    :header="i18n.t('bkAccountingAndBanking.countingAndBanking')"
                >
                    <!-- <a-checkbox v-show="true" v-model:checked="billingAsOfficeCheck">{{
                        i18n.t('bkSupplier.sameAsOffice')
                    }}</a-checkbox> -->
                    <!-- billing address -->
                    <a-row :gutter="[24, 24]" v-for="(method, index) in form.bank_pay_method" :key="method.bank_type">
                        <a-col :span="2">
                            <a-form-item name="is_default" label="Default">
                                <a-radio
                                    :style="{display: 'flex', height: '30px', lineHeight: '30px'}"
                                    v-model:checked="method.is_default"
                                    @change="isDefaultChange(index)"
                                ></a-radio>
                            </a-form-item>
                        </a-col>
                        <a-col :span="4">
                            <a-form-item name="title" label="Bank Name">
                                <a-input v-model:value="method.title"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col :span="5">
                            <a-form-item name="bank_type" :label="i18n.t('bkAccountingAndBanking.bankType')">
                                <a-select
                                    :placeholder="i18n.t('bkSupplier.msgPhrSelect')"
                                    v-model:value="method.bank_type"
                                    show-search
                                    :filter-option="filterOption"
                                >
                                    <a-select-option
                                        v-for="item in bankTypeOption"
                                        :key="item.value"
                                        :value="item.value"
                                        >{{ item.label }}
                                    </a-select-option>
                                </a-select>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="method['bank_type'] == 'EFT'" :span="4">
                            <a-form-item name="transit_no" :label="i18n.t('bkAccountingAndBanking.transitNumber')">
                                <a-input v-model:value="method.transit_no"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="method['bank_type'] == 'EFT'" :span="4">
                            <a-form-item name="branch_no" :label="i18n.t('bkAccountingAndBanking.branchNumber')">
                                <a-input v-model:value="method.branch_no"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="method['bank_type'] == 'EFT'" :span="4">
                            <a-form-item name="account_number" :label="i18n.t('bkAccountingAndBanking.bankAccount')">
                                <a-input v-model:value="method.account_number"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col
                            v-if="
                                method['bank_type'] == 'ACH' ||
                                method['bank_type'] == 'IBAN' ||
                                method['bank_type'] == 'SWIFT'
                            "
                            :span="4"
                        >
                            <a-form-item name="receiver_name" :label="i18n.t('bkAccountingAndBanking.receiverName')">
                                <a-input v-model:value="method.receiver_name"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="method['bank_type'] == 'ACH'" :span="4">
                            <a-form-item name="rounting_no" :label="i18n.t('bkAccountingAndBanking.routingNumber')">
                                <a-input v-model:value="method.rounting_no"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="method['bank_type'] == 'IBAN'" :span="4">
                            <a-form-item name="IBAN No." :label="i18n.t('bkAccountingAndBanking.IBANNumber')">
                                <a-input v-model:value="method.iban_no"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col v-if="method['bank_type'] == 'SWIFT'" :span="4">
                            <a-form-item name="swift_no" :label="i18n.t('bkAccountingAndBanking.swiftNumber')">
                                <a-input v-model:value="method.swift_no"></a-input>
                            </a-form-item>
                        </a-col>
                        <a-col
                            v-if="
                                method['bank_type'] == 'ACH' ||
                                method['bank_type'] == 'IBAN' ||
                                method['bank_type'] == 'SWIFT'
                            "
                            :span="4"
                        ></a-col>
                        <a-col :span="1">
                            <a-button type="link" danger @click="removePayMethod(index)" style="padding-top: 42px">
                                <svg-icon name="icon_delete"></svg-icon>
                            </a-button>
                        </a-col>
                    </a-row>
                    <div style="text-align: center; padding-top: 40px">
                        <a-button class="invoice-add" type="primary" ghost @click="addPayMethod" :disabled="5 > 5">
                            <template #icon><plus-outlined /></template>
                            {{ i18n.t('bkAp.addPayMethod') }}
                        </a-button>
                    </div>
                </a-collapse-panel>
            </a-collapse>
        </a-form>
        <footer>
            <a-button @click="cancel" class="cancel-button" shape="round">
                {{ i18n.t('commonTag.cancel') }}
            </a-button>
            <a-button type="primary" shape="round" :loading="formLoading" @click="save">
                {{ editMode ? i18n.t('commonTag.save') : i18n.t('commonTag.save') }}
            </a-button>
        </footer>
    </div>
</template>
<style lang="scss" scoped>
.supplier-form-wrap {
    padding: 20px 24px 0px;

    .customer-form-block {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        :deep(.ant-collapse-item) {
            .ant-collapse-header {
                padding-left: 0;
                padding-right: 28px;
                font-size: 16px;
                border-bottom: 1px solid #e2e2ea;
                border-radius: 0;
            }

            .ant-collapse-content-box {
                padding-left: 0;
                padding-right: 0;
                border-bottom: 1px solid #e2e2ea;
            }
        }
    }

    .customer-form-divider {
        margin-top: 0px;
        margin-bottom: 0px;
        border-color: #e2e2ea;
    }

    footer {
        padding: 12px 0px;
        text-align: right;

        .cancel-button {
            border-color: #004fc1;
            color: #004fc1;
        }

        .ant-btn {
            min-width: 65px;
        }

        .ant-btn + .ant-btn {
            margin-left: 8px;
            min-width: 75px;
        }
    }
}

.invoice-add {
    width: 100%;
    margin-top: 8px;
    border-radius: 2px;
    border-style: dashed;
}
</style>
