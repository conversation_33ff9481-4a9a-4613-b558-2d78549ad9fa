<template>
    <a-modal :visible="visible" @update:visible="$emit('update:visible')" style="width: 80%; height: auto">
        <template #footer>
            <a-button key="format" @click="formatJson">Format</a-button>
            <a-button key="submit" type="primary" @click="handleOk">Save</a-button>
        </template>
        <a-form :layout="'vertical'" autocomplete="off">
            <a-form-item label="Reason Code Configuration">
                <a-textarea
                    v-model:value="jsonContent"
                    :rows="20"
                    :placeholder="'Please enter the configuration in JSON format'"
                />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import http from '@/api/requestNew'

const props = defineProps<{
    visible: boolean
    companyCode: string
}>()

const emit = defineEmits(['update:visible', 'save'])

const jsonContent = ref('')

// 监听 visible 变化，当对话框显示时获取数据
watch(() => props.visible, async (newVal) => {
    if (newVal) {
        try {
            const res = await http.get(`/prefectClient/reason_code/${props.companyCode}`)
            if (res.status === 200) {
                jsonContent.value = JSON.stringify(res.data, null, 2)
            } else {
                message.error('Failed to fetch reason code data')
            }
        } catch (error) {
            console.error('Error fetching reason code data:', error)
            message.error('Failed to fetch reason code data')
        }
    }
}, { immediate: true })

const formatJson = () => {
    try {
        const obj = JSON.parse(jsonContent.value)
        jsonContent.value = JSON.stringify(obj, null, 2)
    } catch (e) {
        message.error('Invalid JSON format')
    }
}

const handleOk = async () => {
    try {
        const config = JSON.parse(jsonContent.value)
        try {
            const res = await http.post(`/prefectClient/reason_code/${props.companyCode}`, config)
            if (res.status === 200 || res.status === 201) {
                message.success('Reason code saved successfully')
                emit('save', config)
                emit('update:visible', false)
            } else {
                message.error('Failed to save reason code')
            }
        } catch (error) {
            console.error('Error saving reason code:', error)
            message.error('Failed to save reason code')
        }
    } catch (e) {
        message.error('Invalid JSON format')
    }
}
</script>