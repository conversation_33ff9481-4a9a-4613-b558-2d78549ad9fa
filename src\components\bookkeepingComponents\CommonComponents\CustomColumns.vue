<!-- @format -->

<script setup lang="ts">
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {computed, ref} from 'vue'
import SvgIcon from '@/components/SvgIcon.vue'
import * as _ from 'lodash'

const i18n: Composer = i18nInstance.global
const props = defineProps<{
    customizeTable: string[]
    defaultTable: string[]
    prefix: string
}>()

const {customizeTable} = _.cloneDeep(props)

const emits = defineEmits(['save', 'dismiss'])

const customize = ref(customizeTable)

const tableList = computed(() => {
    return props.defaultTable.map(i => ({name: i, showed: customize.value.includes(i)}))
})

const cancel = () => {
    emits('dismiss')
}

const save = () => {
    emits('save', customize.value)
}

const hide = (name: string, index: number) => {
    customize.value = customize.value.filter(i => i !== name)
}

const show = (name: string, index: number) => {
    customize.value.push(name)
}
</script>

<template>
    <div class="columns-page-wrap">
        <div class="page-body">
            <div class="title">
                <span>
                    {{ i18n.t('columns.title') }}
                </span>
                <span>
                    {{ i18n.t('columns.operation') }}
                </span>
            </div>
            <div v-for="(i, index) in tableList" :key="i.name" class="item">
                <span>
                    {{ i18n.t(prefix + '.' + i.name) }}
                </span>
                <span>
                    <svg-icon v-if="i.showed" @click="hide(i.name, index)" name="icon_password2"></svg-icon>
                    <svg-icon v-else @click="show(i.name, index)" name="icon_password1"></svg-icon>
                </span>
            </div>
        </div>

        <a-divider class="page-divider" />
        <div class="page-footer">
            <a-button @click="cancel" size="small" shape="round"> {{ i18n.t('commonTag.cancel') }} </a-button>
            <a-button size="small" type="primary" shape="round" @click="save">
                {{ i18n.t('commonTag.save') }}
            </a-button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.columns-page-wrap {
    .page-body {
        padding: 20px 24px 0px;
        min-height: 500px;
        .title {
            font-weight: 700;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 8px;
        }
        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }
    }
    .page-divider {
        margin: 0;
    }
    .page-footer {
        padding: 12px 24px;
        text-align: right;
        .ant-btn {
            min-width: 65px;
        }
        .ant-btn + .ant-btn {
            margin-left: 8px;
        }
    }
}
</style>
