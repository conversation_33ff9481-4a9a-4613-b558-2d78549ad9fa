/** @format */

import service from '../../../../api/request'
import lodash from 'lodash'
import type {ActionContext} from 'vuex'
import servicev1 from '@/api/requestNew'

const http = service
const httpv1 = servicev1
const ReconciliationStore = {
    namespaced: true,
    state: {
        bankReconciliationList: [],
        totalNumber: 0,
        eStatementInfo: {},
        // reconciliationWithEsList: [],
    },
    mutations: {
        updateBankReconciliationList(state: {bankReconciliationList: any[]}, list: any) {
            state.bankReconciliationList = [...list]
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateEStatementInfo(state: {eStatementInfo: any}, info: any) {
            state.eStatementInfo = {...info}
        },
        // updateReconciliationWithEsList(state, list) {
        //   state.reconciliationWithEsList = [...list]
        // },
    },
    actions: {
        async fetchBrToReconciledList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {eStatementId: any},
        ) {
            const dataSort = (arr: any[]) => {
                const arr1 = arr[0]
                const arr2 = arr[1]
                const arrNew = lodash.differenceBy(arr2, arr1, 'id')
                if (arr.length > 1) {
                    arr[1] = arrNew
                }
                return arr
            }

            const response2 = await http.post(
                `/bk/br/auto/reconciliation/estatement/${payload.eStatementId}/invoice/list`,
                payload,
            )
            if (
                response2.data.code == 1000 &&
                response2.data.data.invoice.length > 0 &&
                response2.data.data.invoice[0].length > 0
            ) {
                // store.commit('updateBankReconciliationList', response2.data.data.invoice.flat())
                store.commit('updateBankReconciliationList', dataSort(response2.data.data.invoice))
                store.commit('updateTotalFoundNumber', response2.data.data.invoice[0].length)
                return response2
            } else {
                const response1 = await http.post(
                    `/bk/br/reconciliation/estatement/${payload.eStatementId}/invoice/list`,
                    payload,
                )
                /*
        for(var i=0;i<response1.data.data.list.length;i++){
          if(response1.data.data.list[i].deposit === null){
            response1.data.data.list[i].invoiceType = "1"
          }
        }
        */
                store.commit('updateBankReconciliationList', dataSort([response1.data.data.list]))
                store.commit('updateTotalFoundNumber', response1.data.data.totalCount)
                return response1
            }
        },
        async fetchBrToReconciledListbyhand(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {eStatementId: any},
        ) {
            const dataSort = (arr: any[]) => {
                const arr1 = arr[0]
                const arr2 = arr[1]
                const arrNew = lodash.differenceBy(arr2, arr1, 'id')
                if (arr.length > 1) {
                    arr[1] = arrNew
                }
                return arr
            }
            const response3 = await http.post(
                `/bk/br/reconciliation/estatement/${payload.eStatementId}/invoice/list`,
                payload,
            )
            store.commit('updateBankReconciliationList', dataSort([response3.data.data.list]))
            store.commit('updateTotalFoundNumber', response3.data.data.totalCount)
            return response3
        },
        async submitReconciliation(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            //const response = await http.post('/bk/br/reconciliation/old/submit', payload)
            const response = await http.post('/bk/br/reconciliation/submit', payload)
            return response
        },
        async submitReconciliationV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            //const response = await http.post('/bk/br/reconciliation/old/submit', payload)
            const response = await httpv1.post('/invoice-statement/api/v1/es-reconcile/submit', payload)
            return response
        },
        async submitReconciliationV1Integration(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            //const response = await http.post('/bk/br/reconciliation/old/submit', payload)
            const response = await httpv1.post('/invoice-statement/api/v1/es-reconcile/submit/integration', payload)
            return response
        },
        async submitReconciliationV1Integration8001(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post('/prefectClient/post_fi_doc', payload)
            return response
        },
        async submitReconciliationFTV1Integration8001(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post('/prefectClient/ft', payload)
            return response
        },
        async submitReconciliationEEV1Integration(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post('/prefectClient/ee', payload)
            return response
        },
        async submitReconciliationEEDepositV1Integration(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post('/prefectClient/ee_deposit', payload)
            return response
        },
        async submitReconciliationReverseV1Integration8001(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post(
                `/prefectClient/reverse_post_fi_doc?transaction_id=${payload.transaction_id}&posting_date=${payload.posting_date}&company_code=${payload.company_code}&sap_document_no=${payload.sap_document_no}`,
            )
            return response
        },
        async submitReconciliationReverseFTV1Integration(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post(
                `/prefectClient/reverse_post_fi_doc_ft?transaction_id=${payload.transaction_id}&posting_date=${payload.posting_date}&company_code=${payload.company_code}&sap_document_no=${payload.sap_document_no}`,
            )
            return response
        },
        async submitReconciliationEE(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post('/invoice-statement/api/v1/es-reconcile/submit/ee', payload)
            return response
        },

        async submitReconciliationWithEsList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await http.post('/bk/br/reconciliation/submitWithEsList', payload)
            return response
        },

        async updateIntegrationBrSapStatusV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any; status: any},
        ) {
            const response = await httpv1.patch(
                `/invoice-statement/api/v1/ap/integration/br/${payload.id}/sap/status`,
                payload,
            )
            return response
        },
        async sendBrDataToSap(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpv1.post(`/handleexport/post_fi_doc?transaction_id=${payload.transaction_id}`)
            return response
        },
        async sendBrReverseDataToSap(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpv1.post(
                `/handleexport/reverse_post_fi_doc?fidoc_no=${payload.sap_document_no}&posting_date=${payload.posting_date}`,
            )
            return response
        },

        async getReconciliationWithEsList(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any},
        ) {
            return await http.post(`bk/br/auto/reconciliation/invoice/${payload.id}/estatement/list`, payload)
            // store.commit('updateReconciliationWithEsList', response.data.data[0])
            // return response
        },
        async getReconciliationWithEsListManual(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {id: any},
        ) {
            return await http.post(`/bk/br/reconciliation/invoice/${payload.id}/estatment/list`, payload)
        },
        async submitReconciliation2(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: {
                id: any
                brType: string
                createTime: string | any[]
                date: any
                deposit: number | null
                withdrawal: any
                bankAccount: any
                matchList: {invoice: any[]}
            },
        ) {
            console.log(payload, 111)
            // function getNowFormatDate() {
            //   let date = new Date();
            //   let separator = "-";
            //   let year = date.getFullYear();
            //   let month = date.getMonth() + 1;
            //   let strDate = date.getDate();
            //   if (month >= 1 && month <= 9) {
            //     month = "0" + month;
            //   }
            //   if (strDate >= 0 && strDate <= 9) {
            //     strDate = "0" + strDate;
            //   }
            //   return year + separator + month + separator + strDate;
            // }

            return http.post('/bk/br/reconciliation/submit', {
                statementId: payload.id,
                //"statementId": payload.matchList.eStatement[0].id,
                invoiceType: payload.brType === '0' ? '1' : '2',
                paymentDate: payload.createTime.slice(0, 10),
                postingDate: payload.date,
                esAmount: payload.brType === '0' ? payload.deposit : payload.withdrawal,
                //"esAmount": payload.matchList.eStatement[0].totalFee,
                bankAccount: payload.bankAccount,
                // "bpNumber": payload.matchList.invoice[0][0].bpNumber,
                brAmount: payload.deposit === 0 || payload.deposit === null ? payload.withdrawal : payload.deposit,
                invoiceList: payload.matchList.invoice[0],
            })
        },
    },
}

export default ReconciliationStore
