/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'
import servicev1 from '@/api/requestNew'

const http = service
const httpNew = servicev1

const HelpStore = {
    namespaced: true,
    state: {
        faqList: [],
        totalNumber: 0,
    },
    mutations: {
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateFaqList(state: {faqList: any[]}, list: any) {
            state.faqList = list
        },
    },
    actions: {
        //获取FAQ  Api
        async getFaqList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            let response = [] as any
            if (payload.message_ext0) {
                response = await httpNew.get(
                    `/logs/logs?type=4&$limit=${payload.$limit}&$skip=${payload.$skip}&message_ext0=${payload.message_ext0}`,
                )
            } else {
                response = await httpNew.get(`/logs/logs?type=4&$limit=${payload.$limit}&$skip=${payload.$skip}`)
            }

            const list = response.data.data
            const num = response.data.total
            store.commit('updateFaqList', list)
            store.commit('updateTotalFoundNumber', num)
            return response
        },
    },
}

export default HelpStore
