/** @format */

import type {ActionContext} from 'vuex'
import service from '@/api/requestNew'
import serviceKYC from '@/api/requestKyc'

const http = service
const httpKYC = serviceKYC

const TaxCalculationStore = {
    namespaced: true,
    state: {
        taxRatesList: [],
    },
    mutations: {
        updateTaxRatesList(state: {taxRatesList: any[]}, list: any) {
            state.taxRatesList = [...list]
        },
    },
    actions: {
        async fetchTaxRates(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const response = await http.get('/system-preferences/api/v1/tax-info?$limit=-1')

            store.commit('updateTaxRatesList', response.data)
            return response
        },

        async fetchTaxRates2(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            if (['65QW', '8888', '66QW', '67QW', '68QW'].includes(payload.companyCode)) {
                const res = [
                    {
                        alias: 'VAT',
                        category: 'Tax Chart',
                        categoryName: 'Country Tax',
                        fieldName: 'VAT',
                        value: 16,
                    },
                ]
                store.commit('updateTaxRatesList', res)
                return res
            }
            const response = await httpKYC.post('/taxConf/queryTax', payload)

            store.commit('updateTaxRatesList', response.data.body?.taxes || [])
            return response
        },
    },
}

export default TaxCalculationStore
