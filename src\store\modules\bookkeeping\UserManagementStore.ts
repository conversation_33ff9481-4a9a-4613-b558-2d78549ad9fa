/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../api/request'
import serviceNew from '@/api/requestNew'

const http = service
const httpv1 = serviceNew

const UserManagementStore = {
    namespaced: true,
    state: {
        companyInfo: {},
        userAccountInfo: {},
    },
    mutations: {
        updateCompanyInfo(state: {companyInfo: any}, info: any) {
            state.companyInfo = {...info}
        },
        updateUserAccountInfo(state: {userAccountInfo: any}, info: any) {
            state.userAccountInfo = {...info}
        },
    },
    actions: {
        async createCompany(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/company/add', payload)
            store.commit('updateCompanyInfo', response.data.data)
            return response
        },
        async createUserAccount(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/management/account/create', payload)
            store.commit('updateUserAccountInfo', response.data.data)
            return response
        },

        async changePassword(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            // store.commit('updateUserAccountInfo', response.data.data)
            return await httpv1.patch(`/users/api/users/${payload?.id}`, payload)
        },

        async getGoogleQrCode(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.get(`/users/api/v1/outer?action=google_qrcode&qr=${payload}`)
        },

        async sendGoogleAuthCode(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return await httpv1.post(`/users/api/v1/outer?action=google_auth`, payload)
        },
    },
}

export default UserManagementStore
