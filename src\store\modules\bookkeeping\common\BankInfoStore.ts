/** @format */

import type {ActionContext} from 'vuex'
import service from '../../../../api/request'
import serviceV1 from '../../../../api/requestNew'

const http = service
const httpV1 = serviceV1

const BankInfoStore = {
    namespaced: true,
    state: {
        bankList: [],
        totalNumber: 0,
        bankInfo: {},
    },
    mutations: {
        updateBankList(state: {bankList: any[]}, list: any) {
            state.bankList = [...list]
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
        updateBankInfo(state: {bankInfo: any[]}, info: any) {
            state.bankInfo = {...info}
        },
    },
    actions: {
        async fetchAllBankList(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/bank/list', payload)
            store.commit('updateBankList', response.data.data.list)
            store.commit('updateTotalFoundNumber', response.data.data.totalCount)
            return response
        },
        async fetchAllBankListV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpV1.get(`/users/api/v1/company-bank`, {params: payload})
            store.commit('updateBankList', response.data.data)
            store.commit('updateTotalFoundNumber', response.data.total)
            return response
        },
        async fetchAllPlaidBankListV1(
            store: ActionContext<{[key: string]: any}, Record<string, unknown>>,
            payload: any,
        ) {
            const response = await httpV1.get(`/invoice-statement/api/v1/plaid-bank-info/company-bank`, {
                params: payload,
            })
            store.commit('updateBankList', response.data.data)
            store.commit('updateTotalFoundNumber', response.data.paginated.total)
            return response
        },
        async fetchBankInfoV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpV1.get(`/users/api/v1/company-bank/${payload}`)
            store.commit('updateBankInfo', response.data)
            return response
        },
        async createBank(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.post('/bk/bank', payload)
        },
        async updateBank(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.put('/bk/bank', payload)
        },
        async createBankv1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpV1.post('/users/api/v1/company-bank/', payload)
        },
        async createPlaidBankv1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpV1.post('/invoice-statement/api/v1/plaid-bank-info/', payload)
        },
        async updateBankv1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpV1.put(`/users/api/v1/company-bank/${payload.id}`, payload)
        },
        async clearBankInfoOfCoA(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpV1.patch(`/users/api/v1/company-bank/`, {params: payload})
        },
        async updatePlaidBankv1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpV1.patch(`/invoice-statement/api/v1/plaid-bank-info/${payload.bank_id}`, payload)
        },
        async deleteBank(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/bk/bank/${payload}`)
        },
        async sendBankInfoEmail(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, bankId: any) {
            return http.put(`/bk/bank/email/${bankId}`)
        },
        async sendBankInfoEmailV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return httpV1.post(`/invoice-statement/api/v1/es-connect/email`, payload)
        },
    },
}

export default BankInfoStore
