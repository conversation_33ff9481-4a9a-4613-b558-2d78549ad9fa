/** @format */

import type {ActionContext} from 'vuex'
// import service from '../../../../api/request'
import service from '../../../../api/requestNew'

const http = service

const ContactStore = {
    namespaced: true,
    state: {
        contactList: [],
        totalNumber: 0,
    },
    mutations: {
        updateList(state: {contactList: any[]}, list: any) {
            state.contactList = [...list]
        },
        updateTotalFoundNumber(state: {totalNumber: any}, num: any) {
            state.totalNumber = num
        },
    },
    actions: {
        async fetchContacts(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            // const response = await http.get(
            //     `/system-preferences/api/v1/contact?$limit=${payload.limit}&$skip=${payload.skip}`,
            // )
            const response = await http.get(`/system-preferences/api/v1/contact`, {params: payload})
            const list = response.data.data.map((item: any) => {
                return {
                    ...item,
                }
            })
            store.commit('updateList', list)
            // change to 2w as no total now
            store.commit('updateTotalFoundNumber', response.data.total)
            return response
        },
        // async fetchContactNumber(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
        //     const response = await http.get(`/system-preferences/api/v1/contact`)
        //     const list = response.data.map((item: any) => {
        //         return {
        //             ...item,
        //         }
        //     })
        //     // change to 2w as no total now
        //     store.commit('updateTotalFoundNumber', list.length)
        // },
        async createContact(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.post('/system-preferences/api/v1/contact', payload)
        },
        async updateContact(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.put(`/system-preferences/api/v1/contact/${payload.id}`, payload)
        },
        async updateContactSomeValues(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.patch(`/system-preferences/api/v1/contact/${payload.id}`, payload)
        },
        async pitchUpdateContact(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.patch(`/system-preferences/api/v1/contact/${payload.id}`, payload)
        },
        async deleteContact(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            return http.delete(`/system-preferences/api/v1/contact/${payload}`)
        },
    },
}

export default ContactStore
