/** @format */

import service from '../../../../api/request'
import serviceV1 from '../../../../api/requestNew'
import moment from 'moment'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import md5 from 'js-md5'
import type {ActionContext} from 'vuex'

const fiveCurrencyUrl = []
dayjs.extend(customParseFormat)
const http = service
const httpV1 = serviceV1
const today = moment().format('YYYY-MM-DD')
const spotCurrencyStore = {
    namespaced: true,
    state: {
        spotCurrencyList: [],
    },
    mutations: {
        updateSpotCurrencyStoreList(state: {spotCurrencyList: any[]}, list: any) {
            state.spotCurrencyList = [...list]
        },
    },

    actions: {
        async fetchSpotCurrency(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const startDate = dayjs(today).subtract(11, 'day').format('YYYY-MM-DD')
            const endDate = dayjs(today).format('YYYY-MM-DD')
            const re = await http.get(
                `outer/currency/multi/exchange?startDate=${startDate}&endDate=${endDate}&token=${md5(
                    startDate + endDate + 'sap_zhiren_test',
                )}`,
            )
            store.commit('updateSpotCurrencyStoreList', re.data.data)
        },
        async fetchSpotCurrencyV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>) {
            const startDate = dayjs(today).subtract(11, 'day').format('YYYY-MM-DD')
            const endDate = dayjs(today).format('YYYY-MM-DD')
            const re = await httpV1.get(
                `/system-preferences/api/v1/currency-exchange?rate_date[$gte]=${startDate}&rate_date[$lte]=${endDate}&$sort[rate_date]=desc`,
            )
            store.commit('updateSpotCurrencyStoreList', re.data.data)
        },
        async upsertSpotCurrency(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await http.post('/bk/currency', {
                ...payload,
                currencyFr: 'USD',
                currencyTo: 'CAD',
            })
            return response
        },
        async upsertSpotCurrencyV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpV1.post('/system-preferences/api/v1/currency-exchange', {
                ...payload,
                currency_fr: 'USD',
                currency_to: 'CAD',
            })
            return response
        },
        async editSpotCurrencyV1(store: ActionContext<{[key: string]: any}, Record<string, unknown>>, payload: any) {
            const response = await httpV1.patch(`/system-preferences/api/v1/currency-exchange/${payload.id}`, {
                ...payload,
            })
            return response
        },
    },
}

export default spotCurrencyStore
