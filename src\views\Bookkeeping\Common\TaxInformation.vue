<!-- @format -->

<script lang="ts" setup>
import {useStore} from 'vuex'
import validator from 'validator'
import {computed, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch, onBeforeMount, createVNode} from 'vue'
import {message, type FormInstance, Modal} from 'ant-design-vue'
import type {Composer} from 'vue-i18n'
import i18nInstance from '@/locales/i18n'
import {useRouter} from 'vue-router'
import {UserInfo, UserCompany, LocalCurrency, CurrencyCountry, CurrencyLanguage, FinancialYear} from '@/lib/storage'
import BankInformation from '@/views/Bookkeeping/Common/BankInformation.vue'
import Sap from '@/views/Bookkeeping/Common/Sap.vue'
import OutboundingLogs from '@/views/Bookkeeping/Common/OutboundingLogs.vue'
import InboundingLogs from '@/views/Bookkeeping/Common/InboundingLogs.vue'
// import Logs from '@/views/Bookkeeping/Common/Logs.vue'
import {EyeOutlined, EyeInvisibleOutlined, ExclamationCircleOutlined} from '@ant-design/icons-vue'
import * as _ from 'lodash'
import dayjs from 'dayjs'
import type {Dayjs} from 'dayjs'

const i18n: Composer = i18nInstance.global
const store = useStore()
const router = useRouter()
const formRef = ref<FormInstance>()
const form = ref<any>({})
const itemList = ref([] as any[])
const companyLogo = reactive({
    dataStr: '',
    dataObj: null,
})
const editable = ref(false)
const formLoading = ref(false)
const logoLoading = ref(false)
const logoList = ref([] as any[])
const SINGLE_FILE_SIZE_LIMIT = ref(2) // unit: MB
const activeName = ref('pending')
const email = ref('')
const tableLoading = ref(false)
const showTutorial = ref(false)
const userCompany: any = UserCompany.get() || []
const userInfo: any = UserInfo.get() || {}
const isKycAdmin = computed(() => ['kycadmin', 'ntadmin'].includes(userInfo.account))
// mapActions
const fetchCompanyTaxInfo = (query: any) => store.dispatch('TaxInfoStore/fetchCompanyTaxInfoV1', query)
const fetchCompanyLogo = (payload: any) => store.dispatch('TaxInfoStore/fetchCompanyLogo', payload)
const updateCompanyTax = (payload: any) => store.dispatch('TaxInfoStore/updateCompanyTaxV1', payload)
const uploadLogo = (payload: any) => store.dispatch('TaxInfoStore/uploadLogoV1', payload)
const removelogo = (payload: any) => store.dispatch('TaxInfoStore/uploadLogoV1', payload)
const fetchBankInfo = (query: any) => store.dispatch('BankInfoStore/fetchBankInfoV1', query)
const handleClick = (tab: any, event: any) => {
    console.log(tab, event)
}
const updateData = async () => {
    try {
        formLoading.value = true

        await fetchCompanyTaxInfo({code: userCompany[0].code})
        // await fetchBankInfo(userCompany[0].id)
        form.value = _.cloneDeep({...companyTaxInfo.value})
        companyLogo.dataStr = companyLogoUrl.value
        // this.companyLogo = this.companyLogoUrl
    } catch (error) {
        console.log(error)
    } finally {
        formLoading.value = false
    }
}
const updateLogoImg = async (loading = true) => {
    try {
        logoLoading.value = true && loading
        const response = await fetchCompanyTaxInfo({code: userCompany[0].code})
        if (response.status === 200) {
            companyLogo.dataStr = companyLogoUrl.value
        }
    } catch (error) {
        console.log(error)
    } finally {
        logoLoading.value = false
    }
}
const edit = () => {
    toggleEditable(true)
}
const cancel = async () => {
    toggleEditable(false)
    form.value = {...companyTaxInfo.value}
    ;(formRef.value as any).clearValidate()
}
const toggleEditable = (bool: any) => {
    editable.value = bool
}

const readAsDataURL = (file: any) => {
    return new Promise((resolve, reject) => {
        const fileReader = new FileReader()
        fileReader.onload = function () {
            return resolve({data: fileReader.result, name: file.name, size: file.size, type: file.type})
        }
        fileReader.readAsDataURL(file)
    })
}

const save = async () => {
    if (await formRef.value?.validateFields()) {
        try {
            formLoading.value = true
            const query = {
                //todo: booker
                ...form.value,
            }
            const response = await updateCompanyTax(query)
            if (response.status === 200) {
                message.success(i18n.t('ApComponents.success'))
                LocalCurrency.set(query.currency)
                CurrencyCountry.set(query.country)
                CurrencyLanguage.set(query.language)
                FinancialYear.set(query.financial_year)
                toggleEditable(false)
            } else {
                // message.error({
                //     content: 'failed',
                //     duration: 3,
                // })
            }
        } catch (error) {
            console.log(error)
        } finally {
            formLoading.value = false
        }
    } else {
        return false
    }
}
const removeImagebutton = async (e: any) => {
    const response = await removelogo({id: form.value.id, uploadFile: ''})
    if (response.data.code === 200) {
        message.success(i18n.t('ApComponents.success'))
    }
    await updateLogoImg()
}
const logoListChange = async ({file, fileList}: any) => {
    console.log(companyLogo)
    const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
    // file format limit
    if (!isJPG) {
        message.error({
            content: i18n.t('editCompanyManager.uploadLogoFormat'),
            duration: 3,
        })
        clearLogoList()
        return false
    }
    // file size limit
    const isLt2M = file.size / 1024 / 1024 < SINGLE_FILE_SIZE_LIMIT.value
    if (!isLt2M) {
        message.error({
            content: i18n.t('editCompanyManager.uploadLogoSize'),
            duration: 3,
        })
        clearLogoList()
        return false
    }
    await handleUploadLogoRequest(file)
}
const handleUploadLogoRequest = async (content: any) => {
    try {
        logoLoading.value = true
        const fileObj: any = await readAsDataURL(content.originFileObj)
        const response = await uploadLogo({id: form.value.id, uploadFile: fileObj.data})
        if (response.status === 200) {
            message.success(i18n.t('ApComponents.success'))
            companyLogo.dataStr = response.data.logo
            clearLogoList()
            // await updateLogoImg()
        } else {
            // message.error({
            //     content: response.statusText,
            //     duration: 3,
            // })
            companyLogo.dataStr = ''
        }
    } catch (error) {
        console.log(error)
    } finally {
        logoLoading.value = false
    }
}
// const logoUploadError = (err: any, file: any, fileList: any) => {
//     message.error({
//         content: 'Upload Error',
//         duration: 3,
//     })
// }
const clearLogoList = () => {
    // TODO: antd upload
    // uploadCompanyLogo.clearFiles()
}
const showLoading = () => {
    tableLoading.value = true
}
const closeLoading = () => {
    tableLoading.value = false
}

// computed mapState
const companyTaxInfo = computed(() => store.state.TaxInfoStore.companyTaxInfo)
const companyLogoUrl = computed(() => store.state.TaxInfoStore.companyLogoUrl)
const accountCurrencyOptions: any = computed(() => store.state.CommonDropDownStore.bankCurrencyOptions)
const bankInfo = computed(() => store.state.BankInfoStore.bankInfo)

const requireRule = (propName: any) => [
    {
        required: true,
        message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
        trigger: 'blur',
    },
]
const lengthLimitRule = (propName: any, length: any) => {
    const desc = propName === 'gstNo' ? '********** RT1234' : '1234 567890 TQ1234'
    return [
        {
            min: length,
            max: length,
            message: i18n.t('TaxInformation.recorrrect', {msg: i18n.t('taxInfo.' + propName), desc: desc}),
            trigger: 'blur',
        },
    ]
}
const isPhoneNumRule = async (rule: any, value: any) => {
    if (!validator.isNumeric(value) || value.length !== 10) {
        return Promise.reject(i18n.t('TaxInformation.phoneNo'))
    }
    return Promise.resolve()
}
const connect_requireRule = (propName: any) => {
    return [
        {
            required: true,
            message: i18n.t('bkCommonTag.msgRequireRule') + `${propName}`,
            trigger: ['blur', 'change'],
        },
    ]
}
// const rules = reactive({
//     phone: [...requireRule(i18n.t('taxInfo.companyPhone')), {validator: isPhoneNumRule}],
//     gst_hst_no: [...lengthLimitRule('gst_hst_no', 17)],
//     qst_pst_no: [...lengthLimitRule('qst_pst_no', 18)],
//     source: [...connect_requireRule(i18n.t('connectivity.source'))],
//     // password: {},
//     mail_host: [{required: true, message: 'Please input email host.', trigger: ['blur', 'change']}],
//     mail_user: [{required: true, type: 'email', trigger: ['blur', 'change']}],
//     mail_pass: [{required: true, message: 'Please input email password.', trigger: ['blur', 'change']}],
//     // start_date: {},
// })
const rules = computed(() => ({
    phone: [...requireRule(i18n.t('taxInfo.companyPhone')), {validator: isPhoneNumRule}],
    gst_hst_no: [...lengthLimitRule('gst_hst_no', 17)],
    qst_pst_no: [...lengthLimitRule('qst_pst_no', 18)],
    source: [...connect_requireRule(i18n.t('connectivity.source'))],
    mail_host: [{required: true, message: 'Please input email host.', trigger: ['blur', 'change']}],
    mail_user:
        smtpFormState.value.mail_type === 6
            ? [{required: true, message: 'Please input Client ID.', trigger: ['blur', 'change']}]
            : [{required: true, type: 'email', trigger: ['blur', 'change']}],
    mail_pass: [{required: true, message: 'Please input email password.', trigger: ['blur', 'change']}],
}))
const addTutorial = () => {
    nextTick(() => {
        const financialYearStart: any = document.getElementById('financial_year_start')
        const currency: any = document.getElementById('currency')
        financialYearStart.scrollIntoView()

        const tutorialHeight = financialYearStart.clientHeight + currency.clientHeight + 24 + 20
        const tutorialWidth = financialYearStart.clientWidth + 20
        const bodyRect = document.body.getBoundingClientRect()
        const elemRect = financialYearStart.getBoundingClientRect()
        const topOffset = elemRect.top - bodyRect.top - 10
        const leftOffset = elemRect.left - bodyRect.left - 10

        const appEle: any = document.getElementById('app')
        const tutorialDiv = document.createElement('div')
        tutorialDiv.id = 'turorialTaxInfo'
        const tutorialStr = `
                <div class="tutorial-main-page" style="height: ${tutorialHeight}px; width: ${tutorialWidth}px; top: ${topOffset}px; left: ${leftOffset}px">
                    <div
                        class="inner-content"
                        style="height: ${tutorialHeight - 10}px; width: ${
            tutorialWidth - 10
        }px; top: ${topOffset}px; left: ${leftOffset}px"
                    >
                    </div>
                </div>
                <div
                    class="tutorial-tips"
                    style="top: ${topOffset - 100}px; left: ${
            leftOffset + financialYearStart.clientWidth - 120
        }px; width: 240px"
                >
                    <div class="tips-content">
                        <div class="title">${i18n.t('TaxInformation.FY')}</div>
                        <button class="btn-select" type="link" id="turorialTaxInfoBtn"> Got it </button>
                    </div>
                    <div class="arrow-down"></div>
                </div>
                `
        tutorialDiv.innerHTML = tutorialStr
        appEle.appendChild(tutorialDiv)
    })
}
onMounted(async () => {
    itemList.value = ['common', router.currentRoute.value.meta.title]
    await Promise.all([updateData()])

    nextTick(() => {
        if (_.isEmpty(router.currentRoute.value.query) || !router.currentRoute.value.query.showTutorial) return
        if (_.isEmpty(router.currentRoute.value.query) || !router.currentRoute.value.query.id) {
            addTutorial()
            setTimeout(() => {
                const _turorialTaxInfoBtn = document.getElementById('turorialTaxInfoBtn')
                const _turorialTaxInfo = document.getElementById('turorialTaxInfo')
                if (_turorialTaxInfoBtn)
                    _turorialTaxInfoBtn.addEventListener('click', function () {
                        _turorialTaxInfo!.remove()
                        router.push({
                            path: '/bookkeeping/ap/uploadInvoice',
                            query: {showTutorial: 'true'},
                        })
                    })
            }, 100)
            return
        }
        showTutorial.value = true
        activeName.value = router.currentRoute.value.query.id as any
    })
})
onBeforeUnmount(() => {
    const x = document.getElementById('turorialTaxInfo')
    if (x) x.remove()
})

interface ImportForm {
    source: string
    username: string
    password: string
    startDate: Dayjs | string
    endDate: Dayjs | string
}

interface SMTPForm {
    id?: number
    mail_host: string
    mail_user: string
    mail_pass: string
    start_date: Dayjs | string
    company_id: number
    company_code: string
    // mail_port: number
    // mail_tls: number
    mail_type: number
    isPowerAutomate: boolean
    status: boolean | null
}

interface NetdiskForm {
    id?: number
    secret: string
    app_id: string
    key: string
    disk_type: string
}

const fetchSMTP = (payload: any) => store.dispatch('ConnectivityStore/fetchSMTP', payload)
const createSMTP = (payload: any) => store.dispatch('ConnectivityStore/createSMTP', payload)
const updateSMTP = (payload: any) => store.dispatch('ConnectivityStore/updateSMTP', payload)

//const fetchSMTP = (payload: any) => store.dispatch('ConnectivityStore/fetchSMTP')

const importFromOptions = computed(() => store.getters['ConnectivityStore/importSources'])

const smtpStoreList = computed<SMTPForm[]>(() => store.state.ConnectivityStore.smtpItems)

const connect_rules = reactive({
    source: [...connect_requireRule(i18n.t('connectivity.source'))],
    // password: {},
    mail_host: [{required: true, message: 'Please input email host.', trigger: ['blur', 'change']}],
    mail_user: [{required: true, type: 'email', trigger: ['blur', 'change']}],
    mail_pass: [{required: true, message: 'Please input email password.', trigger: ['blur', 'change']}],
    // start_date: {},
})

const state = reactive({
    activeKey: 'import',
    formLoading: true,
})
const passwordInput = reactive({
    enableDisplay: false,
    inputType: 'password',
})
// const submitLoading = ref(false)
const isEnableSubmit = ref(false)

const passwordDisplaySwitch = () => {
    passwordInput.enableDisplay = !passwordInput.enableDisplay
    passwordInput.inputType = passwordInput.enableDisplay ? 'text' : 'password'
}

// const formRef = ref<FormInstance>()
const sourceFormState = ref<ImportForm>({
    source: 'shopify',
    username: '',
    password: '',
    startDate: dayjs(),
    endDate: dayjs(),
})

const initSMTPState: SMTPForm = {
    mail_host: '',
    mail_user: '',
    mail_pass: '',
    start_date: dayjs(),
    company_id: 0,
    company_code: '',
    // mail_port: 0,
    // mail_tls: 0,
    mail_type: 1,
    isPowerAutomate: false,
    status: null,
    tenant_id: '',
    client_id: '',
    client_secret: ''
}

const initNetdiskState: NetdiskForm = {
    secret: '',
    app_id: '',
    key: '',
    disk_type: '',
}

const smtpFormState = ref<SMTPForm>({...initSMTPState})
const smtpFormRef = ref<FormInstance>()
const netDiskFormState = ref<NetdiskForm>({...initNetdiskState})
const getImageUrl = (name: any) => {
    return new URL(`@/assets/image/${name}`, import.meta.url).href
}
const serverTypeOptions = ref([
    {
        key: 1,
        value: 1,
        label: 'Hotmail',
        img: 'server/hotmail.png',
    },
    {
        key: 2,
        value: 2,
        label: 'Outlook.com',
        img: 'server/outlook.png',
    },
    {
        key: 3,
        value: 3,
        label: 'Office 365',
        img: 'server/office365.png',
    },
    {
        key: 4,
        value: 4,
        label: 'Other POP, IMAP',
        img: 'server/other.png',
    },
    {
        key: 5,
        value: 5,
        label: 'Sap',
        img: 'server/sap.png',
    },
    {
        key: 6,
        value: 6,
        label: 'Outlook  Enterprise',
        img: 'server/outlook.png',
    },
])

const netdiskTypeOptions = ref([
    {
        key: 1,
        value: 1,
        label: 'Google Doc',
        img: 'netdisk/googledoc.png',
    },
    {
        key: 2,
        value: 2,
        label: 'Drop Box',
        img: 'netdisk/dropbox.png',
    },
])

const validateSourceEnableSubmit = () => {
    isEnableSubmit.value = !!(
        sourceFormState.value.source &&
        sourceFormState.value.username &&
        sourceFormState.value.password
    )
}
const validateSMTPEnableSubmit = async () => {
    try {
        const result = await smtpFormRef.value?.validateFields()
        if (result) {
            if(smtpFormState.value.isPowerAutomate) {
                showConfirm()
            }else {
                await submitSMTP()
            }
        }
    } catch (e) {
        return
    }
    // isEnableSubmit.value = !!(
    //     smtpFormState.value.mail_type &&
    //     smtpFormState.value.mail_user &&
    //     smtpFormState.value.mail_pass
    // )
}
const validateNetDiskEnableSubmit = () => {
    isEnableSubmit.value = !!(
        sourceFormState.value.source &&
        sourceFormState.value.username &&
        sourceFormState.value.password
    )
}

const submitImport = () => {
    console.log(sourceFormState)
}
const submitSMTP = async () => {
    state.formLoading = true
    const payload = {...smtpFormState.value}
    payload.company_code = userCompany[0].code
    payload.company_id = userCompany[0].id
    try {
        const response = payload.id ? await updateSMTP(payload) : await createSMTP(payload)
        if ([200, 201].includes(response.status)) {
            message.success(i18n.t('ApComponents.success'))
            await fetchSMTP({company_code: userCompany[0].code})
        } else {
            // message.error({content: response.data.message ?? 'failed'})
        }
    } catch (e: any) {
        console.log(e)
    } finally {
        state.formLoading = false
    }
}
const submitNetdisk = async () => {
    console.log('save netdisk')
}

const handleSourceChange = (action: string, e: any) => {
    // TODO
}

const handleSMTPServerChange = (action: string, e: any) => {
    smtpFormState.value.mail_type = e
    smtpFormRef.value?.clearValidate()
    console.log('select', smtpFormState.value)
}
const handleNetdiskServerChange = (action: string, e: any) => {
    // netDiskFormState.value.disk_type = e
    console.log('select', netDiskFormState.value)
}

const showConfirm = () => {
  Modal.confirm({
    title: 'Warning',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, 'Power Automate may take several hours to activate. Once initialization is complete, the status will update to "Running".'),
    async onOk() {
      console.log('OK');
      await submitSMTP();
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};

watch(
    () => smtpStoreList.value,
    (count, preCount) => {
        if (smtpStoreList.value.length > 0) {
            smtpFormState.value = {...smtpStoreList.value[0]}
        }
    },
)

watch(
    () => smtpFormState.value.mail_type,
    (count, prevCount) => {
        console.log(count, prevCount)
        const selected = smtpStoreList.value.find((x: SMTPForm) => x.mail_type === smtpFormState.value.mail_type)
        if (selected) {
            smtpFormState.value = {...selected}
        } else {
            // Object.assign(smtpFormState.value, initSMTPState)
            smtpFormState.value = {...initSMTPState}
            smtpFormState.value.mail_type = count
        }
    },
)

onBeforeMount(async () => {
    await Promise.all([fetchSMTP({company_code: userCompany[0].code})])
    state.formLoading = false
})

onMounted(async () => {
    console.log(smtpStoreList.value)
    state.formLoading = false
})
</script>
<template>
    <div class="page-container-gl_list">
        <a-tabs class="header-tabs" v-model:activeKey="activeName" @change="handleClick">
            <a-tab-pane :disabled="tableLoading" :tab="i18n.t('router.commonCompany')" key="pending">
                <a-form
                    ref="formRef"
                    :model="form"
                    :rules="rules"
                    :layout="'vertical'"
                    label-width="auto"
                    label-position="top"
                    class="form-box"
                >
                    <div class="ap-invoice-block">
                        <a-row :gutter="24">
                            <a-col :span="12">
                                <a-form-item :label="i18n.t('taxInfo.companyLogo')" name="companyLogo">
                                    <a href="javascript:void(0)">
                                        <div
                                            class="company-info-logo-wrapper"
                                            :style="companyLogo.dataStr ? {'text-align': 'left'} : {}"
                                        >
                                            <img v-if="companyLogo.dataStr" :src="companyLogo.dataStr" alt="" />
                                            <div v-else class="company-info-logo-img-placeholder">
                                                <img src="@/assets/image/empty.png" />
                                                <div title="companyLogo"></div>
                                            </div>
                                        </div>
                                    </a>
                                    <!-- </el-upload> -->
                                </a-form-item>
                                <a-form-item style="margin-bottom: 3px">
                                    <a-upload
                                        :loading="logoLoading"
                                        ref="uploadCompanyLogo"
                                        action=""
                                        :multiple="false"
                                        name="file"
                                        :custom-request="() => {}"
                                        v-model:file-list="logoList"
                                        @change="logoListChange"
                                        @remove="removeImagebutton"
                                        list-type="picture"
                                        :accept="'image/jpeg,image/png'"
                                        v-model:value="companyLogo.dataStr"
                                        :show-upload-list="false"
                                        :max-count="1"
                                    >
                                        <a class="ant-upload-text" style="padding-right: 10px">{{
                                            i18n.t('taxInfo.editLogoBtn')
                                        }}</a>
                                    </a-upload>
                                    <a class="ant-upload-text" @click="removeImagebutton">{{
                                        i18n.t('taxInfo.rmLogoBtn')
                                    }}</a>
                                    <!-- <el-upload v-loading="logoLoading" action="" ref="uploadCompanyLogo"
                    :http-request="handleUploadLogoRequest" v-model:value="companyLogo"
                    class="upload-demo form-item-field" :file-list="logoList" :auto-upload="false"
                    :on-change="logoListChange" :on-remove="removeImagebutton" :on-error="logoUploadError" :limit="1"
                    list-type="picture" :show-file-list="false">
                    <el-button type="text" icon="el-icon-picture">Change Image</el-button>
                  </el-upload> -->
                                </a-form-item>
                                <a-form-item :label="i18n.t('taxInfo.companyName')" name="companyName">
                                    <a-input v-model:value="form.name" disabled></a-input>
                                </a-form-item>
                                <a-form-item :label="i18n.t('taxInfo.companyAddress')" name="companyAddress">
                                    <a-input
                                        type="textarea"
                                        v-model:value="form.address_line1"
                                        :autosize="{minRows: 2, maxRows: 3}"
                                        disabled
                                    >
                                    </a-input>
                                </a-form-item>
                                <a-form-item :label="i18n.t('taxInfo.companyEmail')" name="companyEmail">
                                    <a-input v-model:value="form.email" :disabled="!editable"></a-input>
                                </a-form-item>
                                <a-form-item :label="i18n.t('taxInfo.companyPhone')" name="companyPhone">
                                    <a-input v-model:value="form.phone" :disabled="!editable"></a-input>
                                </a-form-item>

                                <a-form-item
                                    v-for="item in form.tax_chart || []"
                                    :label="item.name"
                                    :name="item.name"
                                    :key="item.name"
                                >
                                    <a-input v-model:value="item.value" :disabled="!editable"></a-input>
                                </a-form-item>
                                <!-- <a-form-item :label="i18n.t('taxInfo.gstNo')" name="gstNo">
                                    <a-input v-model:value="form.gst_hst_no" :disabled="!editable"></a-input>
                                </a-form-item>
                                <a-form-item :label="i18n.t('taxInfo.qstNo')" name="qstNo">
                                    <a-input v-model:value="form.qst_pst_no" :disabled="!editable"></a-input>
                                </a-form-item> -->
                                <a-form-item :label="$t('router.fy')" id="financial_year_start" name="qstNo">
                                    <a-date-picker
                                        v-model:value="form.financial_year"
                                        :disabled="!editable || form.financial_year"
                                        format="YYYY-MM"
                                        valueFormat="YYYY-MM"
                                        style="width: 100%"
                                        clearable
                                    >
                                    </a-date-picker>
                                </a-form-item>
                                <a-form-item :label="$t('router.localCurrency')" id="currency" name="currency">
                                    <a-select
                                        :disabled="!editable || !!form.currency"
                                        :placeholder="i18n.t('workTimeManager.msgInput')"
                                        v-model:value="form.currency"
                                    >
                                        <a-select-option
                                            v-for="item in accountCurrencyOptions"
                                            :key="item.key"
                                            :value="item.key"
                                            >{{ item.value }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-item>
                                <a-form-item>
                                    <div class="btn-group">
                                        <div v-show="!editable">
                                            <a-button
                                                type="primary"
                                                :disabled="editable || formLoading"
                                                class="btn"
                                                @click="edit"
                                                shape="round"
                                            >
                                                {{ i18n.t('commonTag.edit') }}
                                            </a-button>
                                        </div>
                                        <div class="btn-subgroup" v-show="editable">
                                            <a-button type="primary" class="btn" @click="save" shape="round">
                                                {{ i18n.t('commonTag.save') }}
                                            </a-button>
                                            <a-button type="text" class="btn" @click="cancel" shape="round">
                                                {{ i18n.t('commonTag.cancel') }}
                                            </a-button>
                                        </div>
                                    </div>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </div>
                </a-form>
            </a-tab-pane>
            <a-tab-pane :disabled="tableLoading" :tab="i18n.t('router.bankInformation')" key="third">
                <div :loading="tableLoading">
                    <bank-information
                        @closeLoading="closeLoading"
                        @showLoading="showLoading"
                        :company="companyTaxInfo"
                        :email="form.email"
                        :showTutorial="showTutorial"
                    >
                    </bank-information>
                </div>
            </a-tab-pane>
            <a-tab-pane
                key="import"
                v-if="!['8888', '8001'].includes(userCompany[0].code)"
                :tab="$t('SettingConnectivities.channel')"
            >
                <div class="content-box-import-form">
                    <div class="import-form-title">{{ i18n.t('SettingConnectivities.importBilling') }}</div>
                    <div class="import-form-wrap">
                        <a-form
                            :model="sourceFormState"
                            :layout="'vertical'"
                            autocomplete="off"
                            @submit="submitImport"
                            @validate="validateSourceEnableSubmit"
                        >
                            <div class="import-form-block">
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-form-item name="source" :label="i18n.t('connectivity.source')">
                                            <a-select
                                                :options="importFromOptions"
                                                :placeholder="i18n.t('connectivity.sourceHolder')"
                                                v-model:value="sourceFormState.source"
                                                @change="handleSourceChange('ship', $event)"
                                            >
                                            </a-select>
                                            <div
                                                class="img-wrap"
                                                v-if="
                                                        sourceFormState.source &&
                                                        importFromOptions.find((i:any) => i.value == sourceFormState.source)
                                                    "
                                            >
                                                <img
                                                    style="height: 36px; padding: 5px 10px"
                                                    :src="importFromOptions.find((i:any) => i.value == sourceFormState.source).image"
                                                />
                                            </div>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12"
                                        ><div>
                                            <img
                                                v-if="
                                                        sourceFormState.source &&
                                                        importFromOptions.find((i:any) => i.value == sourceFormState.source)
                                                    "
                                                class="shopify-logo-wrap"
                                                :src="importFromOptions.find((i:any) => i.value == sourceFormState.source).image"
                                            />
                                        </div>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-form-item required name="username" :label="i18n.t('connectivity.username')">
                                            <a-input v-model:value="sourceFormState.username" />
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item required name="password" :label="i18n.t('connectivity.password')">
                                            <a-input
                                                v-model:value="sourceFormState.password"
                                                :type="passwordInput.inputType"
                                            >
                                                <template #suffix>
                                                    <div @click="passwordDisplaySwitch">
                                                        <eye-invisible-outlined v-if="passwordInput.enableDisplay" />
                                                        <eye-outlined v-else />
                                                    </div>
                                                </template>
                                            </a-input>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-form-item
                                            required
                                            name="startDate"
                                            :label="i18n.t('connectivity.startDate')"
                                        >
                                            <a-date-picker
                                                v-model:value="sourceFormState.startDate"
                                                value-format="YYYY-MM-DD"
                                                style="width: 100%"
                                            >
                                                <!--                                                <template #suffixIcon> </template>-->
                                            </a-date-picker>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item required name="endDate" :label="i18n.t('connectivity.endDate')">
                                            <a-date-picker
                                                v-model:value="sourceFormState.endDate"
                                                value-format="YYYY-MM-DD"
                                                style="width: 100%"
                                            >
                                                <!--                                            <template #suffixIcon> </template>-->
                                            </a-date-picker>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                            </div>
                            <div style="text-align: right">
                                <a-button shape="round" :disabled="!isEnableSubmit" type="primary" html-type="submit">{{
                                    i18n.t('SettingConnectivities.import')
                                }}</a-button>
                            </div>
                        </a-form>
                    </div>
                </div>
            </a-tab-pane>
            <a-tab-pane key="smtp" :tab="$t('SettingConnectivities.SMTP')">
                <div class="content-box-smtp">
                    <div class="content-box-smtp-wrap">
                        <a-form
                            ref="smtpFormRef"
                            :rules="rules"
                            :model="smtpFormState"
                            :layout="'vertical'"
                            autocomplete="off"
                        >
                            <div class="import-form-block">
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-checkbox v-model:checked="smtpFormState.isPowerAutomate">{{ i18n.t('connectivity.powerAutomate') }}</a-checkbox>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48" v-if="!smtpFormState.isPowerAutomate">
                                    <a-col :span="12">
                                        <a-form-item name="mail_type" :label="i18n.t('connectivity.server')">
                                            <a-select
                                                :placeholder="i18n.t('connectivity.sourceHolder')"
                                                v-model:value="smtpFormState.mail_type"
                                                @change="handleSMTPServerChange('ship', $event)"
                                                :options="serverTypeOptions"
                                            >
                                            </a-select>
                                            <div
                                                class="img-wrap"
                                                v-if="
                                                    smtpFormState.mail_type &&
                                                    serverTypeOptions.find(i => i.value == +smtpFormState.mail_type)
                                                "
                                            >
                                                <img
                                                    v-if="smtpFormState.mail_type == 1"
                                                    style="height: 36px; padding: 5px 10px"
                                                    src="@/assets/image/server/hotmail.png"
                                                />
                                                <img
                                                    v-if="smtpFormState.mail_type == 2"
                                                    style="height: 36px; padding: 5px 10px"
                                                    src="@/assets/image/server/outlook.png"
                                                />
                                                <img
                                                    v-if="smtpFormState.mail_type == 3"
                                                    style="height: 36px; padding: 5px 10px"
                                                    src="@/assets/image/server/office365.png"
                                                />
                                                <img
                                                    v-if="smtpFormState.mail_type == 4"
                                                    style="height: 36px; padding: 5px 10px"
                                                    src="@/assets/image/server/other.png"
                                                />
                                                <img
                                                    v-if="smtpFormState.mail_type == 5"
                                                    style="height: 36px; padding: 5px 10px"
                                                    src="@/assets/image/server/sap.png"
                                                />
                                            </div>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48" v-if="!smtpFormState.isPowerAutomate">
                                    <a-col :span="12">
                                        <a-form-item
                                            name="mail_host"
                                            v-if="smtpFormState.mail_type === 6"
                                            label="Tenant ID"
                                        >
                                            <a-input v-model:value="smtpFormState.tenant_id" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48" v-if="!smtpFormState.isPowerAutomate">
                                    <a-col :span="12">
                                        <a-form-item
                                            name="mail_host"
                                            v-if="smtpFormState.mail_type === 4"
                                            :label="i18n.t('connectivity.emailHost')"
                                        >
                                            <a-input v-model:value="smtpFormState.mail_host" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-form-item name="mail_user"  v-if="smtpFormState.mail_type === 6" label="Client ID">
                                            <a-input v-model:value="smtpFormState.client_id" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-form-item name="mail_pass" v-if="smtpFormState.mail_type != 6" :label="$t('SettingConnectivities.pass')">
                                            <a-input
                                                v-model:value="smtpFormState.mail_pass"
                                                :type="passwordInput.inputType"
                                            >
                                                <template #suffix>
                                                    <div @click="passwordDisplaySwitch">
                                                        <eye-invisible-outlined v-if="passwordInput.enableDisplay" />
                                                        <eye-outlined v-else />
                                                    </div>
                                                </template>
                                            </a-input>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-form-item name="mail_pass" v-if="smtpFormState.mail_type === 6" label="Client Secret">
                                            <a-input
                                                v-model:value="smtpFormState.client_secret"
                                            />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48">
                                    <a-col :span="12">
                                        <a-form-item name="mail_user"  :label="i18n.t('connectivity.email')">
                                            <a-input v-model:value="smtpFormState.mail_user" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48" v-if="!smtpFormState.isPowerAutomate">
                                    <a-col :span="12">
                                        <a-form-item
                                            name="start_date"
                                            :disabled="Number(smtpFormState.id) > 0"
                                            :label="i18n.t('connectivity.startDate')"
                                        >
                                            <a-date-picker
                                                v-model:value="smtpFormState.start_date"
                                                value-format="YYYY-MM-DD"
                                                style="width: 100%"
                                            >
                                                <!--   <template #suffixIcon> </template>-->
                                            </a-date-picker>
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                                <a-row :gutter="48" v-if="smtpFormState.isPowerAutomate">
                                    <a-col :span="12">
                                        <a-tag color="orange" v-if="smtpFormState.status !== null && !smtpFormState.status">
                                            Initializing
                                        </a-tag>
                                    </a-col>
                                </a-row>
                            </div>
                            <div style="text-align: left">
                                <a-button shape="round" type="primary" @click="validateSMTPEnableSubmit">{{
                                    i18n.t('SettingConnectivities.save')
                                }}</a-button>
                                <a-button style="margin-left: 10px" shape="round">{{
                                    i18n.t('commonTag.cancel')
                                }}</a-button>
                            </div>
                        </a-form>
                    </div>
                </div>
            </a-tab-pane>
            <a-tab-pane key="sap" :tab="$t('SAP')">
                <Sap />
            </a-tab-pane>
            <a-tab-pane key="outboundingLogs" :tab="$t('Outbounding Logs')" v-if="isKycAdmin">
                <OutboundingLogs />
            </a-tab-pane>
            <a-tab-pane key="inboundingLogs" :tab="$t('Inbounding Logs')" v-if="isKycAdmin">
                <InboundingLogs />
            </a-tab-pane>
            <!-- <a-tab-pane key="logs" :tab="$t('Logs')" v-if="isKycAdmin">
                <Logs />
            </a-tab-pane> -->
            <!-- <a-tab-pane key="logs" :tab="$t('Logs')" v-if="isKycAdmin">
                <Logs />
            </a-tab-pane> -->
        </a-tabs>
    </div>
</template>
<style lang="scss" scoped>
.company-info-logo-img-placeholder,
.company-info-logo-wrapper {
    img {
        max-height: 105px;
    }
}
:deep(.ant-upload-list) {
    display: inline-block;
    margin-left: 22px;
}

.ap-invoice-block {
    padding-left: 8px;
}
.page-container-connectivities {
    height: 100%;
    .tabs-contents {
        border-radius: 12px;
        flex: 1;
    }
}
.content-box-import-form,
.content-box-smtp {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    padding: 24px 20px 20px;
    background-color: #fff;
    border-top-right-radius: 12px;
    border-top-left-radius: 12px;
    //.content-box-smtp-wrap {
    //    width: 40%;
    //    .btn-auth {
    //        margin-top: 20px;
    //        width: 120px;
    //        height: 36px;
    //    }
    //}
}

.import-form-wrap {
}
.import-form-title {
    //  font-family: Calibri;
    font-size: 44px;
    color: #262626;
    line-height: 54px;
    font-weight: 400;
}

.import-form-block {
    padding: 24px 0;
}

.shopify-logo-wrap {
    height: 71px;
    // width: 237px;
}
.ant-btn-primary[disabled],
.ant-btn-primary[disabled]:hover {
    //@extend .ant-btn-primary;
    opacity: 50%;
    color: #fff;
    border-color: #004fc1;
    background: #004fc1;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}
.img-wrap {
    position: absolute;
    right: 30px;
    top: 0px;
}
</style>
