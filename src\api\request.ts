/** @format */

import axios from 'axios'
import router from '../router'
import {Token} from '@/lib/storage'
import {message} from 'ant-design-vue'

// create an axios instance
const service = axios.create({
    baseURL: import.meta.env.VITE_BASIC_API, // url = base url + request url
    withCredentials: true, // send cookies when cross-domain requests
    timeout: 90000, // request timeout
})
// request interceptor
service.interceptors.request.use(
    config => {
        config.headers = {
            ...config.headers,
            token: Token.get() || '',
        }

        return config
    },
    error => {
        console.log(error) // for debug
        return Promise.reject(error)
    },
)

// response interceptor
service.interceptors.response.use(
    response => {
        if (response.data.code == 1001) {
            setTimeout(() => {
                router.replace({name: 'Login'})
            }, 2000)
        }
        if (response.status !== 200) {
            message.error({content: response.status, duration: 5})
        } else {
            return response
        }
    },
    error => {
        if (error.code === 'ECONNABORTED' || error.message === 'Network Error' || error.message.includes('timeout')) {
            message.error({content: 'Time Out Error.', duration: 6})
        } else if (error?.response?.status === 401) {
            router.replace({name: 'Login'})
        } else {
            message.error({
                content: error.response.data.message || 'failed',
                duration: 5,
            })
        }
        return Promise.reject(error)
    },
)

export default service
