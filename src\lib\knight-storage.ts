/**
 * 设置cookie
 *
 * @format
 * @param {string} cname  cookie名
 * @param {string} cvalue cookie值
 * @param {number} ctime  过期时间 (单位:小时)默认值1天
 * @param {string} domain 域名 (默认为当前的域名)
 * @param {string} path   路径 (默认为 /)
 */

export function setCookie(cname: string, cvalue: any, ctime = 24, domain = null, path = '/') {
    const data = `${cname}=${cvalue}`
    const cpath = `path=${path}`
    const cdomain = `domain=${domain}`

    const time = new Date()
    time.setTime(time.getTime() + ctime * 1000 * 60 * 60)
    const expires = `expires=${time.toUTCString()}`

    const cookie = `${data};${expires};${cpath};${domain == null ? '' : cdomain}`
    document.cookie = cookie
}

/**
 * 获取cookie
 * @param {string} cname 要获取的coolie名
 * @returns {string || null} 返回找到的cookie值,如果找不到,返回null
 */
export function getCookie(cname: string) {
    const cookies = document.cookie.split(';')
    const cookieData: {[key: string]: any} = {}
    cookies.forEach(v => {
        const arr = v.trim().split('=')
        cookieData[arr[0]] = arr[1]
    })

    const value = cookieData[cname]

    if (value) {
        return value
    } else {
        return null
    }
}

/**
 * 删除cookie
 * @param {string} cname 要删除的cookie名
 * @param {string} domain 要删除cookie的域名
 * @param {string} path 要删除cookie的路径
 *
 * ! tips: 设置域名是如果没有传域名和路径，删除时可以不传。 设置时传了，删除时就要传一模一样的，否则无法删除
 */
export function delCookie(cname: string, domain = null, path = '/') {
    const data = `${cname}=''`
    const expires = 'expires=Thu, 18 Dec 1998 12:00:00 GM'
    const cpath = `path=${path}`
    const cdomain = `domain=${domain}`
    const cookie = `${data};${expires};${cpath};${domain == null ? '' : cdomain}`
    document.cookie = cookie
}

export class Cookie {
    name: string
    ctime: any
    domain: any
    path: any
    constructor(name: any, ctime: any, domain: any, path: any) {
        this.name = `${name}`
        this.ctime = ctime
        this.domain = domain
        this.path = path
    }

    get() {
        return getCookie(`${this.name}`)
    }

    set(value: any) {
        setCookie(`${this.name}`, value, this.ctime, this.domain, this.path)
    }

    del() {
        delCookie(`${this.name}`)
    }
}

export class Local {
    name: string
    constructor(name: string) {
        this.name = `${name}`
    }

    get() {
        let data = localStorage.getItem(`${this.name}`)
        try {
            data = data ? JSON.parse(data) : null
        } catch (e) {
            console.log(e)
        }
        return data
    }

    set(value: any) {
        try {
            localStorage.setItem(`${this.name}`, JSON.stringify(value))
        } catch (e) {
            if (value === null) return this.del()
            if (typeof value === 'object') return false
            localStorage.setItem(`${this.name}`, value)
        }
    }

    del() {
        localStorage.removeItem(`${this.name}`)
    }
}

export class Session {
    name: string
    constructor(name: string) {
        this.name = `${name}`
    }

    get() {
        let data = sessionStorage.getItem(`${this.name}`)
        try {
            data = data ? JSON.parse(data) : null
        } catch (e) {
            console.log(e)
        }
        return data
    }

    set(value: any) {
        let data
        try {
            data = JSON.stringify(value)
        } catch (e) {
            data = value
        }
        sessionStorage.setItem(`${this.name}`, data)
    }

    del() {
        sessionStorage.removeItem(`${this.name}`)
    }
}
